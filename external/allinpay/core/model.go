package core

import "encoding/json"

// CommonRequest 公共请求参数
type CommonRequest struct {
	AppID        string `json:"appId"`        // 通商云分配给开发者的应用ID,长度<=32
	Method       string `json:"method"`       //	接口名称,长度<=128
	Format       string `json:"format"`       // 仅支持JSON,长度<=40-------非必填
	Charset      string `json:"charset"`      // 请求使用的编码格式utf-8
	SignType     string `json:"signType"`     // 商户生成签名字符串所使用的签名算法类型，目前支持SHA256WithRSA    SHA256WithRSA
	Sign         string `json:"sign"`         // 请求参数的签名串 <=344
	Timestamp    string `json:"timestamp"`    // 发送请求的时间，格式"yyyy-MM-dd HH:mm:ss"    2014-07-24 03:07:50
	Version      string `json:"version"`      //  调用的接口版本    1.0
	AppAuthToken string `json:"appAuthToken"` //   商户授权令牌,目前保留 <=40 --------非必填
	BizContent   string `json:"bizContent"`   // 请求参数的集合，最大长度不限，除公共参数外所有请求参数都必须放在这个参数中传递，具体参照各产品快速接入文档
}

// CommonResponse 公共响应参数
type CommonResponse struct {
	Code    string          `json:"code"`    // 网关返回码
	Msg     string          `json:"msg"`     // 网关返回码描述
	SubCode string          `json:"subCode"` // 业务返回码
	SubMsg  string          `json:"subMsg"`  // 业务返回码描述
	Sign    string          `json:"sign"`    // 签名
	Data    json.RawMessage `json:"data"`    // 返回参数的集合，最大长度不限，除公共参数外所有返回参数都必须放在这个参数中传递
}

package core

import (
	"base/core/xerr"
	"errors"
	"go.uber.org/zap"
)

var (
	RequestError       = errors.New("request result error")
	VerifyResultError  = errors.New("verify result error")
	SignError          = errors.New("sign error")
	GetPrivateKeyError = errors.New("get private key error")
	EncryptionSIError  = errors.New("encryption sensitive information error")
)

func dealError(res *CommonResponse) error {
	switch res.Code {

	case "10000": // 接口调用成功
		if res.SubCode != "OK" {

		}
		switch res.SubCode {
		case "OK":
			return nil
		case "9000":
			//	 支付账户用户标识已绑定
			return xerr.XerrPayAccHasBind
		case "30024":
			return xerr.XerrMobileHasBind
		case "20011":
			return xerr.XerrVerificationCode
		case "40000":
			//	 订单不存在
			return xerr.XerrPayNoOrder
		case "40033":
			return xerr.XerrPayOrderProcessing
		case "":
			//	提现签约
			return nil
		default:
			zap.S().Errorf("dealError:%v", res)
			return xerr.NewErr(xerr.ErrParamError, nil, res.SubMsg)
		}
		return nil
	case "20000": // 服务不可用
		//

		return nil
	case "40002": // 业务处理失败
		return xerr.NewErr(xerr.ErrParamError, nil, res.Msg)
	case "40004": // 业务处理失败

		return errors.New(res.Msg)

	default:
		return nil
	}
}

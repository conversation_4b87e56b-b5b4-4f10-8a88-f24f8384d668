package allinpay

import "encoding/json"

// VerifyResult 企业信息审核结果通知
type VerifyResult struct {
	BizUserId        string `json:"bizUserId"`
	Result           int    `json:"result"`
	AccountSetResult int    `json:"accountSetResult"`
	CheckTime        string `json:"checkTime"`
	FailReason       string `json:"failReason"`
	Remark           string `json:"remark"`
}

// OcrComparisonResult 影印件核对结果异步通知
type OcrComparisonResult struct {
	BizUserId                 string          `json:"bizUserId"`
	ReqSerialNo               string          `json:"reqSerialNo"`               // 请求流水号  【影印件采集（文件上传模式）】接口上送的reqSerialNo字段
	OcrRegnumComparisonResult int             `json:"ocrRegnumComparisonResult"` //  OCR识别与企业工商认证信息是否一致  OCR识别与企业工商认证信息是否一致 0-否 1-是
	OcrIdcardComparisonResult int             `json:"ocrIdcardComparisonResult"` // OCR识别与企业法人实名信息是否一致   OCR识别与企业法人实名信息是否一致 0-否 1-是
	ResultInfo                string          `json:"resultInfo"`                // 比对结果信息	存在多种结果信息一起返回，使用“;”进行拼接
	OcrBusLicenseInfo         json.RawMessage `json:"ocrBusLicenseInfo"`
	OcrIdCardFrontInfo        json.RawMessage `json:"ocrIdCardFrontInfo"`
	OcrIdCardBackInfo         json.RawMessage `json:"ocrIdCardBackInfo"`
}

// SignAcctProtocolResult 账户提现协议签约
type SignAcctProtocolResult struct {
	BizUserId      string `json:"bizUserId"`
	AcctProtocolNo string `json:"acctProtocolNo"` // 账户提现协议编号	商户端需保存
	SignAcctName   string `json:"signAcctName"`   // 签约账户
	Result         string `json:"result"`         // 签订结果	成功：ok，失败：error
}

package utils

import (
	"encoding/hex"
	"log"
	"reflect"
	"testing"
)

func TestName(t *testing.T) {
	sha1PRNG, err := AesSha1PRNG([]byte("WaHVZNHZYX3v4si1bBTVseIwEMPMcKzL"), 128)
	if err != nil {
		log.Println(err)
		return
	}

	a := hex.EncodeToString(AesEcbEncrypt([]byte("6217003910006526298"), sha1PRNG))
	log.Println(a)

	//	db33c38ec84b2ed8c7564d3953f669117a9fc915c37d556570bae78763673e3f

	bytes, err := hex.DecodeString("db33c38ec84b2ed8c7564d3953f669117a9fc915c37d556570bae78763673e3f")
	if err != nil {

	}

	b := AesEcbDecrypt(bytes, sha1PRNG)
	log.Println(string(b))

}

func TestEcbEncrypt(t *testing.T) {
	type args struct {
		data []byte
		key  []byte
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test", args{[]byte("cnbattle"), []byte("WaHVZNHZYX3v4si1bBTVseIwEMPMcKzz")}, "222c165839da6c857d164dd45d975716"},
		{"test2", args{[]byte("allin"), []byte("WaHVZNHZYX3v4si1bBTVseIwEMPMcKzz")}, "2cd39b5edb9de7dbd7caafc4951e27f5"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := AesEcbEncrypt(tt.args.data, tt.args.key); !reflect.DeepEqual(hex.EncodeToString(got), tt.want) {
				t.Errorf("hex.EncodeToString(EcbEncrypt()) = %v, want %v", hex.EncodeToString(got), tt.want)
			}
		})
	}
}

func TestEcbDecrypt(t *testing.T) {
	type args struct {
		data string
		key  []byte
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test", args{"222c165839da6c857d164dd45d975716", []byte("WaHVZNHZYX3v4si1bBTVseIwEMPMcKzz")}, "cnbattle"},
		{"test2", args{"2cd39b5edb9de7dbd7caafc4951e27f5", []byte("WaHVZNHZYX3v4si1bBTVseIwEMPMcKzz")}, "allin"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			decodeString, err := hex.DecodeString(tt.args.data)
			if err != nil {
				t.Fatal(err)
			}
			if got := AesEcbDecrypt(decodeString, tt.args.key); !reflect.DeepEqual(string(got), tt.want) {
				t.Errorf("string(EcbDecrypt()) = %v, want %v", string(got), tt.want)
			}
		})
	}
}

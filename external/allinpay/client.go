package allinpay

import (
	"github.com/cnbattle/allinpay/core"
	"github.com/cnbattle/allinpay/service"
	"go.uber.org/zap"
)

type AllInPay struct {
	cli             *core.Client
	MemberService   service.MemberService   //会员服务
	OrderService    service.OrderService    //订单服务
	MerchantService service.MerchantService //商家服务
}

func NewAllInPay(config core.Config, logger *zap.Logger) *AllInPay {
	client := core.NewAllInPayClient(config, logger)
	return &AllInPay{
		cli:             client,
		MemberService:   service.NewMember(client),
		OrderService:    service.NewOrder(client),
		MerchantService: service.NewMerchant(client),
	}
}

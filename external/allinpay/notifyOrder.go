package allinpay

import (
	"encoding/json"
)

// DepositApplyOrder 保证金充值
type DepositApplyOrder struct {
	PayStatus              string                 `json:"payStatus"`              // 支付状态  仅交易验证方式为“0”时返回  成功：success  进行中：pending  失败：fail
	PayFailMessage         string                 `json:"payFailMessage"`         // 支付失败信息	仅交易验证方式为“0”时返回 只有payStatus为fail时有效
	BizUserId              string                 `json:"bizUserId"`              // 仅交易验证方式为“0”时返回 平台，返回#yunBizUserId_B2C#
	OrderNo                string                 `json:"orderNo"`                // 云商通订单号
	BizOrderNo             string                 `json:"bizOrderNo"`             // 商户订单号（支付订单）
	ReqPayInterfaceNo      string                 `json:"reqPayInterfaceNo"`      // 请求渠道流水号
	PayInterfaceOutTradeNo string                 `json:"payInterfaceOutTradeNo"` // 渠道交易流水号  针对收银宝相关支付渠道返回，对应收银宝交易单号trxid字段： 微信小程序支付（单、集团）
	PayInterfacetrxcode    string                 `json:"payInterfacetrxcode"`    // 通道交易类型  仅收银宝-付款码支付方式返回，对应收银宝接口字段trxcode
	Acct                   string                 `json:"acct"`                   // 支付人帐号  仅收银宝-付款码支付方式返回， 微信支付的openid
	ChannelFee             string                 `json:"channelFee"`             // 渠道手续费	仅收银宝支付方式返回
	Chnldata               string                 `json:"chnldata"`               // 收银宝渠道信息  透传渠道活动参数，目前返回云闪付/微信/支付宝的活动参数；
	ChannelPaytime         string                 `json:"channelPaytime"`         // 渠道交易完成时间	取值为收银宝接口交易完成时间 格式：yyyyMMddHHmmss
	Cusid                  string                 `json:"cusid"`                  // 	渠道商户号	收银宝商户号
	TradeNo                string                 `json:"tradeNo"`                // 交易编号
	PayInfo                string                 `json:"payInfo"`                // 扫码支付信息/ JS支付串信息（微信、支付宝、QQ钱包）/微信小程序/微信原生H5支付串信息/支付宝原生APP支付串信息    收银宝微信小程序支付参数/微信原生小程序支付参数必传 注：有效时间60分钟
	ValidationType         int                    `json:"validationType"`         // 交易验证方式	当支付方式为收银宝快捷且需验证短信验证码时才返回
	MiniprogramPayInfo_VSP map[string]interface{} `json:"miniprogramPayInfo_VSP"` // 小程序收银台支付参数
	ExtendInfo             string                 `json:"extendInfo"`             // 扩展信息
}

// AgentCollectApply 托管代收
type AgentCollectApply struct {
	PayStatus              string          `json:"payStatus"`              //   支付状态	    仅交易验证方式为“0”时返回 成功：success 进行中：pending 失败：fail 订单成功时会发订单结果通知商户。
	PayFailMessage         string          `json:"payFailMessage"`         //   支付失败信息	仅交易验证方式为“0”时返回 只有payStatus为fail时有效
	BizUserId              string          `json:"bizUserId"`              //    商户系统用户标识，商户系统中唯一编号。	仅交易验证方式为“0”时返回
	OrderNo                string          `json:"orderNo"`                // 必填  云商通订单号
	BizOrderNo             string          `json:"bizOrderNo"`             // 必填 商户订单号（支付订单）
	ReqPayInterfaceNo      string          `json:"reqPayInterfaceNo"`      //      	请求渠道流水号
	PayInterfaceOutTradeNo string          `json:"payInterfaceOutTradeNo"` //     渠道交易流水号   针对收银宝相关支付渠道返回，对应收银宝交易单号trxid字段：  微信小程序支付（单、集团）
	PayInterfacetrxcode    string          `json:"payInterfacetrxcode"`    //     通道交易类型  仅收银宝-付款码支付方式返回
	Acct                   string          `json:"acct"`                   //     支付人帐号  仅收银宝-付款码支付方式返回， 微信支付的openid
	ChannelFee             string          `json:"channelFee"`             //     渠道手续费 仅收银宝支付方式返回
	Chnldata               string          `json:"chnldata"`               //     收银宝渠道信息  透传渠道活动参数，目前返回云闪付/微信/支付宝的活动参数；
	ChannelPaytime         string          `json:"channelPaytime"`         //     渠道交易完成时间   取值为收银宝接口交易完成时间  格式：yyyyMMddHHmmss
	Cusid                  string          `json:"cusid"`                  //     渠道商户	收银宝商户号
	TradeNo                string          `json:"tradeNo"`                //     交易编号
	ExtendInfo             string          `json:"extendInfo"`             //     扩展参数	接口将原样返回
	WeChatAPPInfo          string          `json:"weChatAPPInfo"`          //     微信APP支付信息	微信app支付必传（原生
	PayInfo                string          `json:"payInfo"`                //     扫码支付信息/ JS支付串信息（微信、支付宝、QQ钱包）/微信小程序/微信原生H5支付串信息/支付宝原生APP支付串信息   3、收银宝微信小程序支付参数/微信原生小程序支付参数必传  注：有效时间60分钟
	MiniprogrampayinfoVsp  json.RawMessage `json:"miniprogramPayInfo_VSP"` //     小程序收银台支付参数
	//mobilePayInfo_VSP      string          `json:"mobilePayInfo_VSP"`      //     手机安全控件支付参数
	//validationType int `json:"validationType"` //     交易验证方式	当支付方式为收银宝快捷且需验证短信验证码时才返回，返回值为“1”表示需继续调用【确认支付（后台+短信验证码确认）】

}

// NotifyPay 订单结果通知
type NotifyPay struct {
	OrderNo                string `json:"orderNo"`                // 必填  云商通订单号
	BizOrderNo             string `json:"bizOrderNo"`             // 必填  商户订单号（支付订单）
	OriOrderNo             string `json:"oriOrderNo"`             //      原云商通订单号	退款订单该字段才返回
	OriBizOrderNo          string `json:"oriBizOrderNo"`          //      原商户订单号	退款订单该字段才返回
	Amount                 int    `json:"amount"`                 //      订单金额	单位：分
	PayDatetime            string `json:"payDatetime"`            //      订单支付完成时间	云商通订单支付完成时间 yyyy-MM-dd HH:mm:ss
	BuyerBizUserId         string `json:"buyerBizUserId"`         //     商户系统用户标识，商户系统中唯一编号。 付款人
	RefundWhereabouts      int    `json:"refundWhereabouts"`      //     退款去向	退款，必填 1：到账户余额  2：到原支付账户银行卡/微信/支付宝等
	PayWhereabouts         int    `json:"payWhereabouts"`         //     代付去向	代付去向 1：到账户余额
	Acct                   string `json:"acct"`                   //     支付人帐号  仅收银宝-付款码支付方式返回， 微信支付的openid
	Accttype               string `json:"accttype"`               //     借贷标志  刷卡消费交易必传
	Termno                 string `json:"termno"`                 //     终端号	终端代码
	Termauthno             string `json:"termauthno"`             //     终端授权码
	Cusid                  string `json:"cusid"`                  //     渠道商户	收银宝商户号
	PayInterfaceOutTradeNo string `json:"payInterfaceOutTradeNo"` //     通道交易流水号	支付渠道的交易流水号，微信订单详情“商户单号”，
	//ChannelExtendInfo      map[string]interface{} `json:"channelExtendInfo"`
	Chnltrxid           string `json:"chnltrxid"`           //     支付渠道交易单号	如支付宝“订单号”，微信“交易单号”
	Termrefnum          string `json:"termrefnum"`          //     交易参考号
	ChannelFee          string `json:"channelFee"`          //     渠道手续费	取值为收银宝接口手续费字段-fee
	ChannelPaytime      string `json:"channelPaytime"`      //     渠道交易完成时间	取值为收银宝接口交易完成时间-paytime
	PayInterfacetrxcode string `json:"payInterfacetrxcode"` //     收银宝渠道返回的交易类型对应收银宝接口字段trxcode；针对退款订单，透传退款订单落地的“通道交易类型”
	Traceno             string `json:"traceno"`             //     收银宝终端流水
	ExtendInfo          string `json:"extendInfo"`          //     扩展参数
	Chnldata            string `json:"chnldata"`            //     收银宝渠道信息
	Status              string `json:"status"`              //    订单是否成功	“OK”标识支付成功； “pending”表示进行中（中间状态） “error”表示支付失败； 提现在成功和失败时都会通知商户；其他订单只在成功时会通知商户。
}

package service

import "encoding/json"

// MemberType 会员类型
type MemberType int

const (
	MemberTypeIndividual MemberType = 3 // 个人会员	3
	MemberTypeCompany    MemberType = 2 // 企业会员	2
)

// UserStateType 用户状态
type UserStateType int

const (
	UserStateTypeValid     UserStateType = 1 // 有效
	UserStateTypeAuditFail UserStateType = 3 // 审核失败
	UserStateTypeLocked    UserStateType = 5 // 已锁
	UserStateTypeToAudit   UserStateType = 7 // 待审核
)

// CompanyAuditStateType 企业信息审核状态
type CompanyAuditStateType int

const (
	CompanyAuditStateTypeToAudit      UserStateType = 1 // 待审核
	CompanyAuditStateTypeAuditSuccess UserStateType = 2 // 审核成功
	CompanyAuditStateTypeAuditFail    UserStateType = 3 // 审核失败
)

// SourceType 访问终端类型
type SourceType int

const (
	SourceMobile = 1 // mobile
	SourcePC     = 2 // pc
)

// VerificationCodeType 验证码类型
type VerificationCodeType int

const (
	VerificationCodeTypeBind   = 9 // 绑定手机
	VerificationCodeTypeUnbind = 6 // 解绑手机
)

// CompropertyType 企业性质
type CompropertyType string

const (
	CompropertyTypeCompany            = "1" // 1-企业
	CompropertyTypeIndividualBusiness = "2" // 2-个体工商户
	CompropertyTypePublicInstitution  = "3" // 3-事业单位
)

// CompropertyType 企业性质
type CompropertyTypeRes int

const (
	CompropertyTypeResCompany            CompropertyTypeRes = 1 // 1-企业
	CompropertyTypeResIndividualBusiness CompropertyTypeRes = 2 // 2-个体工商户
	CompropertyTypeResPublicInstitution  CompropertyTypeRes = 3 // 3-事业单位
)

type JumpPageType int

const (
	JumpPageTypeH5          JumpPageType = 1 // H5页面
	JumpPageTypeMiniProgram JumpPageType = 2 // 小程序页面
)

// PicType 图像类型
type PicType int

const (
	PicTypeBusinessLicense PicType = 1 // 1-营业执照
	//PicTypeOrganizationInstitution PicType = 2 // 2-组织机构代码证（三证时必传）
	//PicTypeTaxSign                 PicType = 3 // 3-税务登记证（三证时必传）
	//PicTypeBankOpen                PicType = 4 // 4-银行开户证明（非必传，上传《银行开户许可证》或《基本存款账户信息》等可证明平台银行账号和户名的文件）
	//PicTypeInstitutionCredit       PicType = 5 // 5-机构信用代码（非必传）
	//PicTypeICP                     PicType = 6 // 6-ICP备案许可（非必传）
	//PicTypeIndustry                PicType = 7 // 7-行业许可证（非必传）
	PicTypeIDCardFront PicType = 8 // 8-身份证正面（人像面）（必传）
	PicTypeIDCardBack  PicType = 9 // 9-身份证反面（国徽面）（必传）
)

// CardType 银行卡类型
type CardType int

const (
	CardTypeDebit  CardType = 1 // 借记卡	1	整型
	CardTypeCredit CardType = 2 // 信用卡	2	整型
)

// BankBindStateType 银行卡绑定状态
type BankBindStateType int

const (
	BankBindStateTypeBound     BankBindStateType = 1 // 已绑定 1
	BankBindStateTypeUnUnbound BankBindStateType = 2 // 已解除 2
)

// BankCardProType 	银行卡/账户属性
type BankCardProType int

const (
	BankCardProTypeIndividual BankBindStateType = 0 // 0：个人银行卡
	BankCardProTypeCorporate  BankBindStateType = 1 // 1 企业对公账户 2
)

// 01

// CreateMemberReq 创建会员
type CreateMemberReq struct {
	BizUserId   string                 `json:"bizUserId"`   // 商户系统用户标识，商户系统中唯一编号	注意 1.不能输入“中文” 2.不要使用系统保留用户标识：#yunBizUserId_B2C#
	MemberType  MemberType             `json:"memberType"`  // 会员类型 企业会员	2  个人会员	3
	Source      SourceType             `json:"source"`      // 访问终端类型
	ExtendParam map[string]interface{} `json:"extendParam"` // 扩展参数  JSONObject
}

// CreateMemberRes 创建会员响应
type CreateMemberRes struct {
	UserID    string `json:"userId"`    // 云商通用户唯一标识
	BizUserId string `json:"bizUserId"` // 	商户系统用户标识，商户系统中唯一编号。
}

// 02

// SendCaptchaReq 发送验证码
type SendCaptchaReq struct {
	BizUserId            string               `json:"bizUserId"`            // 商户系统用户标识，商户系统中唯一编号
	Phone                string               `json:"phone"`                // 	手机号码 <=999
	VerificationCodeType VerificationCodeType `json:"verificationCodeType"` // 验证码类型   9-绑定手机，6-解绑手机
}

type SendCaptchaRes struct {
	BizUserId string `json:"bizUserId"` // 商户系统用户标识，商户系统中唯一编号
	Phone     string `json:"phone"`     // 	手机号码 <=11
}

//03

// BindPhoneReq 绑定手机
type BindPhoneReq struct {
	BizUserId        string `json:"bizUserId"`        // 商户系统用户标识，商户系统中唯一编号
	Phone            string `json:"phone"`            // 	手机号码 <=999
	VerificationCode string `json:"verificationCode"` // 验证码
}

type BindPhoneRes struct {
	BizUserId string `json:"bizUserId"` // 商户系统用户标识，商户系统中唯一编号
	Phone     string `json:"phone"`     // 	手机号码 <=11
}

//03-1

// UnbindPhoneReq 解绑手机
type UnbindPhoneReq struct {
	BizUserId        string `json:"bizUserId"`        // 商户系统用户标识，商户系统中唯一编号
	Phone            string `json:"phone"`            // 	手机号码 <=999
	VerificationCode string `json:"verificationCode"` // 验证码
}

type UnbindPhoneRes struct {
	BizUserId string `json:"bizUserId"` // 商户系统用户标识，商户系统中唯一编号
	Phone     string `json:"phone"`     // 	手机号码 <=11
	Result    string `json:"result"`    // “OK”表示解绑成功； “error”表示解绑失败。
}

// 04

// SetRealNameReq 个人实名认证
type SetRealNameReq struct {
	BizUserId string `json:"bizUserId"` // 商户系统用户标识，商户系统中唯一编号
	IsAuth    bool   `json:"isAuth"`    //  	true/false  默认为true  目前必须通过云商通认证  ---非必填
	Name      string `json:"name"`      // 	姓名
	//IdentityType      int    `json:"identityType"`      // 	证件类型, 目前仅支持身份证 1
	IdentityNo string `json:"identityNo"` // 	证件号码
	//IdentityBeginDate string `json:"identityBeginDate"` // 	证件有效开始日期 ---非必填
	//IdentityEndDate   string `json:"identityEndDate"`   // 	证件有效截止日期 ---非必填
	//Address           string `json:"address"`           // 	地址 ---非必填
	//ProfessionNo      string `json:"professionNo"`      //  职业 上送代码，详见【职业代码】 ---非必填
	//Telephone         string `json:"telephone"`         // 联系电话  手机号 ---非必填
}

type SetRealNameRes struct {
	BizUserId         string `json:"bizUserId"`         // 商户系统用户标识，商户系统中唯一编号
	IsAuth            bool   `json:"isAuth"`            //  	true/false  默认为true  目前必须通过云商通认证  ---非必填
	Name              string `json:"name"`              // 	姓名
	IdentityType      int    `json:"identityType"`      // 	证件类型
	IdentityNo        string `json:"identityNo"`        // 	证件号码
	IdentityBeginDate string `json:"identityBeginDate"` // 	证件有效开始日期 ---非必填
	IdentityEndDate   string `json:"identityEndDate"`   // 	证件有效截止日期 ---非必填
	Address           string `json:"address"`           // 	地址 ---非必填
	ProfessionNo      string `json:"professionNo"`      //  职业 上送代码，详见【职业代码】 ---非必填
	Telephone         string `json:"telephone"`         // 联系电话  手机号 ---非必填
}

// 05

// SetCompanyInfoReq 设置企业信息
type SetCompanyInfoReq struct {
	BizUserId        string              `json:"bizUserId"`        // 商户系统用户标识，商户系统中唯一编号
	Comproperty      CompropertyType     `json:"comproperty"`      //  	1-企业 2-个体工商户 3-事业单位 不填，则默认1 注：企业名称含“公司”，不支持上送“2-个体工商户”
	CompanyBasicInfo CompanyBasicInfoSet `json:"companyBasicInfo"` // 	企业基本信息
	BackUrl          string              `json:"backUrl"`          // 	企业会员审核结果通知，企业会员审核成功或者失败，将会发送后台通知  --- 非必填

}

type SetCompanyInfoRes struct {
	BizUserId        string `json:"bizUserId"`        // 商户系统用户标识，商户系统中唯一编号
	AccountSetResult int    `json:"accountSetResult"` //  	对私银行账户认证结果 2：认证成功。 3：认证失败。 注：个体工商户的对私银行账户四要素认证结果
	Result           int    `json:"result"`           // 	审核结果  2：审核成功。 3：审核失败。 仅自动审核时返回。
	FailReason       string `json:"failReason"`       // 	失败原因  当法人证件类型非“身份证”时返回：法人证件类型非身份证类型，需人工审核；
	Remark           string `json:"remark"`           // 	备注
}

// UpdateCompanyInfoReq 企业会员信息修改
type UpdateCompanyInfoReq struct {
	BizUserId         string `json:"bizUserId"`         // 必填  商户系统用户标识，商户系统中唯一编号
	Reqsn             string `json:"reqsn"`             // 必填  商户请求流水号	商户端需控制全局唯一
	CompanyName       string `json:"companyName"`       //      企业名称
	CompanyAddress    string `json:"companyAddress"`    //      企业地址
	Telephone         string `json:"telephone"`         //      联系电话
	LegalName         string `json:"legalName"`         //      法人姓名
	IdentityType      int    `json:"identityType"`      //      法人证件类型 如上送，仅支持“1“-身份证
	LegalIds          string `json:"legalIds"`          //      法人证件号码 法人证件号码 AES 加密
	IdentityBeginDate string `json:"identityBeginDate"` //      证件有效开始日期 格式：9999-12-31
	IdentityEndDate   string `json:"identityEndDate"`   //      证件有效截止日期 格式：9999-12-31 若长期有效上送“9999-12-31”
	LegalPhone        string `json:"legalPhone"`        //      法人手机号码
	BackUrl           string `json:"backUrl"`           //      企业会员审核结果通知，企业会员审核成功或者失败，将会发送后台通知  --- 非必填

}

type UpdateCompanyInfoRes struct {
	BizUserId        string `json:"bizUserId"`        // 必填   商户系统用户标识，商户系统中唯一编号
	AccountSetResult int    `json:"accountSetResult"` // 必填   商户请求流水号
	Result           int    `json:"result"`           // 	   修改结果 2：修改成功； 3：修改失败； 4：处理中 注：超过1s未处理完成的修改操作，响应“处理中”
	FailReason       string `json:"failReason"`       // 	   result为“3：修改失败”时，有值
}

/*
账户类型
0- 对私
1- 对公
企业性质=1-企业/3-事业单位，默认1-对公，不支持上送0-对私
企业性质=2-个体工商户，支持上送0-对私/1-对公，不填默认1-对公
*/

/*
账户类型=1-对公，企业对公账户，支持数字和“-”字符

账户类型=0-对私，则上送对私账户（借记卡），且必填

AES加密， href="#_敏感信息加解密" 详细
*/

/*
开户银行名称，详细，需严格按照银行列表上送，部分银行支持多种上送方式，选其一上送即可。

账户类型=1-对公，则必填

注：测试环境仅支持工农中建交。*/

// 06

// GetMemberInfoReq 获取会员信息
type GetMemberInfoReq struct {
	BizUserId string `json:"bizUserId"` // 必填 商户系统用户标识，商户系统中唯一编号
	//AcctOrgType int `json:"acctOrgType"` //     开户机构类型 0-通联 13-华瑞银行
}
type GetMemberInfoRes struct {
	BizUserId  string          `json:"bizUserId"`  // 必填 商户系统用户标识，商户系统中唯一编号
	MemberType MemberType      `json:"memberType"` // 必填 商户系统用户标识，商户系统中唯一编号
	MemberInfo json.RawMessage `json:"memberInfo"` // 必填 商户系统用户标识，商户系统中唯一编号
}

// 07

// GetBankCardBinReq 查询卡bin
type GetBankCardBinReq struct {
	CardNo string `json:"cardNo"` // 银行卡号	AES加密
}
type GetBankCardBinRes struct {
	CardBinInfo CardBin `json:"cardBinInfo"` // 必填 卡bin信息
}

// 08

// SignAcctProtocolReq 账户提现协议签约
type SignAcctProtocolReq struct {
	BizUserId     string       `json:"bizUserId"`     // 必填 商户系统用户标识，商户系统中唯一编号
	SignAcctName  string       `json:"signAcctName"`  // 必填 签约户名  个人会员：名称  企业会员：  法人提现，则上送“法人姓名”   对公户提现，则上送“企业名称”
	JumpPageType  JumpPageType `json:"jumpPageType"`  //     跳转页面类型 1-H5页面  2-小程序页面  兼容存量模式，不上送默认跳转H5页面
	JumpUrl       string       `json:"jumpUrl"`       //     若正常域名或无特殊字符，以http/https开头明文请求，无需加密；  若含IP地址形式或“#”号，通知地址需要按照敏感字段加密方式加密  ASE加密  长度不超过117个字符
	BackUrl       string       `json:"backUrl"`       // 必填 若正常域名或无特殊字符，以http/https开头明文请求，无需加密；  若含IP地址形式或“#”号，通知地址需要按照敏感字段加密方式加密  ASE加密  长度不超过117个字符
	NoContractUrl string       `json:"noContractUrl"` //      平台签约失败或者会员取消签约，跳转返回的页面地址； 若正常域名或无特殊字符，以http/https开头明文请求，无需加密；  若含IP地址形式或“#”号，通知地址需要按照敏感字段加密方式加密  ASE加密 长度不超过117个字符
	Source        SourceType   `json:"source"`        // 必填  访问终端类型
}
type SignAcctProtocolRes struct {
	Url string `json:"url"`
	//BizUserId      string `json:"bizUserId"`      //  必填  商户系统用户标识，商户系统中唯一编号
	//AcctProtocolNo string `json:"acctProtocolNo"` //  必填  账户提现协议编号 商户端需保存
	//SignAcctName   string `json:"signAcctName"`   //  必填  签约户名
	//Result         string `json:"result"`         //  必填  签订结果	成功：ok ，失败：error
}

// 09

// SignContractQueryReq 账户协议签约查询
type SignContractQueryReq struct {
	BizUserId    string     `json:"bizUserId"`    // 必填 商户系统用户标识，商户系统中唯一编号
	SignAcctName string     `json:"signAcctName"` //     签约户名  查询账户提现协议，必填  不填，默认查询会员电子协议
	JumpPageType int        `json:"jumpPageType"` //     跳转页面类型  1-H5页面 2-小程序页面  兼容存量模式，不上送默认跳转H5页面
	JumpUrl      string     `json:"jumpUrl"`      //     点击确定按钮之后，跳转返回的页面地址  若正常域名或无特殊字符，以http/https开头明文请求，无需加密  若含IP地址形式或“#”号，需要加密后再请求 长度不超过117个字符  ASE加密
	Source       SourceType `json:"source"`       //     访问终端类型
}

type SignContractQueryRes struct {
	Url string `json:"url"`
	//BizUserId      string `json:"bizUserId"`      // 必填 商户系统用户标识，商户系统中唯一编号
	//ContractNo     string `json:"ContractNo"`     //     会员电子协议编号 查询“会员电子协议”时返回；  商户端需保存
	//AcctProtocolNo string `json:"acctProtocolNo"` //     账户提现协议编号  查询“账户提现协议”时返回； 商户端需保存
	//Status         string `json:"status"`         // 必填 服务调用是否成功
}

/*
1-营业执照（必传）
2-组织机构代码证（三证时必传）
3-税务登记证（三证时必传）
4-银行开户证明（非必传，上传《银行开户许可证》或《基本存款账户信息》等可证明平台银行账号和户名的文件）
5-机构信用代码（非必传）
6-ICP备案许可（非必传）
7-行业许可证（非必传）
8-身份证正面（人像面）（必传）
9-身份证反面（国徽面）（必传）
*/

// 10

// IdcardCollectReq 影印件采集
type IdcardCollectReq struct {
	BizUserId                  string  `json:"bizUserId"`                  // 必填 商户系统用户标识，商户系统中唯一编号
	OcrComparisonResultBackUrl string  `json:"ocrComparisonResultBackUrl"` //     影印件核对结果异步通知地址，不上送则无异步通知
	PicType                    PicType `json:"picType"`                    // 必填 影印件类型
	Picture                    string  `json:"picture"`                    // 必填 影印件图片  影印件图片的base64码 图片大小不超过700k 图片格式jpg、png
}

type IdcardCollectRes struct {
	BizUserId string `json:"bizUserId"` // 必填 商户系统用户标识，商户系统中唯一编号
	Result    string `json:"result"`    // 必填 上传结果	1-成功 2-失败---实际返回字符串 "1"，"2"
}

/*
CardCheck
无需调用【确认绑定银行卡】;
测试环境模拟的返回规则：
{"卡号第四位为0","交易成功"},
{"卡号第四位为1","查开户方原因"},
{"卡号第四位为2","无效卡号 "},
{"卡号第四位为3","已挂失卡"},
{"卡号第四位为4","余额不足"},
{"卡号第四位为5","无此账户"},
{"卡号第四位为6","原交易失败，不收费"},
{"卡号第四位为7","户名错"},
{"卡号第四位为8","交易超时"}
*/

// UnbindBankCardReq 解绑绑定银行卡
type UnbindBankCardReq struct {
	BizUserId   string `json:"bizUserId"`   // 必填 商户系统用户标识，商户系统中唯一编号
	CardNo      string `json:"cardNo"`      //     	银行卡号 AES加密
	AgreementNo string `json:"agreementNo"` //     	协议编号
}

type UnbindBankCardRes struct {
	BizUserId   string `json:"bizUserId"`   // 必填 商户系统用户标识，商户系统中唯一编号
	CardNo      string `json:"cardNo"`      //      银行卡号	银行卡号
	AgreementNo string `json:"agreementNo"` //     以协议号解绑，则返回协议号
}

// ApplyBindBankCardReq 请求绑定银行卡
type ApplyBindBankCardReq struct {
	BizUserId    string `json:"bizUserId"`    // 必填 商户系统用户标识，商户系统中唯一编号
	CardNo       string `json:"cardNo"`       // 必填 银行卡号	AES加密 详情
	Phone        string `json:"phone"`        // 必填 银行预留手机
	Name         string `json:"name"`         // 必填 姓名	如果是企业会员，请填写法人姓名
	CardCheck    int    `json:"cardCheck"`    //      绑卡方式 默认7：收银宝快捷支付签约，本项目使用8 银行四要素，无需调用【确认绑定银行卡】;
	IdentityType int    `json:"identityType"` // 必填 证件类型	详情目前只支持身份证。
	IdentityNo   string `json:"identityNo"`   // 必填 证件号码	AES加密 详情
	Validate     string `json:"validate"`     //     有效期	格式为月年；如0321，2位月2位年，AES加密 详情。
	Cvv2         string `json:"cvv2"`         // 	 CVV2	3位数字，AES加密 详情
	UnionBank    string `json:"unionBank"`    //     支付行号	12位数字
}
type ApplyBindBankCardRes struct {
	BizUserId string `json:"bizUserId"` // 必填 商户系统用户标识，商户系统中唯一编号
	TranceNum string `json:"tranceNum"` //     流水号	绑卡方式6、7返回
	TransDate string `json:"transDate"` //     申请时间	YYYYMMDD
	BankName  string `json:"bankName"`  // 必填 银行名称
	BankCode  string `json:"bankCode"`  // 必填 银行代码
	CardType  int    `json:"cardType"`  // 必填 银行卡类型	1储蓄卡 2信用卡
}

// BindBankCardReq 确认绑定银行卡
type BindBankCardReq struct {
	BizUserId        string `json:"bizUserId"`        // 必填 商户系统用户标识，商户系统中唯一编号。
	TranceNum        string `json:"tranceNum"`        // （绑卡方式6、7必传）		流水号	请求绑定银行卡接口返回
	TransDate        string `json:"transDate"`        // 	申请时间	请求绑定银行卡接口返回
	Phone            string `json:"phone"`            // 必填 银行预留手机
	Validate         string `json:"validate"`         //     格式为月年；如0321，2位月2位年，AES加密, 详细。
	Cvv2             string `json:"cvv2"`             //     CVV2, AES加密, 详细。
	VerificationCode string `json:"verificationCode"` // 必填 短信验 CVV2, AES加密, 详细。证码
}
type BindBankCardRes struct {
	BizUserId   string `json:"bizUserId"`   // 必填 商户系统用户标识，商户系统中唯一编号。
	TranceNum   string `json:"tranceNum"`   // 必填 流水号
	AgreementNo string `json:"agreementNo"` //     签约协议号	仅绑卡方式6或7 时返回
	TransDate   string `json:"transDate"`   //     （四要素时返回）		申请时间	YYYYMMDD
}

// QueryBankCardReq 查询绑定银行卡
type QueryBankCardReq struct {
	BizUserId string `json:"bizUserId"` // 必填 商户系统用户标识，商户系统中唯一编号。  支持个人会员、企业会员、平台。 若平台，上送固定值：#yunBizUserId_B2C#
	CardNo    string `json:"cardNo"`    //     银行卡号。如为空，则返回用户所有绑定银行卡。	AES加密
}

// BindBankInfo 绑定的银行卡信息
type BindBankInfo struct {
	BankCardNo     string            `json:"bankCardNo"`     // 必填  银行卡号，AES加密， href="#_敏感信息加解密" 详细。
	BankName       string            `json:"bankName"`       // 必填  银行名称
	BindTime       string            `json:"bindTime"`       // 必填  绑定时间，yyyy-MM-dd HH:mm:ss
	AgreementNo    string            `json:"agreementNo"`    // 	 签约协议号 仅绑卡方式6或7 时返回
	CardType       CardType          `json:"cardType"`       // 必填  银行卡类型
	BindState      BankBindStateType `json:"bindState"`      // 必填  绑定状态
	Phone          string            `json:"phone"`          //      银行预留手机号码（仅四要素绑定的银行卡返回）
	BindMethod     string            `json:"bindMethod"`     // 	 若为企业对公户返回0； 其余情况取绑卡方式(首次) 详细
	BankCardPro    BankCardProType   `json:"bankCardPro"`    // 必填  银行卡/账户属性  0：个人银行卡  1：企业对公账户
	BankCityNo     string            `json:"bankCityNo"`     // 	 开户行地区代码 根据中国地区代码表 详情
	BranchBankName string            `json:"branchBankName"` // 	 开户行支行名称
	UnionBank      string            `json:"unionBank"`      // 	 支付行号，12位数字
	Province       string            `json:"province"`       // 	 开户行所在省
	City           string            `json:"city"`           // 	 开户行所在市
}

type QueryBankCardRes struct {
	BindCardList []BindBankInfo `json:"bindCardList"` // 必填 已绑定银行卡信息列表 如没有查到绑定的银行卡，则返回空数组
}

type CardBin struct {
	CardBin       string `json:"cardBin"`       // 卡bin
	CardType      int    `json:"cardType"`      // 卡种
	BankCode      string `json:"bankCode"`      // 发卡行代码
	BankName      string `json:"bankName"`      // 发卡行名称
	CardName      string `json:"cardName"`      // 卡名
	CardLenth     int    `json:"cardLenth"`     // 卡片长度
	CardState     int    `json:"cardState"`     // 状态（1：有效；0：无效）
	CardTypeLabel string `json:"cardTypeLabel"` // 卡种名称
}

// CompanyBasicInfoSet 企业基本信息（设置）
type CompanyBasicInfoSet struct {
	CompanyName       string `json:"companyName"`       // 必填  企业名称，如有括号，用中文格式（）
	CompanyAddress    string `json:"companyAddress"`    //      企业地址 华通银行存管必须上送
	AuthType          int    `json:"authType"`          //      认证类型  1:三证 2:一证 默认1-三证
	UniCredit         string `json:"uniCredit"`         //      统一社会信用（一证） 认证类型为2时必传
	BusinessLicense   string `json:"businessLicense"`   //      营业执照号（三证） 认证类型为1时必传
	OrganizationCode  string `json:"organizationCode"`  //      组织机构代码（三证） 认证类型为1时必传
	TaxRegister       string `json:"taxRegister"`       //      税务登记证（三证） 认证类型为1时必传
	ExpLicense        string `json:"expLicense"`        //      统一社会信用/营业执照号到期时间 格式：yyyy-MM-dd
	Phone             string `json:"phone"`             //      银行预留手机 账户类型=0-对私，则必填
	Telephone         string `json:"telephone"`         //      联系电话  华通银行存管必须上送
	LegalName         string `json:"legalName"`         // 必填  法人姓名
	IdentityType      int    `json:"identityType"`      // 必填  法人证件类型  身份证 1
	LegalIds          string `json:"legalIds"`          // 必填  法人证件号码，AES加密
	IdentityBeginDate string `json:"identityBeginDate"` //      证件有效开始日期 格式：9999-12-31
	IdentityEndDate   string `json:"identityEndDate"`   //      证件有效截止日期  格式：9999-12-31  若长期有效上送“9999-12-31”
	LegalPhone        string `json:"legalPhone"`        // 必填  法人手机号码
	AcctType          int    `json:"acctType"`          // 账户类型 0- 对私 1- 对公. 企业性质=1-企业/3-事业单位，默认1-对公，不支持上送0-对私  企业性质=2-个体工商户，支持上送0-对私/1-对公，不填默认1-对公
	AccountNo         string `json:"accountNo"`         // 必填  账户   账户类型=1-对公，企业对公账户，支持数字和“-”字符  账户类型=0-对私，则上送对私账户（借记卡），且必填  AES加密
	ParentBankName    string `json:"parentBankName"`    //      开户银行名称  账户类型=1-对公，则必填
	BankCityNo        string `json:"bankCityNo"`        //      开户行地区代码
	BankName          string `json:"bankName"`          //      开户行支行名称  账户类型=1-对公，则必填
	UnionBank         string `json:"unionBank"`         //      支付行号，12位数字  账户类型=1-对公，则必填
	Province          string `json:"province"`          //      开户行所在省 开户行所在市必须同时上送
	City              string `json:"city"`              //      开户行所在市 开户行所在省必须同时上送
}

// IndividualMemberInfoGet 个人基本信息（获取）
type IndividualMemberInfoGet struct {
	Name                 string        `json:"name"`                 // 		姓名
	UserState            UserStateType `json:"userState"`            // 必填     用户状态。
	UserId               string        `json:"userId"`               // 必填    	云商通用户id
	Country              string        `json:"country"`              // 		国家
	Province             string        `json:"province"`             // 		省份
	Area                 string        `json:"area"`                 // 		县市
	Address              string        `json:"address"`              // 		地址
	Phone                string        `json:"phone"`                // 		手机号码
	IdentityCardNo       string        `json:"identityCardNo"`       // 		身份证号码，AES加密。
	IdentityBeginDate    string        `json:"identityBeginDate"`    // 		证件有效开始日期
	IdentityEndDate      string        `json:"identityEndDate"`      // 		证件有效截止日期
	IsPhoneChecked       bool          `json:"isPhoneChecked"`       // 		是否绑定手机
	RegisterTime         string        `json:"registerTime"`         // 		创建时间
	RegisterIp           string        `json:"registerIp"`           // 		创建ip
	PayFailAmount        int           `json:"payFailAmount"`        // 		支付失败次数
	IsIdentityChecked    bool          `json:"isIdentityChecked"`    // 		是否进行实名认证
	RealNameTime         string        `json:"realNameTime"`         // 		实名认证时间
	Remark               string        `json:"remark"`               // 		备注
	Source               SourceType    `json:"source"`               // 必填  	访问终端类型
	IsSetPayPwd          bool          `json:"isSetPayPwd"`          // 		是否已设置支付密码
	AcctOrgType          int           `json:"acctOrgType"`          // 		开户机构类型 0-通联
	SubAcctNo            string        `json:"subAcctNo"`            // 		会员开通的通联子账号
	SignAcctProtocolTime string        `json:"signAcctProtocolTime"` // 		账户提现协议签订时间
	AcctProtocolNo       string        `json:"acctProtocolNo"`       // 		账户提现协议编号
}

// CompanyMemberInfoGet 企业基本信息（获取）
type CompanyMemberInfoGet struct {
	CompanyName               string                `json:"companyName"`               // 必填  企业名称，如有括号，用中文格式（）
	CompanyAddress            string                `json:"companyAddress"`            //      企业地址 华通银行存管必须上送
	Comproperty               CompropertyTypeRes    `json:"comproperty"`               //      企业性质
	AuthType                  int                   `json:"authType"`                  // 必填 认证类型  1:三证 2:一证 默认1-三证
	BusinessLicense           string                `json:"businessLicense"`           //      营业执照号（三证） 认证类型为1时必传
	OrganizationCode          string                `json:"organizationCode"`          //      组织机构代码（三证） 认证类型为1时必传
	UniCredit                 string                `json:"uniCredit"`                 //      统一社会信用（一证） 认证类型为2时必传
	TaxRegister               string                `json:"taxRegister"`               //      税务登记证（三证） 认证类型为1时必传
	ExpLicense                string                `json:"expLicense"`                //      统一社会信用/营业执照号到期时间 格式：yyyy-MM-dd
	Telephone                 string                `json:"telephone"`                 //      联系电话  华通银行存管必须上送
	Phone                     string                `json:"phone"`                     //      银行预留手机 账户类型=0-对私，则必填
	LegalName                 string                `json:"legalName"`                 // 必填  法人姓名
	IdentityType              int                   `json:"identityType"`              // 必填  法人证件类型  身份证 1
	LegalIds                  string                `json:"legalIds"`                  // 必填  法人证件号码，AES加密
	IdentityBeginDate         string                `json:"identityBeginDate"`         //      证件有效开始日期 格式：9999-12-31
	IdentityEndDate           string                `json:"identityEndDate"`           //      证件有效截止日期  格式：9999-12-31  若长期有效上送“9999-12-31”
	LegalPhone                string                `json:"legalPhone"`                // 必填  法人手机号码
	AcctType                  int                   `json:"acctType"`                  //      账户类型 0- 对私 1- 对公. 企业性质=1-企业/3-事业单位，默认1-对公，不支持上送0-对私  企业性质=2-个体工商户，支持上送0-对私/1-对公，不填默认1-对公
	AccountNo                 string                `json:"accountNo"`                 //      账户类型
	ParentBankName            string                `json:"parentBankName"`            //      开户银行名称
	BankCityNo                string                `json:"bankCityNo"`                //      开户行地区代码
	BankName                  string                `json:"bankName"`                  //      开户行支行名称  账户类型=1-对公，则必填
	UnionBank                 string                `json:"unionBank"`                 //      支付行号，12位数字  账户类型=1-对公，则必填
	Province                  string                `json:"province"`                  //      开户行所在省 开户行所在市必须同时上送
	City                      string                `json:"city"`                      //      开户行所在市 开户行所在省必须同时上送
	IsSignContract            bool                  `json:"isSignContract"`            //      是否已签电子协议
	Status                    CompanyAuditStateType `json:"status"`                    //      审核状态
	CheckTime                 string                `json:"checkTime"`                 //      审核时间 yyyy-MM-dd HH:mm:ss
	Remark                    string                `json:"remark"`                    //      备注
	FailReason                string                `json:"failReason"`                //      审核失败原因
	AcctOrgType               int                   `json:"acctOrgType"`               //      开户机构类型  0-通联
	SubAcctNo                 string                `json:"subAcctNo"`                 //      会员开通的通联子账号
	UserId                    string                `json:"userId"`                    // 必填  云商通用户id
	IsPhoneChecked            bool                  `json:"isPhoneChecked"`            //      是否绑定手机
	SignAcctProtocolTime      string                `json:"signAcctProtocolTime"`      //      账户提现协议签订时间
	AccountSetResult          int                   `json:"accountSetResult"`          //      对私银行账户认证结果  2：认证成功。  3：认证失败。  注：个体工商户的对私银行账户四要素认证结果
	AcctProtocolNo            string                `json:"acctProtocolNo"`            //      账户提现协议编号
	OcrRegnumComparisonResult int                   `json:"ocrRegnumComparisonResult"` //      OCR识别与企业工商认证信息是否一致  0-否  1-是  若营业执照未进行识别该字段不返
	OcrIdcardComparisonResult int                   `json:"ocrIdcardComparisonResult"` //      	OCR识别与企业法人实名信息是否一致  0-否  1-是  若法人身份证未进行识别该字段不返
	ResultInfo                string                `json:"resultInfo"`                //      比对结果说明 存在多种结果信息一起返回，使用“;”进行拼接
	LegalSignAcctProtocolTime string                `json:"legalSignAcctProtocolTime"` //      法人账户提现协议签订时间 仅当企业会员法人签订账户提现协议时有值；
	LegalAcctProtocolNo       string                `json:"legalAcctProtocolNo"`       //      法人账户提现协议编号  仅当企业会员法人签订账户提现协议时有值；
}

// ApplyBindAcctReq 会员绑定支付账户用户标识
type ApplyBindAcctReq struct {
	BizUserId     string `json:"bizUserId"`     // 必填 商户系统用户标识，商户系统中唯一编号
	OperationType string `json:"operationType"` // 必填 set-绑定  query-查询
	AcctType      string `json:"acctType"`      //     weChatMiniProgram -微信小程序                 set 必填
	Acct          string `json:"acct"`          //     支付账户用户标识  微信小程序支付openid——微信分配   set 必填
}

type ApplyBindAcctRes struct {
	BizUserId string `json:"bizUserId"` // 必填 商户系统用户标识，商户系统中唯一编号
	Result    string `json:"result"`    //     支付账户用户标识绑定结果	成功：OK 失败：error
}

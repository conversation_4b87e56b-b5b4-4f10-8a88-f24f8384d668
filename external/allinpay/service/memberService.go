package service

import (
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/cnbattle/allinpay/core"
	"github.com/cnbattle/allinpay/utils"
	"net/url"
	"strings"
)

// MemberService 会员
type MemberService interface {
	CreateMember(req CreateMemberReq) (CreateMemberRes, error) // 创建会员
	SendCaptcha(req SendCaptchaReq) (SendCaptchaRes, error)    // 发送验证码
	// BindPhone 绑定手机
	BindPhone(req BindPhoneReq) (BindPhoneRes, error)
	// UnbindPhone 绑定手机
	UnbindPhone(req UnbindPhoneReq) (UnbindPhoneRes, error)
	// SetRealName 个人实名认证
	SetRealName(req SetRealNameReq) (SetRealNameRes, error)
	// SetCompanyInfo 设置企业信息
	SetCompanyInfo(req SetCompanyInfoReq) (SetCompanyInfoRes, error)
	// UpdateCompanyInfo 企业会员信息修改
	UpdateCompanyInfo(req UpdateCompanyInfoReq) (UpdateCompanyInfoRes, error)
	// GetMemberInfoForIndividual 获取个人会员信息
	GetMemberInfoForIndividual(req GetMemberInfoReq) (IndividualMemberInfoGet, error)
	// GetMemberInfoForCompany 获取企业会员信息
	GetMemberInfoForCompany(req GetMemberInfoReq) (CompanyMemberInfoGet, error)
	// GetBankCardBin 查询卡bin
	GetBankCardBin(req GetBankCardBinReq) (GetBankCardBinRes, error)
	// SignAcctProtocol 账户提现协议签约
	SignAcctProtocol(req SignAcctProtocolReq) (SignAcctProtocolRes, error)
	// SignContractQuery 账户协议签约查询
	SignContractQuery(req SignContractQueryReq) (SignContractQueryRes, error)
	//	idcardCollect 影印件采集
	IdcardCollect(req IdcardCollectReq) (IdcardCollectRes, error)
	//	UnbindBankCard 解绑绑定银行卡
	UnbindBankCard(req UnbindBankCardReq) (UnbindBankCardRes, error)
	//	ApplyBindBankCard 请求绑定银行卡
	ApplyBindBankCard(req ApplyBindBankCardReq) (ApplyBindBankCardRes, error)
	//	BindBankCard 确认绑定银行卡--银行四要素无需确认
	BindBankCard(req BindBankCardReq) (BindBankCardRes, error)
	//	QueryBankCard 查询绑定银行卡
	QueryBankCard(req QueryBankCardReq) (QueryBankCardRes, error)
	ApplyBindAcct(req ApplyBindAcctReq) (ApplyBindAcctRes, error) // 会员绑定支付账户用户标识
}

type member struct {
	cli *core.Client
}

func NewMember(cli *core.Client) MemberService {
	return &member{
		cli: cli,
	}
}

// CreateMember 创建会员
func (s member) CreateMember(req CreateMemberReq) (CreateMemberRes, error) {
	if len(strings.TrimSpace(req.BizUserId)) < 1 {
		return CreateMemberRes{}, errors.New("bizUserId不能为空")
	}
	if req.MemberType != MemberTypeCompany && req.MemberType != MemberTypeIndividual {
		return CreateMemberRes{}, errors.New("memberType参数错误")
	}
	if len(strings.TrimSpace(req.BizUserId)) < 1 {
		return CreateMemberRes{}, errors.New("bizUserId不能为空")
	}

	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["memberType"] = req.MemberType
	params["source"] = req.Source
	params["extendParam"] = req.ExtendParam
	method := "allinpay.yunst.memberService.createMember"

	var res CreateMemberRes

	err := s.cli.Request(method, params, &res)
	if err != nil {
		return CreateMemberRes{}, err
	}

	return res, nil
}

// SendCaptcha 发送验证码
func (s member) SendCaptcha(req SendCaptchaReq) (SendCaptchaRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId

	//err := utils.CheckPhone(req.Phone)
	//if err != nil {
	//	return SendCaptchaRes{}, err
	//}

	params["phone"] = req.Phone
	params["verificationCodeType"] = req.VerificationCodeType
	method := "allinpay.yunst.memberService.sendVerificationCode"
	var res SendCaptchaRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return SendCaptchaRes{}, err
	}
	return res, nil
}

// BindPhone 绑定手机
func (s member) BindPhone(req BindPhoneReq) (BindPhoneRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["phone"] = req.Phone
	params["verificationCode"] = req.VerificationCode
	method := "allinpay.yunst.memberService.bindPhone"
	var res BindPhoneRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return BindPhoneRes{}, err
	}
	return res, nil
}

// UnbindPhone 解绑手机
func (s member) UnbindPhone(req UnbindPhoneReq) (UnbindPhoneRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["phone"] = req.Phone
	params["verificationCode"] = req.VerificationCode
	method := "allinpay.yunst.memberService.unbindPhone"
	var res UnbindPhoneRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return UnbindPhoneRes{}, err
	}
	return res, nil
}

// SetRealName 个人实名认证
// 绑定银行卡前需先进行实名认证。
// 个人会员创建会员后即可实名认证，与是否绑定手机无关。
// 实名认证是去公安网验证这个人是真实存在的。
func (s member) SetRealName(req SetRealNameReq) (SetRealNameRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["isAuth"] = true
	params["name"] = req.Name
	params["identityType"] = 1

	// aes
	sha1PRNG, err := utils.AesSha1PRNG([]byte(s.cli.AppSecretKey), 128)
	if err != nil {
		return SetRealNameRes{}, err
	}
	IdentityNoEn := hex.EncodeToString(utils.AesEcbEncrypt([]byte(req.IdentityNo), sha1PRNG))

	params["identityNo"] = IdentityNoEn

	// 以下非必填
	//params["identityBeginDate"] = data.IdentityBeginDate
	//params["identityEndDate"] = data.IdentityEndDate
	//params["address"] = data.Address
	//params["professionNo"] = data.ProfessionNo
	//params["telephone"] = data.Telephone

	method := "allinpay.yunst.memberService.setRealName"
	var res SetRealNameRes
	err = s.cli.Request(method, params, &res)
	if err != nil {
		return SetRealNameRes{}, err
	}
	return res, nil
}

// SetCompanyInfo 设置企业信息
func (s member) SetCompanyInfo(req SetCompanyInfoReq) (SetCompanyInfoRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["comproperty"] = req.Comproperty
	params["backUrl"] = req.BackUrl

	// aes
	c := req.CompanyBasicInfo
	sha1PRNG, err := utils.AesSha1PRNG([]byte(s.cli.AppSecretKey), 128)
	if err != nil {
		return SetCompanyInfoRes{}, err
	}

	c.LegalIds = hex.EncodeToString(utils.AesEcbEncrypt([]byte(c.LegalIds), sha1PRNG))
	c.AccountNo = hex.EncodeToString(utils.AesEcbEncrypt([]byte(c.AccountNo), sha1PRNG))

	params["companyBasicInfo"] = c

	//params["companyExtendInfo"]
	method := "allinpay.yunst.memberService.setCompanyInfo"
	var res SetCompanyInfoRes
	err = s.cli.Request(method, params, &res)
	if err != nil {
		return SetCompanyInfoRes{}, err
	}
	return res, nil
}

// UpdateCompanyInfo 企业会员信息修改
func (s member) UpdateCompanyInfo(req UpdateCompanyInfoReq) (UpdateCompanyInfoRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["reqsn"] = req.Reqsn

	// 以下选填
	if len(req.CompanyName) > 1 {
		params["companyName"] = req.CompanyName
	}
	if len(req.CompanyAddress) > 1 {
		params["companyAddress"] = req.CompanyAddress
	}
	if len(req.Telephone) > 1 {
		params["telephone"] = req.Telephone
	}
	if len(req.LegalName) > 1 {
		params["legalName"] = req.LegalName
	}
	if req.IdentityType == 1 {
		//仅支持1 身份证
		params["identityType"] = req.IdentityType
	}
	if len(req.LegalIds) > 1 {
		params["legalIds"] = req.LegalIds
	}
	if len(req.IdentityBeginDate) > 1 {
		params["identityBeginDate"] = req.IdentityBeginDate
	}
	if len(req.IdentityEndDate) > 1 {
		params["identityEndDate"] = req.IdentityEndDate
	}
	if len(req.LegalPhone) > 1 {
		params["legalPhone"] = req.LegalPhone
	}
	params["backUrl"] = req.BackUrl
	method := "allinpay.yunst.memberService.updateCompanyInfo"
	var res UpdateCompanyInfoRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return UpdateCompanyInfoRes{}, err
	}
	return res, nil
}

// GetMemberInfoForIndividual 获取个人会员信息
func (s member) GetMemberInfoForIndividual(req GetMemberInfoReq) (IndividualMemberInfoGet, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["acctOrgType"] = 0
	method := "allinpay.yunst.memberService.getMemberInfo"
	var res GetMemberInfoRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return IndividualMemberInfoGet{}, err
	}
	if res.MemberType != MemberTypeIndividual {
		return IndividualMemberInfoGet{}, errors.New("该用户不是个人会员")
	}

	var i IndividualMemberInfoGet
	err = json.Unmarshal(res.MemberInfo, &i)
	if err != nil {
		return IndividualMemberInfoGet{}, err
	}

	if i.IdentityCardNo != "" {
		sha1PRNG, err := utils.AesSha1PRNG([]byte(s.cli.AppSecretKey), 128)
		if err != nil {
			return IndividualMemberInfoGet{}, err
		}
		//c.LegalIds = hex.EncodeToString(utils.AesEcbEncrypt([]byte(c.LegalIds), sha1PRNG))
		//c.AccountNo = hex.EncodeToString(utils.AesEcbEncrypt([]byte(c.AccountNo), sha1PRNG))

		decodeString, err := hex.DecodeString(i.IdentityCardNo)

		decrypt := utils.AesEcbDecrypt(decodeString, sha1PRNG)

		i.IdentityCardNo = string(decrypt)
	}

	return i, nil

}

// GetMemberInfoForCompany 获取企业会员信息
func (s member) GetMemberInfoForCompany(req GetMemberInfoReq) (CompanyMemberInfoGet, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["acctOrgType"] = 0
	method := "allinpay.yunst.memberService.getMemberInfo"
	var res GetMemberInfoRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return CompanyMemberInfoGet{}, err
	}
	if res.MemberType != MemberTypeCompany {
		return CompanyMemberInfoGet{}, errors.New("该用户不是企业会员")
	}

	// aes
	sha1PRNG, err := utils.AesSha1PRNG([]byte(s.cli.AppSecretKey), 128)
	if err != nil {
		return CompanyMemberInfoGet{}, err
	}
	//c.LegalIds = hex.EncodeToString(utils.AesEcbEncrypt([]byte(c.LegalIds), sha1PRNG))
	//c.AccountNo = hex.EncodeToString(utils.AesEcbEncrypt([]byte(c.AccountNo), sha1PRNG))

	var i CompanyMemberInfoGet
	err = json.Unmarshal(res.MemberInfo, &i)
	if err != nil {
		return CompanyMemberInfoGet{}, err
	}

	if i.AccountNo != "" {
		decodeString, err := hex.DecodeString(i.AccountNo)
		decrypt := utils.AesEcbDecrypt(decodeString, sha1PRNG)
		if err != nil {
			return CompanyMemberInfoGet{}, err
		}
		i.AccountNo = string(decrypt)

	}
	if i.LegalIds != "" {
		decodeString, err := hex.DecodeString(i.LegalIds)
		decrypt := utils.AesEcbDecrypt(decodeString, sha1PRNG)
		if err != nil {
			return CompanyMemberInfoGet{}, err
		}
		i.LegalIds = string(decrypt)
	}

	return i, nil
}

// GetBankCardBin 查询卡bin
func (s member) GetBankCardBin(req GetBankCardBinReq) (GetBankCardBinRes, error) {
	params := map[string]interface{}{}

	encryptionSI, err := s.cli.EncryptionSI(req.CardNo)
	if err != nil {
		return GetBankCardBinRes{}, errors.New("加密cardANo错误")
	}

	params["cardNo"] = encryptionSI
	method := "allinpay.yunst.memberService.getBankCardBin"
	var res GetBankCardBinRes
	err = s.cli.Request(method, params, &res)
	if err != nil {
		return GetBankCardBinRes{}, err
	}
	return res, nil
}

// SignAcctProtocol 账户提现协议签约
func (s member) SignAcctProtocol(req SignAcctProtocolReq) (SignAcctProtocolRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["signAcctName"] = req.SignAcctName
	params["jumpPageType"] = req.JumpPageType

	//params["jumpUrl"] = req.JumpUrl

	params["backUrl"] = req.BackUrl

	// 平台签约失败或者会员取消签约，跳转返回的页面地址；
	//params["noContractUrl"] = req.NoContractUrl

	params["source"] = req.Source
	method := "allinpay.yunst.memberService.signAcctProtocol"
	var res SignAcctProtocolRes

	err := s.cli.RequestNotVerify(method, params, &res)
	if err != nil {
		return SignAcctProtocolRes{}, err
	}
	return res, nil
}

func CreateUriParam(sysID string, param map[string]string) string {
	version := param["version"]
	sign := param["sign"]
	timestamp := param["timestamp"]
	bizContent := param["bizContent"]

	//reqBytes, _ := json.Marshal(req.Req)
	p := fmt.Sprintf("?sysid=%s&v=%s&timestamp=%s&req=%s&sign=%s&", sysID, version,
		EncodeURIComponent(timestamp), EncodeURIComponent(bizContent), EncodeURIComponent(sign))
	return p
}

func EncodeURIComponent(str string) string {
	return strings.Replace(url.QueryEscape(str), "+", "%20", -1)
}

func dealUrl(originUrl string, params map[string]string) (string, error) {
	baseUrl, err := url.Parse(originUrl)
	if err != nil {
		return "", err
	}
	query := url.Values{}
	//for k, v := range params {
	//	query.Add(k, v)
	//}

	baseUrl.RawQuery = query.Encode()

	return baseUrl.String(), nil
}

// SignContractQuery 账户协议签约查询
func (s member) SignContractQuery(req SignContractQueryReq) (SignContractQueryRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["signAcctName"] = req.SignAcctName
	params["jumpUrl"] = req.JumpUrl
	params["source"] = req.Source
	method := "allinpay.yunst.memberService.signContractQuery"
	var res SignContractQueryRes
	err := s.cli.RequestNotVerify(method, params, &res)
	if err != nil {
		return SignContractQueryRes{}, err
	}
	return res, nil
}

// IdcardCollect 影印件采集
func (s member) IdcardCollect(req IdcardCollectReq) (IdcardCollectRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["ocrComparisonResultBackUrl"] = req.OcrComparisonResultBackUrl
	params["picType"] = req.PicType
	params["picture"] = req.Picture
	method := "allinpay.yunst.memberService.idcardCollect"
	var res IdcardCollectRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return IdcardCollectRes{}, err
	}
	return res, nil
}

func (s member) UnbindBankCard(req UnbindBankCardReq) (UnbindBankCardRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId

	// aes
	sha1PRNG, err := utils.AesSha1PRNG([]byte(s.cli.AppSecretKey), 128)
	if err != nil {
		return UnbindBankCardRes{}, err
	}

	cardNo := hex.EncodeToString(utils.AesEcbEncrypt([]byte(req.CardNo), sha1PRNG))

	params["cardNo"] = cardNo
	//params["agreementNo"] =
	method := "allinpay.yunst.memberService.unbindBankCard"
	var res UnbindBankCardRes
	err = s.cli.Request(method, params, &res)
	if err != nil {
		return UnbindBankCardRes{}, err
	}
	return res, nil
}

// ApplyBindBankCard 请求绑定银行卡
func (s member) ApplyBindBankCard(req ApplyBindBankCardReq) (ApplyBindBankCardRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId

	// aes
	sha1PRNG, err := utils.AesSha1PRNG([]byte(s.cli.AppSecretKey), 128)
	if err != nil {
		return ApplyBindBankCardRes{}, err
	}

	cardNo := hex.EncodeToString(utils.AesEcbEncrypt([]byte(req.CardNo), sha1PRNG))
	identityNo := hex.EncodeToString(utils.AesEcbEncrypt([]byte(req.IdentityNo), sha1PRNG))

	params["cardNo"] = cardNo
	params["phone"] = req.Phone
	params["name"] = req.Name
	params["cardCheck"] = req.CardCheck
	params["identityType"] = req.IdentityType

	params["identityNo"] = identityNo
	//params["validate"] = req.IdentityNo
	//params["cvv2"] = req.IdentityNo
	//params["unionBank"] = req.IdentityNo
	method := "allinpay.yunst.memberService.applyBindBankCard"
	var res ApplyBindBankCardRes
	err = s.cli.Request(method, params, &res)
	if err != nil {
		return ApplyBindBankCardRes{}, err
	}
	return res, nil
}

func (s member) BindBankCard(req BindBankCardReq) (BindBankCardRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["tranceNum"] = req.TranceNum // 绑卡方式6、7必传）
	params["phone"] = req.Phone
	params["verificationCode"] = req.VerificationCode
	method := "allinpay.yunst.memberService.bindBankCard"
	var res BindBankCardRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return BindBankCardRes{}, err
	}
	return res, nil
}

// QueryBankCard 查询绑定银行卡
func (s member) QueryBankCard(req QueryBankCardReq) (QueryBankCardRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["cardNo"] = req.CardNo
	method := "allinpay.yunst.memberService.queryBankCard"
	var res QueryBankCardRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return QueryBankCardRes{}, err
	}
	return res, nil
}

// ApplyBindAcct 会员绑定支付账户用户标识
func (s member) ApplyBindAcct(req ApplyBindAcctReq) (ApplyBindAcctRes, error) {
	params := map[string]interface{}{}
	params["bizUserId"] = req.BizUserId
	params["operationType"] = req.OperationType
	params["acctType"] = req.AcctType
	params["acct"] = req.Acct
	method := "allinpay.yunst.memberService.applyBindAcct"
	var res ApplyBindAcctRes
	err := s.cli.Request(method, params, &res)
	if err != nil {
		return ApplyBindAcctRes{}, err
	}
	return res, nil
}

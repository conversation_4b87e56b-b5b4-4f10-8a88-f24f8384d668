package service

// QueryMerchantBalanceReq 平台账户集余额查询
type QueryMerchantBalanceReq struct {
	AccountSetNo string `json:"accountSetNo"` // 必填 账户集编号	平台标准账户集编号
}

type QueryMerchantBalanceRes struct {
	AllAmount    int `json:"allAmount"`    // 必填 总额
	FreezeAmount int `json:"freezeAmount"` // 必填 冻结额	单位：分
}

// QueryReserveFundBalanceReq 平台头寸查询
type QueryReserveFundBalanceReq struct {
	SysID       string `json:"sysid"`       //    分配的系统编号
	FundAcctSys int    `json:"fundAcctSys"` //    资金托管系统  1-通联通  2-电子账户  不填默认为通联通
}

type QueryReserveFundBalanceRes struct {
	AccountNo   string `json:"accountNo"`   // 必填 账户号 通联通：通联通商户号
	AccountName string `json:"accountName"` // 必填 账户名 通联通：通联通商户名称
	Balance     int    `json:"balance"`     // 必填 余额   取渠道可用余额
	DefClr      int    `json:"defClr	"`     // 必填 是否默认结算户  电子账户管理不返回该字段
}

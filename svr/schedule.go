package svr

import (
	"base/queueConsumerService"
	"base/service/buyerService"
	"base/service/buyerStatsService"
	"base/service/couponUserService"
	"base/service/messageService"
	"base/service/orderAgentPayService"
	"base/service/supplierStatService"
	"base/service/trackService"
	"base/util"
	"context"
	"time"

	"github.com/go-co-op/gocron"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func LoadSchedule() {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("LoadSchedule %v", err)
		}
	}()

	s := gocron.NewScheduler(time.Local)

	do, err := s.Every(1).Day().At("18:00").Do(func() {
		err := supplierStatService.NewSupplierStatService().CalcDaily(context.Background())
		if err != nil {
			zap.S().Errorf("执行定时任务失败,%v", err.Error())
		}
	})

	s.Every(1).Day().At("9:00;12:00;15:25;18:05").Do(func() {
		refreshBuyerStats()
	})

	s.Every(1).Day().At("17:00").Do(func() {
		sendRebateNotify()
	})

	s.Every(5).Second().Do(func() {
		queueConsumerService.CheckQueue(context.Background())
	})

	// 每隔1天，检查用户优惠券
	s.Every(1).Day().Do(func() {
		couponUserService.NewCouponUserService().CheckCouponUser(context.Background())
	})

	if err != nil {
		zap.S().Errorf("执行定时任务失败,%v", err.Error())
		return
	}
	_ = do

	s.StartAsync()

}

func refreshBuyerStats() {
	now := time.Now()
	//milli := time.Now().UnixMilli()
	//begin, err := util.DayStartZeroTimestamp(milli)

	ctx := context.Background()

	for i := 1; i < 20; i++ {
		active, count, err := trackService.NewTrackService().ListLatestActive(ctx, int64(i), 50)
		if err != nil {

		}
		_ = count

		if len(active) < 1 {
			break
		}

		for _, z := range active {
			//t := int64(z.Score)
			name := z.Member.(string)

			// 判断t是否是今日，非今日不更新
			//if t < begin {
			//	continue
			//}

			buyerID, _ := util.ConvertToObjectWithCtx(ctx, name)

			buyer, _ := buyerService.NewBuyerService().GetByID(ctx, buyerID)
			//if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			//
			//}
			//zap.S().Infof("刷新 会员统计:%s", buyer.BuyerName)
			buyerStatsService.NewBuyerStatsService().Fresh(ctx, buyer.ID)
		}

	}

	seconds := time.Now().Sub(now).Seconds()
	zap.S().Infof("refreshBuyerStats cost %v:", seconds)

	//if err != nil {
	//	zap.S().Errorf("执行定时任务失败,%v", err.Error())
	//}
}

func sendRebateNotify() {
	defer func() {
		messageService.NewMessageService().RemoveRebateNotify()
	}()

	listRebateNotify := messageService.NewMessageService().ListRebateNotify()
	milli := time.Now().UnixMilli()
	begin, end, _ := util.DayScopeTimestamp(milli)

	buyerIDs := make([]primitive.ObjectID, 0, len(listRebateNotify))

	for _, s := range listRebateNotify {
		id, _ := primitive.ObjectIDFromHex(s)
		buyerIDs = append(buyerIDs, id)
	}

	if len(buyerIDs) < 1 {
		zap.S().Warnf("sendRebateNotify 待发送会员数组为空，跳过今日返利通知")
		return
	}

	filter := bson.M{
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
		"agent_pay_result.pay_status": "success",
		"agent_pay_result.status":     "OK",
		"buyer_id": bson.M{
			"$in": buyerIDs,
		},
	}
	orderAgentPays, err := orderAgentPayService.NewOrderAgentPayService().List(context.Background(), filter)
	if err != nil {
		return
	}

	//

	mUser := make(map[primitive.ObjectID]int)
	var userIDs []primitive.ObjectID
	for _, pay := range orderAgentPays {
		mUser[pay.UserID] += pay.RebateServiceFeeAmount
		userIDs = append(userIDs, pay.UserID)
	}
}

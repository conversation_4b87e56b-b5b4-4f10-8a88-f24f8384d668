package orderWarehouseService

import (
	"base/core/xerr"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/service/orderDebtService"
	"base/service/orderQualityService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/orderStatusRecordService"
	"base/service/orderStockUpService"
	"base/service/servicePointService"
	"base/types"
	"base/util"
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

var cacheOverWeight = "cacheOverWeight"

// ServiceInterface 备货
type ServiceInterface interface {
	ListQuality(ctx context.Context, warehouseID, servicePointID primitive.ObjectID, timestamp int64, hasQuality bool) ([]model.OrderStockUp, error)
	UpdateQuality(ctx context.Context, id primitive.ObjectID, req types.QualityReq) error
	SyncSortData(ctx context.Context, qualities []model.OrderQuality, orderIDs []primitive.ObjectID) error
	//SyncSortData2(ctx context.Context, orderIDs string) error
	UpdateSort(ctx context.Context, req types.OrderSortReq) error
	ListSort(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64) ([]model.OrderStockUp, error)
	ListShip(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64, hasSort bool) (interface{}, error)
	ListSortOrder(ctx context.Context, id primitive.ObjectID) (interface{}, error)
	ListOrderToStockUp(ctx context.Context, warehouseID primitive.ObjectID, endTimestamp int64) ([]model.Order, error)
	//UpdateOrderToStockUp(ctx context.Context, warehouseID primitive.ObjectID, endTimestamp, targetTimestamp int64) error
	ListOrderToQualityTemp(ctx context.Context, warehouseID primitive.ObjectID, ts int64) ([]model.Order, error)
	Stats(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64, hasQuality bool) ([]QualityStats, error)
	ToShip(ctx context.Context, orderIDs []primitive.ObjectID) error // 集中仓-发货
	ShipRetail(ctx context.Context, orderID primitive.ObjectID, LogisticsImageList []model.FileInfo) error
	ConfirmRetail(ctx context.Context, orderIDs []primitive.ObjectID) error

	CheckOverWeight(ctx context.Context, content string) error
	ListOverWeight(ctx context.Context) ([]model.OverWeight, error)

	//CheckAfterShip(ctx context.Context, orderIDs string) error
}
type orderWarehouseService struct {
	mdb                *mongo.Database
	rdb                *redis.Client
	l                  *zap.SugaredLogger
	orderS             orderService.ServiceInterface
	orderStockUpS      orderStockUpService.ServiceInterface
	orderQualityS      orderQualityService.ServiceInterface
	orderRefundS       orderRefundService.ServiceInterface
	orderDebtS         orderDebtService.ServiceInterface
	servicePointS      servicePointService.ServiceInterface
	orderStatusRecordS orderStatusRecordService.ServiceInterface
	mnsS               *mnsSendService.MnsClient
}

func NewOrderWarehouseService() ServiceInterface {
	return orderWarehouseService{
		mdb:                global.MDB,
		rdb:                global.RDBDefault,
		l:                  global.OrderLogger.Sugar(),
		orderS:             orderService.NewOrderService(),
		orderStockUpS:      orderStockUpService.NewOrderStockUpService(),
		orderQualityS:      orderQualityService.NewOrderQualityService(),
		orderRefundS:       orderRefundService.NewOrderRefundService(),
		orderDebtS:         orderDebtService.NewOrderDebtService(),
		servicePointS:      servicePointService.NewServicePointService(),
		orderStatusRecordS: orderStatusRecordService.NewOrderStatusRecordService(),
		mnsS:               mnsSendService.NewMNSClient(),
	}
}

func (s orderWarehouseService) ListQuality(ctx context.Context, warehouseID, servicePointID primitive.ObjectID, timestamp int64, hasQuality bool) ([]model.OrderStockUp, error) {
	ups, err := s.orderStockUpS.ListByWarehouse(ctx, warehouseID, timestamp, hasQuality)
	if err != nil {
		return nil, err
	}

	return ups, nil
}

func (s orderWarehouseService) ListSort(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64) ([]model.OrderStockUp, error) {
	ups, err := s.orderStockUpS.ListBySort(ctx, warehouseID, timestamp)
	if err != nil {
		return nil, err
	}

	return ups, nil
}

func (s orderWarehouseService) ListShip(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64, hasSort bool) (interface{}, error) {
	ups, err := s.orderStockUpS.ListBySort(ctx, warehouseID, timestamp)
	if err != nil {
		return nil, err
	}

	// 所有服务点
	mPointName := make(map[primitive.ObjectID]string)
	for _, i := range ups {
		for _, point := range i.QualityServicePointList {
			mPointName[point.ServicePointID] = point.ServicePointName
		}
	}
	//mp := make(map[primitive.ObjectID][]model.PerOrder)
	//for pointID, _ := range mPoint {
	//	var orderL []model.PerOrder
	//	for _, up := range ups {
	//		for _, order := range up.OrderList {
	//			if order.ServicePointID == pointID {
	//				orderL = append(orderL, order)
	//			}
	//		}
	//	}
	//	mp[pointID] = orderL
	//}

	//type o struct {
	//}
	//
	//type PerPointShip struct {
	//	ServicePointID   primitive.ObjectID `json:"service_point_id"`
	//	ServicePointName string             `json:"service_point_name"`
	//	OrderList        []model.PerOrder   `json:"order_list"`
	//}
	//
	//var l []PerPointShip
	//
	//for pointID, orders := range mp {
	//	item := PerPointShip{
	//		ServicePointID:   pointID,
	//		ServicePointName: mPoint[pointID].ServicePointName,
	//		OrderList:        orders,
	//	}
	//	l = append(l, item)
	//}

	mOrderPoint := make(map[primitive.ObjectID]primitive.ObjectID)
	for _, up := range ups {
		for _, order := range up.OrderList {
			mOrderPoint[order.OrderID] = order.ServicePointID
		}
	}

	//return l, nil
	m := make(map[primitive.ObjectID][]Info)
	mOrder := make(map[primitive.ObjectID]string)
	mOrderShip := make(map[primitive.ObjectID]bool)
	//mOrderSort := make(map[primitive.ObjectID]bool)
	for _, up := range ups {
		for _, order := range up.OrderList {
			mOrder[order.OrderID] = order.BuyerName
			mOrderShip[order.OrderID] = order.HasShip
			//mOrderSort[order.OrderID] = order.SortHas

			if v, ok := m[order.OrderID]; ok {
				v = append(v, Info{
					ProductTitle: up.ProductTitle,
					DueNum:       order.DueNum,
					SortNum:      order.SortNum,
					SortHas:      order.SortHas,
				})
				m[order.OrderID] = v
				continue
			}
			m[order.OrderID] = []Info{
				{
					ProductTitle: up.ProductTitle,
					DueNum:       order.DueNum,
					SortNum:      order.SortNum,
					SortHas:      order.SortHas,
				},
			}
		}
	}
	var list []Per
	for id, infos := range m {
		sortHas := true
		for _, info := range infos {
			if info.SortHas == false {
				if sortHas {
					sortHas = false
				}
			}
		}
		if !sortHas {
			continue
		}
		item := Per{
			OrderID:     id,
			HasShip:     mOrderShip[id],
			HasSort:     sortHas,
			BuyerName:   mOrder[id],
			ProductList: infos,
		}
		list = append(list, item)
	}

	mPoint := make(map[primitive.ObjectID][]Per)
	for orderID, pointID := range mOrderPoint {
		for _, per := range list {
			if per.OrderID == orderID {
				mPoint[pointID] = append(mPoint[pointID], per)
			}
		}
	}

	var resList []PointPer
	for pointID, pers := range mPoint {
		item := PointPer{
			ServicePointID:   pointID,
			ServicePointName: mPointName[pointID],
			OrderList:        pers,
		}
		resList = append(resList, item)
	}

	return resList, nil
}

type PointPer struct {
	ServicePointID   primitive.ObjectID `json:"service_point_id"`
	ServicePointName string             `json:"service_point_name"`
	OrderList        []Per              `json:"order_list"`
}

type Per struct {
	OrderID     primitive.ObjectID `json:"order_id"`
	HasShip     bool               `json:"has_ship"`
	HasSort     bool               `json:"has_sort"`
	BuyerName   string             `json:"buyer_name"`
	ProductList []Info             `json:"product_list"`
}

type Info struct {
	//List         []model.PerOrder `json:"list"`
	ProductTitle string `json:"product_title"`
	DueNum       int    `json:"due_num"`
	SortNum      int    `json:"sort_num"`
	SortHas      bool   `json:"sort_has"`
}

func (s orderWarehouseService) ListSortOrder(ctx context.Context, id primitive.ObjectID) (interface{}, error) {
	up, err := s.orderStockUpS.GetStockUp(ctx, id)
	if err != nil {
		return nil, err
	}
	var ids []primitive.ObjectID
	for _, order := range up.OrderList {
		ids = append(ids, order.OrderID)
	}

	return up, nil
}

func (s orderWarehouseService) ListOrderToStockUp(ctx context.Context, warehouseID primitive.ObjectID, endTimestamp int64) ([]model.Order, error) {
	filter := bson.M{
		"created_at": bson.M{
			"$lte": endTimestamp,
		}, // 已支付
		"pay_status": model.PayStatusTypePaid, // 已支付
		//"warehouse_id": warehouseID,
		"order_status": model.OrderStatusTypeToStockUp, // 待备货
	}
	list, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

//func (s orderWarehouseService) UpdateOrderToStockUp(ctx context.Context, warehouseID primitive.ObjectID, endTimestamp, targetTimestamp int64) error {
//	filter := bson.M{
//		"created_at": bson.M{
//			"$lte": endTimestamp,
//		}, // 已支付
//		"pay_status":   model.PayStatusTypePaid,        // 已支付
//		"warehouse_id": warehouseID,                    // 已支付
//		"order_status": model.OrderStatusTypeToStockUp, // 待备货
//	}
//
//	status := model.OrderStatusTypeToQuality
//	key := model.BackRecordKey(status)
//
//	now := time.Now().UnixMilli()
//	update := bson.M{
//		"$set": bson.M{
//			"order_status":               status, // 待品控
//			"order_status_record." + key: now,    // 状态记录
//		},
//	}
//
//	err := s.orderS.UpdateMany(ctx, filter, update)
//	if err != nil {
//		return err
//	}
//	return nil
//}

// ListOrderToQualityTemp 临时--待品控
func (s orderWarehouseService) ListOrderToQualityTemp(ctx context.Context, warehouseID primitive.ObjectID, ts int64) ([]model.Order, error) {
	filter := bson.M{
		"stock_up_day_time": ts,
		"pay_status":        model.PayStatusTypePaid,        // 已支付
		"order_status":      model.OrderStatusTypeToQuality, // 待品控
	}

	list, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderWarehouseService) Stats(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64, hasQuality bool) ([]QualityStats, error) {
	upList, err := s.orderStockUpS.ListByWarehouse(ctx, warehouseID, timestamp, hasQuality)
	if err != nil {
		return nil, err
	}

	mByPoint := make(map[primitive.ObjectID][]model.OrderStockUp)
	mPoint := make(map[primitive.ObjectID]string)
	for _, up := range upList {
		for _, order := range up.OrderList {
			mByPoint[order.ServicePointID] = append(mByPoint[order.ServicePointID], up)
			mPoint[order.ServicePointID] = order.ServicePointName
		}
	}

	var list []QualityStats
	for id, ups := range mByPoint {
		mNumSupplier := make(map[primitive.ObjectID]int)
		var numOrder int
		var numProduct int
		var weight int
		for _, up := range ups {
			mNumSupplier[up.SupplierID] = 0
			numOrder += len(up.OrderList)
			for _, order := range up.OrderList {
				numProduct += order.QualityNum
				weight += up.PerRoughWeight * order.QualityNum
			}
		}

		list = append(list, QualityStats{
			ServicePointID:   id,
			ServicePointName: mPoint[id],
			SupplierNum:      len(mNumSupplier),
			OrderNum:         numOrder,
			ProductNum:       numProduct,
			ProductTypeNum:   len(ups),
			Weight:           weight,
		})

	}

	return list, nil
}

type QualityStats struct {
	ServicePointID   primitive.ObjectID `json:"service_point_id"`
	ServicePointName string             `json:"service_point_name"`
	SupplierNum      int                `json:"supplier_num"`
	ProductNum       int                `json:"product_num"`
	ProductTypeNum   int                `json:"product_type_num"`
	OrderNum         int                `json:"order_num"`
	Weight           int                `json:"weight"`
}

// UpdateQuality 品控
func (s orderWarehouseService) UpdateQuality(ctx context.Context, id primitive.ObjectID, req types.QualityReq) error {
	now := time.Now().UnixMilli()
	up, err := s.orderStockUpS.GetStockUp(ctx, id)
	if err != nil {
		return err
	}

	if !up.StockUpHas {
		return xerr.NewErr(xerr.ErrParamError, nil, "尚未备货，不能品控")
	}

	err = checkQuality(up, req)
	if err != nil {
		return err
	}

	mPoint := make(map[string]int)
	for _, v := range req.QualityServicePointList {
		mPoint[v.ServicePointID] = v.QualityNum
	}

	ql := up.QualityServicePointList
	for i, point := range ql {
		ql[i].QualityNum = mPoint[point.ServicePointID.Hex()]
	}

	orderL := up.OrderList
	for i, o := range orderL {
		orderL[i].QualityNum = mPoint[o.ServicePointID.Hex()]
	}

	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		err = s.orderStockUpS.UpdateOne(sessCtx, bson.M{"_id": id}, bson.M{"$set": bson.M{
			"order_list":                 orderL,
			"quality_service_point_list": ql,
			"quality_has":                true,
			"updated_at":                 now,
		}})
		if err != nil {
			return nil, err
		}
		var oIDs []primitive.ObjectID
		for _, order := range up.OrderList {
			oIDs = append(oIDs, order.OrderID)
		}

		filter := bson.M{
			"_id": bson.M{"$in": oIDs},
			"order_status": bson.M{
				"$lte": model.OrderStatusTypeToSort, // 小于分拣环节的
			},
		}

		status := model.OrderStatusTypeToSort
		key := model.BackRecordKey(status)

		err = s.orderS.UpdateMany(sessCtx, filter, bson.M{"$set": bson.M{
			"order_status":               model.OrderStatusTypeToSort, // 待分拣
			"order_status_record." + key: now,                         // 状态记录
		}})
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

// UpdateSort 分拣
func (s orderWarehouseService) UpdateSort(ctx context.Context, req types.OrderSortReq) error {
	id, err := util.ConvertToObjectWithNote(req.ID, "备货信息ID")
	if err != nil {
		return err
	}
	up, err := s.orderStockUpS.GetStockUp(ctx, id)
	if err != nil {
		return err
	}

	// 校验
	err = checkSort(up, req)
	if err != nil {
		return err
	}

	oList := up.OrderList
	for _, order := range req.OrderList {
		oID, err := util.ConvertToObjectWithNote(order.OrderID, "req sort order id")
		if err != nil {
			return err
		}
		for i, perOrder := range oList {
			if perOrder.OrderID == oID {
				oList[i].SortNum = order.Num
				oList[i].SortHas = true
				if up.IsCheckWeight {
					oList[i].SortWeight = order.Weight
				}
			}
		}
	}
	var oIDs []primitive.ObjectID
	for _, order := range up.OrderList {
		oIDs = append(oIDs, order.OrderID)
	}
	now := time.Now().UnixMilli()

	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 更新备货单
		err = s.orderStockUpS.UpdateOne(sessCtx, bson.M{"_id": id}, bson.M{"$set": bson.M{
			"order_list": oList,
			//"sort_has":   true,
			"updated_at": now,
		}})

		// 更新订单
		filter := bson.M{
			"_id": bson.M{"$in": oIDs},
			"order_status": bson.M{
				"$lte": model.OrderStatusTypeToShip, // 小于发货环节的
			},
		}
		status := model.OrderStatusTypeToShip
		key := model.BackRecordKey(status)

		err = s.orderS.UpdateMany(sessCtx, filter, bson.M{"$set": bson.M{
			"order_status":               status, // 待发货
			"order_status_record." + key: now,    // 状态记录
		}})
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

// ToShip 发货--涉及更新状态，退款和补差订单--临时
func (s orderWarehouseService) ToShip(ctx context.Context, orderIDs []primitive.ObjectID) error {
	marshal, _ := json.Marshal(orderIDs)
	s.l.Infof("发货订单总计：%d,数据：%s", len(orderIDs), string(marshal))

	orders, err := s.orderS.ListByOrderIDs(ctx, orderIDs)
	if err != nil {
		return err
	}

	err = s.checkToShip(orders)
	if err != nil {
		return err
	}

	qualities, err := s.orderQualityS.List(ctx, bson.M{
		"order_list.order_id": bson.M{
			"$in": orderIDs,
		},
	})
	if err != nil {
		return err
	}

	// 检查品控单和订单对应
	for _, id := range orderIDs {
		var exist bool
		for _, quality := range qualities {
			if exist {
				break
			}
			for _, temp := range quality.OrderList {
				if id == temp.OrderID {
					exist = true
					break
				}
			}
		}
		if !exist {
			s.l.Errorf("品控单中不存在订单:%s", id.Hex())
			return xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("品控单中不存在订单:%s", id.Hex()))
		}
	}

	for _, q := range qualities {
		if !q.SortHas {
			return xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("发货失败，订单存在未分拣,%s", q.ProductTitle))
		}
	}

	//var idSStr string
	for _, d := range orderIDs {
		err = s.orderQualityS.UpdateMany(ctx, bson.M{
			"order_list.order_id": d,
		}, bson.M{
			"$set": bson.M{
				"order_list.$.has_ship": true,
			},
		})
		if err != nil {
			s.l.Errorf("发货更新备货单错误：%v", err)
			return err
		}
		//idSStr += d.Hex() + ","
	}

	statusArrive := model.OrderStatusTypeToArrive
	keyToArrive := model.BackRecordKey(statusArrive)

	statusReceive := model.OrderStatusTypeToReceive
	keyToReceive := model.BackRecordKey(statusReceive)

	filter := bson.M{
		"_id": bson.M{
			"$in": orderIDs,
		},
	}

	now := time.Now().UnixMilli()

	update := bson.M{
		"order_status":                        model.OrderStatusTypeToReceive, // 待收货
		"order_status_record." + keyToArrive:  now,                            // 待到货
		"order_status_record." + keyToReceive: now,                            // 待收货
	}

	err = s.orderS.UpdateMany(ctx, filter, bson.M{"$set": update})
	if err != nil {
		s.l.Error("更新发货订单状态错误:", err)
		return err
	}

	// 同步数据
	err = s.SyncSortData(ctx, qualities, orderIDs)
	if err != nil {
		s.l.Errorf("SyncSortData error: %s", err.Error())
		return err
	}

	return nil
}

// ShipRetail 零售单-发货
func (s orderWarehouseService) ShipRetail(ctx context.Context, orderID primitive.ObjectID, LogisticsImageList []model.FileInfo) error {
	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		return err
	}

	if order.PayStatus != model.PayStatusTypePaid {
		s.l.Errorf("订单%s未付款，不能发货", order.ID.Hex())
		return xerr.NewErr(xerr.ErrParamError, nil, "存在订单未付款，不能发货")
	}
	if order.OrderStatus == model.OrderStatusTypeToArrive {
		s.l.Errorf("存在订单%s已发货，请勿重复发货", order.ID.Hex())
		return xerr.NewErr(xerr.ErrParamError, nil, "存在订单已发货，请勿重复发货")
	}
	if order.OrderStatus != model.OrderStatusTypeToShip {
		s.l.Errorf("订单%s未处于待发货状态，不能发货", order.ID.Hex())
		return xerr.NewErr(xerr.ErrParamError, nil, "存在订单未处于待发货状态，不能发货")
	}

	statusArrive := model.OrderStatusTypeToArrive
	keyToArrive := model.BackRecordKey(statusArrive)

	statusReceive := model.OrderStatusTypeToReceive
	keyToReceive := model.BackRecordKey(statusReceive)

	filter := bson.M{
		"_id": orderID,
	}

	now := time.Now().UnixMilli()

	update := bson.M{
		"order_status":                        model.OrderStatusTypeToReceive, // 待收货
		"order_status_record." + keyToArrive:  now,                            // 待到货
		"order_status_record." + keyToReceive: now,                            // 待收货
		"has_check_after_ship":                true,
		"logistics_image_list":                LogisticsImageList,
		"logistics_time":                      now,
	}

	err = s.orderS.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		s.l.Error("更新发货订单状态错误:", err)
		return err
	}

	mnsSendService.NewMNSClient().SendOrderAutoReceive(orderID, 48)

	return nil
}

func (s orderWarehouseService) ConfirmRetail(ctx context.Context, orderIDs []primitive.ObjectID) error {
	marshal, _ := json.Marshal(orderIDs)
	s.l.Infof("零售单-确认订单总计：%d,数据：%s", len(orderIDs), string(marshal))

	orders, err := s.orderS.ListByOrderIDs(ctx, orderIDs)
	if err != nil {
		return err
	}

	err = s.checkConfirmShip(orders)
	if err != nil {
		return err
	}

	statusQuality := model.OrderStatusTypeToQuality
	keyToQuality := model.BackRecordKey(statusQuality)

	statusSort := model.OrderStatusTypeToSort
	keyToSort := model.BackRecordKey(statusSort)

	filter := bson.M{
		"_id": bson.M{
			"$in": orderIDs,
		},
	}

	now := time.Now().UnixMilli()

	update := bson.M{
		"order_status":                        model.OrderStatusTypeToShip, // 待发货
		"order_status_record." + keyToQuality: now,                         // 待到货
		"order_status_record." + keyToSort:    now,                         // 待收货
	}

	err = s.orderS.UpdateMany(ctx, filter, bson.M{"$set": update})
	if err != nil {
		s.l.Error("更新确认订单状态错误:", err)
		return err
	}

	return nil
}

// SyncSortData 同步分拣数据
func (s orderWarehouseService) SyncSortData(ctx context.Context, qualities []model.OrderQuality, orderIDs []primitive.ObjectID) error {
	// 1. 同步分拣数据
	//分拣信息
	qualitiesMarshal, _ := json.Marshal(qualities)
	zap.S().Infof("分拣信息：%s", string(qualitiesMarshal))

	for _, quality := range qualities {
		for _, order := range quality.OrderList {
			var existInShipOrder bool
			for _, d := range orderIDs {
				// 是否存在发货订单
				if order.OrderID == d {
					existInShipOrder = true
					break
				}
			}

			if !existInShipOrder {
				// 跳过-不在本次发货列表
				zap.S().Warnf("SyncSortData 不在本次发货列表,跳过订单：%s，会员：%s", order.OrderID.Hex(), order.BuyerName)
				continue
			}

			// 应有总重 = 标准重量*分拣数量
			deserveWeight := quality.PerRoughWeight * order.SortNum
			update := bson.M{
				"product_list.$.due_weight":       deserveWeight, // 分拣数*毛重
				"product_list.$.sort_num":         order.SortNum,
				"product_list.$.sort_weight":      order.SortWeight,
				"product_list.$.sort_user_id":     order.SortUserID,
				"product_list.$.sort_user_name":   order.SortUserName,
				"product_list.$.sort_user_mobile": order.SortUserMobile,
				"product_list.$.photo_list":       order.PhotoList,
				"product_list.$.reason_img":       quality.ReasonImg,
				"product_list.$.reason_type":      quality.ReasonType,
			}

			if order.SortNum == 0 {
				//	 全退
				update["product_list.$.is_ship_refund_all"] = true
			}

			marshal, _ := json.Marshal(order)
			zap.S().Infof("同步更新分拣数据，order:%s,update:%s", string(marshal), update)

			err := s.orderS.UpdateOne(ctx, bson.M{"_id": order.OrderID, "product_list.product_id": quality.ProductID, "product_list.sku_id_code": quality.SkuIDCode},
				bson.M{"$set": update})
			if err != nil {
				return err
			}
		}
	}

	// 2. 创建退款和补差
	filter := bson.M{
		"_id": bson.M{
			"$in": orderIDs,
		},
		//"order_status":         model.OrderStatusTypeToArrive,
		"has_check_after_ship": false,
	}
	orderList, err := s.orderS.List(ctx, filter)
	if err != nil {
		return err
	}

	zap.S().Infof("CheckAfterShip order list len::%d", len(orderList))

	var debtList []model.OrderDebt
	for _, order := range orderList {
		pSettleList, err := s.orderRefundS.BackShipSettle(ctx, order)
		if err != nil {
			return err
		}

		debt, err := s.orderDebtS.Create(ctx, order, pSettleList)
		if err != nil {
			return err
		}
		if debt.ID != primitive.NilObjectID {
			debtList = append(debtList, debt)
		}
	}

	// 订单-全退状态
	var orderRefundAllIDList []primitive.ObjectID
	for _, order := range orderList {
		refundAll := true
		for _, i := range order.ProductList {
			if i.IsShipRefundAll == false {
				refundAll = false
				break
			}
		}
		if refundAll {
			orderRefundAllIDList = append(orderRefundAllIDList, order.ID)
		}
	}

	if len(orderRefundAllIDList) > 0 {
		refundAll, _ := json.Marshal(orderRefundAllIDList)
		s.l.Infof("订单全退列表::%s", string(refundAll))
		update := bson.M{
			"order_refund_all": true,
			"order_status":     model.OrderStatusTypeFinish,
		}
		err = s.orderS.UpdateMany(ctx, bson.M{"_id": bson.M{
			"$in": orderRefundAllIDList,
		}}, bson.M{"$set": update})
	}

	//if len(ids) > 0 {
	now := time.Now().UnixMilli()
	err = s.orderS.UpdateMany(ctx, bson.M{"_id": bson.M{"$in": orderIDs}}, bson.M{"$set": bson.M{
		"has_check_after_ship": true,
		"updated_at":           now,
	}})
	if err != nil {
		return err
	}
	//}

	// 配送单
	zeroTimestamp, err := util.DayStartZeroTimestamp(now)
	if err != nil {
		return err
	}

	var buyerList []model.MNSBuyer
	for _, order := range orderList {
		var exist bool
		for _, b := range buyerList {
			if b.BuyerID == order.BuyerID.Hex() && b.DeliverType == order.DeliverType {
				exist = true
				break
			}
		}
		if !exist {
			buyerList = append(buyerList, model.MNSBuyer{
				BuyerID:     order.BuyerID.Hex(),
				DeliverType: order.DeliverType,
			})
		}
	}

	//genData := model.MNSGenDeliverNote{
	//	BuyerList: buyerList,
	//	Timestamp: zeroTimestamp,
	//}

	for _, buyerPerGen := range buyerList {
		genData := model.MNSGenDeliverNote{
			BuyerList: []model.MNSBuyer{buyerPerGen},
			Timestamp: zeroTimestamp,
		}

		mnsSendService.NewMNSClient().SendDeliverNoteGenerate(genData)
	}

	refundBatch := backRefundBatch(debtList)
	// 执行退款
	for index, batch := range refundBatch {
		if len(batch) == 0 {
			continue
		}
		basicSecond := index*60 + 10
		_ = basicSecond
		marshal, _ := json.Marshal(batch)
		zap.S().Warnf("退款：%s", string(marshal))
		mnsSendService.NewMNSClient().SendShipSettle(ctx, batch, int64(basicSecond))
	}

	return nil
}

func backRefundBatch(list []model.OrderDebt) [][]primitive.ObjectID {
	byParentOrder := make(map[primitive.ObjectID][]model.OrderDebt)
	maxLen := 0
	for _, d := range list {
		if d.RefundFinalAmount <= 0 {
			continue
		}

		byParentOrder[d.ParentOrderID] = append(byParentOrder[d.ParentOrderID], d)
		if len(byParentOrder[d.ParentOrderID]) > maxLen {
			maxLen = len(byParentOrder[d.ParentOrderID])
		}
	}

	resList := make([][]primitive.ObjectID, maxLen)

	for _, orderRefunds := range byParentOrder {
		for index, refund := range orderRefunds {
			resList[index] = append(resList[index], refund.ID)
		}
	}

	return resList

}

//func (s orderWarehouseService) CheckAfterShip(ctx context.Context, orderIDs string) error {
//defer func() {
//	if err := recover(); err != nil {
//		zap.S().Errorf("CheckAfterShip error:%v", err)
//		return
//	}
//}()
//s.l.Infof("------------CheckAfterShip-------------")
//
//split := strings.Split(orderIDs, ",")
//s.l.Infof("CheckAfterShip split len:%d，orderIDs:%s", len(split), orderIDs)
//var orderIDList []primitive.ObjectID
//for _, i := range split {
//	id, err := util.ConvertToObjectWithNote(i, "sync CheckAfterShip order id")
//	if err != nil {
//		s.l.Errorf("CheckAfterShip 订单ID转换错误：%v", err)
//		return err
//	}
//	orderIDList = append(orderIDList, id)
//}
//
//filter := bson.M{
//	"_id": bson.M{
//		"$in": orderIDList,
//	},
//	"order_status":         model.OrderStatusTypeToArrive,
//	"has_check_after_ship": false,
//}
//orders, err := s.orderS.List(ctx, filter)
//if err != nil {
//	return err
//}
//
//zap.S().Infof("CheckAfterShip order list len::%d", len(orders))
//
//var ids []primitive.ObjectID
//var refundIDs []primitive.ObjectID
//for _, order := range orders {
//	refunds, err := s.orderRefundS.CreateShipRefund(ctx, order)
//	if err != nil {
//		return err
//	}
//
//	_, err = s.orderDebtS.Create(ctx, order, refunds)
//	if err != nil {
//		return err
//	}
//	ids = append(ids, order.ID)
//
//	for _, refund := range refunds {
//		refundIDs = append(refundIDs, refund.ID)
//	}
//
//}
//
//if len(ids) > 0 {
//	err = s.orderS.UpdateMany(ctx, bson.M{"_id": bson.M{"$in": ids}}, bson.M{"$set": bson.M{
//		"has_check_after_ship": true,
//		"updated_at":           time.Now().UnixMilli(),
//	}})
//	if err != nil {
//		return err
//	}
//}
//
//if len(refundIDs) > 0 {
//	// 通知 退款
//	marshal, _ := json.Marshal(refundIDs)
//	zap.S().Infof("CheckAfterShip 退款ID列表::%s", string(marshal))
//	mnsSendService.NewMNSClient().SendDoAfterShipRefund(refundIDs)
//}

//return nil
//}

func (s orderWarehouseService) CheckOverWeight(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("CheckOverWeight error:%v", err)
			return
		}
	}()

	var data model.MNSOverWeight
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	var ids []primitive.ObjectID
	for _, s2 := range data.OrderIDList {
		id, err := util.ConvertToObjectWithCtx(ctx, s2)
		if err != nil {
			return err
		}
		ids = append(ids, id)
	}

	orders, err := s.orderS.ListByOrderIDs(ctx, ids)
	if err != nil {
		return err
	}

	var list []model.OverWeight
	now := time.Now().UnixMilli()

	for _, o := range orders {
		for _, p := range o.ProductList {
			//n := float64(p.DueWeight) * 0.1
			if p.SortWeight-p.DueWeight > 0 {
				item := model.OverWeight{
					BuyerID:        o.BuyerID,
					BuyerName:      o.BuyerName,
					OrderID:        o.ID,
					ProductID:      p.ProductID,
					SupplierID:     o.SupplierID,
					SupplierName:   o.SupplierName,
					ProductTitle:   p.ProductTitle,
					Num:            p.Num,
					SortNum:        p.SortNum,
					RoughWeight:    p.RoughWeight,
					DueWeight:      p.DueWeight,
					SortWeight:     p.SortWeight,
					OverWeight:     p.SortWeight - p.DueWeight,
					StockUpDayTime: o.StockUpDayTime,
					CreatedAt:      now,
				}
				list = append(list, item)
			}
		}
	}

	for _, weight := range list {
		marshal, err := json.Marshal(weight)
		if err != nil {
			return err
		}

		s.rdb.ZAdd(ctx, cacheOverWeight, &redis.Z{
			Member: string(marshal),
			Score:  float64(weight.CreatedAt),
		})
	}

	return nil
}

func (s orderWarehouseService) ListOverWeight(ctx context.Context) ([]model.OverWeight, error) {
	now := time.Now()

	milli := now.UnixMilli()

	before := now.Add(-time.Hour * 24 * 1).UnixMilli()

	//zs := s.rdb.ZRevRangeWithScores(ctx, cacheOverWeight, before, milli).Val()
	//zs := s.rdb.ZRange(ctx, cacheOverWeight, before, milli).Val()
	zs := s.rdb.ZRangeByScore(ctx, cacheOverWeight, &redis.ZRangeBy{
		Min: strconv.Itoa(int(before)),
		Max: strconv.Itoa(int(milli)),
	}).Val()

	var list []model.OverWeight
	for _, z := range zs {
		//ss := z.Member.(string)
		var info model.OverWeight
		err := json.Unmarshal([]byte(z), &info)
		if err != nil {
			return nil, err
		}
		list = append(list, info)
	}

	return list, nil
}

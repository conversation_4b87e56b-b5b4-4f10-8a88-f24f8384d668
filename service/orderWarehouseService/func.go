package orderWarehouseService

import (
	"base/core/xerr"
	"base/model"
	"base/types"
	"context"
	jsoniter "github.com/json-iterator/go"
	"go.uber.org/zap"
)

// 检查是否符合发货条件
func (s orderWarehouseService) checkToShip(list []model.Order) error {
	for _, i := range list {
		if i.PayStatus != model.PayStatusTypePaid {
			s.l.<PERSON>rf("订单%s未付款，不能发货", i.ID.Hex())
			return xerr.NewErr(xerr.ErrParamError, nil, "存在订单未付款，不能发货")
		}
		if i.OrderStatus == model.OrderStatusTypeToArrive {
			s.l.<PERSON>("存在订单%s已发货，请勿重复发货", i.ID.Hex())
			return xerr.NewErr(xerr.ErrParamError, nil, "存在订单已发货，请勿重复发货")
		}
		if i.OrderStatus != model.OrderStatusTypeToShip {
			s.l.Errorf("订单%s未处于待发货状态，不能发货", i.ID.Hex())
			return xerr.NewErr(xerr.ErrParamError, nil, "存在订单未处于待发货状态，不能发货")
		}
	}
	return nil
}

// 检查是否符合发货条件
func (s orderWarehouseService) checkConfirmShip(list []model.Order) error {
	for _, i := range list {
		if i.PayStatus != model.PayStatusTypePaid {
			s.l.Errorf("订单%s未付款，不能确认订单", i.ID.Hex())
			return xerr.NewErr(xerr.ErrParamError, nil, "存在订单未付款，不能确认订单")
		}
		if i.OrderStatus > model.OrderStatusTypeToStockUp {
			s.l.Errorf("存在订单%s已确认，请勿重复确认", i.ID.Hex())
			return xerr.NewErr(xerr.ErrParamError, nil, "存在订单已确认，请勿重复确认")
		}
		if i.OrderStatus != model.OrderStatusTypeToStockUp {
			s.l.Errorf("订单%s未处于待确认状态，不能确认订单", i.ID.Hex())
			return xerr.NewErr(xerr.ErrParamError, nil, "存在订单未处于待确认状态，不能确认订单")
		}
	}
	return nil
}

//
//// CheckShippedExistDebt 检查存在补差-重量
//func (s orderWarehouseService) CheckShippedExistDebt(ctx context.Context, order model.Order) (model.OrderDebt, error) {
//	if order.HasDebtOrder {
//		return model.OrderDebt{}, nil
//	}
//
//	var hasDebt bool
//	for _, p := range order.ProductList {
//		if p.IsCheckWeight && p.SortWeight > p.TotalWeight {
//			// 重量检查-重量补差
//			hasDebt = true
//		}
//	}
//	if hasDebt {
//		//	补差
//		debt, err := s.orderDebtS.Create(ctx, order)
//		if err != nil {
//			return model.OrderDebt{}, err
//		}
//		return debt, err
//
//	}
//	return model.OrderDebt{}, nil
//}

// CheckShippedExistRefund 检查存在退款
func (s orderWarehouseService) CheckShippedExistRefund(ctx context.Context, order model.Order) (model.OrderRefund, error) {
	//if order.HasShipRefundOrder {
	//	return model.OrderRefund{}, nil
	//}
	//var hasRefund bool
	//for _, p := range order.ProductList {
	//
	//	if p.Num != p.SortNum {
	//		//	数量退款
	//		hasRefund = true
	//	}
	//	if p.IsCheckWeight && p.SortWeight < p.TotalWeight {
	//		// 重量检查-重量退款
	//		hasRefund = true
	//	}
	//}
	//if hasRefund {
	//	refund, err := s.orderRefundS.CreateShipRefund(ctx, order)
	//	if err != nil {
	//		return model.OrderRefund{}, err
	//	}
	//
	//	return refund, nil
	//}
	//
	//return model.OrderRefund{}, nil

	return model.OrderRefund{}, nil
}

func checkSort(up model.OrderStockUp, req types.OrderSortReq) error {
	if !up.QualityHas {
		return xerr.NewErr(xerr.ErrParamError, nil, "不能分拣，尚未完成品控")
	}

	for _, order := range up.OrderList {
		if order.HasShip {
			for _, sort := range req.OrderList {
				if order.OrderID.Hex() == sort.OrderID {
					if order.SortNum != sort.Num {
						return xerr.NewErr(xerr.ErrParamError, nil, "已发货订单不能更改分拣数据【数量】")
					}
					if up.IsCheckWeight {
						if order.SortWeight != sort.Weight {
							return xerr.NewErr(xerr.ErrParamError, nil, "已发货订单不能更改分拣数据【重量】")
						}
					}
				}
			}
		}
	}

	mOriginOrder := make(map[string]model.PerOrder)
	for _, order := range up.OrderList {
		mOriginOrder[order.OrderID.Hex()] = order
	}
	var count int
	var qualityCount int

	mCheck := make(map[string]int)
	for _, i := range req.OrderList {
		if _, ok := mCheck[i.OrderID]; ok {
			return xerr.NewErr(xerr.ErrParamError, nil, "分拣错误，订单重复")
		}
		mCheck[i.OrderID] = 0
		if v, ok := mOriginOrder[i.OrderID]; ok {
			qualityCount = v.QualityNum
			if i.Num > v.DueNum {
				zap.S().Errorf("分拣数量错误，应有数量：%d，请求数量：%d", v.DueNum, i.Num)
				return xerr.NewErr(xerr.ErrParamError, nil, "分拣错误，数量不能大于订单应有数量")
			}
			if up.IsCheckWeight {
				if i.Num != 0 && i.Weight == 0 {
					zap.S().Errorf("分拣重量不能为0")
					return xerr.NewErr(xerr.ErrParamError, nil, "分拣重量不能为0")
				}
				if i.Num == 0 && i.Weight != 0 {
					zap.S().Errorf("分拣数量等于0，重量不为0")
					return xerr.NewErr(xerr.ErrParamError, nil, "分拣数量等于0，重量只能为0")
				}
				//if i.Weight > v.DueWeight {
				//	zap.S().Errorf("分拣重量错误，应有重量%d，请求重量%d", v.DueWeight, i.Weight)
				//	return xerr.NewErr(xerr.ErrParamError, nil, "分拣重量错误，重量不能大于订单应有重量")
				//}
			}
		} else {
			reqBytes, _ := jsoniter.Marshal(req.OrderList)
			upBytes, _ := jsoniter.Marshal(up.OrderList)
			zap.S().Errorf("分拣订单存在归属错误%s，请求分拣订单列表%v，备货单订单列表%v", i.OrderID, string(reqBytes), string(upBytes))
			return xerr.NewErr(xerr.ErrParamError, nil, "分拣存在订单归属错误")
		}
		count += i.Num
	}

	if count != qualityCount {
		return xerr.NewErr(xerr.ErrParamError, nil, "分拣总数不等于品控总数")
	}
	return nil
}

func checkQuality(up model.OrderStockUp, req types.QualityReq) error {
	var qNum int
	for _, i := range req.QualityServicePointList {
		qNum += i.QualityNum
	}

	if qNum > up.StockUpHasNum {
		return xerr.NewErr(xerr.ErrParamError, nil, "品控数不可大于已备货数")
	}
	mPoint := make(map[string]int)
	for _, v := range req.QualityServicePointList {
		mPoint[v.ServicePointID] = v.QualityNum
	}

	for _, v := range up.QualityServicePointList {
		if n, ok := mPoint[v.ServicePointID.Hex()]; ok {
			if v.QualityDueNum < n {
				zap.S().Errorf("品控入库数大于应入库数,应入库数：%v，品控数%v，备货单信息%v，请求参数%v", v.QualityDueNum, n, up, req)
				return xerr.NewErr(xerr.ErrParamError, nil, "品控入库数大于应入库数")
			}
			continue
		}
		zap.S().Errorf("品控入库的服务点存在缺失,请求参数%v，备货单参数%v", req, up)
		return xerr.NewErr(xerr.ErrParamError, nil, "品控入库的服务点存在缺失")

	}

	return nil
}

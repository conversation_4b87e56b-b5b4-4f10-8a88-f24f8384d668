package productHistoryService

import (
	"base/dao"
	"base/dao/productHistoryDao"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

// ServiceInterface 商品首页记录
type ServiceInterface interface {
	NotifyDownSale(ctx context.Context, historyType int, objectID, productID primitive.ObjectID) error
	ListUpSale(ctx context.Context, historyType int, productID primitive.ObjectID) ([]model.ProductHistory, error)
}

type productHistoryService struct {
	productHistoryDao productHistoryDao.DaoInt
}

func NewProductHistoryService() ServiceInterface {
	return productHistoryService{
		productHistoryDao: dao.ProductHistoryDao,
	}
}

func (s productHistoryService) NotifyDownSale(ctx context.Context, historyType int, objectID, productID primitive.ObjectID) error {
	data := model.ProductHistory{
		ID:          primitive.NewObjectID(),
		HistoryType: historyType,
		ObjectID:    objectID,
		ProductID:   productID,
		CreatedAt:   time.Now().UnixMilli(),
	}
	err := s.productHistoryDao.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

// ProductHistory 商品记录表
type ProductHistory struct {
	ID          primitive.ObjectID `bson:"_id" json:"id"`
	HistoryType int                `bson:"history_type" json:"history_type"` // 记录类型  1 快捷栏 2主题 3.专区
	ProductID   primitive.ObjectID `bson:"product_id" json:"product_id"`
	CreatedAt   int64              `bson:"created_at" json:"created_at"`
}

func (s productHistoryService) ListUpSale(ctx context.Context, historyType int, productID primitive.ObjectID) ([]model.ProductHistory, error) {
	filter := bson.M{
		"history_type": historyType,
		"product_id":   productID,
	}
	list, err := s.productHistoryDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	err = s.productHistoryDao.DeleteMany(ctx, filter)
	if err != nil {
		zap.S().Errorf("删除商品记录错误，忽略，错误%v", err)
		return nil, err
	}

	return list, nil
}

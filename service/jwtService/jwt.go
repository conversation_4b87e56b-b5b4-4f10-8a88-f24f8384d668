package jwtService

import (
	"base/core/config"
	"base/core/xerr"
	"base/global"
	"context"
	"github.com/go-redis/redis/v8"
	"github.com/golang-jwt/jwt/v4"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

var cache = "refresh-token:"
var cacheAccess = "user-access-token:"

var cacheBuyerRefresh = "refresh-token-buyer:"
var cacheBuyerAccess = "user-access-token-buyer:"

var cacheAdminRefresh = "refresh-token-admin:"
var cacheAdminAccess = "user-access-token-admin:"

type JwtService struct {
	rdb          *redis.Client
	AccessSecret string `json:"access_secret"`
	AccessExpire int    `json:"access_expire"`
}

func NewJwtService() JwtService {
	c := config.Conf.Auth
	return JwtService{
		rdb:          global.RDBDefault,
		AccessSecret: c.AccessSecret,
		AccessExpire: c.AccessExpire,
	}
}

type MyClaims struct {
	ID string `json:"id"`
	jwt.RegisteredClaims
}

func (s JwtService) RefreshToken(ctx context.Context, rt string) (tokenString string, refreshToken string, expireAt int64, err error) {
	val := s.rdb.Exists(ctx, cache+rt).Val()
	if val > 0 {
		userID := s.rdb.Get(ctx, cache+rt).Val()
		expire := time.Now().Add(time.Duration(s.AccessExpire) * time.Minute)
		my := MyClaims{
			userID,
			jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(expire),
				Issuer:    "server",
			},
		}
		newWithClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, my)

		signedString, err := newWithClaims.SignedString([]byte(s.AccessSecret))
		if err != nil {
			return "", "", 0, err
		}

		s2 := uuid.New().String()

		s.rdb.Set(context.Background(), cache+s2, userID, time.Minute*time.Duration(s.AccessExpire+60))
		s.rdb.Set(context.Background(), cacheAccess+userID, signedString, time.Minute*time.Duration(s.AccessExpire))

		return signedString, s2, expire.UnixMilli(), err
	}
	//不存在
	return "", "", 0, xerr.NewErr(xerr.ErrLoginExpire, nil)
}

func (s JwtService) MakeToken(userID string) (tokenString string, refreshToken string, expireAt int64, err error) {
	expire := time.Now().Add(time.Duration(s.AccessExpire) * time.Minute)
	my := MyClaims{
		userID,
		jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expire),
			Issuer:    "server",
		},
	}
	newWithClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, my)

	signedString, err := newWithClaims.SignedString([]byte(s.AccessSecret))
	if err != nil {
		return "", "", 0, err
	}

	s2 := uuid.New().String()

	s.rdb.Set(context.Background(), cache+s2, userID, time.Minute*time.Duration(s.AccessExpire+60))
	s.rdb.Set(context.Background(), cacheAccess+userID, signedString, time.Minute*time.Duration(s.AccessExpire))

	return signedString, s2, expire.UnixMilli(), err
}

func (s JwtService) MakeTokenForBuyer(buyerID primitive.ObjectID) (tokenString string, refreshToken string, expireAt int64, err error) {
	expire := time.Now().Add(time.Duration(s.AccessExpire) * time.Minute)
	my := MyClaims{
		buyerID.Hex(),
		jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expire),
			Issuer:    "server",
		},
	}
	newWithClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, my)

	signedString, err := newWithClaims.SignedString([]byte(s.AccessSecret))
	if err != nil {
		return "", "", 0, err
	}

	s2 := uuid.New().String()

	s.rdb.Set(context.Background(), cacheAdminRefresh+s2, buyerID.Hex(), time.Minute*time.Duration(s.AccessExpire+60))
	s.rdb.Set(context.Background(), cacheAdminAccess+buyerID.Hex(), signedString, time.Minute*time.Duration(s.AccessExpire))

	return signedString, s2, expire.UnixMilli(), err
}
func (s JwtService) MakeTokenForAdmin(adminID primitive.ObjectID) (tokenString string, refreshToken string, expireAt int64, err error) {
	expire := time.Now().Add(time.Duration(s.AccessExpire) * time.Minute)
	my := MyClaims{
		adminID.Hex(),
		jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expire),
			Issuer:    "server",
		},
	}
	newWithClaims := jwt.NewWithClaims(jwt.SigningMethodHS256, my)

	signedString, err := newWithClaims.SignedString([]byte(s.AccessSecret))
	if err != nil {
		return "", "", 0, err
	}

	s2 := uuid.New().String()

	s.rdb.Set(context.Background(), cacheBuyerRefresh+s2, adminID.Hex(), time.Minute*time.Duration(s.AccessExpire+60))
	s.rdb.Set(context.Background(), cacheBuyerAccess+adminID.Hex(), signedString, time.Minute*time.Duration(s.AccessExpire))

	return signedString, s2, expire.UnixMilli(), err
}

func (s JwtService) ParseToken(j string) (*MyClaims, error) {

	token, err := jwt.ParseWithClaims(j, &MyClaims{}, func(token *jwt.Token) (interface{}, error) {
		return []byte(s.AccessSecret), nil
	})
	if err != nil {
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorMalformed != 0 {
				zap.S().Warnf("授权信息错误%s", j)
				return nil, xerr.NewErr(xerr.ErrLoginExpire, nil, "授权信息错误，请重新登录")
			}
			if ve.Errors&jwt.ValidationErrorExpired != 0 {
				zap.S().Warnf("授权信息已过期%s", j)
				return nil, xerr.NewErr(xerr.ErrLoginExpire, nil, "授权信息已过期，请重新登录")
			}
			if ve.Errors&jwt.ValidationErrorNotValidYet != 0 {
				zap.S().Warnf("token未生效%s", j)
				return nil, xerr.NewErr(xerr.ErrLoginExpire, nil, "token未生效，请重新登录")
			}
		}
		return nil, xerr.NewErr(xerr.ErrLoginExpire, nil)
	}

	if claims, ok := token.Claims.(*MyClaims); ok && token.Valid {
		return claims, nil
	} else {
		return nil, err
	}
}

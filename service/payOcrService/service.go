package payOcrService

import (
	"base/core/xerr"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/allInPayUserService"
	"base/service/authenticationService"
	"base/service/ossService"
	"base/util"
	"context"
	"fmt"
	pays "github.com/cnbattle/allinpay/service"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// PayOcrService 影印件采集
type PayOcrService interface {
	ToPayOcr(ctx context.Context, ObjectID primitive.ObjectID, objectType model.ObjectType, picType pays.PicType) error
}

type payOcrService struct {
	authenticationS authenticationService.ServiceInterface

	ossS          ossService.OssService
	AllInPayS     payModule.MemberService
	AllInPayUserS allInPayUserService.ServiceInterface
}

func (s payOcrService) ToPayOcr(ctx context.Context, ObjectID primitive.ObjectID, objectType model.ObjectType, picType pays.PicType) error {
	authentication, err := s.authenticationS.GetByObject(ctx, ObjectID, objectType)
	if err != nil {
		return err
	}
	var b64 string

	if picType == pays.PicTypeBusinessLicense {
		b, err := s.ossS.DownLoad(authentication.Company.BusinessLicenseImg.Name)
		if err != nil {
			return err
		}
		b64 = util.ToBase64(b)
	}

	if picType == pays.PicTypeIDCardFront {
		b, err := s.ossS.DownLoad(authentication.Company.Legal.IdCardFrontImg.Name)
		if err != nil {
			return err
		}
		b64 = util.ToBase64(b)
	}

	if picType == pays.PicTypeIDCardBack {
		b, err := s.ossS.DownLoad(authentication.Company.Legal.IdCardBackImg.Name)
		if err != nil {
			return err
		}
		b64 = util.ToBase64(b)
	}

	req := pays.IdcardCollectReq{
		BizUserId:                  authentication.PayBizUserId,
		OcrComparisonResultBackUrl: global.BackHost + global.BackUrl,
		PicType:                    picType, // 1-营业执照（必传）
		Picture:                    b64,
	}
	res, err := s.AllInPayS.IdcardCollectS(req)
	if err != nil {
		zap.S().Errorf("pay-ocr错误%v,%v", picType, err)
		return err
	}
	if res.Result == "1" {
		//提交成功
		err = s.authenticationS.UpdateHasSubmitPayOcr(ctx, authentication.ID, picType)
		if err != nil {
			return err
		}
		return nil
	}
	return xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("识别返回失败,result:%v", res.Result))
}

func NewPayOcrService() PayOcrService {
	return payOcrService{
		authenticationS: authenticationService.NewAuthenticationService(),
		ossS:            ossService.NewOssService(),

		AllInPayS:     payModule.NewMember(),
		AllInPayUserS: allInPayUserService.NewAllInPayUserService(),
	}
}

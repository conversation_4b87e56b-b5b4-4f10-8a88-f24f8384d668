package buyerGroupService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/buyerGroupDao"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type ServiceInterface interface {
	Upsert(ctx context.Context, id primitive.ObjectID, title string, img model.FileInfo, conditionType model.ConditionType) error
	Get(ctx context.Context, id primitive.ObjectID) (model.BuyerGroup, error)
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M) ([]model.BuyerGroup, error)
}

type buyerGroupService struct {
	buyerGroupDao buyerGroupDao.DaoInt
}

func NewBuyerGroupService() ServiceInterface {
	return buyerGroupService{
		buyerGroupDao: dao.BuyerGroupDao,
	}
}

func (s buyerGroupService) Upsert(ctx context.Context, id primitive.ObjectID, title string, img model.FileInfo, conditionType model.ConditionType) error {
	now := time.Now().UnixMilli()

	if id != primitive.NilObjectID {
		// 更新
		info, err := s.Get(ctx, id)
		if err != nil {
			return err
		}
		info.Title = title
		info.Img = img
		info.ConditionType = conditionType
		info.UpdatedAt = now
		err = s.buyerGroupDao.UpdateOne(ctx, bson.M{"_id": id}, bson.M{
			"$set": info,
		})
		if err != nil {
			return err
		}
		return nil
	}
	// 新增
	// 判断群数量
	count, err := s.buyerGroupDao.Count(ctx, bson.M{})
	if err != nil {
		return err
	}
	if count >= 5 {
		return xerr.NewErr(xerr.ErrParamError, nil, "创建失败，交流群已达到5个")
	}

	data := model.BuyerGroup{
		ID:            primitive.NewObjectID(),
		Title:         title,
		ConditionType: conditionType,
		Img:           img,
		CreatedAt:     now,
		UpdatedAt:     now,
	}
	err = s.buyerGroupDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s buyerGroupService) Get(ctx context.Context, id primitive.ObjectID) (model.BuyerGroup, error) {
	data, err := s.buyerGroupDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.BuyerGroup{}, err
	}
	return data, nil
}

func (s buyerGroupService) List(ctx context.Context, filter bson.M) ([]model.BuyerGroup, error) {
	list, err := s.buyerGroupDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s buyerGroupService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	err := s.buyerGroupDao.Delete(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

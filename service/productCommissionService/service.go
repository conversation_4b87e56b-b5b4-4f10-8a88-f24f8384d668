package productCommissionService

import (
	"base/dao"
	"base/dao/productCommissionDao"
	"base/global"
	"base/model"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ServiceInterface 产品佣金
type ServiceInterface interface {
	Upsert(ctx context.Context, data model.ProductCommission) error
	GetByProduct(ctx context.Context, productID primitive.ObjectID) (model.ProductCommission, error)
	List(filter bson.M) ([]model.ProductCommission, error)
	ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.ProductCommission, error)
}

type productCommissionService struct {
	rdb                  *redis.Client
	productCommissionDao productCommissionDao.DaoInt
}

func (s productCommissionService) ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.ProductCommission, error) {
	if len(ids) < 1 {
		return nil, nil
	}
	commissions, err := s.productCommissionDao.Find(ctx, bson.M{"product_id": bson.M{"$in": ids}})
	if err != nil {
		return nil, err
	}
	return commissions, nil
}

func (s productCommissionService) Upsert(ctx context.Context, data model.ProductCommission) error {
	err := s.productCommissionDao.Upsert(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s productCommissionService) GetByProduct(ctx context.Context, productID primitive.ObjectID) (model.ProductCommission, error) {
	filter := bson.M{
		"product_id": productID,
	}
	partnerCommission, err := s.productCommissionDao.Get(context.Background(), filter)
	if err != nil {
		return model.ProductCommission{}, err
	}
	return partnerCommission, nil
}

func (s productCommissionService) List(filter bson.M) ([]model.ProductCommission, error) {
	list, err := s.productCommissionDao.Find(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func NewProductCommissionService() ServiceInterface {
	return productCommissionService{
		rdb:                  global.RDBDefault,
		productCommissionDao: dao.ProductCommissionDao,
	}
}

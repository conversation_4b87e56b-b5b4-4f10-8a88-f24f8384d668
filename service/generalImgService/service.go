package generalImgService

import (
	"base/global"
	"context"

	"github.com/go-redis/redis/v8"
)

var productCommonImgKey = "product_common_img"

// ServiceInterface 通用图片服务接口
type ServiceInterface interface {
	UpdateProductCommonImg(ctx context.Context, path string) error
	GetProductCommonImg(ctx context.Context) (string, error)
}

// generalImgService 通用图片服务
type generalImgService struct {
	rdb *redis.Client
}

// NewGeneralImgService 创建通用图片服务
func NewGeneralImgService() ServiceInterface {
	return generalImgService{
		rdb: global.RDBDefault,
	}
}

// UpdateProductCommonImg 更新商品公共图片
func (s generalImgService) UpdateProductCommonImg(ctx context.Context, path string) error {
	// 直接更新缓存
	s.rdb.Set(ctx, "product_common_img", path, 0)
	return nil
}

// GetProductCommonImg 获取商品公共图片
func (s generalImgService) GetProductCommonImg(ctx context.Context) (string, error) {
	path, err := s.rdb.Get(ctx, "product_common_img").Result()
	if err != nil && err != redis.Nil {
		return "", err
	}
	return path, nil
}

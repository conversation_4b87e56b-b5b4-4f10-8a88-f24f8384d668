package generalImgService

import (
	"base/model"
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// 缓存
var cache = "generalImg:"

func get(r *redis.Client, imgType string) model.GeneralImg {
	key := cache + imgType
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := r.Get(ctx, key).Bytes()
		if err != nil {
			zap.S().Error("get err")
			return model.GeneralImg{}
		}
		var i model.GeneralImg
		err = json.Unmarshal(bytes, &i)
		if err != nil {
			zap.S().Error("unmarshal,", err)
			return model.GeneralImg{}
		}
		return i
	}
	return model.GeneralImg{}
}

func set(r *redis.Client, info model.GeneralImg) {
	key := cache + info.Type

	bytes, err := json.Marshal(info)
	if err != nil {
		zap.S().Error("set marshal,", err)
		return
	}
	r.Set(context.Background(), key, bytes, 0)
}

func del(r *redis.Client, imgType string) {
	r.Del(context.Background(), cache+imgType)
}

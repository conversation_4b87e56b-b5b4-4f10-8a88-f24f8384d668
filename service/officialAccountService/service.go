package officialAccountService

import (
	"base/core/config"
	"base/core/xerr"
	"base/global"
	"base/model"
	"base/util"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"io"
	"net/http"
	"net/url"
	"time"
)

var accessTokenCache = "official-account-access-token"

type ServiceInterface interface {
	GetAccessToken() (accessToken string, err error)
	SendMessage(ctx context.Context, openID string) error
	ListUser(ctx context.Context, nextOpenID string) ([]string, error)
	GetUser(ctx context.Context, openID string) (model.OfficialAccountUser, error)
	Online(ctx context.Context) error
}

type officialAccountService struct {
	AppID  string
	Secret string
	rdb    *redis.Client
}

func NewOfficialAccountService() ServiceInterface {
	c := config.Conf.Official
	return officialAccountService{
		AppID:  c.AppID,
		Secret: c.AppSecret,
		rdb:    global.RDBDefault,
	}
}

func (s officialAccountService) GetAccessToken() (accessToken string, err error) {
	key := accessTokenCache
	val := s.rdb.Exists(context.Background(), key).Val()
	if val > 0 {
		v := s.rdb.Get(context.Background(), key).Val()
		if v != "" {
			return v, nil
		}
	}
	tokenRemote, _, err := s.getAccessTokenRemote()
	if err != nil {
		return "", err
	}
	if tokenRemote != "" {
		result, err := s.rdb.Set(context.Background(), key, tokenRemote, time.Second*7000).Result()
		if err != nil {
			return "", err
		}
		_ = result
	}

	return tokenRemote, nil
}

func (s officialAccountService) getAccessTokenRemote() (accessToken string, expiresIn int, err error) {
	parse, err := url.Parse("https://api.weixin.qq.com/cgi-bin/token")

	values := url.Values{}
	values.Add("grant_type", "client_credential")
	values.Add("appid", s.AppID)
	values.Add("secret", s.Secret)

	parse.RawQuery = values.Encode()

	resp, err := http.Get(parse.String())
	if err != nil {
		return "", 0, err
	}
	var info AccessTokenInfo
	body, _ := io.ReadAll(resp.Body)
	err = json.Unmarshal(body, &info)
	if err != nil {
		return "", 0, err
	}
	return info.AccessToken, info.ExpiresIn, nil
}

// SendMessage 模板消息
func (s officialAccountService) SendMessage(ctx context.Context, openID string) error {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return err
	}

	urlStr := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s", accessToken)
	b := map[string]interface{}{
		"touser":      openID,
		"template_id": "4NL-dJ_Te3YSeT0iG-6TqFuHcTw_1uj-TgQST4FYEmE",
		//"url":         "",
		//"miniprogram": map[string]interface{}{
		//	"appid":    r.MiniAppID,
		//	"pagepath": r.PagePath,
		//},
		//"client_msg_id":"MSG_000001",
		"data": map[string]interface{}{
			"thing16": map[string]string{
				"value": "张三",
			},
			"thing5": map[string]string{
				"value": "这是留言信息",
			},
		},
	}

	var res SendResp

	marshal, _ := json.Marshal(b)
	zap.S().Infof("统一下发消息：%s", string(marshal))

	resp, err := util.NewResty().Post(urlStr, b, &res)
	if err != nil {
		return err
	}
	_ = resp
	bytes, _ := json.Marshal(resp)
	zap.S().Infof("resp %s", string(bytes))
	if res.Errcode != 0 {
		zap.S().Errorf("res.Errmsg , %s", res.Errmsg)
		return xerr.NewErr(xerr.ErrParamError, nil, res.Errmsg)
	}
	return nil
}

func (s officialAccountService) Online(ctx context.Context) error {
	appkey := "BC-452e2c6866424a9294ce0c305bc4c53c"
	urlStr := fmt.Sprintf("http://rest-hz.goeasy.io/v2/im/here_now?appkey=%s&userIds=platform476d5c69fb11", appkey)
	var res SendResp

	resp, err := util.NewResty().Get(urlStr, nil, &res)
	if err != nil {
		return err
	}
	_ = resp
	bytes, _ := json.Marshal(resp)
	zap.S().Infof("resp %s", string(bytes))
	if res.Errcode != 0 {
		zap.S().Errorf("res.Errmsg , %s", res.Errmsg)
		return xerr.NewErr(xerr.ErrParamError, nil, res.Errmsg)
	}
	return nil
}

// ListUser 查询用户
func (s officialAccountService) ListUser(ctx context.Context, nextOpenID string) ([]string, error) {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return nil, err
	}

	urlStr := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/user/get?access_token=%s&next_openid=%s", accessToken, nextOpenID)

	var res ListRes

	resp, err := util.NewResty().Get(urlStr, nil, &res)
	if err != nil {
		return nil, err
	}
	_ = resp

	return res.Data.OpenID, nil
}

// GetUser 查询用户
func (s officialAccountService) GetUser(ctx context.Context, openID string) (model.OfficialAccountUser, error) {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return model.OfficialAccountUser{}, err
	}

	urlStr := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/user/info?access_token=%s&openid=%s", accessToken, openID)

	var res GetUserRes

	resp, err := util.NewResty().Get(urlStr, nil, &res)
	if err != nil {
		return model.OfficialAccountUser{}, err
	}
	_ = resp

	data := model.OfficialAccountUser{
		OpenID:        res.OpenID,
		UnionID:       res.UnionID,
		Subscribe:     res.Subscribe,
		SubscribeTime: res.SubscribeTime,
		Remark:        res.Remark,
	}

	return data, nil
}

type GetUserRes struct {
	ID            primitive.ObjectID `json:"id"`
	OpenID        string             `json:"openid"`
	UnionID       string             `json:"unionid"`
	Subscribe     int                `json:"subscribe"`
	SubscribeTime int64              `json:"subscribe_time"`
	Remark        string             `json:"remark"`
}

type SendResp struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
}

type ListRes struct {
	Total int `json:"total"`
	Count int `json:"count"`
	Data  struct {
		OpenID     []string `json:"openid"`
		NextOpenID string   `json:"next_openid"`
	} `json:"data"`
}

type AccessTokenInfo struct {
	AccessToken string `json:"access_token"` // 凭证
	ExpiresIn   int    `json:"expires_in"`   // 凭证有效时间，单位：秒。目前是7200秒之内的值
}

package entityService

import (
	"base/dao"
	"base/dao/entityDao"
	"base/global"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
)

// ServiceInterface 主体
type ServiceInterface interface {
	//Upsert(ctx context.Context, req types.EntityInfo, originData model.Entity, objectID primitive.ObjectID, objectType model.ObjectType) (primitive.ObjectID, error)
	//GetBySupplier(supplierID primitive.ObjectID) (model.Entity, error)
	//GetByServicePoint(servicePointID primitive.ObjectID) (model.Entity, error)
	//GetByWarehouse(warehouseID primitive.ObjectID) (model.Entity, error)
	//
	//CheckBySupplier(supplierID primitive.ObjectID) (bool, error)
	//CheckByServicePoint(servicePointID primitive.ObjectID) (bool, error)
	//CheckByWarehouse(warehouseID primitive.ObjectID) (bool, error)
}

type entityService struct {
	rdb       *redis.Client
	entityDao entityDao.DaoInt
}

//
//func (s entityService) CheckBySupplier(supplierID primitive.ObjectID) (bool, error) {
//	i, err := s.GetBySupplier(supplierID)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		return false, err
//	}
//	if i.ID == primitive.NilObjectID {
//		return false, nil
//	}
//	return true, nil
//}
//
//func (s entityService) CheckByServicePoint(servicePointID primitive.ObjectID) (bool, error) {
//	i, err := s.GetByServicePoint(servicePointID)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		return false, err
//	}
//	if i.ID == primitive.NilObjectID {
//		return false, nil
//	}
//	return true, nil
//}
//
//func (s entityService) CheckByWarehouse(warehouseID primitive.ObjectID) (bool, error) {
//	i, err := s.GetByWarehouse(warehouseID)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		return false, err
//	}
//	if i.ID == primitive.NilObjectID {
//		return false, nil
//	}
//	return true, nil
//}
//
//func (s entityService) Upsert(ctx context.Context, req types.EntityInfo, originData model.Entity, objectID primitive.ObjectID, objectType model.ObjectType) (primitive.ObjectID, error) {
//	now := time.Now().UnixMilli()
//
//	l := req.Legal
//	co := model.Legal{
//		LegalName:      l.LegalName,
//		IdentityType:   1,
//		LegalIds:       l.LegalIds,
//		LegalPhone:     l.LegalPhone,
//		IDCard:         l.IDCard,
//		IdCardFrontImg: l.IdCardFrontImg,
//		IdCardBackImg:  l.IdCardBackImg,
//	}
//	data := model.Entity{
//		ID:         primitive.NewObjectID(),
//		ObjectType: objectType,
//		ObjectID:   objectID,
//		EntityType: req.EntityType,
//		EntityName: req.EntityName,
//		CreditCode: req.CreditCode,
//		//BusinessLicenseValidTo:       req.BusinessLicenseValidTo,
//		BusinessLicenseImg: req.BusinessLicenseImg,
//		BusinessLicense:    req.BusinessLicense,
//		//BusinessLicenseElectronicImg: req.BusinessLicenseElectronicImg,
//		Legal:     co,
//		CreatedAt: now,
//	}
//
//	if originData.ID != primitive.NilObjectID {
//		data.ID = originData.ID
//		data.UpdatedAt = now
//		data.CreatedAt = originData.CreatedAt
//	}
//
//	err := s.entityDao.Upsert(ctx, data)
//	if err != nil {
//		return primitive.NilObjectID, err
//	}
//	return data.ID, nil
//}
//
//func (s entityService) GetBySupplier(supplierID primitive.ObjectID) (model.Entity, error) {
//	object, err := s.entityDao.GetByObject(int(model.ObjectTypeSupplier), supplierID)
//	if err != nil {
//		return model.Entity{}, err
//	}
//	return object, nil
//}
//
//func (s entityService) GetByServicePoint(servicePointID primitive.ObjectID) (model.Entity, error) {
//	object, err := s.entityDao.GetByObject(int(model.ObjectTypeServicePoint), servicePointID)
//	if err != nil {
//		return model.Entity{}, err
//	}
//	return object, nil
//}
//
//func (s entityService) GetByWarehouse(warehouseID primitive.ObjectID) (model.Entity, error) {
//	object, err := s.entityDao.GetByObject(int(model.ObjectTypeWarehouse), warehouseID)
//	if err != nil {
//		return model.Entity{}, err
//	}
//	return object, nil
//}

func NewEntityService() ServiceInterface {
	return entityService{
		rdb:       global.RDBDefault,
		entityDao: dao.EntityDao,
	}
}

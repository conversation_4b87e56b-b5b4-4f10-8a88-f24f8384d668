package orderStatusRecordService

import (
	"base/dao"
	"base/dao/orderDao"
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

// ServiceInterface 订单状态记录
type ServiceInterface interface {
	UpdateStatusRecord(ctx context.Context, orderIDs []primitive.ObjectID, status model.OrderStatusType) error
}

type orderStatusRecordService struct {
	mdb      *mongo.Database
	rdb      *redis.Client
	l        *zap.SugaredLogger
	orderDao orderDao.DaoInt
}

func NewOrderStatusRecordService() ServiceInterface {
	return orderStatusRecordService{
		mdb:      global.MDB,
		rdb:      global.RDBDefault,
		l:        global.OrderLogger.Sugar(),
		orderDao: dao.OrderDao,
	}
}

func (s orderStatusRecordService) UpdateStatusRecord(ctx context.Context, orderIDs []primitive.ObjectID, status model.OrderStatusType) error {
	filter := bson.M{
		"_id": bson.M{
			"$in": orderIDs,
		},
	}
	now := time.Now().UnixMilli()
	key := model.BackRecordKey(status)
	if key == "" {
		s.l.Errorf("更新订单状态记录错误，获取不到key，请求状态%v", status)
		return nil
	}
	update := bson.M{
		"$set": bson.M{
			"order_status_record." + key: now,
		},
	}
	err := s.orderDao.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

package allInPayUserService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/allInPayUserDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/util"
	"context"
	"errors"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

// ServiceInterface 支付用户信息
type ServiceInterface interface {
	Create(ctx context.Context, memberType pays.MemberType, userID, objectID primitive.ObjectID, objectType model.ObjectType) (model.AllInPayUser, error)

	UpdateMobile(ctx context.Context, id primitive.ObjectID, mobile string) error

	GetByObjectType(ctx context.Context, id primitive.ObjectID, objectType model.ObjectType) (model.AllInPayUser, error)
	GetByUserIDAndObjectType(ctx context.Context, userID primitive.ObjectID, objectType model.ObjectType) (model.AllInPayUser, error)

	GetByBuyer(id primitive.ObjectID) (model.AllInPayUser, error)
	GetBySupplier(id primitive.ObjectID) (model.AllInPayUser, error)
	GetByServicePoint(id primitive.ObjectID) (model.AllInPayUser, error)
	GetByWarehouse(id primitive.ObjectID) (model.AllInPayUser, error)

	CheckByBuyer(id primitive.ObjectID) (bool, error)
	CheckBySupplier(id primitive.ObjectID) (bool, error)
	CheckByServicePoint(id primitive.ObjectID) (bool, error)
	CheckByWarehouse(id primitive.ObjectID) (bool, error)
}

type allInPayUserService struct {
	rdb             *redis.Client
	allInPayUserDao allInPayUserDao.DaoInt
	// 支付 会员
	AllInPayS payModule.MemberService
}

func (s allInPayUserService) Create(ctx context.Context, memberType pays.MemberType, userID, objectID primitive.ObjectID, objectType model.ObjectType) (model.AllInPayUser, error) {
	now := time.Now().UnixMilli()

	// 创建会员
	createMember, err := s.AllInPayS.CreateMemberS(pays.CreateMemberReq{
		BizUserId:  util.NewUUID(),
		MemberType: memberType,
		Source:     pays.SourceMobile,
		ExtendParam: map[string]interface{}{
			"object_id":   objectID.Hex(),
			"object_type": objectType,
		}})
	if err != nil {
		return model.AllInPayUser{}, err
	}

	data := model.AllInPayUser{
		ID:           primitive.NewObjectID(),
		UserID:       userID,
		ObjectType:   objectType,
		ObjectID:     objectID,
		Mobile:       "",
		MemberType:   memberType,
		PayUserID:    createMember.UserID,
		PayBizUserId: createMember.BizUserId,
		CreatedAt:    now,
	}

	err = s.allInPayUserDao.Create(ctx, data)
	if err != nil {
		return model.AllInPayUser{}, err
	}
	return data, nil
}

func (s allInPayUserService) UpdateMobile(ctx context.Context, id primitive.ObjectID, mobile string) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"$set": bson.M{
			"mobile":     mobile,
			"updated_at": time.Now().UnixMilli(),
		},
	}
	err := s.allInPayUserDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}
func (s allInPayUserService) CheckByBuyer(id primitive.ObjectID) (bool, error) {
	i, err := s.GetByBuyer(id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	if i.ID == primitive.NilObjectID {
		return false, nil
	}
	return true, nil
}

func (s allInPayUserService) CheckBySupplier(id primitive.ObjectID) (bool, error) {
	i, err := s.GetBySupplier(id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	if i.ID == primitive.NilObjectID {
		return false, nil
	}
	return true, nil
}

func (s allInPayUserService) CheckByWarehouse(id primitive.ObjectID) (bool, error) {
	i, err := s.GetByWarehouse(id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	if i.ID == primitive.NilObjectID {
		return false, nil
	}
	return true, nil
}

func (s allInPayUserService) CheckByServicePoint(id primitive.ObjectID) (bool, error) {
	i, err := s.GetByServicePoint(id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	if i.ID == primitive.NilObjectID {
		return false, nil
	}
	return true, nil
}

func (s allInPayUserService) GetByBuyer(id primitive.ObjectID) (model.AllInPayUser, error) {
	res, err := s.allInPayUserDao.GetByObject(int(model.ObjectTypeBuyer), id)
	if err != nil {
		return model.AllInPayUser{}, err
	}
	return res, nil
}
func (s allInPayUserService) GetByUserIDAndObjectType(ctx context.Context, userID primitive.ObjectID, objectType model.ObjectType) (model.AllInPayUser, error) {
	filter := bson.M{
		"user_id":     userID,
		"object_type": objectType,
	}
	get, err := s.allInPayUserDao.Get(ctx, filter)
	if err != nil {
		return model.AllInPayUser{}, err
	}
	return get, nil
}
func (s allInPayUserService) GetByObjectType(ctx context.Context, id primitive.ObjectID, objectType model.ObjectType) (model.AllInPayUser, error) {
	switch objectType {
	case model.ObjectTypeBuyer:
		res, err := s.GetByBuyer(id)
		return res, err
	case model.ObjectTypeSupplier:
		res, err := s.GetBySupplier(id)
		return res, err
	case model.ObjectTypeServicePoint:
		res, err := s.GetByServicePoint(id)
		return res, err
	case model.ObjectTypeWarehouse:
		res, err := s.GetByWarehouse(id)
		return res, err
	default:
		return model.AllInPayUser{}, xerr.NewErr(xerr.ErrParamError, nil, "对象类型错误")

	}

}

func (s allInPayUserService) GetBySupplier(id primitive.ObjectID) (model.AllInPayUser, error) {
	res, err := s.allInPayUserDao.GetByObject(int(model.ObjectTypeSupplier), id)
	if err != nil {
		return model.AllInPayUser{}, err
	}
	return res, nil
}

func (s allInPayUserService) GetByServicePoint(id primitive.ObjectID) (model.AllInPayUser, error) {
	res, err := s.allInPayUserDao.GetByObject(int(model.ObjectTypeServicePoint), id)
	if err != nil {
		return model.AllInPayUser{}, err
	}
	return res, nil
}
func (s allInPayUserService) GetByWarehouse(id primitive.ObjectID) (model.AllInPayUser, error) {
	res, err := s.allInPayUserDao.GetByObject(int(model.ObjectTypeWarehouse), id)
	if err != nil {
		return model.AllInPayUser{}, err
	}
	return res, nil
}

func NewAllInPayUserService() ServiceInterface {
	return allInPayUserService{
		rdb:             global.RDBDefault,
		allInPayUserDao: dao.AllInPayUserDao,
		AllInPayS:       payModule.NewMember(),
	}
}

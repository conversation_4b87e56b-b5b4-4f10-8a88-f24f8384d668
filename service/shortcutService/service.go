package shortcutService

import (
	"base/dao"
	"base/dao/shortcutDao"
	"base/model"
	"base/service/productHistoryService"
	"base/types"
	"base/util"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, req types.ShortcutCreate) (primitive.ObjectID, error)
	Init(ctx context.Context, data model.Shortcut) error
	Update(ctx context.Context, req types.ShortcutUpdate) error
	UpdateProduct(ctx context.Context, id primitive.ObjectID, pIDs []primitive.ObjectID) error
	DownProduct(ctx context.Context, productID primitive.ObjectID, isDel bool) error
	UpProduct(ctx context.Context, productID primitive.ObjectID) error
	UpdateSort(ctx context.Context, req types.SwipeUpdateSort) error
	List(ctx context.Context, page, limit int64) ([]model.Shortcut, int64, error)
	ListAll(ctx context.Context, visible bool) ([]model.Shortcut, error)
	ListAllWithPoint(ctx context.Context, visible bool, pointID primitive.ObjectID) ([]model.Shortcut, error)
	ListVisible(ctx context.Context, pointID primitive.ObjectID) ([]model.Shortcut, error)
	Del(ctx context.Context, id primitive.ObjectID) error
	Get(ctx context.Context, id primitive.ObjectID) (model.Shortcut, error)
}

type shortcutService struct {
	db              shortcutDao.DaoInt
	productHistoryS productHistoryService.ServiceInterface
}

func NewShortcutService() ServiceInterface {
	return shortcutService{
		db:              dao.ShortcutDao,
		productHistoryS: productHistoryService.NewProductHistoryService(),
	}
}

func (s shortcutService) Get(ctx context.Context, id primitive.ObjectID) (model.Shortcut, error) {
	get, err := s.db.Get(ctx, bson.M{"_id": id})
	return get, err
}

func (s shortcutService) DownProduct(ctx context.Context, productID primitive.ObjectID, isDel bool) error {
	filter := bson.M{
		"product_list": bson.M{
			"$in": bson.A{productID},
		},
	}

	list, err := s.db.ListByCus(ctx, filter)
	if err != nil {
		return err
	}

	update := bson.M{
		"$pull": bson.M{
			"product_list": bson.M{
				"$in": bson.A{productID},
			},
		},
	}

	err = s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}

	if isDel {
		return nil
	}

	for _, v := range list {
		zap.S().Infof("down,id:%s,product_id:%s--------------------", v.ID.Hex(), productID.Hex())
		s.productHistoryS.NotifyDownSale(ctx, model.HistoryTypeShortCut, v.ID, productID)
	}

	return nil
}

func (s shortcutService) UpProduct(ctx context.Context, productID primitive.ObjectID) error {
	list, err := s.productHistoryS.ListUpSale(ctx, model.HistoryTypeShortCut, productID)
	if err != nil {
		return err
	}

	for _, v := range list {

		filter := bson.M{"_id": v.ObjectID}
		shortcut, err := s.Get(ctx, v.ObjectID)
		if err != nil {
			return err
		}

		var pList []primitive.ObjectID
		pList = append(pList, productID)

		for _, id := range shortcut.ProductList {
			pList = append(pList, id)
		}

		update := bson.M{
			"$set": bson.M{
				"product_list": pList,
			},
		}
		zap.S().Infof("up,id:%s,product_id:%s--------------------", v.ID.Hex(), productID.Hex())
		err = s.db.UpdateInfo(ctx, filter, update)
		if err != nil {
			zap.S().Errorf("shortcutService UpProduct错误%v", err)
		}
	}
	return nil
}

func (s shortcutService) UpdateSort(ctx context.Context, req types.SwipeUpdateSort) error {
	for _, sort := range req.List {
		id, err := util.ConvertToObject(sort.ID)
		if err != nil {
			return err
		}
		err = s.db.UpdateInfo(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{
			"sort": sort.Sort,
		}})
		if err != nil {
			return err
		}
	}
	return nil
}

func (s shortcutService) UpdateProduct(ctx context.Context, id primitive.ObjectID, pIDs []primitive.ObjectID) error {
	err := s.db.UpdateInfo(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{
		"product_list": pIDs,
	}})
	if err != nil {
		return err
	}
	return nil
}

func (s shortcutService) Create(ctx context.Context, req types.ShortcutCreate) (primitive.ObjectID, error) {
	now := time.Now().UnixMilli()

	count, err := s.db.Count(ctx)
	if err != nil {
		return [12]byte{}, err
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		return [12]byte{}, err
	}

	shortcut := model.Shortcut{
		ID:             primitive.NewObjectID(),
		ServicePointID: pointID,
		Title:          req.Title,
		Sort:           int(count + 1),
		Visible:        true,
		Page:           req.Page,
		TopImg:         req.TopImg,
		Icon:           req.Icon,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	shortcut.ProductList = make([]primitive.ObjectID, len(req.ProductList))
	for i, productId := range req.ProductList {
		id, err := primitive.ObjectIDFromHex(productId)
		if err != nil {
			return [12]byte{}, err
		}
		shortcut.ProductList[i] = id
	}

	err = s.db.Create(ctx, shortcut)
	if err != nil {
		return primitive.NilObjectID, err
	}
	return shortcut.ID, nil
}

func (s shortcutService) Init(ctx context.Context, data model.Shortcut) error {
	err := s.db.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s shortcutService) Update(ctx context.Context, req types.ShortcutUpdate) error {
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		return err
	}

	productList := make([]primitive.ObjectID, len(req.ProductList))
	for i, productId := range req.ProductList {
		productList[i], err = primitive.ObjectIDFromHex(productId)
		if err != nil {
			return err
		}
	}

	return s.db.UpdateInfo(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{
		"title":        req.Title,
		"sort":         req.Sort,
		"visible":      req.Visible,
		"page":         req.Page,
		"top_img":      req.TopImg,
		"product_list": productList,
		"icon":         req.Icon,
		"updated_at":   time.Now().UnixMilli(),
	}})
}

func (s shortcutService) List(ctx context.Context, page, limit int64) ([]model.Shortcut, int64, error) {
	list, i, err := s.db.List(ctx, bson.M{}, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s shortcutService) ListAll(ctx context.Context, visible bool) ([]model.Shortcut, error) {
	filter := bson.M{
		"visible": visible,
	}
	list, err := s.db.ListByCus(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s shortcutService) ListAllWithPoint(ctx context.Context, visible bool, pointID primitive.ObjectID) ([]model.Shortcut, error) {
	filter := bson.M{
		"visible":          visible,
		"service_point_id": pointID,
	}
	list, err := s.db.ListByCus(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}
func (s shortcutService) ListVisible(ctx context.Context, pointID primitive.ObjectID) ([]model.Shortcut, error) {
	list, err := s.db.ListByCus(ctx, bson.M{
		"visible":          true,
		"service_point_id": pointID,
	})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s shortcutService) Del(ctx context.Context, Id primitive.ObjectID) error {
	return s.db.Delete(ctx, Id)
}

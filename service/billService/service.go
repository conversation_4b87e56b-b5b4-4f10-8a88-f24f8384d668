package billService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/billDao"
	"base/global"
	"base/model"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, data model.Bill) error
	CheckExportLimit(ctx context.Context, buyerID primitive.ObjectID) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.Bill, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Bill, int64, error)
}

type billService struct {
	rdb     *redis.Client
	billDao billDao.DaoInt
}

func NewBillService() ServiceInterface {
	return billService{
		rdb:     global.RDBDefault,
		billDao: dao.BillDao,
	}
}

func (s billService) CheckExportLimit(ctx context.Context, buyerID primitive.ObjectID) error {
	//	每日只能导出10次
	key := "billExportDay:" + buyerID.Hex()
	count, err := s.rdb.Incr(ctx, key).Result()

	// 过期时间，当天
	now := time.Now()
	endTime := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	//diff := int(endTime.Sub(now).Seconds())
	s.rdb.ExpireAt(ctx, key, endTime)

	if err != nil {
		return err
	}
	if count > 5 {
		return xerr.ErrBillExportLimit
	}
	_ = count

	return nil
}

func (s billService) Create(ctx context.Context, data model.Bill) error {
	err := s.billDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s billService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	now := time.Now().UnixMilli()

	update := bson.M{
		"updated_at": now,
		"deleted_at": now,
	}
	err := s.billDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s billService) GetByID(ctx context.Context, id primitive.ObjectID) (model.Bill, error) {
	filter := bson.M{
		"_id": id,
	}
	fee, err := s.billDao.Get(ctx, filter)
	if err != nil {
		return model.Bill{}, err
	}
	return fee, nil
}

func (s billService) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Bill, int64, error) {
	list, count, err := s.billDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

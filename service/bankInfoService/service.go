package bankInfoService

import (
	"base/dao"
	"base/dao/bankInfoDao"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-********/v2/client"
)

// ServiceInterface 银行编码
type ServiceInterface interface {
	GetByName(ctx context.Context, name string) (model.BankInfo, error)
	GetByCode(ctx context.Context, code string) (model.BankInfo, error)
}

type bankInfoService struct {
	bankInfoDao bankInfoDao.DaoInt
}

func (b bankInfoService) GetByName(ctx context.Context, name string) (model.BankInfo, error) {
	return b.bankInfoDao.GetByName(ctx, name)
}

func (b bankInfoService) GetByCode(ctx context.Context, code string) (model.BankInfo, error) {
	return b.bankInfoDao.GetByCode(ctx, code)
}

func NewBankInfoService() ServiceInterface {
	return bankInfoService{
		bankInfoDao: dao.BankInfoDao,
	}
}

package withdrawApplyOrderService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/withdrawApplyOrderDao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"base/service/miniService"
	"base/service/payAccountService"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, auth model.Authentication, amount int, accountType model.AccountType) error
	CreateData(ctx context.Context, data model.WithdrawApplyOrder) error
	CreatePlatform(ctx context.Context, amount int) error
	ListByObject(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType) ([]model.WithdrawApplyOrder, error)
	CheckExistOrder(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType) error
	ActiveUpdateStatus(ctx context.Context, withdrawID string) error
	UpdateOne(ctx context.Context, filter, update bson.M) error

	NotifyPayStatus(ctx context.Context, res allinpay.NotifyPay) error

	YeeNotifyPayStatus(ctx context.Context, res model.YeeWithdrawNotify) error
}

type withdrawApplyOrderService struct {
	mdb                   *mongo.Database
	l                     *zap.SugaredLogger
	withdrawApplyOrderDao withdrawApplyOrderDao.DaoInt
	authenticationS       authenticationService.ServiceInterface
	payAccountS           payAccountService.ServiceInterface
	mini                  miniService.ServiceInterface
	// 通联支付订单
	allInPayOrderS payModule.OrderService

	mnsSendS *mnsSendService.MnsClient
}

func NewWithdrawApplyOrderService() ServiceInterface {
	return withdrawApplyOrderService{
		mdb:                   global.MDB,
		l:                     global.OrderLogger.Sugar(),
		withdrawApplyOrderDao: dao.WithdrawApplyOrderDao,
		authenticationS:       authenticationService.NewAuthenticationService(),
		payAccountS:           payAccountService.NewPayAccountService(),
		mini:                  miniService.NewMiniService(),
		allInPayOrderS:        payModule.NewOrderS(),

		mnsSendS: mnsSendService.NewMNSClient(),
	}
}

func (s withdrawApplyOrderService) CheckExistOrder(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType) error {
	filter := bson.M{
		"object_id":   objectID,
		"object_type": objectType,
		"pay_status":  model.PayStatusTypePending,
	}
	get, err := s.withdrawApplyOrderDao.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if get.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "提现失败，已存在提现订单，请稍后重试")
	}
	return nil
}

// Create 提现申请
func (s withdrawApplyOrderService) Create(ctx context.Context, auth model.Authentication, amount int, accountType model.AccountType) error {
	if !auth.IsMobileVerify {
		return xerr.NewErr(xerr.ErrParamError, nil, "请先验证手机号")
	}
	err := s.CheckExistOrder(ctx, auth.ObjectID, auth.ObjectType)
	if err != nil {
		return err
	}

	// 检查余额情况
	userBalance, err := s.payAccountS.GetNormalUserBalance(ctx, auth.PayBizUserId)
	if err != nil {
		return err
	}
	if userBalance.AllAmount < amount {
		//-userBalance.DepositFreezenAmount-userBalance.FreezenAmount
		return xerr.NewErr(xerr.ErrParamError, nil, "余额不足")
	}

	now := time.Now().UnixMilli()
	bk := auth.BankAccount

	if auth.Company.CompanyType == model.CompanyTypeCo {
		// 公司
		if accountType == model.AccountTypeSelf {
			bk = auth.PersonalBankAccount
		}
	}

	data := model.WithdrawApplyOrder{
		ID:               primitive.NewObjectID(),
		AuthenticationID: auth.ID,
		Amount:           amount,
		ObjectType:       auth.ObjectType,
		ObjectID:         auth.ObjectID,
		BizOrderNo:       util.NewUUID(),
		BizUserId:        auth.PayBizUserId,
		AccountSetNo:     global.AllInPayAccountSetInfo.EscrowUserNo,
		Fee:              100,
		ValidateType:     0,                   // 不验证
		BankCardNo:       bk.CardNumber,       // 银行卡号/账号
		BankCardPro:      int(bk.AccountType), // 银行卡/账户属性 0：个人银行卡  1：企业对公账户  如果不传默认为0  平台提现，必填1
		PayStatus:        model.PayStatusTypePending,
		CreatedAt:        now,
	}

	err = s.withdrawApplyOrderDao.Create(ctx, data)
	if err != nil {
		return err
	}

	res, err := s.allInPayOrderS.WithdrawApplyS(pays.WithdrawApplyReq{
		BizOrderNo:          data.BizOrderNo,
		BizUserId:           data.BizUserId,
		AccountSetNo:        global.AllInPayAccountSetInfo.EscrowUserNo, // 托管用户集
		Amount:              data.Amount,
		Fee:                 data.Fee,
		ValidateType:        0,
		BackUrl:             global.BackHost + global.BackUrlWithdraw,
		OrderExpireDatetime: util.WithDrawExpire(),
		BankCardNo:          data.BankCardNo,
		BankCardPro:         data.BankCardPro,
		WithdrawType:        "D0",
		IndustryCode:        global.IndustryCode,
		IndustryName:        global.IndustryName,
		Summary:             "提现",
		ExtendInfo:          "bizUserID" + auth.PayBizUserId,
		Source:              pays.SourceMobile,
		//PayMethod:           nil, // 支付方式 如不传，则默认为通联通代付
	})
	if err != nil {
		err = s.withdrawApplyOrderDao.Update(ctx, bson.M{
			"_id": data.ID,
		}, bson.M{
			"$set": bson.M{
				"pay_status": model.PayStatusTypeFail,
				"updated_at": time.Now().UnixMilli(),
			},
		})
		return err
	}

	// 赋值
	var ps model.WithdrawPayResult
	ps.PayStatus = res.PayStatus
	ps.PayFailMessage = res.PayFailMessage
	ps.BizUserId = res.BizUserId
	ps.OrderNo = res.OrderNo
	ps.BizOrderNo = res.BizOrderNo
	ps.ExtendInfo = res.ExtendInfo

	data.WithdrawPayResult = ps

	err = s.withdrawApplyOrderDao.Update(ctx, bson.M{
		"_id": data.ID,
	}, bson.M{
		"$set": bson.M{
			"withdraw_pay_result": ps,
			"updated_at":          time.Now().UnixMilli(),
		},
	})
	if err != nil {
		s.l.Errorf("更新提现申请结果错误，提现单%s，错误%v", data.ID.Hex(), err)
		return err
	}

	s.mnsSendS.SendCheckWithdraw(data.ID.Hex())

	return nil
}

func (s withdrawApplyOrderService) CreateData(ctx context.Context, data model.WithdrawApplyOrder) error {
	err := s.withdrawApplyOrderDao.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s withdrawApplyOrderService) UpdateOne(ctx context.Context, filter, update bson.M) error {
	err := s.withdrawApplyOrderDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

// CreatePlatform 提现申请-平台
func (s withdrawApplyOrderService) CreatePlatform(ctx context.Context, amount int) error {
	// 检查余额情况
	balance, err := s.payAccountS.GetPlatformStandardBalanceNo(ctx)
	if err != nil {
		return err
	}
	if balance.AllAmount < amount {
		return xerr.NewErr(xerr.ErrParamError, nil, "余额不足")
	}

	now := time.Now().UnixMilli()

	data := model.WithdrawApplyOrder{
		ID:               primitive.NewObjectID(),
		AuthenticationID: primitive.NilObjectID,
		Amount:           amount,
		ObjectType:       model.ObjectTypePlatform,
		ObjectID:         primitive.NilObjectID,
		BizOrderNo:       util.NewUUID(),
		BizUserId:        global.AllInPayAccountSetInfo.PlatformBizUserId,
		AccountSetNo:     global.AllInPayAccountSetInfo.StandardBalanceNo,
		Fee:              0,
		ValidateType:     0,                                                // 不验证
		BankCardNo:       global.AllInPayAccountSetInfo.PlatformCardNumber, // 银行卡号/账号
		BankCardPro:      1,                                                // 银行卡/账户属性 0：个人银行卡  1：企业对公账户  如果不传默认为0  平台提现，必填1
		PayStatus:        model.PayStatusTypePending,
		CreatedAt:        now,
	}

	err = s.withdrawApplyOrderDao.Create(ctx, data)
	if err != nil {
		return err
	}

	res, err := s.allInPayOrderS.WithdrawApplyS(pays.WithdrawApplyReq{
		BizOrderNo:          data.BizOrderNo,
		BizUserId:           data.BizUserId,
		AccountSetNo:        data.AccountSetNo,
		Amount:              data.Amount,
		Fee:                 data.Fee,
		ValidateType:        0,
		BackUrl:             global.BackHost + global.BackUrlWithdraw,
		OrderExpireDatetime: util.WithDrawExpire(),
		BankCardNo:          data.BankCardNo,
		BankCardPro:         data.BankCardPro,
		WithdrawType:        "D0",
		IndustryCode:        global.IndustryCode,
		IndustryName:        global.IndustryName,
		Summary:             "提现",
		ExtendInfo:          "",
		Source:              pays.SourceMobile,
		//PayMethod:           nil, // 支付方式 如不传，则默认为通联通代付
	})
	if err != nil {
		return err
	}

	// 赋值
	var ps model.WithdrawPayResult
	ps.PayStatus = res.PayStatus
	ps.PayFailMessage = res.PayFailMessage
	ps.BizUserId = res.BizUserId
	ps.OrderNo = res.OrderNo
	ps.BizOrderNo = res.BizOrderNo
	ps.ExtendInfo = res.ExtendInfo

	data.WithdrawPayResult = ps

	err = s.withdrawApplyOrderDao.Update(ctx, bson.M{
		"_id": data.ID,
	}, bson.M{
		"$set": bson.M{
			"withdraw_pay_result": ps,
			"updated_at":          time.Now().UnixMilli(),
		},
	})
	if err != nil {
		s.l.Errorf("更新提现申请结果错误，提现单%s，错误%v", data.ID.Hex(), err)
		return err
	}

	s.mnsSendS.SendCheckWithdraw(data.ID.Hex())

	return nil
}

func (s withdrawApplyOrderService) ListByObject(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType) ([]model.WithdrawApplyOrder, error) {
	filter := bson.M{
		"object_id":   objectID,
		"object_type": objectType,
	}
	list, err := s.withdrawApplyOrderDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	// 对未成功订单进行查询
	for i, order := range list {
		_ = i
		_ = order
		if order.PayStatus != model.PayStatusTypePaid {
			////	 主动查询
			//req := pays.GetOrderStatusReq{
			//	OrderNo:    order.WithdrawPayResult.OrderNo,
			//	BizOrderNo: order.BizOrderNo,
			//}
			//res, err := s.allInPayOrderS.GetOrderStatusS(req)
			//if err != nil {
			//	return nil, err
			//}
			//// 4 成功
			//payStatus1 := model.PayStatusTypePaid
			//payStatusStr := "OK"
			//payStatusError := ""
			//resPayStatusStr := "success"
			//
			//if res.OrderStatus == 3 {
			//	// 交易发生错误
			//	payStatus1 = model.PayStatusTypeFail
			//	payStatusStr = "error"
			//	resPayStatusStr = "error"
			//	payStatusError = res.ErrorMessage
			//}

			////	 主动更新
			//err = s.withdrawApplyOrderDao.Update(ctx, bson.M{"_id": order.ID}, bson.M{"$set": bson.M{
			//	"pay_status":                           payStatus1,
			//	"withdraw_pay_result.status":           payStatusStr,
			//	"withdraw_pay_result.pay_status":       resPayStatusStr,
			//	"withdraw_pay_result.pay_fail_message": payStatusError,
			//}})
			//if err != nil {
			//	return nil, err
			//}
			//list[i].PayStatus = payStatus1
			//list[i].WithdrawPayResult.PayStatus = payStatusStr

		}
	}

	return list, nil
}

func (s withdrawApplyOrderService) ActiveUpdateStatus(ctx context.Context, withdrawID string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("ActiveUpdateStatus error:%v", err)
			return
		}
	}()
	id, err := util.ConvertToObjectWithNote(withdrawID, "ActiveUpdateStatus withdrawID")
	if err != nil {
		return err
	}
	filter := bson.M{
		"_id": id,
	}
	data, err := s.withdrawApplyOrderDao.Get(ctx, filter)
	if err == mongo.ErrNoDocuments {
		s.l.Warnf("提现主动检查，提现单%s不存在，忽略", data.ID.Hex())
		return nil
	}
	if err != nil {
		return err
	}
	if data.PayStatus == model.PayStatusTypePaid {
		s.l.Infof("提现主动检查%s，已支付，忽略", data.ID.Hex())
		return nil
	}

	req := pays.GetOrderStatusReq{
		OrderNo:    data.WithdrawPayResult.OrderNo,
		BizOrderNo: data.BizOrderNo,
	}
	res, err := s.allInPayOrderS.GetOrderStatusS(req)
	if err == xerr.XerrPayNoOrder {
		s.l.Warnf("提现主动检查，提现单%s，支付订单%s不存在，忽略", data.ID.Hex(), req.BizOrderNo)
		return nil
	}

	//4 成功
	payStatus1 := model.PayStatusTypePaid
	payStatusStr := "success"
	payStatusError := ""

	if res.OrderStatus == 3 {
		// 交易发生错误
		payStatus1 = model.PayStatusTypeFail
		payStatusStr = "fail"
		payStatusError = res.ErrorMessage
	}

	marshal, err := json.Marshal(res)
	if err != nil {
		zap.S().Errorf("%v", err)
	}
	s.l.Errorf("主动检查提现%s", string(marshal))

	if res.OrderStatus != 3 && res.OrderStatus != 4 {
		//	 主动更新
		var statusStr string
		if res.OrderStatus == 4 {
			statusStr = "OK"
		}
		err = s.withdrawApplyOrderDao.Update(ctx, bson.M{"_id": withdrawID}, bson.M{"$set": bson.M{
			"pay_status":                           payStatus1,
			"withdraw_pay_result.status":           statusStr,
			"withdraw_pay_result.pay_status":       payStatusStr,
			"withdraw_pay_result.pay_fail_message": payStatusError,
		}})
		if err != nil {
			return err
		}
		return nil
	}
	return nil
}

// NotifyPayStatus 回调
func (s withdrawApplyOrderService) NotifyPayStatus(ctx context.Context, res allinpay.NotifyPay) error {
	// 订单成功、订单失败
	filter := bson.M{"biz_order_no": res.BizOrderNo}
	update := bson.M{
		"withdraw_pay_result.status": res.Status,
		//"withdraw_pay_result.channel_extend_info":        res.ChannelExtendInfo, // 空
		"withdraw_pay_result.pay_interface_out_trade_no": res.PayInterfaceOutTradeNo,
	}

	if res.Status == "OK" {
		update["pay_status"] = model.PayStatusTypePaid
		update["withdraw_pay_result.pay_status"] = "success"
		update["withdraw_pay_result.pay_date_time"] = res.PayDatetime
		update["withdraw_pay_result.acct"] = res.Acct
	} else {
		s.l.Errorf("提现回调失败状态：%s,bizOrderNo%s", res.Status, res.BizOrderNo)
		update["pay_status"] = model.PayStatusTypeFail
		update["withdraw_pay_result.pay_status"] = "fail"
	}
	err := s.withdrawApplyOrderDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s withdrawApplyOrderService) YeeNotifyPayStatus(ctx context.Context, notify model.YeeWithdrawNotify) error {
	filter := bson.M{
		"yee_withdraw_result.order_no": notify.OrderNo,
	}
	applyOrder, err := s.withdrawApplyOrderDao.Get(ctx, filter)
	if err != nil {
		return err
	}

	if applyOrder.YeeWithdrawResult.Status == "SUCCESS" {
		return nil
	}

	debitAmount := util.DealMoneyToFenInt(notify.DebitAmount)
	receiveAmount := util.DealMoneyToFenInt(notify.ReceiveAmount)
	orderAmount := util.DealMoneyToFenInt(notify.OrderAmount)
	fee := util.DealMoneyToFenInt(notify.Fee)

	update := bson.M{
		"yee_withdraw_result.finish_time":    notify.FinishTime,
		"yee_withdraw_result.reverse_time":   notify.ReverseTime,
		"yee_withdraw_result.account_name":   notify.AccountName,
		"yee_withdraw_result.fee":            fee,
		"yee_withdraw_result.bank_name":      notify.BankName,
		"yee_withdraw_result.debit_amount":   debitAmount,
		"yee_withdraw_result.receive_amount": receiveAmount,
		"yee_withdraw_result.order_time":     notify.OrderTime,
		"yee_withdraw_result.order_amount":   orderAmount,
		"yee_withdraw_result.account_no":     notify.AccountNo,
		"yee_withdraw_result.fail_reason":    notify.FailReason,
		"yee_withdraw_result.receive_type":   notify.ReceiveType,
		"yee_withdraw_result.merchant_no":    notify.MerchantNo,
		"yee_withdraw_result.status":         notify.Status,
		"yee_withdraw_result.is_reversed":    notify.IsReversed,
	}

	if notify.Status == "SUCCESS" {
		update["pay_status"] = model.PayStatusTypePaid
	}

	err = s.withdrawApplyOrderDao.Update(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	return nil
}

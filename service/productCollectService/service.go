package productCollectService

import (
	"base/dao"
	"base/dao/productCollectDao"
	"base/global"
	"base/model"
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

// ServiceInterface 分类标签
type ServiceInterface interface {
	Upsert(ctx context.Context, buyerID, productID, pointID primitive.ObjectID) error
	Get(ctx context.Context, buyerID, productID primitive.ObjectID) (bool, error)
	Delete(ctx context.Context, ids []primitive.ObjectID) error
	List(ctx context.Context, buyerID, pointID primitive.ObjectID, page, limit int64) ([]model.ProductCollect, int64, error)
	Check(ctx context.Context, buyerID, productID primitive.ObjectID) (bool, error)
}

type productCollectService struct {
	mdb         *mongo.Database
	pCollectDao productCollectDao.DaoInt
}

func (s productCollectService) Get(ctx context.Context, buyerID, productID primitive.ObjectID) (bool, error) {
	filter := bson.M{"buyer_id": buyerID, "product_id": productID}
	data, err := s.pCollectDao.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	return data.ID != primitive.NilObjectID, nil
}

func (s productCollectService) Delete(ctx context.Context, ids []primitive.ObjectID) error {
	if len(ids) < 1 {
		return nil
	}
	err := s.pCollectDao.DeleteMany(ctx, bson.M{
		"_id": bson.M{"$in": ids},
	})
	if err != nil {
		return err
	}
	return nil
}

func (s productCollectService) Upsert(ctx context.Context, buyerID, productID, pointID primitive.ObjectID) error {
	filter := bson.M{"buyer_id": buyerID, "product_id": productID}
	collect, err := s.pCollectDao.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if collect.ID != primitive.NilObjectID {
		err = s.pCollectDao.Delete(ctx, filter)
		if err != nil {
			return err
		}
		return nil
	}
	data := model.ProductCollect{
		ID:             primitive.NewObjectID(),
		BuyerID:        buyerID,
		ProductID:      productID,
		ServicePointID: pointID,
		CreatedAt:      time.Now().UnixMilli(),
	}

	err = s.pCollectDao.Create(ctx, data)
	return err
}

func (s productCollectService) Check(ctx context.Context, buyerID, productID primitive.ObjectID) (bool, error) {
	filter := bson.M{"buyer_id": buyerID, "product_id": productID}
	count, err := s.pCollectDao.Count(ctx, filter)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func (s productCollectService) List(ctx context.Context, buyerID, pointID primitive.ObjectID, page, limit int64) ([]model.ProductCollect, int64, error) {
	filter := bson.M{
		"buyer_id":         buyerID,
		"service_point_id": pointID,
	}
	list, i, err := s.pCollectDao.ListByPage(ctx, filter, page, limit)
	return list, i, err
}

func NewProductCollectService() ServiceInterface {
	return productCollectService{
		mdb:         global.MDB,
		pCollectDao: dao.ProductCollectDao,
	}
}

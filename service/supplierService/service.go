package supplierService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/supplierDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/allInPayUserService"
	"base/service/authenticationService"
	"base/service/bankAccountService"
	"base/service/entityService"
	"base/service/messageService"
	"base/service/ossService"
	"base/service/userService"
	"base/service/warehouseService"
	"base/service/yeeMerchantService"
	"base/types"
	"base/util"
	"context"
	"errors"
	"time"

	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ServiceInterface 供应商
type ServiceInterface interface {
	Create(ctx context.Context, req types.SupplierApplyReq, userID primitive.ObjectID) error
	//UpdateApply(ctx context.Context, req types.SupplierApplyReq) error
	UpdateAvatar(ctx context.Context, id primitive.ObjectID, avatar model.FileInfo) error
	UpdateStatus(ctx context.Context, id primitive.ObjectID, status model.AccountStatusType) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	Audit(ctx context.Context, info model.Supplier, servicePointID string, auditStatus model.AuditStatusType, auditFailReason string) error
	List(filter bson.M, page, limit int64) ([]model.Supplier, int64, error)
	ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.Supplier, error)
	GetByUser(userID primitive.ObjectID) (model.Supplier, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.Supplier, error)
	CheckStatusByUserID(userID primitive.ObjectID) error
	CheckOneExist(ctx context.Context, id primitive.ObjectID) error

	CheckTag(ctx context.Context, id primitive.ObjectID) error
	BindTag(ctx context.Context, tag model.SupplierTag, supplierIDs []primitive.ObjectID, updateType string) error

	// UpdateTag 更新标签
	UpdateTag(ctx context.Context, id primitive.ObjectID, title, color string) error

	// UnfreezeFrozenBalanceAmount 解冻供应商冻结金额
	UnfreezeFrozenBalanceAmount(ctx context.Context, supplierID primitive.ObjectID, amount int) error

	// AddFrozenBalanceAmount 增加供应商冻结金额
	AddFrozenBalanceAmount(ctx context.Context, supplierID primitive.ObjectID, amount int) error

	// UpdateIsBusinessManage 更新是否经营管理
	UpdateIsBusinessManage(ctx context.Context, id primitive.ObjectID, isBusinessManage bool) error
}

type supplierService struct {
	mdb             *mongo.Database
	rdb             *redis.Client
	supplierDao     supplierDao.DaoInt
	msg             messageService.ServiceInterface
	entityService   entityService.ServiceInterface
	bankAccountS    bankAccountService.ServiceInterface
	UserS           userService.ServiceInterface
	authenticationS authenticationService.ServiceInterface
	yeeMerchantS    yeeMerchantService.ServiceInterface
	warehouseS      warehouseService.ServiceInterface
	ossS            ossService.OssService

	AllInPayS     payModule.MemberService
	AllInPayUserS allInPayUserService.ServiceInterface
}

// NewSupplierService 创建供应商服务
func NewSupplierService() ServiceInterface {
	return supplierService{
		mdb:             global.MDB,
		rdb:             global.RDBDefault,
		supplierDao:     dao.SupplierDao,
		msg:             messageService.NewMessageService(),
		entityService:   entityService.NewEntityService(),
		bankAccountS:    bankAccountService.NewBankCardService(),
		UserS:           userService.NewUserService(),
		authenticationS: authenticationService.NewAuthenticationService(),
		yeeMerchantS:    yeeMerchantService.NewYeeMerchantService(),
		warehouseS:      warehouseService.NewWarehouseServiceService(),

		ossS: ossService.NewOssService(),

		AllInPayS:     payModule.NewMember(),
		AllInPayUserS: allInPayUserService.NewAllInPayUserService(),
	}
}

func (s supplierService) ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.Supplier, error) {
	if len(ids) < 1 {
		return nil, nil
	}
	filter := bson.M{
		"deleted_at": 0,
		"_id": bson.M{
			"$in": ids,
		},
	}
	list, err := s.supplierDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s supplierService) CheckOneExist(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	count, err := s.supplierDao.Count(ctx, filter)
	if err != nil {
		return err
	}
	if count != 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "供应商不存在")
	}
	return nil
}

func (s supplierService) CheckStatusByUserID(userID primitive.ObjectID) error {
	byUser, err := s.supplierDao.GetByUserID(context.Background(), userID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return xerr.NewErr(xerr.ErrParamError, nil, "供应商不存在")
	}
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if byUser.AccountStatus != model.AccountStatusTypeNormal {
		return xerr.NewErr(xerr.ErrParamError, nil, "账号异常，请联系管理员")
	}
	return nil
}

func (s supplierService) Create(ctx context.Context, req types.SupplierApplyReq, userID primitive.ObjectID) error {
	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		return err
	}

	user, err := s.UserS.Get(ctx, userID)
	if err != nil {
		return err
	}

	getByUser, err := s.GetByUser(userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if getByUser.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "已存在信息，请勿重复申请")
	}

	now := time.Now().UnixMilli()

	data := model.Supplier{
		ID:                 primitive.NewObjectID(),
		UserID:             userID,
		ShopSimpleName:     req.ShopSimpleName,
		Address:            req.Address,
		ContactUser:        req.ContactUserName,
		TagList:            []model.SupplierTag{},
		Location:           req.Location,
		AccountStatus:      model.AccountStatusTypeNormal,
		SupplierServiceFee: 1.5,
		Level:              req.Level,
		ServicePointID:     servicePointID,
		CreatedAt:          now,
	}

	if req.Level == "station" {
		stationID, err := util.ConvertToObjectWithCtx(ctx, req.StationID)
		if err != nil {
			return err
		}
		data.StationID = stationID
	}

	err = s.supplierDao.Create(ctx, data)
	if err != nil {
		return err
	}

	err = s.yeeMerchantS.CreateBySupplier(ctx, user.Mobile, data)
	if err != nil {
		return err
	}

	return nil
}

//
//func (s supplierService) UpdateApply(ctx context.Context, req types.SupplierApplyReq) error {
//	id, err := util.ConvertToObject(req.ID)
//	if err != nil {
//		return err
//	}
//	data, err := s.Get(ctx, id)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		return err
//	}
//
//	now := time.Now().UnixMilli()
//
//	//warehouse, err := s.warehouseS.GetByRegion(ctx, rID)
//	//if err != nil {
//	//	return err
//	//}
//	data.ShopSimpleName = req.ShopSimpleName
//	data.Address = req.AuthenticationReq.Address
//	data.ContactUser = req.AuthenticationReq.ContactUserName
//	//data.WarehouseID = warehouse.ID
//	data.Location = req.AuthenticationReq.Location
//	data.SupplierServiceFee = 1.5
//	data.UpdatedAt = now
//
//	// 开启事务
//	session, err := s.mdb.Client().StartSession()
//	if err != nil {
//		return err
//	}
//	defer session.EndSession(ctx)
//
//	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
//		err = s.supplierDao.Update(sessCtx, bson.M{"_id": id}, bson.M{"$set": data})
//		if err != nil {
//			return nil, err
//		}
//		err = s.authenticationS.UpdateReApply(sessCtx, req.AuthenticationReq, data.ID, model.ObjectTypeSupplier)
//		if err != nil {
//			return nil, err
//		}
//		return nil, nil
//	})
//	if err != nil {
//		return err
//	}
//
//	del(s.rdb, data.ID)
//
//	return nil
//}

func (s supplierService) UpdateAvatar(ctx context.Context, id primitive.ObjectID, avatar model.FileInfo) error {
	update := bson.M{
		"avatar_img": avatar,
		"updated_at": time.Now().UnixMilli(),
	}
	del(s.rdb, id)

	err := s.supplierDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}

func (s supplierService) UpdateStatus(ctx context.Context, id primitive.ObjectID, status model.AccountStatusType) error {
	update := bson.M{
		"account_status": status,
		"updated_at":     time.Now().UnixMilli(),
	}

	err := s.supplierDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, id)

	return nil
}

func (s supplierService) UpdateMany(ctx context.Context, filter, update bson.M) error {
	// 临时使用，未删缓存
	err := s.supplierDao.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierService) Get(ctx context.Context, id primitive.ObjectID) (model.Supplier, error) {
	m := get(s.rdb, id)
	if m.ID == primitive.NilObjectID {
		i, err := s.supplierDao.Get(ctx, bson.M{"_id": id})
		if err != nil {
			return model.Supplier{}, err
		}
		set(s.rdb, i)
		return i, nil
	}
	return m, nil
}

func (s supplierService) List(filter bson.M, page, limit int64) ([]model.Supplier, int64, error) {
	list, i, err := s.supplierDao.ListByPage(context.Background(), filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

// CompanyBasicInfoSet 企业基本信息（设置）
type CompanyBasicInfoSet struct {
	IdentityBeginDate string `json:"identityBeginDate"` //      证件有效开始日期 格式：9999-12-31
	IdentityEndDate   string `json:"identityEndDate"`   //      证件有效截止日期  格式：9999-12-31  若长期有效上送“9999-12-31”
	AcctType          int    `json:"acctType"`          // 账户类型 0- 对私 1- 对公. 企业性质=1-企业/3-事业单位，默认1-对公，不支持上送0-对私  企业性质=2-个体工商户，支持上送0-对私/1-对公，不填默认1-对公
	AccountNo         string `json:"accountNo"`         // 必填  账户类型
	ParentBankName    string `json:"parentBankName"`    //      开户银行名称
	BankCityNo        string `json:"bankCityNo"`        //      开户行地区代码
	BankName          string `json:"bankName"`          //      开户行支行名称  账户类型=1-对公，则必填
	UnionBank         string `json:"unionBank"`         //      支付行号，12位数字  账户类型=1-对公，则必填
	Province          string `json:"province"`          //      开户行所在省 开户行所在市必须同时上送
	City              string `json:"city"`              //      开户行所在市 开户行所在省必须同时上送
}

func (s supplierService) Audit(ctx context.Context, info model.Supplier, servicePointID string, auditStatus model.AuditStatusType, auditFailReason string) error {
	update := bson.M{
		"audit_status":      auditStatus,
		"audit_fail_reason": auditFailReason,
		"updated_at":        time.Now().UnixMilli(),
	}

	if auditStatus == model.AuditStatusTypePass {
		pointID, err := util.ConvertToObjectWithCtx(ctx, servicePointID)
		if err != nil {
			return err
		}
		update["audit_fail_reason"] = ""
		update["service_point_id"] = pointID
	}

	err := s.supplierDao.Update(ctx, bson.M{"_id": info.ID}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, info.ID)

	return nil
}

func (s supplierService) GetByUser(userID primitive.ObjectID) (model.Supplier, error) {
	i, err := s.supplierDao.GetByUserID(context.Background(), userID)
	if err != nil {
		return model.Supplier{}, err
	}
	return i, nil
}

func (s supplierService) CheckTag(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"tag_list._id": id,
	}
	get, err := s.supplierDao.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if get.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "删除错误,已被商家使用", get.ID.Hex())
	}
	return nil
}

func (s supplierService) UpdateTag(ctx context.Context, id primitive.ObjectID, title, color string) error {
	filter := bson.M{
		"tag_list._id": id,
	}
	suppliers, err := s.supplierDao.List(ctx, filter)
	if err != nil {
		return err
	}

	update := bson.M{
		"tag_list.$.title": title,
		"tag_list.$.color": color,
	}
	err = s.supplierDao.UpdateMany(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	for _, supplier := range suppliers {
		del(s.rdb, supplier.ID)

	}

	return nil
}

func (s supplierService) BindTag(ctx context.Context, tag model.SupplierTag, supplierIDs []primitive.ObjectID, updateType string) error {
	if len(supplierIDs) < 1 {
		return nil
	}

	filter := bson.M{
		"_id": bson.M{
			"$in": supplierIDs,
		},
	}

	update := bson.M{
		"$addToSet": bson.M{
			"tag_list": tag,
		},
	}

	if updateType == "remove" {
		update = bson.M{
			"$pull": bson.M{
				"tag_list": tag,
			},
		}
	}

	err := s.supplierDao.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}

	for _, id := range supplierIDs {
		del(s.rdb, id)
	}

	return nil
}

// UnfreezeFrozenBalanceAmount 解冻供应商冻结金额
func (s supplierService) UnfreezeFrozenBalanceAmount(ctx context.Context, supplierID primitive.ObjectID, amount int) error {
	supplier, err := s.Get(ctx, supplierID)
	if err != nil {
		return err
	}

	if supplier.FrozenBalanceAmount < amount {
		return xerr.NewErr(xerr.ErrParamError, nil, "供应商冻结金额不足")
	}

	freezeAmount := supplier.FrozenBalanceAmount - amount

	update := bson.M{
		"frozen_balance_amount": freezeAmount,
	}

	err = s.supplierDao.Update(ctx, bson.M{"_id": supplierID}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, supplierID)

	return nil
}

func (s supplierService) AddFrozenBalanceAmount(ctx context.Context, supplierID primitive.ObjectID, amount int) error {
	supplier, err := s.Get(ctx, supplierID)
	if err != nil {
		return err
	}
	if !supplier.IsBusinessManage {
		// 跳过
		return nil
	}

	newFrozenBalance := supplier.FrozenBalanceAmount + amount

	update := bson.M{
		"frozen_balance_amount": newFrozenBalance,
	}

	err = s.supplierDao.Update(ctx, bson.M{"_id": supplierID}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, supplierID)

	return nil
}

func (s supplierService) UpdateIsBusinessManage(ctx context.Context, id primitive.ObjectID, isBusinessManage bool) error {
	update := bson.M{
		"is_business_manage": isBusinessManage,
	}
	err := s.supplierDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	del(s.rdb, id)
	return nil
}

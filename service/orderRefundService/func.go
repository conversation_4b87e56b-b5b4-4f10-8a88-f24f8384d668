package orderRefundService

import (
	"base/core/xerr"
	"base/model"
	"base/types"
	"base/util"
	"context"
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func backSettleData(order model.Order, p model.ProductOrder) model.ProductSettle {
	data := model.ProductSettle{
		ProductID:                     p.ProductID,
		SkuIDCode:                     p.SkuIDCode,
		SkuName:                       p.SkuName,
		ProductImageID:                p.ProductImageID,
		ProductTitle:                  p.ProductTitle,
		ProductCover:                  p.ProductCoverImg,
		IsCheckWeight:                 p.IsCheckWeight,
		RoughWeight:                   p.RoughWeight,
		OutWeight:                     p.OutWeight,
		NetWeight:                     p.NetWeight,
		Price:                         p.Price,
		SettleUnitPrice:               p.SettleUnitPrice,
		Num:                           p.Num,
		UnitTransportFee:              p.TransportFeePerKG,
		DueWeight:                     p.DueWeight,
		SortWeight:                    p.SortWeight,
		SortNum:                       p.Sort<PERSON>um,
		OrderProductAmount:            p.ProductAmount,
		ProductRoughWeightUnitPriceKG: p.ProductRoughWeightUnitPriceKG,
		CouponRoughWeightUnitPriceKG:  p.CouponRoughWeightUnitPriceKG,
		CouponSplitAmount:             p.CouponSplitAmount,
	}

	var amount int
	var diffAmount int
	var diffWeight int
	var refundCouponAmount int // 退款优惠券金额
	var settleResultType model.SettleResultType
	settleResultType = model.SettleResultTypeNone

	if p.IsCheckWeight {
		//	 计重
		dueWeight := p.Num * p.RoughWeight // 应有重量
		sortWeight := p.SortWeight         // 分拣重量
		diffWeight = sortWeight - dueWeight

		//calcWeighAmount(dueWeight, p.ProductRoughWeightUnitPriceKG)

		// paidUnitPrice := p.ProductRoughWeightUnitPriceKG

		// if p.CouponSplitAmount > 0 {
		// 	paidUnitPrice = p.ProductRoughWeightUnitPriceKG - p.CouponRoughWeightUnitPriceKG
		// }

		if diffWeight > 0 {
			// 补差
			// 按商品单价
			diffAmount = calcWeighAmount(diffWeight, p.ProductRoughWeightUnitPriceKG)

			settleResultType = model.SettleResultTypeDebt
			amount = diffAmount
		}

		if diffWeight < 0 {

			diffWeight = diffWeight * -1
			// 退款
			// 按实付单价-减掉优惠券后
			originProductAmount := calcWeighAmount(diffWeight, p.ProductRoughWeightUnitPriceKG)

			// 计算退款优惠券金额
			refundCouponAmount = calcWeighAmount(diffWeight, p.CouponRoughWeightUnitPriceKG)

			diffAmount = originProductAmount - refundCouponAmount

			//	 退款
			settleResultType = model.SettleResultTypeRefund
			amount = diffAmount
		}

	}

	if !p.IsCheckWeight && p.SortNum != p.Num {
		// 计件-退款
		// 每件商品的优惠额
		perProductCouponAmount := backPerProductCouponAmount(p.CouponSplitAmount, p.Num)

		diffNum := p.Num - p.SortNum

		// 计算退款优惠券金额
		refundCouponAmount = perProductCouponAmount * diffNum

		// 减去优惠额
		diffAmount = diffNum*p.Price - refundCouponAmount
		settleResultType = model.SettleResultTypeRefund
		amount = diffAmount
	}

	data.DiffProductAmount = amount

	// 服务费
	// var totalServiceFee int
	//	按比例

	if settleResultType == model.SettleResultTypeRefund && amount > 0 {
		// 退款
		// 服务费
		// paidProduct := p.ProductAmount - p.CouponSplitAmount

		// totalServiceFee = backQualityRefundServiceFee(amount, paidProduct, p.TotalServiceFee)
		// data.TotalServiceFee = min(totalServiceFee, p.TotalServiceFee)
	}

	data.SettleProductAmount = p.ProductAmount

	if p.SortNum == 0 {
		settleResultType = model.SettleResultTypeRefund
		data.DiffProductAmount = p.PaidAmount - p.TotalServiceFee
		data.TotalServiceFee = p.TotalServiceFee
	}

	data.SettleResultType = settleResultType

	// 计算退款优惠券金额
	data.RefundCouponAmount = refundCouponAmount

	return data
}

func backPerProductCouponAmount(couponSplitAmount, num int) int {
	// 计算每个商品平摊的优惠券金额
	if couponSplitAmount == 0 || num == 0 {
		return 0
	}

	// 计算每件商品的优惠金额
	couponSplitAmountDe := decimal.NewFromInt(int64(couponSplitAmount))
	numDe := decimal.NewFromInt(int64(num))

	// 每件商品的优惠金额 = 总优惠金额 / 商品数量
	perCouponAmount := couponSplitAmountDe.Div(numDe).Round(0)

	return int(perCouponAmount.IntPart())
}

func calcWeighAmount(weight int, roughWeightUnitPriceKG int) int {
	w := toDec(weight).Div(toDec(1000))
	unit := toDec(roughWeightUnitPriceKG)
	round := w.Mul(unit).Round(0)
	return int(round.IntPart())
}

// CheckShippedExistDebtOrRefund 检查存在补差和退款
func (s orderRefundService) CheckShippedExistDebtOrRefund(order model.Order) error {
	for _, p := range order.ProductList {
		if p.Num != p.SortNum {
			//	数量补差
		}
		if p.IsCheckWeight && p.SortWeight > p.TotalWeight {
			// 重量检查-重量补差
		}

	}

	return nil
}

func (s orderRefundService) listProductInfo(ctx context.Context, req types.OrderCreateReq) ([]model.Product, map[primitive.ObjectID]model.Product, map[primitive.ObjectID]types.PerProduct, error) {
	var productIDs []primitive.ObjectID
	mReqProduct := make(map[primitive.ObjectID]types.PerProduct)
	for _, v := range req.SupplierList {
		for _, per := range v.ProductList {
			productID, err := util.ConvertToObject(per.ProductID)
			if err != nil {
				return nil, nil, nil, err
			}
			productIDs = append(productIDs, productID)
			mReqProduct[productID] = per
		}
	}
	products, err := s.productS.ListByIDs(ctx, productIDs)
	if err != nil {
		return nil, nil, nil, err
	}

	mProduct := make(map[primitive.ObjectID]model.Product)
	for _, v := range products {
		mProduct[v.ID] = v
	}

	return products, mProduct, mReqProduct, nil
}

// 优惠
// func (s orderRefundService) dealCashCoupon(ctx context.Context, couponID string, couponAmount, productTotal int) (primitive.ObjectID, int, error) {
// 	id, err := util.ConvertToObjectWithNote(couponID, "coupon_id")
// 	if err != nil {
// 		return primitive.NilObjectID, 0, err
// 	}

// 	get, err := s.couponAccountS.Get(ctx, id)
// 	if err != nil {
// 		return primitive.NilObjectID, 0, err
// 	}

// 	if productTotal < get.Amount {
// 		s.l.Errorf("商品金额须大于优惠金额，优惠券金额%d，产品总价%d，请求参数%d", get.Amount, productTotal, couponAmount)
// 		return primitive.NilObjectID, 0, xerr.NewErr(xerr.ErrOrder, nil)
// 	}
// 	if get.Amount != couponAmount {
// 		s.l.Errorf("优惠金额错误，优惠券金额%d，产品总价%d，请求参数%d", get.Amount, productTotal, couponAmount)
// 		return primitive.NilObjectID, 0, xerr.NewErr(xerr.ErrOrder, nil)
// 	}

// 	return get.ID, get.Amount, nil
// }

func (s orderRefundService) checkAfterSaleRefund(order model.Order, productID primitive.ObjectID, skuIDCode string, amount int, qualityRefundAmount int) (model.ProductOrder, error) {
	milli := time.UnixMilli(order.OrderStatusRecord.ReceiveTime)
	t := time.Duration(s.afterSaleHour) * time.Hour
	a := milli.Add(t).UnixMilli()
	now := time.Now().UnixMilli()
	if now > a {
		return model.ProductOrder{}, xerr.NewErr(xerr.ErrParamError, nil, "无法申请退款，商品已过48小时售后期")
	}

	refundedAmount := qualityRefundAmount

	var f bool
	var r model.ProductOrder
	for _, p := range order.ProductList {
		if p.ProductID == productID && p.SkuIDCode == skuIDCode {
			f = true
			r = p

			exist := p.ProductAmount - refundedAmount
			if amount > exist {
				s.l.Errorf("退款金额不能大于剩余可退金额，实际剩余金额：%d,退款请求金额:%d", exist, amount)
				return r, xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("退款金额不能大于剩余可退金额,最多可退%0.2f元", util.DealMoneyToYuan(exist)))
			}

			if amount > p.PaidAmount-p.TotalTransportFee {
				s.l.Errorf("退款金额不能大于实际支付金额（不包含运费），实际支付金额：%d/g,退款请求金额:%d/g", p.PaidAmount-p.TotalTransportFee, amount)
				return r, xerr.NewErr(xerr.ErrParamError, nil, "退款申请金额不能大于支付金额（不包含运费）")
			}
			break
		}
	}
	if !f {
		s.l.Errorf("检查售后退款订单，订单不含此商品，订单：%s,商品:%s", order.ID.Hex(), productID.Hex())
		return r, xerr.NewErr(xerr.ErrParamError, nil, "订单不含此商品")
	}
	return r, nil
}

func (s orderRefundService) ListAllRefundByOrder(ctx context.Context, orderID primitive.ObjectID) ([]model.OrderRefund, error) {
	filter := bson.M{
		"order_id": orderID,
	}
	list, err := s.orderRefundDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s orderRefundService) ListAllRefundByOrderProduct(ctx context.Context, orderID, productID primitive.ObjectID) ([]model.OrderRefund, error) {
	filter := bson.M{
		"order_id":   orderID,
		"product_id": productID,
		"pay_status": model.PayStatusTypePaid,
	}
	list, err := s.orderRefundDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	return list, nil
}

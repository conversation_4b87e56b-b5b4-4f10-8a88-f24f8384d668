package orderService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/orderDao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"base/service/cartService"
	"base/service/couponUserService"
	"base/service/deliverFeeRuleService"
	"base/service/indexPartProductService"
	"base/service/miniService"
	"base/service/parentOrderService"
	"base/service/payAccountService"
	"base/service/productCommissionService"
	"base/service/productImageService"
	"base/service/productService"
	"base/service/serviceFeeService"
	"base/service/servicePointService"
	"base/service/supplierService"
	"base/service/userAddrService"
	"base/service/userService"
	"base/service/yeeMerchantService"
	"base/types"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sync"
	"time"

	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/yop-platform/yop-go-sdk/yop/client"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"github.com/yop-platform/yop-go-sdk/yop/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

// 支付短信缓存
var cachePaySMS = "cachePaySMS:"

// ServiceInterface 订单
type ServiceInterface interface {
	Create(ctx context.Context, buyer model.Buyer, req types.OrderCreateReq) (model.ParentOrder, error) //创建订单-返回父单订单支付编号（尚未支付）

	YeeTradeOrder(ctx context.Context, orderParentID primitive.ObjectID) (model.YeeWechatResult, error)
	YeeTradeOrderQuery(ctx context.Context, orderID, parentMerchantNo, merchantNo string) (model.TradeOrderQueryRes, error)
	YeeTradeOrderQueryForCombine(ctx context.Context, orderParentID primitive.ObjectID) (any, error)

	YeeAggTutelagePrePay(ctx context.Context, orderParentID primitive.ObjectID, openID, ip string) (model.YeeWechatResult, error)

	YeeAccountBookPay(ctx context.Context, orderParentID primitive.ObjectID) error

	ToPayBalance(ctx context.Context, parentOrderID primitive.ObjectID, openID string) (interface{}, error)

	CheckResendPaySMS(ctx context.Context, parentOrder model.ParentOrder) (string, error)

	BalancePayConfirm(ctx context.Context, parentOrder model.ParentOrder, verificationCode, ip string) error
	ResendBalancePaySMS(ctx context.Context, parentOrder model.ParentOrder) (string, error)
	QueryBizOrderStatus(ctx context.Context, orderNo, bizOrderNo string) (pays.GetOrderStatusRes, error)
	QueryBizOrderDetail(ctx context.Context, bizOrderNo string) (pays.GetOrderDetailRes, error)
	QuerySplitDetail(ctx context.Context, bizOrderNo string) (pays.GetOrderSplitRuleListDetailRes, error)
	ListByBuyer(ctx context.Context, id primitive.ObjectID, page, limit int64) ([]model.Order, int64, error)
	ListByStation(ctx context.Context, id primitive.ObjectID, page, limit, timeBegin, timeEnd int64) ([]model.Order, int64, error)
	ListBySupplier(ctx context.Context, id primitive.ObjectID, page, limit, timeBegin, timeEnd int64) ([]model.Order, int64, error)
	ListToPayByBuyer(ctx context.Context, id primitive.ObjectID, page, limit int64) ([]model.Order, int64, error)
	ListToReceiveByBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.Order, int64, error)
	ListToShipByBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.Order, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.Order, error)
	ListWithOption(ctx context.Context, filter bson.M, findOpt *options.FindOptions) ([]model.Order, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Order, int64, error)
	ListByOrderIDs(ctx context.Context, orderIDs []primitive.ObjectID) ([]model.Order, error)
	ListStatusByBuyer(ctx context.Context, buyerID primitive.ObjectID) ([]model.Order, error)
	ListByBizOrderNo(ctx context.Context, bizOrderNo string) ([]model.Order, error)
	ListByParentOrderID(ctx context.Context, parentOrderID primitive.ObjectID) ([]model.Order, error)
	CountSaleProductNumMonthly(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) (int, error)
	CountOrderNumMonthly(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) (int64, error)
	CountReBuyRateMonthly(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) (int64, error)
	CountProduct(ctx context.Context, buyerID primitive.ObjectID) (int64, int64, error)
	GetLatestOrderTime(ctx context.Context, buyerID primitive.ObjectID) (int64, error)
	CountBuyer(ctx context.Context, productID primitive.ObjectID) (int64, int64, int64, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.Order, error)
	GetByIDNum(ctx context.Context, idNum string) (model.Order, error)
	GetByCus(ctx context.Context, filter bson.M) (model.Order, error)
	UpdatePayStatus(ctx context.Context, bizOrderNo string, payStatus model.PayStatusType) error
	UpdateOrdersPayStatusByParentOrder(ctx context.Context, parentOrderID primitive.ObjectID, payStatus model.PayStatusType, orderStatus model.OrderStatusType, payMethod model.PayMethodType) error
	UpdateOrderStatus(ctx context.Context, orderID primitive.ObjectID, payStatus model.PayStatusType, status model.OrderStatusType) error
	UpdateOrderSettleUnitPrice(ctx context.Context, orderID, productID primitive.ObjectID, price int) error
	CheckRemoveCommission(ctx context.Context, content string) error
	ShipUploadInfo(ctx context.Context, orderIDList []primitive.ObjectID) error

	UpdateOne(ctx context.Context, filter bson.M, update bson.M) error
	UpdateMany(ctx context.Context, filter bson.M, update bson.M) error

	Close(ctx context.Context, parentOrderID primitive.ObjectID) error

	CancelOrderResult(ctx context.Context, order model.Order, refundResult model.RefundResult) error
	CancelOrderResultCoupon(ctx context.Context, order model.Order, refundResult model.RefundResult) error

	RecoverStockByParentOrder(ctx context.Context, parentOrderID string) error
	RecoverStockByOrder(ctx context.Context, orderID string) error

	NotifyPayStatus(ctx context.Context, res allinpay.NotifyPay) error
	NotifyPay(ctx context.Context, content *payments.Transaction) error

	YeeNotifyTradeOrderPay(ctx context.Context, notify model.YeeTradeOrderPayNotify) error
	YeeNotifyAccountBookPay(ctx context.Context, notify model.YeeTradeOrderPayNotify) error
}

type orderService struct {
	mdb *mongo.Database
	rdb *redis.Client
	l   *zap.SugaredLogger

	yeePay *global.YeePayInfo

	orderDao orderDao.DaoInt
	productS productService.ServiceInterface
	//routeFeeS               routeService.ServiceInterface
	servicePointS servicePointService.ServiceInterface
	//warehouseS              warehouseService.ServiceInterface
	//servicePointCommissionS servicePointCommissionService.ServiceInterface
	productImageS      productImageService.ServiceInterface
	productCommissionS productCommissionService.ServiceInterface
	//供应商
	supplierS supplierService.ServiceInterface
	// 地址
	userAddrS userAddrService.ServiceInterface
	userS     userService.ServiceInterface

	cartS cartService.ServiceInterface

	//	优惠
	couponUserS couponUserService.ServiceInterface

	authenticationS authenticationService.ServiceInterface

	yeeMerchantS yeeMerchantService.ServiceInterface

	mini miniService.ServiceInterface

	// 平台费率
	//PlatformPayCommission float64

	// 平台营销账户余额
	payAccountS payAccountService.ServiceInterface

	allInPayOrderS payModule.OrderService
	// 父单
	parentOrderS parentOrderService.ServiceInterface
	////	补差单
	//orderDebtS orderDebtService.ServiceInterface
	////  退款单
	//orderRefundS orderRefundService.ServiceInterface

	//warehouseLoadFeeS warehouseLoadFeeService.ServiceInterface // 仓配费

	deliverFeeRuleS deliverFeeRuleService.ServiceInterface // 配送费

	mnsSendS *mnsSendService.MnsClient

	orderExpireMinute int64 // 订单过期时间-分钟

	indexPartProductS indexPartProductService.ServiceInterface
}

// NewOrderService 创建订单服务
func NewOrderService() ServiceInterface {
	return orderService{
		mdb: global.MDB,
		rdb: global.RDBDefault,
		l:   global.OrderLogger.Sugar(),

		yeePay: global.YeePay,

		orderDao: dao.OrderDao,
		productS: productService.NewProductService(),
		//routeFeeS:               routeService.NewTransportFeeService(),
		servicePointS: servicePointService.NewServicePointService(),
		//warehouseS:              warehouseService.NewWarehouseServiceService(),
		//servicePointCommissionS: servicePointCommissionService.NewPartnerCommissionService(),
		productImageS:      productImageService.NewProductImageService(),
		productCommissionS: productCommissionService.NewProductCommissionService(),
		supplierS:          supplierService.NewSupplierService(),
		userAddrS:          userAddrService.NewUserAddrService(),
		userS:              userService.NewUserService(),
		couponUserS:        couponUserService.NewCouponUserService(),
		cartS:              cartService.NewCartService(),

		authenticationS: authenticationService.NewAuthenticationService(),
		yeeMerchantS:    yeeMerchantService.NewYeeMerchantService(),

		mini: miniService.NewMiniService(),

		// 平台
		//PlatformPayCommission: config.Conf.PlatformPayCommission,
		payAccountS: payAccountService.NewPayAccountService(),

		allInPayOrderS: payModule.NewOrderS(),
		parentOrderS:   parentOrderService.NewParentOrderService(),

		////	补差单
		//orderDebtS: orderDebtService.NewOrderDebtService(),
		//// 退款单
		//orderRefundS: orderRefundService.NewOrderRefundService(),

		//warehouseLoadFeeS: warehouseLoadFeeService.NewWarehouseLoadFeeService(),
		deliverFeeRuleS: deliverFeeRuleService.NewDeliverFeeRuleService(),

		mnsSendS: mnsSendService.NewMNSClient(),

		orderExpireMinute: global.OrderExpireMinute,

		indexPartProductS: indexPartProductService.NewIndexPartProductService(),
	}
}

func (s orderService) QueryPayOrder(ctx context.Context, tradeNo string) (*payments.Transaction, error) {
	svc := global.PayJSAPI
	req := jsapi.QueryOrderByOutTradeNoRequest{
		Mchid:      core.String("**********"),
		OutTradeNo: core.String(tradeNo),
	}
	resp, result, err := svc.QueryOrderByOutTradeNo(ctx, req)
	_ = result
	if err != nil {
		return nil, err
	}

	code := result.Response.StatusCode
	status := result.Response.Status
	_ = status

	if code == 400 {
		//	订单已关闭
		zap.S().Errorf("查询订单异常：tradeNo:%s,异常:%s", tradeNo, status)
		return nil, xerr.NewErr(xerr.ErrParamError, nil, "订单已关闭")
	}

	if code == 404 {
		//	订单不存在
		zap.S().Errorf("查询订单异常：tradeNo:%s,异常:%s", tradeNo, status)
		return nil, xerr.NewErr(xerr.ErrParamError, nil, "订单不存在")
	}

	return resp, nil
}

func (s orderService) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	err := s.orderDao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s orderService) UpdateMany(ctx context.Context, filter bson.M, update bson.M) error {
	err := s.orderDao.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s orderService) CancelOrderResult(ctx context.Context, order model.Order, refundResult model.RefundResult) error {
	filter := bson.M{
		"_id": order.ID,
	}
	update := bson.M{
		"$set": bson.M{
			"cancel_result": refundResult,
			//"pay_status":    model.PayStatusTypePaid,
			//"order_status":  model.OrderStatusTypeCancel,
			"updated_at": time.Now().UnixMilli(),
		},
	}

	err := s.orderDao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	mnsSendService.NewMNSClient().SendRecoverProductStockByOrder(order.ID.Hex())

	return nil
}

func (s orderService) CancelOrderResultCoupon(ctx context.Context, order model.Order, refundResult model.RefundResult) error {
	filter := bson.M{
		"_id": order.ID,
	}
	update := bson.M{
		"$set": bson.M{
			"cancel_result_coupon": refundResult,
			//"pay_status":    model.PayStatusTypePaid,
			//"order_status":  model.OrderStatusTypeCancel,
			"updated_at": time.Now().UnixMilli(),
		},
	}

	err := s.orderDao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

// NotifyPayStatus 回调
func (s orderService) NotifyPayStatus(ctx context.Context, res allinpay.NotifyPay) error {
	// 只有成功才通知
	if res.Status == "OK" {
		//“OK”标识支付成功；
		parentOrder, err := s.parentOrderS.GetByBizOrderNo(ctx, res.BizOrderNo)
		if err != nil {
			return err
		}

		filter := bson.M{
			"biz_order_no": res.BizOrderNo,
		}
		update := bson.M{
			"pay_status":                      model.PayStatusTypePaid,
			"pay_result.status":               res.Status,
			"pay_result.channel_paytime":      res.ChannelPaytime,
			"pay_result.cusid":                res.Cusid,
			"pay_result.biz_order_no":         res.BizOrderNo,
			"pay_result.channel_fee":          res.ChannelFee,
			"pay_result.pay_interfacetrxcode": res.PayInterfacetrxcode,
			"pay_result.pay_datetime":         res.PayDatetime,
			"pay_result.acct":                 res.Acct,
			"updated_at":                      time.Now().UnixMilli(),
		}
		err = s.parentOrderS.UpdateOne(ctx, filter, bson.M{"$set": update})
		if err != nil {
			s.l.Errorf("更新支付成功状态错误,bizOrderNo%s,失败:%v", res.BizOrderNo, err)
			return err
		}

		//	 子订单
		err = s.UpdateOrdersPayStatusByParentOrder(ctx, parentOrder.ID, model.PayStatusTypePaid, model.OrderStatusTypeToStockUp, parentOrder.PayMethod)
		if err != nil {
			return err
		}

		//	 检查是否需要激活
		//if parentOrder.CouponAccount.CouponID.Hex() == model.NewUserInviteCouponID {
		//	//	更改为已下单
		//	mnsSendService.NewMNSClient().SendUpdateInviteStatus(parentOrder.UserID, model.InviteStatusOrdered)
		//}

		if parentOrder.PayMethod == model.PayMethodTypeBalance {
			//mnsSendService.NewMNSClient().SendCreateRecord(model.BuyerBalanceRecordTypeOrder, parentOrder.BuyerID, parentOrder.ID, parentOrder.PaidAmount)
		}
		s.l.Infof("支付回调成功%s", res.BizOrderNo)
		return nil
	}
	return nil
}

func (s orderService) NotifyPay(ctx context.Context, content *payments.Transaction) error {
	parentOrder, err := s.parentOrderS.GetByOutTradeNo(ctx, *content.OutTradeNo)
	if err != nil {
		return err
	}

	if parentOrder.PayStatus == model.PayStatusTypePaid {
		return nil
	}

	update := bson.M{
		"wx_pay_result.transaction_id":   content.TransactionId,
		"wx_pay_result.trade_state":      content.TradeState,
		"wx_pay_result.trade_state_desc": content.TradeStateDesc,
		"wx_pay_result.bank_type":        content.BankType,
		"wx_pay_result.success_time":     content.SuccessTime,
		"wx_pay_result.payer_amount":     content.Amount.PayerTotal,
		"wx_pay_result.total_amount":     content.Amount.Total,
		"wx_pay_result.source":           content,
	}

	if *content.TradeState == "SUCCESS" {
		update["pay_status"] = model.PayStatusTypePaid
	}

	filter := bson.M{
		"wx_pay_result.out_trade_no": *content.OutTradeNo,
	}
	err = s.parentOrderS.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	//	 子订单
	err = s.UpdateOrdersPayStatusByParentOrder(ctx, parentOrder.ID, model.PayStatusTypePaid, model.OrderStatusTypeToStockUp, parentOrder.PayMethod)
	if err != nil {
		return err
	}

	return nil
}

// YeeNotifyTradeOrderPay 交易下单-回调
func (s orderService) YeeNotifyTradeOrderPay(ctx context.Context, notify model.YeeTradeOrderPayNotify) error {
	parentOrder, err := s.parentOrderS.GetByTradeOrderID(ctx, notify.OrderId)
	if err != nil {
		return err
	}

	if parentOrder.YeeWechatResult.NotifyStatus == "SUCCESS" {
		return nil
	}

	payAmount := util.DealMoneyToFenInt(notify.PayAmount)
	orderAmount := util.DealMoneyToFenInt(notify.OrderAmount)

	update := bson.M{
		"yee_wechat_result.notify_pay_way":                  notify.PayWay,
		"yee_wechat_result.notify_pay_success_date":         notify.PaySuccessDate,
		"yee_wechat_result.notify_channel_order_id":         notify.ChannelOrderId,
		"yee_wechat_result.notify_status":                   notify.Status,
		"yee_wechat_result.notify_channel_trx_id":           notify.ChannelTrxId,
		"yee_wechat_result.notify_pay_amount":               payAmount,
		"yee_wechat_result.notify_order_amount":             orderAmount,
		"yee_wechat_result.notify_payer_user_id":            notify.PayerInfoFormat.UserID,
		"yee_wechat_result.notify_payer_yp_account_book_no": notify.PayerInfoFormat.YpAccountBookNo,
		"yee_wechat_result.notify_sub_order_list":           notify.SubOrderInfoListFormat,
	}

	if notify.Status == "SUCCESS" {
		update["pay_status"] = model.PayStatusTypePaid
	}

	filter := bson.M{
		"yee_wechat_result.order_id": notify.OrderId,
	}
	err = s.parentOrderS.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	//	 子订单
	err = s.UpdateOrdersPayStatusByParentOrder(ctx, parentOrder.ID, model.PayStatusTypePaid, model.OrderStatusTypeToStockUp, parentOrder.PayMethod)
	if err != nil {
		return err
	}

	return nil
}

// YeeNotifyAccountBookPay 记账簿支付
func (s orderService) YeeNotifyAccountBookPay(ctx context.Context, notify model.YeeTradeOrderPayNotify) error {
	parentOrder, err := s.parentOrderS.GetByTradeOrderID(ctx, notify.OrderId)
	if err != nil {
		return err
	}

	if parentOrder.YeeWechatResult.NotifyStatus == "SUCCESS" {
		return nil
	}

	payAmount := util.DealMoneyToFenInt(notify.PayAmount)
	orderAmount := util.DealMoneyToFenInt(notify.OrderAmount)

	update := bson.M{
		"yee_wechat_result.notify_pay_way":                  notify.PayWay,
		"yee_wechat_result.unique_order_no":                 notify.UniqueOrderNo,
		"yee_wechat_result.notify_pay_success_date":         notify.PaySuccessDate,
		"yee_wechat_result.notify_channel_order_id":         notify.ChannelOrderId,
		"yee_wechat_result.notify_status":                   notify.Status,
		"yee_wechat_result.notify_pay_amount":               payAmount,
		"yee_wechat_result.notify_order_amount":             orderAmount,
		"yee_wechat_result.notify_payer_user_id":            notify.PayerInfoFormat.UserID,
		"yee_wechat_result.notify_payer_yp_account_book_no": notify.PayerInfoFormat.YpAccountBookNo,
	}

	if notify.Status == "SUCCESS" {
		update["pay_status"] = model.PayStatusTypePaid
	}

	filter := bson.M{
		"yee_wechat_result.order_id": notify.OrderId,
	}
	err = s.parentOrderS.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	//	 子订单
	err = s.UpdateOrdersPayStatusByParentOrder(ctx, parentOrder.ID, model.PayStatusTypePaid, model.OrderStatusTypeToStockUp, parentOrder.PayMethod)
	if err != nil {
		return err
	}

	if parentOrder.PayMethod == model.PayMethodTypeYeeBalance {
		mnsSendService.NewMNSClient().SendCreateBalanceRecord(model.BuyerBalanceRecordTypeOrder, parentOrder.BuyerID, parentOrder.ID, parentOrder.PaidAmount)
	}

	return nil
}

func (s orderService) NotifyCancel(ctx context.Context, content *model.RefundNotify) error {
	order, err := s.GetByOutTradeNo(ctx, content.OutTradeNo)
	if err != nil {
		return err
	}

	if order.WXCancelResult.Status == "SUCCESS" {
		return nil
	}

	update := bson.M{
		"wx_cancel_result.transaction_id":        content.TransactionId,
		"wx_cancel_result.refund_id":             content.RefundId,
		"wx_cancel_result.status":                content.RefundStatus,
		"wx_cancel_result.user_received_account": content.UserReceivedAccount,
		"wx_cancel_result.success_time":          content.SuccessTime,
		"wx_cancel_result.out_refund_no":         content.OutRefundNo,
		"wx_cancel_result.total":                 content.Amount.Total,
		"wx_cancel_result.refund":                content.Amount.Refund,
		"wx_cancel_result.payer_total":           content.Amount.PayerTotal,
		"wx_cancel_result.payer_refund":          content.Amount.PayerRefund,
		"wx_cancel_result.source":                content,
	}

	if content.RefundStatus == "SUCCESS" {
		update["order_status"] = model.OrderStatusTypeCancel
		update["updated_at"] = time.Now().UnixMilli()
	}

	filter := bson.M{
		"wx_pay_result.out_trade_no": content.OutTradeNo,
	}
	err = s.orderDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s orderService) UpdateOrdersPayStatusByParentOrder(ctx context.Context, parentOrderID primitive.ObjectID, payStatus model.PayStatusType, orderStatus model.OrderStatusType, payMethod model.PayMethodType) error {
	orders, err := s.ListByParentOrderID(ctx, parentOrderID)
	if err != nil {
		return err
	}
	for _, order := range orders {
		filter := bson.M{
			"_id":             order.ID,
			"parent_order_id": parentOrderID,
		}

		now := time.Now().UnixMilli()
		update := bson.M{
			"pay_status":   payStatus,
			"order_status": orderStatus,
			"pay_method":   payMethod,
			"updated_at":   now,
		}

		if payStatus == model.PayStatusTypePaid {
			update["order_status_record.pay_time"] = now
		}

		err = s.orderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
		if err != nil {
			return err
		}
	}
	if orderStatus == model.OrderStatusTypeToStockUp {
		for _, order := range orders {
			if order.DeliverType == model.DeliverTypeDoor {
				err = s.deliverFeeRuleS.DeliverRecord(ctx, order.Address.AddressID, order.ID)
				if err != nil {
					s.l.Errorf("UpdateOrdersPayStatusByParentOrder deliverFeeRuleS.DeliverRecord err %v", err.Error())
				}
			}
		}
	}

	return nil
}

// Close 关闭订单-未支付+未支付成功
func (s orderService) Close(ctx context.Context, parentOrderID primitive.ObjectID) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("Close error:%v", err)
			return
		}
	}()
	parentOrder, err := s.parentOrderS.Get(ctx, parentOrderID)
	if err != nil {
		return err
	}

	if parentOrder.PayStatus == model.PayStatusTypePaid {
		s.l.Infof("订单已支付，取消关闭")
		return nil
	}

	if parentOrder.WXPayResult.OutTradeNo == "" {
		//	 未发起过支付
		s.l.Infof("未支付，直接关闭父单%v和子单", parentOrderID.Hex())
		err = s.closeParentOrderAndChild(ctx, parentOrder)
		if err != nil {
			return err
		}

		// 代金券
		//if parentOrder.CouponAccountID != primitive.NilObjectID {
		//	err = s.couponAccountS.RecoverCoupon(ctx, parentOrder.CouponAccountID)
		//	if err != nil {
		//		return err
		//	}
		//}

		return nil
	}

	// 先查询
	transaction, err := s.QueryPayOrder(ctx, parentOrder.WXPayResult.OutTradeNo)
	if err != nil {
		// 订单不存在
		// 直接关闭业务订单即可
		err = s.closeParentOrderAndChild(ctx, parentOrder)
		if err != nil {
			return err
		}
		return nil
	}

	if *transaction.TradeState != "SUCCESS" {
		err = s.closeParentOrderAndChild(ctx, parentOrder)
		if err != nil {
			return err
		}
		return nil
	}

	////	关闭云商通订单
	//req := pays.CloseOrderReq{
	//	BizOrderNo: parentOrder.BizOrderNo,
	//}
	//res, err := s.allInPayOrderS.CloseOrderS(req)
	//if err != nil {
	//	return err
	//}
	//if res.CloseResult == 2 {
	//	//	关闭失败
	//	if res.OrderStatus == 99 {
	//		// 自动关闭，不用手动关闭
	//		err = s.closeParentOrderAndChild(ctx, parentOrder)
	//		if err != nil {
	//			return err
	//		}
	//		return nil
	//	}
	//	if res.OrderStatus == 3 {
	//		//	 交易失败，一般为超时未支付
	//		s.l.Infof("交易失败，直接关闭父子单，云商通订单%s成功,父单：%s", res.BizOrderNo, parentOrderID.Hex())
	//		err = s.closeParentOrderAndChild(ctx, parentOrder)
	//		if err != nil {
	//			return err
	//		}
	//		return nil
	//	}
	//	s.l.Errorf("关闭云商通订单失败%s,父单：%s,ErrorMessage:%s", res.BizOrderNo, parentOrderID.Hex(), res.ErrorMessage)
	//	return xerr.NewErr(xerr.ErrParamError, nil, res.ErrorMessage)
	//}
	//if res.CloseResult == 1 {
	//	s.l.Infof("关闭云商通订单%s成功,父单：%s", res.BizOrderNo, parentOrderID.Hex())
	//	err = s.closeParentOrderAndChild(ctx, parentOrder)
	//	if err != nil {
	//		return err
	//	}
	//
	//	//// 代金券
	//	//if parentOrder.CouponAccountID != primitive.NilObjectID {
	//	//	err = s.couponAccountS.RecoverCoupon(ctx, parentOrder.CouponAccountID)
	//	//	if err != nil {
	//	//		return err
	//	//	}
	//	//}
	//
	//	return nil
	//}
	return nil
}

func (s orderService) closeParentOrderAndChild(ctx context.Context, parentOrder model.ParentOrder) error {
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		err = s.parentOrderS.UpdatePayStatusByID(sessCtx, parentOrder.ID, model.PayStatusTypeClose)
		if err != nil {
			s.l.Errorf("关闭父单%s,失败:%v", parentOrder.ID.Hex(), err)
			return nil, err
		}
		//	 子订单
		err = s.UpdateOrdersPayStatusByParentOrder(sessCtx, parentOrder.ID, model.PayStatusTypeClose, model.OrderStatusTypeClosed, parentOrder.PayMethod)
		if err != nil {
			s.l.Errorf("关闭子订单%s,失败:%v", parentOrder.ID.Hex(), err)
			return nil, err
		}
		return nil, nil
	})

	if err != nil {
		return err
	}

	// 父单恢复库存
	mnsSendService.NewMNSClient().SendRecoverProductStockByParentOrder(parentOrder.ID.Hex())

	if parentOrder.OrderType != model.OrderTypeRetail {
		s.mnsSendS.SendConsumeIntegral(model.RecordTypeOrderClose, parentOrder.BuyerID, parentOrder.ID, parentOrder.ProductTotalAmount/100)
	}

	return nil
}

func (s orderService) ListStatusByBuyer(ctx context.Context, buyerID primitive.ObjectID) ([]model.Order, error) {
	filter := bson.M{
		"buyer_id": buyerID,
		"created_at": bson.M{
			"$gte": time.Now().AddDate(0, 0, -15).UnixMilli(),
		},
	}
	zap.S().Info(filter)
	list, err := s.orderDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderService) ListBySupplier(ctx context.Context, id primitive.ObjectID, page, limit, timeBegin, timeEnd int64) ([]model.Order, int64, error) {
	filter := bson.M{
		"supplier_id": id,
		"deleted_at":  0,
	}

	if timeBegin > 0 && timeEnd > 0 {
		filter["created_at"] = bson.M{
			"$gte": timeBegin,
			"$lte": timeEnd,
		}
	}
	list, count, err := s.orderDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s orderService) RecoverStockByParentOrder(ctx context.Context, parentOrderID string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("RecoverStockByParentOrder error:%v", err)
			return
		}
	}()
	id, err := util.ConvertToObjectWithNote(parentOrderID, "RecoverStockByParentOrder parentOrderID")
	if err != nil {
		return err
	}
	orders, err := s.ListByParentOrderID(ctx, id)
	if err != nil {
		return err
	}
	m := make(map[primitive.ObjectID]int)
	for _, order := range orders {
		for _, p := range order.ProductList {
			m[p.ProductID] += p.Num
		}
	}
	err = s.productS.RecoverStock(ctx, m)
	if err != nil {
		return err
	}
	return nil
}

func (s orderService) RecoverStockByOrder(ctx context.Context, orderID string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("RecoverStockByOrder error:%v", err)
			return
		}
	}()
	id, err := util.ConvertToObjectWithNote(orderID, "RecoverStockByOrder orderID")
	if err != nil {
		return err
	}
	order, err := s.Get(ctx, id)
	if err != nil {
		return err
	}
	m := make(map[primitive.ObjectID]int)
	for _, p := range order.ProductList {
		m[p.ProductID] += p.Num
	}
	s.l.Infof("RecoverStockByOrder%v", m)
	err = s.productS.RecoverStock(ctx, m)
	if err != nil {
		return err
	}
	return nil
}

type MNSCheckRemoveCommission struct {
	OrderID string `json:"order_id"`
}

func (s orderService) CheckRemoveCommission(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("CheckRemoveCommission error:%v", err)
			return
		}
	}()
	s.l.Infof("CheckRemoveCommission--content:%s", content)

	var data MNSCheckRemoveCommission
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	orderID, err := util.ConvertToObjectWithNote(data.OrderID, "")
	if err != nil {
		return err
	}
	order, err := s.Get(ctx, orderID)
	if err != nil {
		return err
	}
	_ = order

	var productIDs []primitive.ObjectID
	for _, p := range order.ProductList {
		productIDs = append(productIDs, p.ProductID)
	}

	partID, err := util.ConvertToObjectWithNote("652210d71e1d890a1fa47694", "")
	if err != nil {
		return err
	}

	indexPartProducts, err := s.indexPartProductS.ListByCus(ctx, bson.M{
		"index_part_id": partID,
		"product_id": bson.M{
			"$in": productIDs,
		}})
	if err != nil {
		return err
	}
	for _, p := range indexPartProducts {
		update := bson.M{
			"product_list.$.commission_percent": 0,
		}
		err = s.orderDao.UpdateOne(ctx, bson.M{
			"_id":                     order.ID,
			"product_list.product_id": p.ProductID,
		}, bson.M{"$set": update})
		if err != nil {
			return err
		}
	}

	return nil
}

func (s orderService) ListByBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.Order, int64, error) {
	filter := bson.M{
		"buyer_id":   buyerID,
		"deleted_at": 0,
	}
	//if payStatus != 0 {
	//	filter["pay_status"] = payStatus
	//}
	//if orderStatus != 0 {
	//	filter["order_status"] = orderStatus
	//}

	orders, count, err := s.orderDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return orders, count, nil

}

func (s orderService) ListByStation(ctx context.Context, id primitive.ObjectID, page, limit, timeBegin, timeEnd int64) ([]model.Order, int64, error) {
	filter := bson.M{
		"station_id": id,
		"deleted_at": 0,
	}

	if timeBegin > 0 && timeEnd > 0 {
		filter["created_at"] = bson.M{
			"$gte": timeBegin,
			"$lte": timeEnd,
		}
	}

	orders, count, err := s.orderDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return orders, count, nil

}

func (s orderService) CountSaleProductNumMonthly(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) (int, error) {
	filter := bson.M{
		"supplier_id":  supplierID,
		"deleted_at":   0,
		"pay_status":   model.PayStatusTypePaid,
		"order_status": model.OrderStatusTypeFinish,
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	orders, err := s.orderDao.List(ctx, filter)
	if err != nil {
		return 0, err
	}
	count := 0
	for _, order := range orders {
		for _, productOrder := range order.ProductList {
			count += productOrder.Num
		}
	}
	return count, nil

}

func (s orderService) CountOrderNumMonthly(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) (int64, error) {
	filter := bson.M{
		"supplier_id":  supplierID,
		"deleted_at":   0,
		"pay_status":   model.PayStatusTypePaid,
		"order_status": model.OrderStatusTypeFinish,
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	count, err := s.orderDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s orderService) CountReBuyRateMonthly(ctx context.Context, supplierID primitive.ObjectID, begin, end int64) (int64, error) {
	filter := bson.M{
		"supplier_id":  supplierID,
		"deleted_at":   0,
		"pay_status":   model.PayStatusTypePaid,
		"order_status": model.OrderStatusTypeFinish,
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	orders, err := s.orderDao.List(ctx, filter)
	if err != nil {
		return 0, err
	}

	mBuyer := make(map[primitive.ObjectID]primitive.ObjectID)
	for _, order := range orders {
		if v, ok := mBuyer[order.BuyerID]; ok {
			if v != order.ParentOrderID {
				mBuyer[order.BuyerID] = primitive.NilObjectID
			}
			continue
		}
		mBuyer[order.BuyerID] = order.ParentOrderID
	}

	total := len(mBuyer)

	if total == 0 {
		return 0, nil
	}

	n := 0
	for _, i := range mBuyer {
		if i == primitive.NilObjectID {
			n++
		}
	}
	i := decimal.NewFromInt(int64(n)).Mul(decimal.NewFromInt(int64(10000))).Div(decimal.NewFromInt(int64(total))).Round(2).IntPart()

	return i, nil

}

func (s orderService) ListToPayByBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.Order, int64, error) {
	filter := bson.M{
		"buyer_id": buyerID,
		"pay_status": bson.M{
			"$in": bson.A{model.PayStatusTypeToPay, model.PayStatusTypePending},
		},
	}
	orders, count, err := s.orderDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return orders, count, nil

}

func (s orderService) ListToReceiveByBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.Order, int64, error) {
	filter := bson.M{
		"buyer_id": buyerID,
		"pay_status": bson.M{
			"$in": bson.A{model.PayStatusTypePaid, model.PayStatusTypePaidButRefund},
		},
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeToArrive, model.OrderStatusTypeToReceive},
		},
	}
	orders, count, err := s.orderDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return orders, count, nil
}

func (s orderService) ListToShipByBuyer(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.Order, int64, error) {
	filter := bson.M{
		"buyer_id": buyerID,
		"pay_status": bson.M{
			"$in": bson.A{model.PayStatusTypePaid, model.PayStatusTypePaidButRefund},
		},
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeToStockUp, model.OrderStatusTypeToQuality, model.OrderStatusTypeToSort, model.OrderStatusTypeToShip},
		},
	}
	orders, count, err := s.orderDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return orders, count, nil

}

// QueryBizOrderStatus 查询支付订单状态
func (s orderService) QueryBizOrderStatus(ctx context.Context, orderNo, bizOrderNo string) (pays.GetOrderStatusRes, error) {
	_ = ctx
	req := pays.GetOrderStatusReq{
		OrderNo:    orderNo,
		BizOrderNo: bizOrderNo,
	}
	res, err := s.allInPayOrderS.GetOrderStatusS(req)
	if err != nil {
		return pays.GetOrderStatusRes{}, err
	}
	return res, nil
}

// QueryBizOrderDetail 查询支付订单详细状态
func (s orderService) QueryBizOrderDetail(ctx context.Context, bizOrderNo string) (pays.GetOrderDetailRes, error) {
	_ = ctx
	req := pays.GetOrderDetailReq{
		BizOrderNo: bizOrderNo,
	}
	res, err := s.allInPayOrderS.GetOrderDetailS(req)
	if err != nil {
		return pays.GetOrderDetailRes{}, err
	}
	return res, nil
}

// QuerySplitDetail 查询支付订单详细状态
func (s orderService) QuerySplitDetail(ctx context.Context, bizOrderNo string) (pays.GetOrderSplitRuleListDetailRes, error) {
	_ = ctx
	req := pays.GetOrderSplitRuleListDetailReq{
		BizOrderNo: bizOrderNo,
	}
	res, err := s.allInPayOrderS.GetOrderSplitRuleListDetailS(req)
	if err != nil {
		return pays.GetOrderSplitRuleListDetailRes{}, err
	}
	return res, nil
}

func (s orderService) ListByOrderIDs(ctx context.Context, orderIDs []primitive.ObjectID) ([]model.Order, error) {
	if len(orderIDs) < 1 {
		return nil, nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": orderIDs,
		},
	}
	orders, err := s.orderDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return orders, nil
}

func (s orderService) ListByBizOrderNo(ctx context.Context, bizOrderNo string) ([]model.Order, error) {
	filter := bson.M{
		"biz_order_no": bizOrderNo,
	}
	orders, err := s.orderDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return orders, nil
}

func (s orderService) ListByParentOrderID(ctx context.Context, parentOrderID primitive.ObjectID) ([]model.Order, error) {
	filter := bson.M{
		"parent_order_id": parentOrderID,
	}
	orders, err := s.orderDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return orders, nil
}

func (s orderService) UpdatePayStatus(ctx context.Context, bizOrderNo string, payStatus model.PayStatusType) error {
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		orders, err := s.ListByBizOrderNo(sessCtx, bizOrderNo)
		if err != nil {
			return nil, err
		}
		var ids []primitive.ObjectID
		for _, order := range orders {
			ids = append(ids, order.ID)
		}
		filter := bson.M{"_id": bson.M{"$in": ids}}

		update := bson.M{"pay_status": payStatus}
		if payStatus == model.PayStatusTypePaid {
			update["order_status"] = model.OrderStatusTypeToShip // 待发货
		}

		err = s.orderDao.UpdateMany(sessCtx, filter, bson.M{"$set": update})
		if err != nil {
			s.l.Error("更新订单状态错误:", err)
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s orderService) UpdateOrderStatus(ctx context.Context, orderID primitive.ObjectID, payStatus model.PayStatusType, status model.OrderStatusType) error {
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		filter := bson.M{"_id": orderID}
		err = s.orderDao.UpdateMany(sessCtx, filter, bson.M{"$set": bson.M{
			"pay_status":   payStatus,
			"order_status": status,
		}})
		if err != nil {
			s.l.Error("更新订单状态错误:", err)
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}
func (s orderService) UpdateOrderSettleUnitPrice(ctx context.Context, orderID, productID primitive.ObjectID, price int) error {
	filter := bson.M{
		"_id":                     orderID,
		"product_list.product_id": productID,
	}

	order, err := s.Get(ctx, orderID)
	if err != nil {
		return err
	}
	_ = order

	var f bool

	for _, p := range order.ProductList {
		if p.ProductID == productID {
			//if p.IsCheckWeight {
			//	if p.ProductRoughWeightUnitPriceKG < price {
			//		return xerr.NewErr(xerr.ErrParamError, nil, "计重结算单价不能高于销售单价")
			//	}
			//} else {
			//	// 计件
			//	if p.Price < price {
			//		return xerr.NewErr(xerr.ErrParamError, nil, "计件结算价格不能高于销售价")
			//	}
			//}
			f = true
			break
		}
	}

	if !f {
		return xerr.NewErr(xerr.ErrParamError, nil, "订单商品不存在，请刷新")
	}

	err = s.orderDao.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
		"product_list.$.settle_unit_price": price,
	}})
	if err != nil {
		return err
	}
	return nil
}

//
//func (s orderService) ToPay(ctx context.Context, parentOrderID primitive.ObjectID, openID string, payMethod model.PayMethodType) (interface{}, error) {
//	parentOrder, err := s.parentOrderS.Get(ctx, parentOrderID)
//	if err != nil {
//		return "", err
//	}
//	orders, err := s.ListByParentOrderID(ctx, parentOrderID)
//
//	if len(orders) < 1 {
//		s.l.Errorf("去支付，查询父单下无订单:%s", parentOrderID.Hex())
//		return "", err
//	}
//
//	if parentOrder.PayStatus == model.PayStatusTypeClose {
//		s.l.Infof("该订单已关闭，请重新下单%s", parentOrder.ID.Hex())
//		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已关闭，请重新下单")
//	}
//
//	if parentOrder.PayStatus == model.PayStatusTypePaid {
//		s.l.Infof("该订单已支付%s", parentOrder.ID.Hex())
//		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
//	}
//
//	if parentOrder.PayStatus == model.PayStatusTypePending {
//		s.l.Infof("正在支付中，请勿重复支付%s", parentOrder.ID.Hex())
//		return "", xerr.NewErr(xerr.ErrOrder, nil, "正在支付中，请勿重复支付")
//	}
//
//	if parentOrder.Expire < time.Now().UnixMilli() {
//		s.l.Infof("该订单已超时，请重新下单%s", parentOrder.ID.Hex())
//		for _, order := range orders {
//			err = s.UpdateOrderStatus(context.Background(), order.ID, model.PayStatusTypeClose, model.OrderStatusTypeClosed)
//			if err != nil {
//				s.l.Error("主动更新订单至超时未付关闭，错误", err)
//			}
//		}
//		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已超时，请重新下单")
//	}
//
//	//openID, _, _, err := s.mini.GetOpenID(loginCode)
//	//if err != nil {
//	//	return "", err
//	//}
//
//	// 生成BizOrderNo
//	payBizOrderNO, err := s.parentOrderS.UpdateCreatePayBizOrderNO(ctx, parentOrderID, payMethod)
//	if err != nil {
//		s.l.Errorf("更新创建订单号失败%v", err)
//		return "", err
//	}
//	err = s.UpdateMany(ctx, bson.M{"parent_order_id": parentOrderID}, bson.M{"$set": bson.M{"pay_status": model.PayStatusTypePending}})
//	if err != nil {
//		s.l.Errorf("更新子订单支付状态失败%v", err)
//		return "", err
//	}
//
//	statusRes, err := s.QueryBizOrderStatus(context.Background(), "", payBizOrderNO)
//	if !errors.Is(err, xerr.XerrPayNoOrder) {
//		//订单不存在
//	}
//	if statusRes.IsAccountSuccess == 1 && statusRes.OrderStatus == 4 {
//		err = s.UpdatePayStatus(context.Background(), payBizOrderNO, model.PayStatusTypePaid)
//		if err != nil {
//			s.l.Error("主动更新订单已支付状态失败", err)
//		}
//		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
//	}
//	if statusRes.OrderStatus == 6 {
//		//	关闭
//		for _, order := range orders {
//			err = s.UpdateOrderStatus(context.Background(), order.ID, model.PayStatusTypeClose, model.OrderStatusTypeClosed)
//			if err != nil {
//				s.l.Error("主动更新订单至超时未付关闭，错误", err)
//			}
//		}
//		return "", xerr.NewErr(xerr.ErrOrder, nil, "支付订单已关闭")
//	}
//
//	var rList []pays.RecieverItem
//	var receiveDeliverID string
//
//	for _, order := range orders {
//		receiveAmount := order.PaidAmount
//		//if i+1 == len(orders) {
//		//	// 最后一笔
//		//	receiveAmount += parentOrder.DeliverFeeRes.FinalDeliverFee
//		//	receiveDeliverID = order.ReceiverBizUserID
//		//}
//		item := pays.RecieverItem{
//			BizUserId: order.ReceiverBizUserID,
//			Amount:    receiveAmount,
//		}
//		rList = append(rList, item)
//	}
//
//	// 配送费
//	deliverAmount := parentOrder.DeliverFeeRes.FinalDeliverFee
//	if deliverAmount > 0 {
//		// 服务仓
//		pointID := orders[0].ServicePointID
//		servicePointAuth, err := s.authenticationS.GetByServicePoint(ctx, pointID)
//		if err != nil {
//			return nil, err
//		}
//		item := pays.RecieverItem{
//			BizUserId: servicePointAuth.PayBizUserId,
//			Amount:    deliverAmount,
//		}
//		rList = append(rList, item)
//	}
//
//	authentication, err := s.authenticationS.GetByObject(ctx, parentOrder.BuyerID, model.ObjectTypeBuyer)
//	if err != nil {
//		return "", err
//	}
//
//	miniMethod := allinpay.PayMethodMiniProgramBack(parentOrder.PaidAmount, global.WechatAppID, openID)
//
//	validateType := 0
//
//	if payMethod == model.PayMethodTypeBalance {
//		accountNo := global.AllInPayAccountSetInfo.EscrowUserNo
//		miniMethod = allinpay.PayMethodBalance(parentOrder.PaidAmount, accountNo)
//		validateType = 1
//	}
//
//	payerID := authentication.PayBizUserId
//
//	req := pays.AgentCollectApplyReq{
//		BizOrderNo:          payBizOrderNO,
//		PayerId:             payerID,
//		RecieverList:        rList,
//		TradeCode:           global.TradeCodeCollect,
//		Amount:              parentOrder.PaidAmount,
//		Fee:                 0,            // 分账再收
//		ValidateType:        validateType, // 无验证	0	整型	仅渠道验证，通商云不做交易验证   1 短信
//		BackUrl:             global.BackHost + global.BackUrlAgentCollect,
//		OrderExpireDatetime: util.ExpirePayOrderTime(s.orderExpireMinute), // 支付订单过期
//		PayMethod:           miniMethod,
//		IndustryCode:        global.IndustryCode,
//		IndustryName:        global.IndustryName,
//		Source:              pays.SourceMobile,
//		Summary:             "pay" + payerID,
//		ExtendInfo:          "deliver" + receiveDeliverID,
//	}
//	res, err := s.allInPayOrderS.AgentCollectApplyS(req)
//	if err != nil {
//		s.l.Errorf("支付错误%v", err)
//		return "", err
//	}
//
//	ps := model.PayResult{}
//	ps.PayStatus = res.PayStatus
//	ps.PayOpenID = openID
//	ps.PayFailMessage = res.PayFailMessage
//	ps.OrderNo = res.OrderNo
//	ps.BizUserId = res.BizUserId
//	ps.BizOrderNo = res.BizOrderNo
//	ps.ReqPayInterfaceNo = res.ReqPayInterfaceNo
//	ps.PayInterfaceOutTradeNo = res.PayInterfaceOutTradeNo
//	ps.PayInterfacetrxcode = res.PayInterfacetrxcode
//	ps.ChannelFee = res.ChannelFee
//	ps.Chnldata = res.Chnldata
//	ps.ChannelPaytime = res.ChannelPaytime
//	ps.Cusid = res.Cusid
//	ps.TradeNo = res.TradeNo
//	ps.ValidationType = res.ValidationType
//	ps.MiniprogrampayinfoVsp = res.MiniprogrampayinfoVsp
//	ps.ExtendInfo = res.ExtendInfo
//
//	m := make(map[string]interface{})
//	if payMethod == model.PayMethodTypeWechat {
//		//	 微信支付
//		err = json.Unmarshal([]byte(res.PayInfo), &m)
//		if err != nil {
//			s.l.Errorf("解析pay_info错误:%v", err)
//			return nil, err
//		}
//
//		ps.PayInfo = m
//		// 更新父单调用结果
//		err = s.parentOrderS.UpdateBizOrderNoResult(ctx, ps.BizOrderNo, ps)
//		if err != nil {
//			s.l.Error("准备调起支付，更新父单错误", err)
//			return "", err
//		}
//	}
//
//	if payMethod == model.PayMethodTypeBalance {
//		ps.BizUserId = authentication.PayBizUserId
//
//		// 更新父单调用结果
//		err = s.parentOrderS.UpdateBizOrderNoResult(ctx, ps.BizOrderNo, ps)
//		if err != nil {
//			s.l.Error("准备调起支付，更新父单错误", err)
//			return "", err
//		}
//
//		// 余额支付
//		// 支付短信重发限制
//		key := cachePaySMS + authentication.Mobile
//		s.rdb.Set(ctx, key, 0, time.Second*170)
//	}
//
//	mnsSendService.NewMNSClient().SendCheckUserPay(parentOrder.ID.Hex())
//
//	return m, nil
//}

// ToPayBalance 余额支付
func (s orderService) ToPayBalance(ctx context.Context, parentOrderID primitive.ObjectID, openID string) (interface{}, error) {
	parentOrder, err := s.parentOrderS.Get(ctx, parentOrderID)
	if err != nil {
		return "", err
	}
	orders, err := s.ListByParentOrderID(ctx, parentOrderID)

	if len(orders) < 1 {
		s.l.Errorf("去支付，查询父单下无订单:%s", parentOrderID.Hex())
		return "", err
	}

	if parentOrder.PayStatus == model.PayStatusTypeClose {
		s.l.Infof("该订单已关闭，请重新下单%s", parentOrder.ID.Hex())
		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已关闭，请重新下单")
	}

	if parentOrder.PayStatus == model.PayStatusTypePaid {
		s.l.Infof("该订单已支付%s", parentOrder.ID.Hex())
		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
	}

	if parentOrder.PayStatus == model.PayStatusTypePending {
		s.l.Infof("正在支付中，请勿重复支付%s", parentOrder.ID.Hex())
		return "", xerr.NewErr(xerr.ErrOrder, nil, "正在支付中，请勿重复支付")
	}

	if parentOrder.Expire < time.Now().UnixMilli() {
		s.l.Infof("该订单已超时，请重新下单%s", parentOrder.ID.Hex())
		for _, order := range orders {
			err = s.UpdateOrderStatus(context.Background(), order.ID, model.PayStatusTypeClose, model.OrderStatusTypeClosed)
			if err != nil {
				s.l.Error("主动更新订单至超时未付关闭，错误", err)
			}
		}
		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已超时，请重新下单")
	}

	//openID, _, _, err := s.mini.GetOpenID(loginCode)
	//if err != nil {
	//	return "", err
	//}

	// 生成BizOrderNo
	payBizOrderNO, err := s.parentOrderS.UpdateCreatePayBizOrderNO(ctx, parentOrderID, model.PayMethodTypeBalance)
	if err != nil {
		s.l.Errorf("更新创建订单号失败%v", err)
		return "", err
	}
	err = s.UpdateMany(ctx, bson.M{"parent_order_id": parentOrderID}, bson.M{"$set": bson.M{"pay_status": model.PayStatusTypePending}})
	if err != nil {
		s.l.Errorf("更新子订单支付状态失败%v", err)
		return "", err
	}

	statusRes, err := s.QueryBizOrderStatus(context.Background(), "", payBizOrderNO)
	if !errors.Is(err, xerr.XerrPayNoOrder) {
		//订单不存在
	}
	if statusRes.IsAccountSuccess == 1 && statusRes.OrderStatus == 4 {
		err = s.UpdatePayStatus(context.Background(), payBizOrderNO, model.PayStatusTypePaid)
		if err != nil {
			s.l.Error("主动更新订单已支付状态失败", err)
		}
		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
	}
	if statusRes.OrderStatus == 6 {
		//	关闭
		for _, order := range orders {
			err = s.UpdateOrderStatus(context.Background(), order.ID, model.PayStatusTypeClose, model.OrderStatusTypeClosed)
			if err != nil {
				s.l.Error("主动更新订单至超时未付关闭，错误", err)
			}
		}
		return "", xerr.NewErr(xerr.ErrOrder, nil, "支付订单已关闭")
	}

	var rList []pays.RecieverItem
	var receiveDeliverID string

	for _, order := range orders {
		receiveAmount := order.PaidAmount
		//if i+1 == len(orders) {
		//	// 最后一笔
		//	receiveAmount += parentOrder.DeliverFeeRes.FinalDeliverFee
		//	receiveDeliverID = order.ReceiverBizUserID
		//}
		item := pays.RecieverItem{
			BizUserId: order.ReceiverBizUserID,
			Amount:    receiveAmount,
		}
		rList = append(rList, item)
	}

	// 配送费
	deliverAmount := parentOrder.DeliverFeeRes.FinalDeliverFee
	if deliverAmount > 0 {
		// 服务仓
		pointID := orders[0].ServicePointID
		servicePointAuth, err := s.authenticationS.GetByServicePoint(ctx, pointID)
		if err != nil {
			return nil, err
		}
		item := pays.RecieverItem{
			BizUserId: servicePointAuth.PayBizUserId,
			Amount:    deliverAmount,
		}
		rList = append(rList, item)
	}

	authentication, err := s.authenticationS.GetByObject(ctx, parentOrder.BuyerID, model.ObjectTypeBuyer)
	if err != nil {
		return "", err
	}

	accountNo := global.AllInPayAccountSetInfo.EscrowUserNo
	miniMethod := allinpay.PayMethodBalance(parentOrder.PaidAmount, accountNo)

	validateType := 1

	payerID := authentication.PayBizUserId

	//return nil, nil

	req := pays.AgentCollectApplyReq{
		BizOrderNo:          payBizOrderNO,
		PayerId:             payerID,
		RecieverList:        rList,
		TradeCode:           global.TradeCodeCollect,
		Amount:              parentOrder.PaidAmount,
		Fee:                 0,            // 分账再收
		ValidateType:        validateType, // 无验证	0	整型	仅渠道验证，通商云不做交易验证   1 短信
		BackUrl:             global.BackHost + global.BackUrlAgentCollect,
		OrderExpireDatetime: util.ExpirePayOrderTime(s.orderExpireMinute), // 支付订单过期
		PayMethod:           miniMethod,
		IndustryCode:        global.IndustryCode,
		IndustryName:        global.IndustryName,
		Source:              pays.SourceMobile,
		Summary:             "pay" + payerID,
		ExtendInfo:          "deliver" + receiveDeliverID,
	}
	res, err := s.allInPayOrderS.AgentCollectApplyS(req)
	if err != nil {
		s.l.Errorf("支付错误%v", err)
		return "", err
	}

	ps := model.PayResult{}
	ps.PayStatus = res.PayStatus
	ps.PayOpenID = openID
	ps.PayFailMessage = res.PayFailMessage
	ps.OrderNo = res.OrderNo
	ps.BizUserId = res.BizUserId
	ps.BizOrderNo = res.BizOrderNo
	ps.ReqPayInterfaceNo = res.ReqPayInterfaceNo
	ps.PayInterfaceOutTradeNo = res.PayInterfaceOutTradeNo
	ps.PayInterfacetrxcode = res.PayInterfacetrxcode
	ps.ChannelFee = res.ChannelFee
	ps.Chnldata = res.Chnldata
	ps.ChannelPaytime = res.ChannelPaytime
	ps.Cusid = res.Cusid
	ps.TradeNo = res.TradeNo
	ps.ValidationType = res.ValidationType
	ps.MiniprogrampayinfoVsp = res.MiniprogrampayinfoVsp
	ps.ExtendInfo = res.ExtendInfo

	ps.BizUserId = authentication.PayBizUserId

	// 更新父单调用结果
	err = s.parentOrderS.UpdateBizOrderNoResult(ctx, ps.BizOrderNo, ps)
	if err != nil {
		s.l.Error("准备调起支付，更新父单错误", err)
		return "", err
	}

	// 余额支付
	// 支付短信重发限制
	key := cachePaySMS + authentication.Mobile
	s.rdb.Set(ctx, key, 0, time.Second*170)

	//mnsSendService.NewMNSClient().SendCheckUserPay(parentOrder.ID.Hex())

	return nil, nil
}

// YeeTradeOrder 交易下单
func (s orderService) YeeTradeOrder(ctx context.Context, orderParentID primitive.ObjectID) (model.YeeWechatResult, error) {
	parentOrder, err := s.parentOrderS.Get(ctx, orderParentID)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	orders, err := s.ListByParentOrderID(ctx, orderParentID)
	if err != nil {
		return model.YeeWechatResult{}, err
	}
	if len(orders) < 1 {
		s.l.Errorf("去支付，查询父单下无订单:%s", orderParentID.Hex())
		return model.YeeWechatResult{}, err
	}

	if parentOrder.PayStatus == model.PayStatusTypeClose {
		s.l.Infof("该订单已关闭，请重新下单%s", parentOrder.ID.Hex())
		return model.YeeWechatResult{}, xerr.NewErr(xerr.ErrOrder, nil, "该订单已关闭，请重新下单")
	}

	if parentOrder.PayStatus == model.PayStatusTypePaid {
		s.l.Infof("该订单已支付%s", parentOrder.ID.Hex())
		return model.YeeWechatResult{}, xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
	}

	//if parentOrder.PayStatus == model.PayStatusTypePending {
	//	s.l.Infof("正在支付中，请勿重复支付%s", parentOrder.ID.Hex())
	//	return model.WXPayResult{}, xerr.NewErr(xerr.ErrOrder, nil, "正在支付中，请勿重复支付")
	//}

	if parentOrder.Expire < time.Now().UnixMilli() {
		s.l.Infof("该订单已超时，请重新下单%s", parentOrder.ID.Hex())
		for _, order := range orders {
			err = s.UpdateOrderStatus(context.Background(), order.ID, model.PayStatusTypeClose, model.OrderStatusTypeClosed)
			if err != nil {
				s.l.Error("主动更新订单至超时未付关闭，错误", err)
			}
		}
		return model.YeeWechatResult{}, xerr.NewErr(xerr.ErrOrder, nil, "该订单已超时，请重新下单")
	}

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/trade/order")
	yopRequest.IsvPriKey = s.yeePay.ReqPriKey
	yopRequest.AppId = s.yeePay.AppID

	yopRequest.AddParam("orderId", util.NewUUIDNum()) // 合单收款场景中，此参数为合单收款请求号

	yopRequest.AddParam("parentMerchantNo", "***********")

	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayTradeOrder) // 接收支付结果的通知地址

	if parentOrder.CouponAmount > 0 {
		// 易宝营销信息-标识优惠券
		yopRequest.AddParam("ypPromotionInfo", `[{"type":"CUSTOM_ALLOWANCE"}]`)
	}

	// 子单域信息

	orderReq := make([]map[string]string, 0)

	for _, order := range orders {
		merchant, err := s.yeeMerchantS.GetBySupplier(ctx, order.SupplierID)
		if err != nil {
			return model.YeeWechatResult{}, err
		}

		paidAmountStr := util.DealMoneyToYuanStr(order.PaidAmount)
		m := make(map[string]string)
		m["merchantNo"] = merchant.MerchantNo
		m["orderId"] = util.NewUUIDNum()
		m["orderAmount"] = paidAmountStr
		m["goodsName"] = "水果商品订单"
		m["fundProcessType"] = "DELAY_SETTLE"

		if order.CouponSplitAmount > 0 {
			// 易宝营销信息-标识优惠券
			m["ypPromotionInfo"] = `[{"type":"CUSTOM_ALLOWANCE"}]`
		}

		orderReq = append(orderReq, m)
	}

	// 配送费
	finalDeliverFee := parentOrder.DeliverFeeRes.FinalDeliverFee

	if finalDeliverFee > 0 {
		finalDeliverFeeStr := util.DealMoneyToYuanStr(finalDeliverFee)
		merchantPoint, err := s.yeeMerchantS.GetYeeByPoint(ctx, parentOrder.ServicePointID)
		if err != nil {
			return model.YeeWechatResult{}, err
		}
		_ = merchantPoint
		m := map[string]string{
			"merchantNo": merchantPoint.MerchantNo, // 收款商户编号
			//"merchantNo":      "10090781432", // 测试
			"orderId":         util.NewUUIDNum(),
			"orderAmount":     finalDeliverFeeStr, // 商户收款金额，数字格式最多 2 位小数
			"goodsName":       "配送费",              // 简单描述订单信息或商品简介，用于展示在收银台页面或者支付明细中。(*当商品名称超过40个字符时，易宝会默认保留前40个字符)
			"fundProcessType": "DELAY_SETTLE",     // DELAY_SETTLE:分账
		}
		orderReq = append(orderReq, m)
	}

	orderReqM, err := json.Marshal(orderReq)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	yopRequest.AddParam("subOrderDetail", string(orderReqM))

	totalPaidAmountStr := util.DealMoneyToYuanStr(parentOrder.PaidAmount)
	yopRequest.AddParam("orderAmount", totalPaidAmountStr)

	//yopRequest.AddParam("expiredTime", "") // 订单过期时间，格式为yyyy-MM-dd HH:mm:ss，为空时默认在请求下单120分钟后失效，最长支持30天
	//yopRequest.AddParam("redirectUrl", "")   // 支付成功后跳转的URL，如商户指定页面回调地址 有走标准收银台的订单此地址才有作用
	//yopRequest.AddParam("payMerchantNo", "") // 支付商编 合单支付交易时，展示的收款商户编号

	bytes, err := json.Marshal(yopRequest.Params)
	zap.S().Infof("请求：%s", string(bytes))

	yopResp, err := client.DefaultClient.Request(yopRequest)
	if nil != err {
		return model.YeeWechatResult{}, err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	zap.S().Infof("响应：%s", string(marshal))

	var tradeRes model.TradeOrderRes
	err = json.Unmarshal(marshal, &tradeRes)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	dataRes := model.YeeWechatResult{
		OrderID:          tradeRes.OrderId,
		UniqueOrderNo:    tradeRes.UniqueOrderNo,
		Token:            tradeRes.Token,
		ParentMerchantNo: tradeRes.ParentMerchantNo,
	}

	err = s.parentOrderS.UpdateOne(ctx, bson.M{"_id": parentOrder.ID}, bson.M{
		"$set": bson.M{
			"yee_wechat_result": tradeRes,
		},
	})
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	return dataRes, nil
}

func (s orderService) YeeTradeOrderQuery(ctx context.Context, orderID, parentMerchantNo, merchantNo string) (model.TradeOrderQueryRes, error) {
	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/trade/order/query")

	yopRequest.AddParam("orderId", orderID)

	yopRequest.AddParam("parentMerchantNo", parentMerchantNo)
	yopRequest.AddParam("merchantNo", merchantNo)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.TradeOrderQueryRes{}, err
	}
	_ = yopResp

	var r model.TradeOrderQueryRes
	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.TradeOrderQueryRes{}, err
	}
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.TradeOrderQueryRes{}, err
	}

	return r, nil
}

func (s orderService) YeeTradeOrderQueryForCombine(ctx context.Context, orderParentID primitive.ObjectID) (any, error) {
	parentOrder, err := s.parentOrderS.Get(ctx, orderParentID)
	if err != nil {
		return nil, err
	}

	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/trade/order/combine-query")

	yopRequest.AddParam("orderId", parentOrder.YeeWechatResult.OrderID)

	yopRequest.AddParam("parentMerchantNo", parentOrder.YeeWechatResult.ParentMerchantNo)
	yopRequest.AddParam("merchantNo", parentOrder.YeeWechatResult.MerchantNo)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return nil, err
	}
	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	zap.S().Infof("响应：%s", string(marshal))

	m := make(map[string]any)
	err = json.Unmarshal(marshal, &m)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	subOrder := m["subOrderInfoList"]
	var r []model.YeeTradeSubOrder
	subOrderMarshal, err := json.Marshal(subOrder)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(subOrderMarshal, &r)
	if err != nil {
		return nil, err
	}

	return nil, nil
}

// YeeAggTutelagePrePay 聚合托管下单
func (s orderService) YeeAggTutelagePrePay(ctx context.Context, orderParentID primitive.ObjectID, openID, ip string) (model.YeeWechatResult, error) {
	parentOrder, err := s.parentOrderS.Get(ctx, orderParentID)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	if parentOrder.YeeWechatResult.AggOpenID == openID {
		return parentOrder.YeeWechatResult, nil
	}

	tradeOrderResult := parentOrder.YeeWechatResult
	if parentOrder.YeeWechatResult.OrderID == "" {
		tradeOrderResult, err = s.YeeTradeOrder(ctx, orderParentID)
		if err != nil {
			return model.YeeWechatResult{}, err
		}
	}

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/aggpay/tutelage/pre-pay")

	yopRequest.AddParam("payWay", "MINI_PROGRAM") // 小程序支付
	yopRequest.AddParam("channel", "WECHAT")
	yopRequest.AddParam("userIp", ip)
	yopRequest.AddParam("scene", "OFFLINE")

	yopRequest.AddParam("orderId", tradeOrderResult.OrderID)

	toYuan := util.DealMoneyToYuan(parentOrder.PaidAmount)

	// 商户收款请求号
	yopRequest.AddParam("orderAmount", toYuan) // 商户收款请求号
	yopRequest.AddParam("goodsName", "水果商品订单")

	yopRequest.AddParam("fundProcessType", "DELAY_SETTLE")
	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayAggTutelagePrePay) // 接收支付结果的通知地址
	yopRequest.AddParam("limitCredit", "N")                                                   // N:借贷记卡均可支付
	yopRequest.AddParam("token", tradeOrderResult.Token)                                      // 下单接口返回的token
	yopRequest.AddParam("userId", openID)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.YeeWechatResult{}, nil
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	m := make(map[string]string)
	err = json.Unmarshal(marshal, &m)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	tradeRes := tradeOrderResult
	tradeRes.AggOrderId = m["orderId"]
	tradeRes.AggUniqueOrderNo = m["uniqueOrderNo"]
	tradeRes.PrePayTn = m["prePayTn"]
	tradeRes.AppId = m["appId"]
	tradeRes.MiniProgramPath = m["miniProgramPath"]
	tradeRes.MiniProgramOrgId = m["miniProgramOrgId"]
	tradeRes.AggOpenID = openID

	err = s.parentOrderS.UpdateOne(ctx, bson.M{"_id": parentOrder.ID}, bson.M{
		"$set": bson.M{
			"pay_method":        model.PayMethodTypeYeeWechat,
			"yee_wechat_result": tradeRes,
		},
	})
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	return tradeRes, nil
}

// YeeAccountBookPay 记账薄支付
func (s orderService) YeeAccountBookPay(ctx context.Context, orderParentID primitive.ObjectID) error {
	//time.Sleep(time.Microsecond * 1000)
	//return nil
	parentOrder, err := s.parentOrderS.Get(ctx, orderParentID)
	if err != nil {
		return err
	}
	orders, err := s.ListByParentOrderID(ctx, orderParentID)
	if err != nil {
		return err
	}
	if len(orders) < 1 {
		s.l.Errorf("去支付，查询父单下无订单:%s", orderParentID.Hex())
		return err
	}

	if parentOrder.PayStatus == model.PayStatusTypeClose {
		s.l.Infof("该订单已关闭，请重新下单%s", parentOrder.ID.Hex())
		return xerr.NewErr(xerr.ErrOrder, nil, "该订单已关闭，请重新下单")
	}

	if parentOrder.PayStatus == model.PayStatusTypePaid {
		s.l.Infof("该订单已支付%s", parentOrder.ID.Hex())
		return xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
	}

	if parentOrder.PayStatus == model.PayStatusTypePending {
		s.l.Infof("该订单支付中%s", parentOrder.ID.Hex())
		return xerr.NewErr(xerr.ErrOrder, nil, "该订单支付中")
	}

	if parentOrder.Expire < time.Now().UnixMilli() {
		s.l.Infof("该订单已超时，请重新下单%s", parentOrder.ID.Hex())
		for _, order := range orders {
			err = s.UpdateOrderStatus(context.Background(), order.ID, model.PayStatusTypeClose, model.OrderStatusTypeClosed)
			if err != nil {
				s.l.Error("主动更新订单至超时未付关闭，错误", err)
			}
		}
		return xerr.NewErr(xerr.ErrOrder, nil, "该订单已超时，请重新下单")
	}

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/account/enterprise/account-book-pay/order")

	toYuan := util.DealMoneyToYuan(parentOrder.PaidAmount)

	merchant, err := s.yeeMerchantS.GetYeeByBuyer(ctx, parentOrder.BuyerID)
	if err != nil {
		return err
	}

	uuidNum := util.NewUUIDNum()

	parentMerchantNo := "***********"

	params := make(map[string]any)
	params["parentMerchantNo"] = parentMerchantNo
	params["merchantNo"] = parentMerchantNo
	params["ypAccountBookNo"] = merchant.AccountYpAccountBookNo

	orderInfo := make(map[string]any)

	orderInfo["orderId"] = uuidNum
	orderInfo["orderAmount"] = toYuan
	orderInfo["fundProcessType"] = "DELAY_SETTLE"
	orderInfo["goodsName"] = "水果商品订单"
	orderInfo["businessInfo"] = parentOrder.ID.Hex()
	orderInfo["notifyUrl"] = global.BackHost + global.NotifyUrlYeePayAccountBookPay

	if parentOrder.CouponAmount > 0 {
		// 易宝营销信息-标识优惠券
		orderInfo["ypPromotionInfo"] = `[{"type":"CUSTOM_ALLOWANCE"}]`
	}

	params["orderInfo"] = orderInfo

	yopRequest.Content = utils.ParseToJsonStr(params)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return nil
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	var r model.YeeAccountBookPayRes
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.Code != "UA00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.Message)
	}

	payStatus := model.PayStatusTypePending
	if r.OrderStatus == "SUCCESS" {
		payStatus = model.PayStatusTypePaid
	}

	err = s.parentOrderS.UpdateOne(ctx, bson.M{"_id": parentOrder.ID}, bson.M{
		"$set": bson.M{
			"pay_method":                           model.PayMethodTypeYeeBalance,
			"yee_wechat_result.order_id":           r.OrderId,
			"yee_wechat_result.unique_order_no":    r.UniqueOrderNo,
			"yee_wechat_result.parent_merchant_no": parentMerchantNo,
			"yee_wechat_result.merchant_no":        parentMerchantNo,
			"pay_status":                           payStatus,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

// CheckResendPaySMS 检查重发支付短信
func (s orderService) CheckResendPaySMS(ctx context.Context, parentOrder model.ParentOrder) (string, error) {
	authentication, err := s.authenticationS.GetByObject(ctx, parentOrder.BuyerID, model.ObjectTypeBuyer)
	if err != nil {
		return "", err
	}

	key := cachePaySMS + authentication.Mobile

	val := s.rdb.Exists(ctx, key).Val()
	if val > 0 {
		//	 存在
		return authentication.Mobile, nil
	}

	// 发送

	paySMS, err := s.ResendBalancePaySMS(ctx, parentOrder)
	if err != nil {
		return "", err
	}

	return paySMS, nil
}

func (s orderService) BalancePayConfirm(ctx context.Context, parentOrder model.ParentOrder, verificationCode, ip string) error {
	orders, err := s.ListByParentOrderID(ctx, parentOrder.ID)
	if len(orders) < 1 {
		s.l.Errorf("去支付，查询父单下无订单:%s", parentOrder.ID.Hex())
		return err
	}

	if parentOrder.PayStatus == model.PayStatusTypeClose {
		s.l.Infof("该订单已关闭，请重新下单%s", parentOrder.ID.Hex())
		return xerr.NewErr(xerr.ErrOrder, nil, "该订单已关闭，请重新下单")
	}

	if parentOrder.PayStatus == model.PayStatusTypePaid {
		s.l.Infof("该订单已支付%s", parentOrder.ID.Hex())
		return xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
	}

	if parentOrder.Expire < time.Now().UnixMilli() {
		s.l.Infof("该订单已超时，请重新下单%s", parentOrder.ID.Hex())
		for _, order := range orders {
			err = s.UpdateOrderStatus(context.Background(), order.ID, model.PayStatusTypeClose, model.OrderStatusTypeClosed)
			if err != nil {
				s.l.Error("主动更新订单至超时未付关闭，错误", err)
			}
		}
		return xerr.NewErr(xerr.ErrOrder, nil, "该订单已超时，请重新下单")
	}

	req := pays.PayByBackSMSReq{
		BizOrderNo:       parentOrder.BizOrderNo,
		BizUserId:        parentOrder.BizOrderNoResult.BizUserId,
		VerificationCode: verificationCode,
		ConsumerIp:       ip,
	}
	res, err := s.allInPayOrderS.PayByBackSMS(req)
	if err != nil {
		s.l.Errorf("确认支付错误%v", err)
		return err
	}

	_ = res

	marshal, _ := json.Marshal(res)
	zap.S().Infof("确认支付响应:%s", string(marshal))

	mnsSendService.NewMNSClient().SendCheckUserPay(parentOrder.ID.Hex())

	return nil
}

// ResendBalancePaySMS 重发支付短信验证码
func (s orderService) ResendBalancePaySMS(ctx context.Context, parentOrder model.ParentOrder) (string, error) {
	if parentOrder.PayStatus == model.PayStatusTypeClose {
		s.l.Infof("该订单已关闭，请重新下单%s", parentOrder.ID.Hex())
		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已关闭，请重新下单")
	}

	if parentOrder.PayStatus == model.PayStatusTypePaid {
		s.l.Infof("该订单已支付%s", parentOrder.ID.Hex())
		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
	}

	if parentOrder.Expire < time.Now().UnixMilli() {
		s.l.Infof("该订单已超时，请重新下单%s", parentOrder.ID.Hex())
		return "", xerr.NewErr(xerr.ErrOrder, nil, "该订单已超时，请重新下单")
	}

	req := pays.ResendPaySMSReq{
		BizOrderNo: parentOrder.BizOrderNo,
	}
	res, err := s.allInPayOrderS.ResendPaySMS(req)
	if err != nil {
		s.l.Errorf("重发支付短信验证码错误%v", err)
		return "", err
	}

	_ = res

	key := cachePaySMS + res.Phone

	val := s.rdb.Exists(ctx, key).Val()
	if val > 0 {
		//	 存在
		return "", nil
	}

	s.rdb.Set(ctx, key, 0, time.Second*170)

	return res.Phone, nil
}

func (s orderService) Create(ctx context.Context, buyer model.Buyer, req types.OrderCreateReq) (model.ParentOrder, error) {
	// 同一个采购商2秒内只能提交一次订单
	buyerKey := "createOrder:" + buyer.ID.Hex()
	val := s.rdb.Exists(ctx, buyerKey).Val()
	if val > 0 {
		return model.ParentOrder{}, xerr.NewErr(xerr.ErrParamError, nil, "请勿重复提交")
	} else {
		set := s.rdb.Set(ctx, buyerKey, "", time.Second*2)
		_ = set
	}

	var err error

	// 优惠券
	var couponUser model.CouponUser
	if req.CouponUserID != "" {
		couponUserID, err := util.ConvertToObjectWithCtx(ctx, req.CouponUserID)
		if err != nil {
			return model.ParentOrder{}, err
		}
		couponUser, err = s.couponUserS.Get(ctx, couponUserID)
		if err != nil {
			return model.ParentOrder{}, err
		}

		if couponUser.CouponStatus != model.CouponStatusTypeValid {
			return model.ParentOrder{}, xerr.NewErr(xerr.ErrParamError, nil, "优惠券已失效")
		}
	}

	now := time.Now().UnixMilli()

	var parentOrder model.ParentOrder

	orderExpire := time.Now().Add(time.Minute * time.Duration(s.orderExpireMinute)).UnixMilli() // 订单过期时间

	var address model.Address
	addressID, err := util.ConvertToObjectWithCtx(ctx, req.AddressID)
	if err != nil {
		return model.ParentOrder{}, xerr.NewErr(xerr.ErrParamError, nil, "地址缺失")
	}
	address, err = s.userAddrS.Get(ctx, addressID)
	if err != nil {
		return model.ParentOrder{}, err
	}

	servicePointID, _ := primitive.ObjectIDFromHex("647d77ef1db1e622b23c3339")

	firstPoint, err := s.servicePointS.Get(ctx, servicePointID)
	if err != nil {
		return model.ParentOrder{}, err
	}

	productTotal, products, mProduct, err := s.listProductInfo(ctx, req)
	if err != nil {
		return model.ParentOrder{}, err
	}

	err = s.checkUserType(buyer.UserType, products)
	if err != nil {
		return model.ParentOrder{}, err
	}

	deliverFeeRes, err := s.deliverFeeRuleS.CalcDistanceFee(ctx, address, req.DeliverType, req.InstantDeliverType, productTotal, 0, firstPoint)
	if err != nil {
		return model.ParentOrder{}, err
	}

	orderAddress := model.OrderAddress{
		AddressID: address.ID,
		Location:  address.Location,
		Contact:   address.Contact,
		Address:   address.Address,
	}

	// 订单列表
	var orders []model.Order

	// 商品总数
	//productCount := len(products)
	nowIndex := 1

	// 服务费
	serviceFeeType := buyer.ServiceFeeType

	// 销量
	mSoldCount := make(map[primitive.ObjectID]int)

	var couponSplitAmountTotal int // 已分配优惠额

	orderImage := make(map[primitive.ObjectID][]model.Product)

	mCart := make(map[string]primitive.ObjectID)

	// 已分配优惠额
	for orderIndex, v := range req.SupplierList {
		var pList []model.ProductOrder
		var sWeight int
		var sPaid int
		var sServiceFee int
		var sAmount int
		var sProductAmount int
		var sCouponAmount int // 供应商优惠额

		var pImageList []model.Product

		for _, per := range v.ProductList {
			pID, err := util.ConvertToObjectWithCtx(ctx, per.ProductID)
			if err != nil {
				return model.ParentOrder{}, err
			}
			pData := mProduct[pID]

			pImageList = append(pImageList, pData)

			mCart[per.SkuIDCode] = pID

			// 获取sku
			sku := getSku(per.SkuIDCode, pData.SkuList)
			price := sku.Price
			roughWeight := sku.RoughWeight
			outWeight := sku.OutWeight
			netWeight := sku.NetWeight

			totalWeight := roughWeight * per.Num

			// 商品总金额
			productAmountPer := price * per.Num

			// 服务费
			priceServiceFee, err := serviceFeeService.NewServiceFeeService().CalcServiceFeeForProduct(ctx, serviceFeeType, price)
			if err != nil {
				return model.ParentOrder{}, err
			}
			productServiceFee := priceServiceFee * per.Num

			pTotal := productAmountPer + productServiceFee

			// 优惠券
			productCouponAmount := backCouponAmount(couponUser, couponSplitAmountTotal, productTotal, productAmountPer)

			productRoughPrice, err := backPerRoughPrice(productAmountPer, totalWeight, pData.IsCheckWeight)
			couponRoughPrice, err := backPerRoughPrice(productCouponAmount, totalWeight, pData.IsCheckWeight)

			zap.S().Infof("商品：%s,总毛重单价:%d ", per.ProductID, productRoughPrice)

			if err != nil {
				return model.ParentOrder{}, err
			}

			settleUnitPrice := price
			if mProduct[pID].IsCheckWeight {
				settleUnitPrice = productRoughPrice
			}

			pPaid := pTotal - productCouponAmount // 实付

			perProduct := model.ProductOrder{
				ProductID:                     pID,
				CategoryIDs:                   pData.CategoryIDs,
				ProductTitle:                  mProduct[pID].Title,
				ProductCoverImg:               mProduct[pID].CoverImg,
				CostPrice:                     pData.CostPrice,
				Price:                         price,
				SettleUnitPrice:               settleUnitPrice,
				Num:                           per.Num,
				RoughWeight:                   roughWeight,
				OutWeight:                     outWeight,
				NetWeight:                     netWeight,
				ProductRoughWeightUnitPriceKG: productRoughPrice,
				UserPayRoughWeightUnitPriceKG: productRoughPrice,
				CouponRoughWeightUnitPriceKG:  couponRoughPrice,
				TotalWeight:                   totalWeight,
				SortNum:                       0,
				SortWeight:                    0,
				IsCheckWeight:                 pData.IsCheckWeight,
				SkuIDCode:                     sku.IDCode,
				SkuName:                       sku.Name,
				HasParam:                      pData.HasParam,
				ProductParamType:              pData.ProductParamType,
				// StandardAttr:                  pData.StandardAttr,
				// NonStandardAttr:               pData.NonStandardAttr,
				ProductAmount:     productAmountPer,
				TotalAmount:       pTotal,
				TotalTransportFee: 0,
				TransportFeePerKG: 0,
				TotalServiceFee:   productServiceFee,
				CouponSplitAmount: productCouponAmount,
				PaidAmount:        pPaid,
				CommissionPercent: pData.CommissionPercent,
				AfterSaleStatus:   model.AfterSaleStatusTypeNot,
				PurchaseNote:      pData.PurchaseNote,
				LinkBrandStatus:   pData.LinkBrandStatus,
				LinkBrandID:       pData.LinkBrandID,
				LinkBrandName:     pData.LinkBrandName,
			}

			//}

			pList = append(pList, perProduct)
			sWeight += perProduct.TotalWeight
			sPaid += perProduct.PaidAmount
			sServiceFee += perProduct.TotalServiceFee
			sAmount += perProduct.TotalAmount
			sProductAmount += perProduct.ProductAmount
			sCouponAmount += productCouponAmount
			nowIndex++
			//	销量，如果存在就相加，不存在就赋值
			if _, ok := mSoldCount[pID]; ok {
				mSoldCount[pID] += perProduct.Num
			} else {
				mSoldCount[pID] = perProduct.Num
			}

			couponSplitAmountTotal += productCouponAmount
		}

		supplierID, err := util.ConvertToObject(v.SupplierID)
		if err != nil {
			return model.ParentOrder{}, err
		}
		supplier, err := s.supplierS.Get(ctx, supplierID)
		if err != nil {
			return model.ParentOrder{}, err
		}

		osr := model.OrderStatusRecord{
			CreateOrderTime: now,
		}
		idNum := s.GenerateIDNum(ctx)

		orderCreatedAt := now + int64(orderIndex)

		order := model.Order{
			ID:                       primitive.NewObjectID(),
			IDNum:                    idNum,
			BuyerID:                  buyer.ID,
			UserType:                 buyer.UserType,
			BuyerName:                buyer.BuyerName,
			OrderType:                model.OrderTypeWholeSale,
			SupplierLevel:            supplier.Level,
			SupplierID:               supplierID,
			SupplierName:             supplier.ShopSimpleName,
			TotalAmount:              sAmount,
			TransportFeePerKG:        0,
			TotalTransportFee:        0,
			TotalServiceFee:          sServiceFee,
			ProductTotalAmount:       sProductAmount,
			ProductList:              pList,
			CouponUserID:             couponUser.ID,
			CouponAmount:             couponUser.CouponAmount,
			CouponMinAmount:          couponUser.MinAmount,
			CouponTitle:              couponUser.Title,
			CouponSplitAmount:        sCouponAmount,
			PaidAmount:               sPaid,
			Address:                  orderAddress,
			ServicePointID:           firstPoint.ID,
			ServicePointName:         firstPoint.Name,
			PayStatus:                model.PayStatusTypeToPay,
			OrderStatus:              model.OrderStatusTypeToStockUp,
			CreatedAt:                orderCreatedAt,
			DeliverType:              req.DeliverType,
			DeliverFeeRes:            deliverFeeRes,
			InstantDeliverType:       deliverFeeRes.InstantDeliverType,
			InstantDeliverName:       deliverFeeRes.InstantDeliverName,
			OrderNote:                req.OrderNote,
			Expire:                   orderExpire,
			OrderStatusRecord:        osr,
			DeliveryImgList:          []model.FileInfo{},
			LogisticsImageList:       []model.FileInfo{},
			LogisticsNoList:          []string{},
			LogisticsAutoReceiveHour: 0,
			LogisticsName:            req.LogisticsName,
		}

		orderImage[order.ID] = pImageList

		orders = append(orders, order)
	}

	// 处理优惠券剩余金额
	if couponUser.ID != primitive.NilObjectID && couponUser.CouponAmount > 0 {
		// 检查优惠券分摊金额是否已经达到优惠券总金额
		if couponSplitAmountTotal < couponUser.CouponAmount {
			// 计算剩余未分摊的优惠券金额
			remainingCouponAmount := couponUser.CouponAmount - couponSplitAmountTotal

			// 如果有剩余金额，将其分配到第一个订单的第一个商品
			if len(orders) > 0 && len(orders[0].ProductList) > 0 && remainingCouponAmount > 0 {
				orders[0].CouponSplitAmount += remainingCouponAmount
				orders[0].PaidAmount -= remainingCouponAmount

				// 商品优惠额
				orders[0].ProductList[0].CouponSplitAmount += remainingCouponAmount
				orders[0].ProductList[0].PaidAmount -= remainingCouponAmount
				s.l.Infof("优惠券剩余金额 %d 分配到订单 %s 的第一个商品", remainingCouponAmount, orders[0].IDNum)
			}
		}
	}

	// 父单数据
	parentOrder.ID = primitive.NewObjectID()
	parentOrder.UserID = buyer.UserID
	parentOrder.BuyerID = buyer.ID
	parentOrder.ServicePointID = firstPoint.ID

	parentOrder.ProductTotalAmount = productTotal
	parentOrder.PayStatus = model.PayStatusTypeToPay
	parentOrder.CreatedAt = now
	parentOrder.Expire = orderExpire
	parentOrder.DeliverType = req.DeliverType
	parentOrder.InstantDeliverType = deliverFeeRes.InstantDeliverType
	parentOrder.InstantDeliverName = deliverFeeRes.InstantDeliverName
	parentOrder.OrderNote = req.OrderNote
	parentOrder.DeliverFeeRes = deliverFeeRes

	parentOrder.LogisticsName = req.LogisticsName
	parentOrder.OrderType = model.OrderTypeWholeSale

	// 优惠券
	if couponUser.ID != primitive.NilObjectID && couponUser.CouponAmount > 0 {
		if couponUser.CouponAmount >= productTotal {
			return model.ParentOrder{}, xerr.NewErr(xerr.ErrParamError, nil, "商品总金额小于优惠券金额")
		}
		parentOrder.CouponUserID = couponUser.ID
		parentOrder.CouponTitle = couponUser.Title
		parentOrder.CouponAmount = couponUser.CouponAmount
		parentOrder.CouponMinAmount = couponUser.MinAmount
	}

	var parentTransportFee, parentTotalServiceFee int

	for i := range orders {
		orders[i].ParentOrderID = parentOrder.ID
		parentTransportFee += orders[i].TotalTransportFee
		parentTotalServiceFee += orders[i].TotalServiceFee
	}
	parentOrder.TotalServiceFee = parentTotalServiceFee
	parentOrder.TotalTransportFee = parentTransportFee

	allTotalAmount := productTotal + deliverFeeRes.FinalDeliverFee + parentTotalServiceFee + parentTransportFee

	parentOrder.TotalAmount = allTotalAmount
	parentOrder.PaidAmount = allTotalAmount - parentOrder.CouponAmount

	// jsonParentOrder, err := json.Marshal(parentOrder)
	// if err != nil {
	// 	return model.ParentOrder{}, err
	// }
	// zap.S().Info("parentOrder:", string(jsonParentOrder))

	// jsonOrders, err := json.Marshal(orders)
	// if err != nil {
	// 	return model.ParentOrder{}, err
	// }
	// zap.S().Info("orders:", string(jsonOrders))

	err = s.parentOrderS.Create(ctx, parentOrder)
	if err != nil {
		return model.ParentOrder{}, err
	}
	// 创建供应商拆单
	err = s.orderDao.CreateMany(ctx, orders)
	if err != nil {
		return model.ParentOrder{}, err
	}

	// 15分钟关闭父单
	s.mnsSendS.SendOrderCloseParent(ctx, parentOrder.ID, s.orderExpireMinute*60)

	s.mnsSendS.SendGenerateIntegral(model.RecordTypeOrder, buyer.ID, parentOrder.ID, parentOrder.ProductTotalAmount/100)

	// 发送优惠券已使用消息
	if parentOrder.CouponUserID != primitive.NilObjectID {
		s.mnsSendS.SendCouponUserUsed(couponUser.ID.Hex(), parentOrder.ID.Hex(), 2)
	}

	// 直接创建商品快照，传入订单ID
	for k, v := range orderImage {
		err = s.productImageS.Create(ctx, k, v, now)
		if err != nil {
			return model.ParentOrder{}, err
		}
	}

	// 删除购物车
	err = s.cartS.RemoveCartProduct(ctx, buyer.ID, mCart)
	if err != nil {
		return model.ParentOrder{}, err
	}

	// 更新商品销量
	err = s.productS.AddSoldCount(ctx, mSoldCount)
	if err != nil {
		return model.ParentOrder{}, err
	}

	zap.S().Info("order cost：", time.Now().UnixMilli()-now)

	return parentOrder, nil
}

func (s orderService) List(ctx context.Context, filter bson.M) ([]model.Order, error) {
	orders, err := s.orderDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return orders, nil
}

func (s orderService) ListWithOption(ctx context.Context, filter bson.M, findOpt *options.FindOptions) ([]model.Order, error) {
	orders, err := s.orderDao.ListWithOption(ctx, filter, findOpt)
	if err != nil {
		return nil, err
	}
	return orders, nil
}

func (s orderService) Count(ctx context.Context, filter bson.M) (int64, error) {
	i, err := s.orderDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return i, nil
}

func (s orderService) CountProduct(ctx context.Context, buyerID primitive.ObjectID) (int64, int64, error) {
	i, amount, err := s.orderDao.CountProductNum(ctx, buyerID)
	if err != nil {
		return 0, 0, err
	}
	return i, amount, nil
}

func (s orderService) GetLatestOrderTime(ctx context.Context, buyerID primitive.ObjectID) (int64, error) {
	filter := bson.M{
		"buyer_id":   buyerID,
		"pay_status": 4,
		//"order_status": bson.M{
		//	"$nin": []model.OrderStatusType{model.OrderStatusTypeCancel, model.OrderStatusTypeClosed},
		//},
	}
	one, count, err := s.orderDao.ListByPage(ctx, filter, 1, 1)
	if err != nil {
		return 0, err
	}
	_ = count
	var createdAt int64
	if len(one) > 0 {
		createdAt = one[0].CreatedAt
	}
	return createdAt, nil
}

func (s orderService) CountBuyer(ctx context.Context, productID primitive.ObjectID) (int64, int64, int64, error) {
	i, buyerNum, orderNum, err := s.orderDao.CountBuyerAndProductNum(ctx, productID)
	if err != nil {
		return 0, 0, 0, err
	}
	return i, buyerNum, orderNum, nil
}

func (s orderService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Order, int64, error) {
	orders, count, err := s.orderDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return orders, count, nil
}

func (s orderService) Get(ctx context.Context, id primitive.ObjectID) (model.Order, error) {
	order, err := s.orderDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.Order{}, err
	}
	return order, nil
}

func (s orderService) GetByIDNum(ctx context.Context, idNum string) (model.Order, error) {
	order, err := s.orderDao.Get(ctx, bson.M{"id_num": idNum})
	if err != nil {
		return model.Order{}, err
	}
	return order, nil
}

func (s orderService) GetByOutTradeNo(ctx context.Context, outTradeNo string) (model.Order, error) {
	filter := bson.M{
		"wx_pay_result.out_trade_no": outTradeNo,
	}
	order, err := s.orderDao.Get(ctx, filter)
	if err != nil {
		return model.Order{}, err
	}
	return order, nil
}

func (s orderService) GetByCus(ctx context.Context, filter bson.M) (model.Order, error) {
	order, err := s.orderDao.Get(ctx, filter)
	if err != nil {
		return model.Order{}, err
	}
	return order, nil
}

// GenerateIDNum 订单数字编号
var idNumLock sync.Mutex

func (s orderService) GenerateIDNum(ctx context.Context) string {
	idNumLock.Lock()
	defer idNumLock.Unlock()
	now := time.Now()
	key := "orderIDNum:" + now.Format("20060102")
	val := s.rdb.Incr(ctx, key).Val()

	s.rdb.Expire(ctx, key, time.Hour*24)

	orderNum := fmt.Sprintf("%04d", val)

	t := now.Format("20060102150405")

	id := t + orderNum

	return id
}

// ShipUploadInfo 上传发货信息
func (s orderService) ShipUploadInfo(ctx context.Context, orderIDList []primitive.ObjectID) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("ShipUploadInfo error:%v", err)
			return
		}
	}()

	if len(orderIDList) < 1 {
		return nil
	}

	orders, err := s.List(ctx, bson.M{
		"_id": bson.M{
			"$in": orderIDList,
		},
	})
	if err != nil {
		return err
	}

	var parentIDs []primitive.ObjectID

	for _, order := range orders {
		var f bool
		for _, d := range parentIDs {
			if order.ParentOrderID == d {
				f = true
				break
			}
		}
		if !f {
			parentIDs = append(parentIDs, order.ParentOrderID)
		}
	}

	parentOrders, err := s.parentOrderS.List(ctx, bson.M{
		"_id": bson.M{
			"$in": parentIDs,
		},
		"pay_method": model.PayMethodTypeWechat,
	})
	if err != nil {
		return err
	}

	for _, p := range parentOrders {
		status, err := s.mini.ShipStatus(p.WXPayResult.TransactionID)
		if err != nil {
			return err
		}
		if status == 1 {
			err = s.mini.ShipUploadInfo(p.WXPayResult.PayOpenID, p.WXPayResult.TransactionID, false)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

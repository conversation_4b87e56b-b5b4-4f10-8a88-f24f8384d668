package orderService

import (
	"base/core/xerr"
	"base/model"
	"base/types"
	"base/util"
	"context"
	"encoding/json"
	"math"

	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s orderService) listProductInfo(ctx context.Context, req types.OrderCreateReq) (int, []model.Product, map[primitive.ObjectID]model.Product, error) {
	var productTotal int

	var productIDs []primitive.ObjectID
	for _, v := range req.SupplierList {
		for _, per := range v.ProductList {
			productID, err := util.ConvertToObjectWithCtx(ctx, per.ProductID)
			if err != nil {
				return 0, nil, nil, err
			}
			productIDs = append(productIDs, productID)
		}
	}

	products, err := s.productS.ListByIDs(ctx, productIDs)
	if err != nil {
		return 0, nil, nil, err
	}

	mProduct := make(map[primitive.ObjectID]model.Product)
	for _, v := range products {
		mProduct[v.ID] = v
	}

	// 商品总价
	for _, v := range req.SupplierList {
		for _, per := range v.ProductList {
			productID, err := util.ConvertToObjectWithCtx(ctx, per.ProductID)
			if err != nil {
				return 0, nil, nil, err
			}
			sku := getSku(per.SkuIDCode, mProduct[productID].SkuList)
			productTotal += sku.Price * per.Num
		}
	}

	return productTotal, products, mProduct, nil
}

// func (s orderService) listProductInfoForRetail(ctx context.Context, req types.RetailOrderCreateReq) ([]model.Product, map[primitive.ObjectID]model.Product, error) {
// 	var productIDs []primitive.ObjectID

// 	for _, per := range req.ProductList {
// 		productID, err := util.ConvertToObjectWithCtx(ctx, per.ProductID)
// 		if err != nil {
// 			return nil, nil, err
// 		}
// 		productIDs = append(productIDs, productID)
// 	}
// 	products, err := s.productS.ListByIDs(ctx, productIDs)
// 	if err != nil {
// 		return nil, nil, err
// 	}

// 	mProduct := make(map[primitive.ObjectID]model.Product)
// 	for _, v := range products {
// 		mProduct[v.ID] = v
// 	}

// 	return products, mProduct, nil
// }

// func (s orderService) checkBuyLimit(supplierList []types.Supplier) error {
// 	if len(supplierList) > 10 {
// 		return xerr.NewErr(xerr.ErrParamError, nil, "下单供应商数不能超过20")
// 	}
// 	var sumSingleP int
// 	for _, supplier := range supplierList {
// 		sumSingleP += len(supplier.ProductList)
// 	}
// 	if sumSingleP > 100 {
// 		return xerr.NewErr(xerr.ErrParamError, nil, "下单单品数不能超过100")
// 	}
// 	return nil
// }

func (s orderService) checkUserType(t model.UserType, products []model.Product) error {
	for _, p := range products {
		if t == model.UserTypeYHT && t != p.UserType {
			return xerr.NewErr(xerr.ErrParamError, nil, "益禾堂客户请前往首页选择商品")
		}
	}
	return nil
}

func getSku(skuIDCode string, skuList []model.Sku) model.Sku {
	for _, i := range skuList {
		if i.IDCode == skuIDCode {
			return i
		}
	}
	return model.Sku{}
}

// 处理单一商品价格
//func (s orderService) dealOneProductPrice(ctx context.Context, p model.Product) {
//	discount, err := productService.NewProductService().BackValidDiscount(ctx, p.Num, p.DiscountList)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	_ = discount
//
//	price := p.Price
//	originPrice := p.OriginPrice
//	if originPrice == 0 {
//		originPrice = price
//	}
//
//	if discount > 0 {
//		priceDe := decimal.NewFromInt(int64(price))
//		discountDe := decimal.NewFromInt(int64(discount))
//		discountPrice := priceDe.Mul(discountDe).Div(decimal.NewFromInt(100)).Round(2).IntPart()
//
//		// 交换
//		originPrice = price
//
//		price = int(discountPrice)
//	}
//
//	productAmountPer := originPrice * p.Num
//
//	//totalTransportFeePer := orderService.BackTransportFee(route.FeePerKG, w)
//	productAmount += productAmountPer
//	//totalTransportFee += totalTransportFeePer
//	totalWeight += p.RoughWeight * p.Num
//
//	if originPrice > price {
//		diffAmount := (originPrice - price) * p.Num
//		totalDiscountAmount += diffAmount
//
//		productID, _ := util.ConvertToObjectWithCtx(ctx, p.ProductID)
//
//		discountAmountList = append(discountAmountList, DiscountAmountPer{
//			ProductID: productID,
//			Amount:    diffAmount,
//		})
//	}
//}

// 代金券
func (s orderService) dealCoupon(ctx context.Context, couponAccountID string, couponAmount, couponConditionAmount int) (model.CouponUser, error) {
	if len(couponAccountID) == 24 {
		id, err := util.ConvertToObjectWithNote(couponAccountID, "couponAccountID")
		if err != nil {
			return model.CouponUser{}, err
		}

		data, err := s.couponUserS.Get(ctx, id)
		if err != nil {
			return model.CouponUser{}, err
		}

		marshal, _ := json.Marshal(data)

		if data.CouponStatus != model.CouponStatusTypeValid {
			s.l.Errorf("优惠券状态不是可用状态，用户优惠券ID：%s，优惠券信息：%s", couponAccountID, string(marshal))
			return model.CouponUser{}, xerr.NewErr(xerr.ErrParamError, nil, "优惠券不可用")
		}

		if data.CouponAmount != couponAmount {
			s.l.Errorf("优惠券使用金额错误，用户优惠券ID：%s，请求使用金额：%d，优惠券信息：%s", couponAccountID, couponAmount, string(marshal))
			return model.CouponUser{}, xerr.NewErr(xerr.ErrOrder, nil)
		}

		return data, nil
	}

	return model.CouponUser{}, nil
}

// 返回各毛重单价
func backPerRoughPrice(productAmount, totalRoughWeight int, isCheckWeight bool) (int, error) {
	if !isCheckWeight {
		// 不计重
		return 0, nil
	}
	productAmountDe := decimal.NewFromInt(int64(productAmount)) // 商品总额
	//couponAmountDe := decimal.NewFromInt(int64(couponAmount))   // 优惠券

	w := decimal.NewFromInt(int64(totalRoughWeight)).Div(decimal.NewFromInt(1000)) // 毛重 -> kg

	allPayPer := productAmountDe.Div(w).Round(0).IntPart()

	//userPayPer := productAmountDe.Sub(couponAmountDe).Div(w).Round(0).IntPart()

	return int(allPayPer), nil
}

func toDec(num int) decimal.Decimal {
	return decimal.NewFromInt(int64(num))
}

// 返回单一商品优惠额度
func backCouponAmount(coupon model.CouponUser, couponSplitAmountTotal, productTotal, perProductAmount int) int {
	if coupon.CouponAmount == 0 {
		return 0
	}
	couponAmountDe := decimal.NewFromInt(int64(coupon.CouponAmount))

	productTotalDe := decimal.NewFromInt(int64(productTotal))

	perProductAmountDe := decimal.NewFromInt(int64(perProductAmount))

	// 比例
	percent := couponAmountDe.Div(productTotalDe)

	amountDe := percent.Mul(perProductAmountDe).Round(0).IntPart()

	amount := int(amountDe)

	if couponSplitAmountTotal+amount > coupon.CouponAmount {
		return coupon.CouponAmount - couponSplitAmountTotal
	}

	return amount
}

// 返回商品总价
func backTotalProductAmount(num int, product model.Product) int {
	return num * product.Weight.RoughWeight
}

// 运费手续费0.35%
func backTransportCommission(totalAmount int, moneyCommission float64) int {
	amount := float64(totalAmount) * moneyCommission
	// 上
	return int(math.Ceil(amount))
}

// 总商品抽成
func backTotalProductSplit(list []model.ProductOrder, mProduct map[primitive.ObjectID]model.Product) int {
	var amount float64
	for _, i := range list {
		amount += float64(i.ProductAmount) * float64(mProduct[i.ProductID].CommissionPercent) / 100
	}
	// 上
	return int(math.Ceil(amount))
}

func backPlatform(totalAmount int, moneyCommission float64) int {
	amount := float64(totalAmount) * moneyCommission
	//上
	return int(math.Ceil(amount))
}

// 集中仓-运费实际收入
func backWarehouseTransportAmount(totalAmount int, moneyCommission float64) int {
	amount := float64(totalAmount) * (1 - moneyCommission)
	return int(math.Floor(amount))
}

// 返回各角色的商品抽成
func backSplitProductCommission(total int, commission model.ServicePointCommission) (warehouse, point, platform int) {
	all := float64(total)

	// 集中仓-向下
	warehouse = int(math.Floor(all * float64(100-commission.PointPercent-commission.PlatformPercent) / 100))
	// 服务点-向下
	point = int(math.Floor(all * float64(100-commission.PointPercent) / 100))

	// 平台-剩余
	platform = total - warehouse - point
	return
}

// 供应商
func backSupplier(totalAmount int, moneyCommission float64) int {
	amount := float64(totalAmount) * moneyCommission
	return int(math.Floor(amount))
}

// 服务点
func backServicePoint(totalAmount int, moneyCommission float64) int {
	amount := float64(totalAmount) * moneyCommission
	return int(math.Floor(amount))
}

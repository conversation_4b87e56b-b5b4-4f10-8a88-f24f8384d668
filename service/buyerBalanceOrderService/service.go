package buyerBalanceOrderService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/buyerBalanceOrderDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"base/service/buyerBalanceAccountService"
	"base/service/miniService"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

// ServiceInterface 会员余额
type ServiceInterface interface {
	CreateRebate(ctx context.Context, buyerID, agentPayID, orderID primitive.ObjectID, amount int) error
	CreateAndPay(ctx context.Context, buyerID primitive.ObjectID, amount int, openID string) (interface{}, error)
	Withdraw(ctx context.Context, buyerID primitive.ObjectID, amount int) error
	ListWithdraw(ctx context.Context, payStatus model.PayStatusType) ([]model.BuyerBalanceOrder, error)
	AuditWithdraw(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M) ([]model.BuyerBalanceOrder, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.BuyerBalanceOrder, int64, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.BuyerBalanceOrder, error)
	UpdatePayStatus(ctx context.Context, res allinpay.NotifyPay) error
	CheckWithdrawOver(ctx context.Context, buyerID primitive.ObjectID) (bool, error)
	CheckWithdrawDay(ctx context.Context, buyerID primitive.ObjectID) error

	CloseByBizOrderNo(ctx context.Context, content string) error

	//	NotifyPayStatus 异步通知
	NotifyPayStatus(ctx context.Context, res allinpay.NotifyPay) error

	NotifyWithdrawPayStatus(ctx context.Context, res allinpay.NotifyPay) error
}

type buyerBalanceOrderService struct {
	mdb *mongo.Database
	rdb *redis.Client
	l   *zap.SugaredLogger

	authenticationS authenticationService.ServiceInterface

	mini miniService.ServiceInterface

	allInPayOrderS payModule.OrderService

	buyerBalanceAccountS buyerBalanceAccountService.ServiceInterface

	buyerBalanceOrderDao buyerBalanceOrderDao.DaoInt
}

func NewBuyerBalanceOrderService() ServiceInterface {
	return buyerBalanceOrderService{
		mdb: global.MDB,
		rdb: global.RDBDefault,
		l:   global.OrderLogger.Sugar(),

		authenticationS: authenticationService.NewAuthenticationService(),
		mini:            miniService.NewMiniService(),

		allInPayOrderS: payModule.NewOrderS(),

		buyerBalanceAccountS: buyerBalanceAccountService.NewBuyerBalanceAccountService(),

		buyerBalanceOrderDao: dao.BuyerBalanceOrderDao,
	}
}

func (s buyerBalanceOrderService) CreateAndPay(ctx context.Context, buyerID primitive.ObjectID, amount int, openID string) (interface{}, error) {
	key := "existBalanceDeposit:" + buyerID.Hex()
	val := s.rdb.Exists(ctx, key).Val()
	if val > 0 {
		return nil, xerr.NewErr(xerr.ErrParamError, nil, "操作频繁，稍后重试")
	} else {
		set := s.rdb.Set(ctx, key, "", time.Second*10)
		_ = set
	}

	now := time.Now().UnixMilli()
	data := model.BuyerBalanceOrder{
		ID:         primitive.NewObjectID(),
		BuyerID:    buyerID,
		BizOrderNo: util.NewUUID(),
		OrderType:  1,
		Amount:     amount,
		PayStatus:  model.PayStatusTypeToPay,
		CreatedAt:  now,
		UpdatedAt:  now,
	}
	err := s.buyerBalanceOrderDao.Create(ctx, data)
	if err != nil {
		return model.BuyerBalanceOrder{}, err
	}

	authentication, err := s.authenticationS.GetByBuyer(ctx, data.BuyerID)
	if err != nil {
		return nil, err
	}

	miniMethod := allinpay.PayMethodMiniProgramBackDeposit(amount, global.WechatAppID, openID)

	expire := util.ExpirePayOrderTime(global.OrderExpireMinute)

	req := pays.DepositApplyReq{
		BizOrderNo:          data.BizOrderNo,
		BizUserId:           authentication.PayBizUserId,
		AccountSetNo:        global.AllInPayAccountSetInfo.EscrowUserNo, // 托管专用账户集编号
		Amount:              amount,
		Fee:                 0, // 分账再收
		ValidateType:        0, // 无验证	0	整型	仅渠道验证，通商云不做交易验证
		BackUrl:             global.BackHost + global.BackUrlDepositBuyerBalance,
		OrderExpireDatetime: expire, // 支付订单过期
		PayMethod:           miniMethod,
		IndustryCode:        global.IndustryCode,
		IndustryName:        global.IndustryName,
		Source:              pays.SourceMobile,
		Summary:             "充值",
		ExtendInfo:          "",
	}

	res, err := s.allInPayOrderS.DepositApplyS(req)
	if err != nil {
		s.l.Error("充值支付错误", err)
		return "", err
	}

	ps := model.PayResult{}
	ps.PayStatus = res.PayStatus
	ps.PayOpenID = openID
	ps.PayFailMessage = res.PayFailMessage
	ps.OrderNo = res.OrderNo
	ps.BizUserId = res.BizUserId
	ps.BizOrderNo = res.BizOrderNo
	ps.ReqPayInterfaceNo = res.ReqPayInterfaceNo
	ps.PayInterfaceOutTradeNo = res.PayInterfaceOutTradeNo
	ps.PayInterfacetrxcode = res.PayInterfacetrxcode
	ps.ChannelFee = res.ChannelFee
	ps.Chnldata = res.Chnldata
	ps.ChannelPaytime = res.ChannelPaytime
	ps.Cusid = res.Cusid
	ps.Acct = res.Acct
	ps.TradeNo = res.TradeNo
	ps.ValidationType = res.ValidationType
	ps.MiniprogrampayinfoVsp = res.MiniprogramPayInfoVSP
	ps.ExtendInfo = res.ExtendInfo

	m := make(map[string]interface{})
	err = json.Unmarshal([]byte(res.PayInfo), &m)
	if err != nil {
		s.l.Errorf("充值订单解析pay_info错误:%v", err)
		return nil, err
	}

	ps.PayInfo = m

	err = s.buyerBalanceOrderDao.UpdateOne(ctx, bson.M{"biz_order_no": res.BizOrderNo}, bson.M{"$set": bson.M{
		"pay_status": model.PayStatusTypePending,
		"pay_result": ps,
		"updated_at": now,
	}})
	if err != nil {
		s.l.Errorf("更新充值调起支付异常%v", err)
		return "", err
	}

	//mnsSendService.NewMNSClient().SendCloseDepositOrder(model.MNSBizOrderNo{
	//	BizOrderNo: data.BizOrderNo,
	//})

	return m, nil
}
func (s buyerBalanceOrderService) CreateRebate(ctx context.Context, buyerID, agentPayID, orderID primitive.ObjectID, amount int) error {
	_, err := s.buyerBalanceOrderDao.Get(ctx, bson.M{
		"order_id": orderID,
	})

	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if !errors.Is(err, mongo.ErrNoDocuments) {
		//	存在
		return xerr.NewErr(xerr.ErrParamError, nil, "已存在")
	}

	now := time.Now().UnixMilli()
	data := model.BuyerBalanceOrder{
		ID:         primitive.NewObjectID(),
		BuyerID:    buyerID,
		OrderID:    orderID,
		BizOrderNo: "",
		OrderType:  3, // rebate
		Amount:     amount,
		PayStatus:  model.PayStatusTypePaid,
		Note:       "返利,支付单id：" + agentPayID.Hex(),
		CreatedAt:  now,
		UpdatedAt:  now,
	}
	err = s.buyerBalanceOrderDao.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s buyerBalanceOrderService) GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.BuyerBalanceOrder, error) {
	filter := bson.M{
		"biz_order_no": bizOrderNo,
	}
	data, err := s.buyerBalanceOrderDao.Get(ctx, filter)
	if err != nil {
		return model.BuyerBalanceOrder{}, err
	}
	return data, nil
}

func (s buyerBalanceOrderService) Withdraw(ctx context.Context, buyerID primitive.ObjectID, amount int) error {
	auth, err := s.authenticationS.GetByBuyer(ctx, buyerID)
	if err != nil {
		return err
	}

	// 检查余额情况
	userBalance, err := s.allInPayOrderS.QueryBalanceS(pays.QueryBalanceReq{
		BizUserId:    auth.PayBizUserId,
		AccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
	})
	if err != nil {
		return err
	}
	if userBalance.AllAmount < amount {
		return xerr.NewErr(xerr.ErrParamError, nil, "余额不足")
	}

	err = s.CheckWithdrawDay(ctx, buyerID)
	if err != nil {
		return err
	}

	fee := 0
	over, err := s.CheckWithdrawOver(ctx, buyerID)
	if err != nil {
		return err
	}

	if over {
		fee = 100
	}

	now := time.Now().UnixMilli()

	data := model.BuyerBalanceOrder{
		ID:                   primitive.NewObjectID(),
		BuyerID:              buyerID,
		BizOrderNo:           util.NewUUID(),
		Amount:               amount,
		OrderType:            2, // 提现
		PayStatus:            model.PayStatusTypePending,
		CreatedAt:            now,
		UpdatedAt:            now,
		Note:                 "提现",
		WithdrawFee:          fee,
		WithdrawBizUserID:    auth.PayBizUserId,
		WithdrawValidateType: 0,
		WithdrawBankCardNo:   auth.IndividualBankcardNo, // 个人银行卡
		WithdrawBankCardPro:  0,                         // 个人银行卡
		WithdrawAccountSetNo: global.AllInPayAccountSetInfo.EscrowUserNo,
	}

	err = s.buyerBalanceOrderDao.Create(ctx, data)
	if err != nil {
		return err
	}

	res, err := s.allInPayOrderS.WithdrawApplyS(pays.WithdrawApplyReq{
		BizOrderNo:          data.BizOrderNo,
		BizUserId:           data.WithdrawBizUserID,
		AccountSetNo:        global.AllInPayAccountSetInfo.EscrowUserNo, // 托管用户集
		Amount:              data.Amount,
		Fee:                 data.WithdrawFee,
		ValidateType:        0,
		BackUrl:             global.BackHost + global.BackUrlWithdrawBuyer,
		OrderExpireDatetime: util.WithDrawExpire(),
		BankCardNo:          data.WithdrawBankCardNo,
		BankCardPro:         data.WithdrawBankCardPro,
		WithdrawType:        "D0",
		IndustryCode:        global.IndustryCode,
		IndustryName:        global.IndustryName,
		Summary:             "提现",
		ExtendInfo:          "bizUserID" + data.WithdrawBizUserID,
		Source:              pays.SourceMobile,
		//PayMethod:           nil, // 支付方式 如不传，则默认为通联通代付
	})
	if err != nil {
		err = s.buyerBalanceOrderDao.UpdateOne(ctx, bson.M{
			"_id": data.ID,
		}, bson.M{
			"$set": bson.M{
				"pay_status": model.PayStatusTypeFail,
				"updated_at": time.Now().UnixMilli(),
			},
		})
		return err
	}

	// 赋值
	var ps model.WithdrawPayResult
	ps.PayStatus = res.PayStatus
	ps.PayFailMessage = res.PayFailMessage
	ps.BizUserId = res.BizUserId
	ps.OrderNo = res.OrderNo
	ps.BizOrderNo = res.BizOrderNo
	ps.ExtendInfo = res.ExtendInfo

	err = s.buyerBalanceOrderDao.UpdateOne(ctx, bson.M{
		"_id": data.ID,
	}, bson.M{
		"$set": bson.M{
			"pay_result": ps,
			"updated_at": time.Now().UnixMilli(),
		},
	})
	if err != nil {
		s.l.Errorf("更新提现申请结果错误，提现单%s，错误%v", data.ID.Hex(), err)
		return err
	}

	return nil
}

// CheckWithdrawOver 每月第二笔提现收取手续费
func (s buyerBalanceOrderService) CheckWithdrawOver(ctx context.Context, buyerID primitive.ObjectID) (bool, error) {
	now := time.Now().UnixMilli()

	begin, end, err := util.MonthScopeTimestamp(now)
	if err != nil {
		return false, err
	}

	filter := bson.M{
		"order_type": 2, // 提现
		"buyer_id":   buyerID,
		"created_at": bson.M{
			"$gte": begin,
			"$lt":  end,
		},
	}

	count, err := s.buyerBalanceOrderDao.Count(ctx, filter)
	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// CheckWithdrawDay 每日两笔提现
func (s buyerBalanceOrderService) CheckWithdrawDay(ctx context.Context, buyerID primitive.ObjectID) error {
	now := time.Now().UnixMilli()

	begin, end, err := util.DayScopeTimestamp(now)
	if err != nil {
		return err
	}

	filter := bson.M{
		"order_type": 2, // 提现
		"buyer_id":   buyerID,
		"created_at": bson.M{
			"$gte": begin,
			"$lt":  end,
		},
	}

	count, err := s.buyerBalanceOrderDao.Count(ctx, filter)
	if err != nil {
		return err
	}
	if count > 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "每日最多两笔提现，特殊情况请咨询客服")
	}

	return nil
}

func (s buyerBalanceOrderService) ListWithdraw(ctx context.Context, payStatus model.PayStatusType) ([]model.BuyerBalanceOrder, error) {

	return nil, nil
}

func (s buyerBalanceOrderService) AuditWithdraw(ctx context.Context, id primitive.ObjectID) error {
	order, err := s.Get(ctx, id)
	if err != nil {
		return err
	}
	_ = order

	return nil
}

func (s buyerBalanceOrderService) CloseByBizOrderNo(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("deliverNoteService Generate error:%v", err)
			return
		}
	}()

	var data model.MNSBizOrderNo

	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}
	marshal, _ := json.Marshal(data)

	zap.S().Infof("关闭充值单信息：%s", string(marshal))

	depositOrder, err := s.GetByBizOrderNo(ctx, data.BizOrderNo)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if errors.Is(err, mongo.ErrNoDocuments) {
		zap.S().Infof("查询充值单信息：%s，不存在，跳过", string(marshal))
		return nil
	}

	if depositOrder.PayStatus == model.PayStatusTypePending {
		//	 关闭订单
		req := pays.CloseOrderReq{
			BizOrderNo: data.BizOrderNo,
		}
		resClose, err := s.allInPayOrderS.CloseOrderS(req)
		if err != nil {
			return err
		}
		bytes, _ := json.Marshal(resClose)
		zap.S().Infof("关闭信息：%s", string(bytes))

		filter := bson.M{
			"biz_order_no": data.BizOrderNo,
		}

		err = s.buyerBalanceOrderDao.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
			"pay_status": model.PayStatusTypeClose,
			"updated_at": time.Now().UnixMilli(),
		}})
	}

	return nil
}

// NotifyPayStatus 回调
func (s buyerBalanceOrderService) NotifyPayStatus(ctx context.Context, res allinpay.NotifyPay) error {
	// 只有成功才通知
	if res.Status == "OK" {
		//“OK”标识支付成功；
		err := s.UpdatePayStatus(ctx, res)
		if err != nil {
			s.l.Error("充值 更新支付成功状态错误：", err)
			return err
		}
	}

	marshal, _ := json.Marshal(res)
	s.l.Infof("NotifyPayStatus %s", string(marshal))

	return nil
}

func (s buyerBalanceOrderService) UpdatePayStatus(ctx context.Context, res allinpay.NotifyPay) error {
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		data, err := s.GetByBizOrderNo(ctx, res.BizOrderNo)
		if err != nil {
			return nil, err
		}
		filter := bson.M{"_id": data.ID}
		err = s.buyerBalanceOrderDao.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
			"pay_status":                            model.PayStatusTypePaid,
			"pay_result.status":                     res.Status,
			"pay_result.channel_paytime":            res.ChannelPaytime,
			"pay_result.cusid":                      res.Cusid,
			"pay_result.biz_order_no":               res.BizOrderNo,
			"pay_result.biz_user_id":                res.BuyerBizUserId,
			"pay_result.amount":                     res.Amount,
			"pay_result.channel_fee":                res.ChannelFee,
			"pay_result.pay_interfacetrxcode":       res.PayInterfacetrxcode,
			"pay_result.pay_interface_out_trade_no": res.PayInterfaceOutTradeNo, // 商户单号
			"pay_result.termrefnum":                 res.Termrefnum,
			"pay_result.termauthno":                 res.Termauthno,
			"pay_result.chnltrxid":                  res.Chnltrxid,
			"pay_result.pay_datetime":               res.PayDatetime,
			"pay_result.acct":                       res.Acct,
		}})

		err = s.buyerBalanceAccountS.UpdateDeposit(ctx, data.BuyerID, data.Amount)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})

	if err != nil {
		s.l.Error("更新充值订单状态错误:", err)
		return err
	}
	return nil
}

func (s buyerBalanceOrderService) List(ctx context.Context, filter bson.M) ([]model.BuyerBalanceOrder, error) {
	list, err := s.buyerBalanceOrderDao.List(ctx, filter)
	return list, err
}

func (s buyerBalanceOrderService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.BuyerBalanceOrder, int64, error) {
	list, count, err := s.buyerBalanceOrderDao.ListByPage(ctx, filter, page, limit)
	return list, count, err
}

func (s buyerBalanceOrderService) Get(ctx context.Context, id primitive.ObjectID) (model.BuyerBalanceOrder, error) {
	data, err := s.buyerBalanceOrderDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.BuyerBalanceOrder{}, err
	}
	return data, nil
}

// NotifyWithdrawPayStatus 回调
func (s buyerBalanceOrderService) NotifyWithdrawPayStatus(ctx context.Context, res allinpay.NotifyPay) error {
	// 订单成功、订单失败
	filter := bson.M{"biz_order_no": res.BizOrderNo}
	update := bson.M{
		"pay_result.status": res.Status,
		//"withdraw_pay_result.channel_extend_info":        res.ChannelExtendInfo, // 空
		"pay_result.pay_interface_out_trade_no": res.PayInterfaceOutTradeNo,
	}

	if res.Status == "OK" {
		update["pay_status"] = model.PayStatusTypePaid
		update["result.pay_status"] = "success"
		update["result.pay_date_time"] = res.PayDatetime
		update["result.acct"] = res.Acct
	} else {
		s.l.Errorf("提现回调失败状态：%s,bizOrderNo%s", res.Status, res.BizOrderNo)
		update["pay_status"] = model.PayStatusTypeFail
		update["withdraw_pay_result.pay_status"] = "fail"
	}
	err := s.buyerBalanceOrderDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}

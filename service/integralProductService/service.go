package integralProductService

import (
	"base/dao"
	"base/dao/integralProductDao"
	"base/global"
	"base/model"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, data model.IntegralProduct) error
	UpdateData(ctx context.Context, data model.IntegralProduct) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	UpdateStock(ctx context.Context, id primitive.ObjectID, stock int) error
	UpdateSort(ctx context.Context, id primitive.ObjectID, sort int) error
	UpdateStatus(ctx context.Context, id primitive.ObjectID, status model.IntegralProductStatus) error
	Get(ctx context.Context, id primitive.ObjectID) (model.IntegralProduct, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralProduct, int64, error)
}

type integralProductService struct {
	mdb                *mongo.Database
	rdb                *redis.Client
	IntegralProductDao integralProductDao.DaoInt
}

func NewIntegralProductService() ServiceInterface {
	return integralProductService{
		mdb:                global.MDB,
		rdb:                global.RDBDefault,
		IntegralProductDao: dao.IntegralProductDao,
	}
}

func (s integralProductService) Create(ctx context.Context, data model.IntegralProduct) error {
	err := s.IntegralProductDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s integralProductService) UpdateData(ctx context.Context, data model.IntegralProduct) error {
	err := s.IntegralProductDao.Replace(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s integralProductService) UpdateStock(ctx context.Context, id primitive.ObjectID, stock int) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"stock":      stock,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.IntegralProductDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s integralProductService) UpdateSort(ctx context.Context, id primitive.ObjectID, sort int) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"sort":       sort,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.IntegralProductDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s integralProductService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	now := time.Now().UnixMilli()
	update := bson.M{
		"deleted_at": now,
		"updated_at": now,
	}
	err := s.IntegralProductDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s integralProductService) UpdateStatus(ctx context.Context, id primitive.ObjectID, status model.IntegralProductStatus) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"status":     status,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.IntegralProductDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s integralProductService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralProduct, int64, error) {
	list, i, err := s.IntegralProductDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s integralProductService) Get(ctx context.Context, id primitive.ObjectID) (model.IntegralProduct, error) {
	filter := bson.M{
		"_id": id,
	}
	i, err := s.IntegralProductDao.Get(ctx, filter)
	if err != nil {
		return model.IntegralProduct{}, err
	}
	return i, nil
}

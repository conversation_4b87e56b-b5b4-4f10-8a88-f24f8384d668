package orderPointService

import (
	"base/core/xerr"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/service/buyerService"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/orderStatusRecordService"
	"base/service/orderStockUpService"
	"base/service/servicePointService"
	"base/types"
	"base/util"
	"context"
	"time"

	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	ListToArrive(ctx context.Context, servicePointID primitive.ObjectID) ([]model.Order, error)
	ListToSelfGet(ctx context.Context, servicePointID primitive.ObjectID) ([]model.Order, error)
	ListSelfGet(ctx context.Context, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error)
	ListInstant(ctx context.Context, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error)
	ListLogistics(ctx context.Context, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error)
	ListToAllot(ctx context.Context, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error)
	ListByBuyerAndPoint(ctx context.Context, buyerID, addressID, servicePointID, deliveryUserID primitive.ObjectID, begin, end int64, receiveHas bool) ([]model.Order, error)
	ListByBuyerAndPointSelfGet(ctx context.Context, buyerID, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error)
	ListByBuyerAndPointInstant(ctx context.Context, buyerID, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error)
	ListByBuyerAndPointLogistics(ctx context.Context, buyerID, servicePointID, addressID primitive.ObjectID, begin, end int64) ([]model.Order, error)
	DoAllot(ctx context.Context, buyerID []primitive.ObjectID, servicePointID primitive.ObjectID, deliveryUser model.DeliveryMan, begin, end int64) error
	AllotList(ctx context.Context, servicePointID, deliveryUserID primitive.ObjectID, begin, end int64, receiverHas bool) ([]model.Order, error)
	ListToDeliver(ctx context.Context, servicePointID primitive.ObjectID) ([]model.Order, error)
	ListOrders(ctx context.Context, servicePointID primitive.ObjectID, page, limit, begin, end int64) ([]model.Order, int64, error)
	DoArrive(ctx context.Context, orderIDs []primitive.ObjectID) error
	DoReceive(ctx context.Context, orderID primitive.ObjectID, imgList []model.FileInfo, now int64) error
	AutoReceive(ctx context.Context, content string) error
	LogisticsCreate(ctx context.Context, orderIDs []primitive.ObjectID, noList []string, imgList []model.FileInfo, autoReceiveHour int) error
	UpdateQuality(ctx context.Context, id primitive.ObjectID, num int) error

	UpdateSort(ctx context.Context, req types.OrderSortReq) error
	ListSort(ctx context.Context, servicePointID primitive.ObjectID, timestamp int64) (interface{}, error)
	ListSortOrder(ctx context.Context, id primitive.ObjectID) (interface{}, error)
	//CheckAfterShip(ctx context.Context) error

	//	品控

	ListOrderToStockUp(ctx context.Context, servicePointID, stationID primitive.ObjectID, endTimestamp int64) ([]model.Order, error)
}

type orderPointService struct {
	mdb                *mongo.Database
	rdb                *redis.Client
	l                  *zap.SugaredLogger
	orderS             orderService.ServiceInterface
	buyerS             buyerService.ServiceInterface
	orderStockUpS      orderStockUpService.ServiceInterface
	orderRefundS       orderRefundService.ServiceInterface
	orderDebtS         orderDebtService.ServiceInterface
	servicePointS      servicePointService.ServiceInterface
	orderStatusRecordS orderStatusRecordService.ServiceInterface
}

func NewOrderPointService() ServiceInterface {
	return orderPointService{
		mdb:                global.MDB,
		rdb:                global.RDBDefault,
		l:                  global.OrderLogger.Sugar(),
		buyerS:             buyerService.NewBuyerService(),
		orderS:             orderService.NewOrderService(),
		orderStockUpS:      orderStockUpService.NewOrderStockUpService(),
		orderRefundS:       orderRefundService.NewOrderRefundService(),
		orderDebtS:         orderDebtService.NewOrderDebtService(),
		servicePointS:      servicePointService.NewServicePointService(),
		orderStatusRecordS: orderStatusRecordService.NewOrderStatusRecordService(),
	}
}

func (s orderPointService) ListToArrive(ctx context.Context, servicePointID primitive.ObjectID) ([]model.Order, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
		"order_status":     model.OrderStatusTypeToArrive,
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) ListToSelfGet(ctx context.Context, servicePointID primitive.ObjectID) ([]model.Order, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
		"deliver_type": bson.M{
			"$in": bson.A{model.DeliverTypeSelfPickUp, model.DeliverTypeLogistics},
		},
		"order_status": model.OrderStatusTypeToReceive,
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) ListSelfGet(ctx context.Context, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
		//"deliver_type": bson.M{
		//	"$in": bson.A{model.DeliverTypeSelfPickUp, model.DeliverTypeLogistics},
		//},
		"deliver_type": model.DeliverTypeSelfPickUp,
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		},
		"order_status_record.arrive_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

// ListInstant 即时配送
func (s orderPointService) ListInstant(ctx context.Context, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
		//"deliver_type": bson.M{
		//	"$in": bson.A{model.DeliverTypeSelfPickUp, model.DeliverTypeLogistics},
		//},
		"deliver_type": model.DeliverTypeInstantDeliver,
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		},
		"order_status_record.arrive_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) ListLogistics(ctx context.Context, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
		//"deliver_type": bson.M{
		//	"$in": bson.A{model.DeliverTypeSelfPickUp, model.DeliverTypeLogistics},
		//},
		"deliver_type": model.DeliverTypeLogistics,
		"order_status": bson.M{
			"$in": bson.A{model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		},
		"order_status_record.arrive_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) ListToDeliver(ctx context.Context, servicePointID primitive.ObjectID) ([]model.Order, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
		"deliver_type":     model.DeliverTypeDoor,
		"order_status":     model.OrderStatusTypeToReceive,
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) ListToAllot(ctx context.Context, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error) {
	filter := bson.M{
		"service_point_id":   servicePointID,
		"delivery_allot_has": false,
		"deliver_type":       model.DeliverTypeDoor,
		"order_status":       model.OrderStatusTypeToReceive,
		"order_status_record.ship_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) AllotList(ctx context.Context, servicePointID, deliveryUserID primitive.ObjectID, begin, end int64, receiverHas bool) ([]model.Order, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
		//"delivery_allot_has": true,
		"deliver_type": model.DeliverTypeDoor,
		"order_status": model.OrderStatusTypeToReceive,
		"order_status_record.ship_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}
	if deliveryUserID != primitive.NilObjectID {
		filter["delivery_user_id"] = deliveryUserID
	}
	if receiverHas {
		filter["order_status"] = model.OrderStatusTypeFinish
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) ListByBuyerAndPoint(ctx context.Context, buyerID, addressID, servicePointID, deliveryUserID primitive.ObjectID, begin, end int64, receiverHas bool) ([]model.Order, error) {
	filter := bson.M{
		"buyer_id":           buyerID,
		"service_point_id":   servicePointID,
		"deliver_type":       model.DeliverTypeDoor,
		"order_status":       model.OrderStatusTypeToReceive,
		"address.address_id": addressID,
		"order_status_record.ship_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	if deliveryUserID != primitive.NilObjectID {
		filter["delivery_user_id"] = deliveryUserID
	}

	if receiverHas {
		filter["order_status"] = model.OrderStatusTypeFinish
	}

	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) ListByBuyerAndPointSelfGet(ctx context.Context, buyerID, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error) {
	filter := bson.M{
		"buyer_id":         buyerID,
		"service_point_id": servicePointID,
		//"deliver_type": bson.M{
		//	"$in": bson.A{model.DeliverTypeSelfPickUp, model.DeliverTypeLogistics},
		//},
		"deliver_type": model.DeliverTypeSelfPickUp,
		//"order_status":     model.OrderStatusTypeToReceive,
		"order_status_record.ship_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) ListByBuyerAndPointInstant(ctx context.Context, buyerID, servicePointID primitive.ObjectID, begin, end int64) ([]model.Order, error) {
	filter := bson.M{
		"buyer_id":         buyerID,
		"service_point_id": servicePointID,
		//"deliver_type": bson.M{
		//	"$in": bson.A{model.DeliverTypeSelfPickUp, model.DeliverTypeLogistics},
		//},
		"deliver_type": model.DeliverTypeInstantDeliver,
		//"order_status":     model.OrderStatusTypeToReceive,
		"order_status_record.ship_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) ListByBuyerAndPointLogistics(ctx context.Context, buyerID, servicePointID, addressID primitive.ObjectID, begin, end int64) ([]model.Order, error) {
	filter := bson.M{
		"buyer_id":         buyerID,
		"service_point_id": servicePointID,
		//"deliver_type": bson.M{
		//	"$in": bson.A{model.DeliverTypeSelfPickUp, model.DeliverTypeLogistics},
		//},
		"address.address_id": addressID,
		"deliver_type":       model.DeliverTypeLogistics,
		//"order_status":     model.OrderStatusTypeToReceive,
		"order_status_record.ship_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	_ = orders

	return orders, nil
}

func (s orderPointService) DoAllot(ctx context.Context, buyerIDs []primitive.ObjectID, servicePointID primitive.ObjectID, deliveryUser model.DeliveryMan, begin, end int64) error {
	filter := bson.M{
		"buyer_id": bson.M{
			"$in": buyerIDs,
		},
		"service_point_id":   servicePointID,
		"deliver_type":       model.DeliverTypeDoor,
		"delivery_allot_has": false,
		"order_status":       model.OrderStatusTypeToReceive,
		"order_status_record.ship_time": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}
	//orders, err := s.orderS.List(ctx, filter)
	//if err != nil {
	//	return err
	//}
	//_ = orders
	//
	//var ids []primitive.ObjectID
	//for _, order := range orders {
	//	ids = append(ids, order.ID)
	//}
	//filter2 := bson.M{
	//	"_id": bson.M{
	//		"$in": ids,
	//	},
	//}
	update := bson.M{
		"$set": bson.M{
			"delivery_allot_has": true,
			"delivery_user_id":   deliveryUser.UserID,
			"delivery_user_name": deliveryUser.UserName,
		},
	}

	err := s.orderS.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

func (s orderPointService) ListOrders(ctx context.Context, servicePointID primitive.ObjectID, page, limit, begin, end int64) ([]model.Order, int64, error) {
	filter := bson.M{
		"second_point_id": servicePointID,
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
		"order_status": bson.M{
			"$nin": bson.A{model.OrderStatusTypeClosed, model.OrderStatusTypeCancel, model.OrderStatusTypeRefundAll},
		},
	}
	orders, count, err := s.orderS.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}

	return orders, count, nil
}

func (s orderPointService) DoArrive(ctx context.Context, orderIDs []primitive.ObjectID) error {
	filter := bson.M{
		"_id": bson.M{
			"$in": orderIDs,
		},
	}

	status := model.OrderStatusTypeToReceive
	key := model.BackRecordKey(status)

	now := time.Now().UnixMilli()

	update := bson.M{
		"$set": bson.M{
			"order_status":               status,
			"order_status_record." + key: now, // 状态记录
		},
	}
	err := s.orderS.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

func (s orderPointService) DoReceive(ctx context.Context, orderID primitive.ObjectID, imgList []model.FileInfo, now int64) error {
	filter := bson.M{
		"_id": orderID,
	}

	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		return err
	}
	if order.OrderStatus == model.OrderStatusTypeFinish {
		s.l.Infof("已经确认收货，订单Id%s，取消确认收货", orderID.Hex())
		return nil
	}

	if order.OrderStatus != model.OrderStatusTypeToReceive {
		s.l.Infof("订单未处于待收货状态，订单Id%s，取消确认收货", orderID.Hex())
		return nil
	}

	status := model.OrderStatusTypeFinish
	key := model.BackRecordKey(status)

	update := bson.M{
		"$set": bson.M{
			"delivery_img_list":          imgList,
			"order_status":               status,
			"order_status_record." + key: now, // 状态记录
		},
	}
	err = s.orderS.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = filter
	_ = update

	err = s.buyerS.UpdateActiveExpire(ctx, order.BuyerID)
	if err != nil {
		s.l.Errorf("更新buyer expire异常%v", err)
		err = nil
	}

	seconds := time.Now().Add(time.Hour * 48).Unix()
	seconds = seconds - time.Now().Unix()
	mnsSendService.NewMNSClient().SendNormalOrderDivide(ctx, orderID, seconds)

	// 配送费
	mnsSendService.NewMNSClient().SendDeliverFeeDivide(ctx, order.ParentOrderID)

	// 配送费明细生成
	mnsSendService.NewMNSClient().SendDeliverFeeDetailGenerate(ctx, order.ParentOrderID)

	//if order.CouponAccount.CouponID.Hex() == model.NewUserRewardCouponID {
	//	// 邀新奖励券
	//	mnsSendService.NewMNSClient().SendActiveRewardCoupon(order.CouponAccount.ID)
	//}

	//	 检查是否需要激活
	//if order.CouponAccount.CouponID.Hex() == model.NewUserInviteCouponID {
	//	mnsSendService.NewMNSClient().SendUpdateInviteStatus(order.UserID, model.InviteStatusReceived)
	//}

	return nil
}

type MNSAutoReceive struct {
	OrderID string `json:"order_id"`
}

func (s orderPointService) AutoReceive(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("AutoReceive error:%v", err)
			return
		}
	}()

	s.l.Infof("AutoReceive--content:%s", content)

	var data MNSAutoReceive
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	orderID, err := util.ConvertToObjectWithCtx(ctx, data.OrderID)
	if err != nil {
		return err
	}

	filter := bson.M{
		"_id": orderID,
	}

	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		return err
	}

	if order.OrderStatus != model.OrderStatusTypeToReceive {
		s.l.Infof("订单未处于待收货，订单Id%s，取消确认收货", orderID.Hex())
		return nil
	}

	if order.OrderStatus == model.OrderStatusTypeFinish {
		s.l.Infof("已经确认收货，订单Id%s，取消确认收货", orderID.Hex())
		return nil
	}

	status := model.OrderStatusTypeFinish
	key := model.BackRecordKey(status)

	now := time.Now().UnixMilli()

	update := bson.M{
		"order_status":               status,
		"order_status_record." + key: now, // 状态记录
	}

	if len(order.DeliveryImgList) < 1 {
		imgList := []model.FileInfo{
			{
				Type: "image",
				Name: "sys/no-img.png",
			},
		}
		update["delivery_img_list"] = imgList
	}

	err = s.orderS.UpdateOne(ctx, filter, bson.M{
		"$set": update})
	if err != nil {
		return err
	}

	seconds := time.Now().Add(time.Hour * 48).Unix()
	seconds = seconds - time.Now().Unix()
	mnsSendService.NewMNSClient().SendNormalOrderDivide(ctx, orderID, seconds)

	// 配送费
	mnsSendService.NewMNSClient().SendDeliverFeeDivide(ctx, order.ParentOrderID)

	// 配送费明细生成
	mnsSendService.NewMNSClient().SendDeliverFeeDetailGenerate(ctx, order.ParentOrderID)

	//if order.CouponAccount.CouponID.Hex() == model.NewUserInviteCouponID {
	//	// 邀新奖励券
	//	mnsSendService.NewMNSClient().SendActiveRewardCoupon(order.CouponAccount.ID)
	//}
	//
	////	 检查是否需要激活
	//if order.CouponAccount.CouponID.Hex() == model.NewUserInviteCouponID {
	//	mnsSendService.NewMNSClient().SendUpdateInviteStatus(order.UserID, model.InviteStatusReceived)
	//}

	return nil
}

func (s orderPointService) ListSort(ctx context.Context, servicePointID primitive.ObjectID, timestamp int64) (interface{}, error) {
	ups, err := s.orderStockUpS.ListByServicePoint(ctx, servicePointID, timestamp)
	if err != nil {
		return nil, err
	}

	return ups, nil
}

func (s orderPointService) ListSortOrder(ctx context.Context, id primitive.ObjectID) (interface{}, error) {
	up, err := s.orderStockUpS.GetStockUp(ctx, id)
	if err != nil {
		return nil, err
	}
	var ids []primitive.ObjectID
	for _, order := range up.OrderList {
		ids = append(ids, order.OrderID)
	}

	return up, nil
}

type QualityStats struct {
	ServicePointID   primitive.ObjectID `json:"service_point_id"`
	ServicePointName string             `json:"service_point_name"`
	SupplierNum      int                `json:"supplier_num"`
	ProductNum       int                `json:"product_num"`
	ProductTypeNum   int                `json:"product_type_num"`
	OrderNum         int                `json:"order_num"`
	Weight           int                `json:"weight"`
}

// UpdateQuality 品控
func (s orderPointService) UpdateQuality(ctx context.Context, id primitive.ObjectID, num int) error {

	now := time.Now().UnixMilli()
	up, err := s.orderStockUpS.GetStockUp(ctx, id)
	if err != nil {
		return err
	}

	if !up.StockUpHas {
		return xerr.NewErr(xerr.ErrParamError, nil, "尚未备货，不能品控")
	}

	if num > up.StockUpHasNum {
		return xerr.NewErr(xerr.ErrParamError, nil, "品控数不可大于已备货数")
	}
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {

		err = s.orderStockUpS.UpdateOne(sessCtx, bson.M{"_id": id}, bson.M{"$set": bson.M{
			"quality_has_num": num,
			"quality_has":     true,
			"updated_at":      now,
		}})
		if err != nil {
			return nil, err
		}
		var oIDs []primitive.ObjectID
		for _, order := range up.OrderList {
			oIDs = append(oIDs, order.OrderID)
		}

		filter := bson.M{
			"_id": bson.M{"$in": oIDs},
			"order_status": bson.M{
				"$lte": model.OrderStatusTypeToSort, // 小于分拣环节的
			},
		}

		status := model.OrderStatusTypeToSort
		key := model.BackRecordKey(status)

		err = s.orderS.UpdateMany(sessCtx, filter, bson.M{"$set": bson.M{
			"order_status":               model.OrderStatusTypeToSort, // 待分拣
			"order_status_record." + key: now,                         // 状态记录
		}})
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

// UpdateSort 分拣
func (s orderPointService) UpdateSort(ctx context.Context, req types.OrderSortReq) error {
	id, err := util.ConvertToObjectWithNote(req.ID, "备货信息ID")
	if err != nil {
		return err
	}
	up, err := s.orderStockUpS.GetStockUp(ctx, id)
	if err != nil {
		return err
	}

	//// 校验
	//err = checkSort(up, req)
	//if err != nil {
	//	return err
	//}

	oList := up.OrderList
	for _, order := range req.OrderList {
		oID, err := util.ConvertToObjectWithNote(order.OrderID, "req sort order id")
		if err != nil {
			return err
		}
		for i, perOrder := range oList {
			if perOrder.OrderID == oID {
				oList[i].SortNum = order.Num
				if up.IsCheckWeight {
					oList[i].SortWeight = order.Weight
				}
			}
		}
	}
	var oIDs []primitive.ObjectID
	for _, order := range up.OrderList {
		oIDs = append(oIDs, order.OrderID)
	}
	now := time.Now().UnixMilli()

	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 更新备货单
		err = s.orderStockUpS.UpdateOne(sessCtx, bson.M{"_id": id}, bson.M{"$set": bson.M{
			"order_list": oList,
			"updated_at": now,
		}})

		// 更新订单
		filter := bson.M{
			"_id": bson.M{"$in": oIDs},
			"order_status": bson.M{
				"$lte": model.OrderStatusTypeToShip, // 小于发货环节的
			},
		}
		status := model.OrderStatusTypeToShip
		key := model.BackRecordKey(status)

		err = s.orderS.UpdateMany(sessCtx, filter, bson.M{"$set": bson.M{
			"order_status":               status, // 待发货
			"order_status_record." + key: now,    // 状态记录
		}})
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

// LogisticsCreate 物流信息
func (s orderPointService) LogisticsCreate(ctx context.Context, orderIDs []primitive.ObjectID, noList []string, imgList []model.FileInfo, autoReceiveHour int) error {
	filter := bson.M{
		"_id": bson.M{"$in": orderIDs},
	}

	now := time.Now().UnixMilli()

	err := s.orderS.UpdateMany(ctx, filter, bson.M{"$set": bson.M{
		"logistics_auto_receive_hour": autoReceiveHour,
		"logistics_no_list":           noList,
		"logistics_image_list":        imgList,
		"logistics_time":              now,
	}})
	if err != nil {
		return err
	}

	// 延迟自动收货
	for _, d := range orderIDs {
		mnsSendService.NewMNSClient().SendOrderAutoReceive(d, autoReceiveHour)
	}

	return nil
}

func (s orderPointService) ListOrderToStockUp(ctx context.Context, servicePointID, stationID primitive.ObjectID, endTimestamp int64) ([]model.Order, error) {
	filter := bson.M{
		"created_at": bson.M{
			"$lte": endTimestamp,
		}, // 已支付
		"pay_status":       model.PayStatusTypePaid, // 已支付
		"service_point_id": servicePointID,
		"order_status":     model.OrderStatusTypeToStockUp, // 待备货
		"order_type":       model.OrderTypeWholeSale,
	}

	if stationID != primitive.NilObjectID {
		filter["supplier_level"] = model.SupplierLevelStation
		filter["station_id"] = stationID
	}

	list, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

//
//func (s orderPointService) CheckAfterShip(ctx context.Context) error {
//	s.l.Infof("------------CheckAfterShip-------------")
//	filter := bson.M{
//		"order_status":         model.OrderStatusTypeToArrive,
//		"has_check_after_ship": false,
//	}
//	orders, err := s.orderS.List(ctx, filter)
//	if err != nil {
//		return err
//	}
//	log.Println("len::", len(orders))
//
//	//s.orderStockUpS.ListStockOrder()
//
//	// has_check_after_ship
//	var debtList []primitive.ObjectID
//	for _, order := range orders {
//		create, err := s.orderDebtS.Create(ctx, order)
//		if err != nil {
//			return err
//		}
//		if create.ID != primitive.NilObjectID {
//			debtList = append(debtList, create.OrderID)
//			log.Println("do debt::", order.ID.Hex())
//		}
//	}
//
//	log.Println(len(debtList))
//
//	return nil
//}

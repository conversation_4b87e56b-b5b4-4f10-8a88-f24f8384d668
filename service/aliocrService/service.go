package aliocrService

import (
	"base/core/xerr"
	"base/global"
	"base/model"
	"encoding/json"
	ocr_api20210707 "github.com/alibabacloud-go/ocr-api-20210707/client"
	"github.com/alibabacloud-go/tea/tea"
)

type AliOcrService struct {
	ocr *ocr_api20210707.Client
}

func NewAliOcrService() *AliOcrService {
	return &AliOcrService{
		ocr: global.AliOcr,
	}
}

func (s AliOcrService) OcrLicense(url string) (model.BusinessLicense, error) {
	req := ocr_api20210707.RecognizeBusinessLicenseRequest{
		Url: tea.String(url),
	}
	res, err := s.ocr.RecognizeBusinessLicense(&req)
	if err != nil {
		return model.BusinessLicense{}, err
	}
	b := res.Body
	if b.Code != nil {
		return model.BusinessLicense{}, xerr.NewErr(xerr.ErrParamError, nil, *b.Message)
	}
	data := b.Data

	var r licenseRes
	err = json.Unmarshal([]byte(*data), &r)
	if err != nil {
		return model.BusinessLicense{}, err
	}

	d := r.Data
	resFmt := model.BusinessLicense{
		RegistrationNumber:  d.CreditCode,
		Name:                d.CompanyName,
		Type:                d.CompanyType,
		Address:             d.BusinessAddress,
		LegalRepresentative: d.LegalPerson,
		RegisteredCapital:   d.RegisteredCapital,
		FoundDate:           d.RegistrationDate,
		BusinessTerm:        d.ValidPeriod,
		BusinessScope:       d.BusinessScope,
	}

	return resFmt, nil
}

type licenseRes struct {
	Data struct {
		RegistrationDate  string `json:"RegistrationDate"`
		BusinessAddress   string `json:"businessAddress"`
		BusinessScope     string `json:"businessScope"`
		CompanyForm       string `json:"companyForm"`
		CompanyName       string `json:"companyName"`
		CompanyType       string `json:"companyType"`
		CreditCode        string `json:"creditCode"`
		LegalPerson       string `json:"legalPerson"`
		RegisteredCapital string `json:"registeredCapital"`
		ValidFromDate     string `json:"validFromDate"`
		ValidPeriod       string `json:"validPeriod"`
		ValidToDate       string `json:"validToDate"`
	} `json:"data"`
}

func (s AliOcrService) OcrIdCard(url string) (model.IDCardOcr, error) {
	req := ocr_api20210707.RecognizeIdcardRequest{
		Url: tea.String(url),
	}
	res, err := s.ocr.RecognizeIdcard(&req)
	if err != nil {
		if e, ok := err.(*tea.SDKError); ok {
			if *e.Code == *tea.String("illegalImageContent") {
				return model.IDCardOcr{}, xerr.NewErr(xerr.ErrOcrIdCardFront, nil, "请上传正确的图片")
			}
			if *e.Code == *tea.String("unmatchedImageType") {
				return model.IDCardOcr{}, xerr.NewErr(xerr.ErrOcrIdCardFront, nil, "请上传正确的图片")
			}
		}
		return model.IDCardOcr{}, err
	}
	b := res.Body
	if b.Code != nil {
		return model.IDCardOcr{}, xerr.NewErr(xerr.ErrParamError, nil, *b.Message)
	}
	data := b.Data

	var r idCardRes
	err = json.Unmarshal([]byte(*data), &r)
	if err != nil {
		return model.IDCardOcr{}, err
	}

	face := r.Data.Face.Data
	back := r.Data.Back.Data
	resFmt := model.IDCardOcr{
		Name:      face.Name,
		Sex:       face.Sex,
		Ethnicity: face.Ethnicity,
		Birth:     face.BirthDate,
		Address:   face.Address,
		Number:    face.IdNumber,
		//ValidFrom:       face,
		Issue: back.IssueAuthority,
	}

	return resFmt, nil
}

type idCardRes struct {
	Data struct {
		Face struct {
			Data struct {
				Name      string `json:"name"`
				Sex       string `json:"sex"`
				Ethnicity string `json:"ethnicity"`
				Address   string `json:"address"`
				BirthDate string `json:"birthDate"`
				IdNumber  string `json:"idNumber"`
			} `json:"data"`
		} `json:"Face"`
		Back struct {
			Data struct {
				IssueAuthority string `json:"issueAuthority"`
				ValidPeriod    string `json:"validPeriod"`
			} `json:"data"`
		} `json:"back"`
	} `json:"data"`
}

func (s AliOcrService) OcrBankCard(url string) (model.BankCardOcr, error) {
	req := ocr_api20210707.RecognizeBankCardRequest{
		Url: tea.String(url),
	}
	res, err := s.ocr.RecognizeBankCard(&req)
	if err != nil {
		if e, ok := err.(*tea.SDKError); ok {
			if *e.Code == *tea.String("illegalImageContent") {
				return model.BankCardOcr{}, xerr.NewErr(xerr.ErrOcrIdCardFront, nil, "请上传正确的图片")
			}
			if *e.Code == *tea.String("unmatchedImageType") {
				return model.BankCardOcr{}, xerr.NewErr(xerr.ErrOcrIdCardFront, nil, "请上传正确的图片")
			}
		}
		return model.BankCardOcr{}, err
	}
	b := res.Body
	if b.Code != nil {
		return model.BankCardOcr{}, xerr.NewErr(xerr.ErrParamError, nil, *b.Message)
	}
	data := b.Data

	var r bankCardRes
	err = json.Unmarshal([]byte(*data), &r)
	if err != nil {
		return model.BankCardOcr{}, err
	}

	d := r.Data

	resFmt := model.BankCardOcr{
		BankName:   d.BankName,
		CardNumber: d.CardNumber,
		ExpiryDate: d.ValidToDate,
	}

	return resFmt, nil
}

type bankCardRes struct {
	Data struct {
		BankName    string `json:"bankName"`
		CardNumber  string `json:"cardNumber"`
		ValidToDate string `json:"validToDate"`
		CardType    string `json:"cardType"`
	} `json:"data"`
}

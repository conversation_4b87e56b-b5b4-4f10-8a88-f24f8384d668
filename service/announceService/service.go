package announceService

import (
	"base/global"
	"base/model"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"strings"
	"time"
)

var cache = "announce:"

type ServiceInterface interface {
	Upsert(ctx context.Context, pointID primitive.ObjectID, content []string) error
	UpsertYHT(ctx context.Context, content string) error
	Get(ctx context.Context, pointID primitive.ObjectID) ([]string, error)
	GetYHT(ctx context.Context) (string, error)
	List(ctx context.Context) ([]model.Announce, error)
}

type announceService struct {
	rdb *redis.Client
}

func NewAnnounceService() ServiceInterface {
	return announceService{
		rdb: global.RDBDefault,
	}
}

func (s announceService) Upsert(ctx context.Context, pointID primitive.ObjectID, content []string) error {
	marshal, err := json.Marshal(content)
	if err != nil {
		return err
	}
	//con = content
	s.rdb.Set(ctx, cache+pointID.Hex(), string(marshal), time.Hour*24*60)

	return nil
}

func (s announceService) UpsertYHT(ctx context.Context, content string) error {
	s.rdb.Set(ctx, cache+"YHT", content, time.Hour*24*60)

	return nil
}

func (s announceService) Get(ctx context.Context, pointID primitive.ObjectID) ([]string, error) {
	bytes, err := s.rdb.Get(ctx, cache+pointID.Hex()).Bytes()
	if errors.Is(err, redis.Nil) {
		// 不存在
		return nil, nil
	}
	if err != nil {
		zap.S().Errorf("announceService 查询错误:%s,服务仓id：%s", err.Error(), pointID.Hex())
		return nil, nil
	}
	var content []string
	err = json.Unmarshal(bytes, &content)
	if err != nil {
		return nil, err
	}
	return content, nil
}
func (s announceService) GetYHT(ctx context.Context) (string, error) {
	val := s.rdb.Get(ctx, cache+"YHT").Val()
	return val, nil
}

func (s announceService) List(ctx context.Context) ([]model.Announce, error) {
	names := s.rdb.Keys(ctx, "announce:*").Val()

	contentList := make([]model.Announce, 0, len(names))

	for _, name := range names {
		bytes, err := s.rdb.Get(ctx, name).Bytes()
		if err != nil {
			zap.S().Errorf("announceService 查询错误%s", err.Error())
			return nil, nil
		}
		var content []string
		err = json.Unmarshal(bytes, &content)
		if err != nil {
			return nil, err
		}

		split := strings.Split(name, ":")

		id, err := util.ConvertToObjectWithCtx(ctx, split[1])
		if err != nil {
			return nil, err
		}

		contentList = append(contentList, model.Announce{
			ServicePointID: id,
			Content:        content,
		})
	}

	return contentList, nil
}

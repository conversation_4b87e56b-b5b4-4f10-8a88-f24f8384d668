package orderAdjustSettleService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/orderAdjustSettleDao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/service/orderService"
	"base/service/parentOrderService"
	"base/service/userService"
	"base/service/yeeMerchantService"
	"base/types"
	"base/util"
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/yop-platform/yop-go-sdk/yop/request"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInt 订单调整结算记录服务接口
type ServiceInt interface {
	Create(ctx context.Context, req types.OrderAdjustSettleCreateReq, userID primitive.ObjectID) error
	Update(ctx context.Context, adjustmentID primitive.ObjectID, req types.OrderAdjustSettleUpdateReq, userID primitive.ObjectID) error
	Get(ctx context.Context, adjustmentID primitive.ObjectID) (model.OrderAdjustSettle, error)
	List(ctx context.Context, req types.OrderAdjustSettleListReq) ([]model.OrderAdjustSettle, int64, error)
	ListByBuyer(ctx context.Context, req types.OrderAdjustSettleListReq) ([]model.OrderAdjustSettle, int64, error)
	ListBySupplier(ctx context.Context, req types.OrderAdjustSettleListReq) ([]model.OrderAdjustSettle, int64, error)
	Confirm(ctx context.Context, adjustmentID primitive.ObjectID) error
	Close(ctx context.Context, adjustmentID primitive.ObjectID) error
	GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.OrderAdjustSettle, error)
	ProcessRefund(ctx context.Context, content string) error
	DoRefundYee(ctx context.Context, adjustSettle model.OrderAdjustSettle) error
	YeeNotifyRefundAdjustSettle(ctx context.Context, notify model.YeeTradeRefundNotify) error
}

type orderAdjustSettleService struct {
	dao          orderAdjustSettleDao.DaoInt
	orderService orderService.ServiceInterface
	userService  userService.ServiceInterface
	parentOrderS parentOrderService.ServiceInterface
	yeeMerchantS yeeMerchantService.ServiceInterface
	yeePay       *global.YeePayInfo
	mdb          *mongo.Database
}

// NewService 创建订单调整结算记录服务
func NewService() ServiceInt {
	return &orderAdjustSettleService{
		dao:          dao.OrderAdjustSettleDao,
		orderService: orderService.NewOrderService(),
		userService:  userService.NewUserService(),
		parentOrderS: parentOrderService.NewParentOrderService(),
		yeeMerchantS: yeeMerchantService.NewYeeMerchantService(),
		yeePay:       global.YeePay,
		mdb:          global.MDB,
	}
}

// Create 创建订单调整结算记录
func (s *orderAdjustSettleService) Create(ctx context.Context, req types.OrderAdjustSettleCreateReq, userID primitive.ObjectID) error {
	orderID, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		return xerr.NewErr(xerr.ErrParamError, nil, "无效的订单ID")
	}

	// 检查该订单是否已存在调整结算记录
	exists, err := s.Exists(ctx, orderID)
	if err != nil {
		return err
	}
	if exists {
		return xerr.NewErr(xerr.ErrParamError, nil, "该订单已存在调整结算记录")
	}

	// 检查订单是否存在
	order, err := s.orderService.Get(ctx, orderID)
	if err != nil {
		return err
	}

	// 调用校验函数
	if err := s.validateProductAdjustSettle(ctx, order, req.ProductList, req.Remark); err != nil {
		return err
	}

	// 获取操作员信息
	user, err := s.userService.Get(ctx, userID)
	if err != nil {
		return xerr.NewErr(xerr.ErrParamError, nil, "操作员信息获取失败")
	}

	remark := util.DealWrap(req.Remark)

	// 构建商品调价明细
	var productAdjustments []model.ProductAdjustSettle
	var totalAdjustmentAmount int

	for _, productReq := range req.ProductList {
		// 将string类型的ProductID转换为ObjectID
		productID, err := util.ConvertToObjectWithCtx(ctx, productReq.ProductID)
		if err != nil {
			return xerr.NewErr(xerr.ErrParamError, nil, "无效的商品ID")
		}

		// 查找订单中对应的商品
		var orderProduct *model.ProductOrder
		for _, p := range order.ProductList {
			if p.ProductID == productID && p.SkuIDCode == productReq.SkuIDCode {
				orderProduct = &p
				break
			}
		}

		pPer := model.ProductAdjustSettle{
			ProductID:       productID,
			ProductTitle:    orderProduct.ProductTitle,
			ProductCoverImg: orderProduct.ProductCoverImg.Name,
			SkuIDCode:       orderProduct.SkuIDCode,
			SkuName:         orderProduct.SkuName,
			OrderAmount:     orderProduct.ProductAmount,
			AdjustAmount:    productReq.AdjustAmount,
			AdjustRemark:    productReq.AdjustRemark,
		}

		productAdjustments = append(productAdjustments, pPer)
		totalAdjustmentAmount += productReq.AdjustAmount
	}

	milli := time.Now().UnixMilli()
	// 创建调整结算记录
	adjustment := model.OrderAdjustSettle{
		ID:              primitive.NewObjectID(),
		OrderID:         orderID,
		BuyerID:         order.BuyerID,
		BuyerName:       order.BuyerName,
		SupplierID:      order.SupplierID,
		SupplierName:    order.SupplierName,
		Status:          model.OrderAdjustSettleStatusDraft,
		TotalAmount:     totalAdjustmentAmount,
		ProductList:     productAdjustments,
		Remark:          remark,
		CreatedByUserID: userID,
		CreatedByName:   user.Mobile,
		CreatedAt:       milli,
		UpdatedAt:       milli,
	}

	err = s.dao.Create(ctx, adjustment)
	if err != nil {
		return err
	}

	return nil
}

// Exists 判断订单调整结算记录是否存在
func (s *orderAdjustSettleService) Exists(ctx context.Context, orderID primitive.ObjectID) (bool, error) {
	count, err := s.dao.Count(ctx, bson.M{"order_id": orderID})
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// Update 更新订单调整结算记录
func (s *orderAdjustSettleService) Update(ctx context.Context, adjustmentID primitive.ObjectID, req types.OrderAdjustSettleUpdateReq, userID primitive.ObjectID) error {
	// 获取调整结算记录
	adjustment, err := s.Get(ctx, adjustmentID)
	if err != nil {
		return err
	}

	// 检查状态是否允许编辑
	if adjustment.Status != model.OrderAdjustSettleStatusDraft {
		return xerr.NewErr(xerr.ErrParamError, nil, "只有草稿状态的调整结算记录才能编辑")
	}

	// 获取订单信息
	order, err := s.orderService.Get(ctx, adjustment.OrderID)
	if err != nil {
		return err
	}

	remark := util.DealWrap(req.Remark)

	// 调用通用校验函数
	if err := s.validateProductAdjustSettle(ctx, order, req.ProductList, remark); err != nil {
		return err
	}

	// 重新构建商品调价明细
	var productAdjustments []model.ProductAdjustSettle
	var totalAdjustmentAmount int

	for _, productReq := range req.ProductList {
		// 将string类型的ProductID转换为ObjectID
		productID, err := util.ConvertToObjectWithCtx(ctx, productReq.ProductID)
		if err != nil {
			return xerr.NewErr(xerr.ErrParamError, nil, "无效的商品ID")
		}

		// 查找订单中对应的商品（此处已经在校验函数中验证过，理论上不会为空）
		var orderProduct *model.ProductOrder
		for _, p := range order.ProductList {
			if p.ProductID == productID && p.SkuIDCode == productReq.SkuIDCode {
				orderProduct = &p
				break
			}
		}

		productAdjustment := model.ProductAdjustSettle{
			ProductID:       productID,
			ProductTitle:    orderProduct.ProductTitle,
			ProductCoverImg: orderProduct.ProductCoverImg.Name,
			SkuIDCode:       productReq.SkuIDCode,
			SkuName:         orderProduct.SkuName,
			OrderAmount:     orderProduct.ProductAmount,
			AdjustAmount:    productReq.AdjustAmount,
			AdjustRemark:    productReq.AdjustRemark,
		}

		productAdjustments = append(productAdjustments, productAdjustment)
		totalAdjustmentAmount += productReq.AdjustAmount
	}

	// 更新记录
	update := bson.M{
		"$set": bson.M{
			"total_amount": totalAdjustmentAmount,
			"product_list": productAdjustments,
			"remark":       remark,
			"updated_at":   time.Now().UnixMilli(),
		},
	}

	filter := bson.M{"_id": adjustmentID}
	err = s.dao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

// validateProductAdjustSettle 通用商品调整结算校验函数
func (s *orderAdjustSettleService) validateProductAdjustSettle(ctx context.Context, order model.Order, productList []types.ProductAdjustSettleReq, remark string) error {
	// 检查订单状态是否允许调价
	if order.OrderStatus != model.OrderStatusTypeToReceive {
		return xerr.NewErr(xerr.ErrParamError, nil, "只有已发货和待收货的订单才能进行调整结算")
	}

	// 验证商品调整结算明细不能为空
	if len(productList) == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "商品调整结算明细不能为空")
	}

	if remark == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "备注不能为空")
	}

	// 验证调整结算金额
	for i, adjustment := range productList {
		if adjustment.AdjustAmount == 0 {
			return xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("第%d个商品的调整结算金额不能为0", i+1))
		}
	}

	// 验证商品调整明细
	for _, productReq := range productList {
		// 将string类型的ProductID转换为ObjectID
		productID, err := util.ConvertToObjectWithCtx(ctx, productReq.ProductID)
		if err != nil {
			return xerr.NewErr(xerr.ErrParamError, nil, "无效的商品ID")
		}

		// 查找订单中对应的商品
		var orderProduct *model.ProductOrder
		for _, p := range order.ProductList {
			if p.ProductID == productID && p.SkuIDCode == productReq.SkuIDCode {
				orderProduct = &p
				break
			}
		}

		if orderProduct == nil {
			return xerr.NewErr(xerr.ErrParamError, nil, "订单中不存在商品")
		}

		if orderProduct.SortNum == 0 {
			return xerr.NewErr(xerr.ErrParamError, nil, "商品分拣数量为0，无法进行调整结算")
		}

		if orderProduct.ProductAmount < productReq.AdjustAmount {
			return xerr.NewErr(xerr.ErrParamError, nil, "调整金额不能大于商品总价，请重新调整")
		}

		if productReq.AdjustRemark == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "调整备注不能为空")
		}
	}

	return nil
}

// Get 获取订单调整结算记录
func (s *orderAdjustSettleService) Get(ctx context.Context, adjustmentID primitive.ObjectID) (model.OrderAdjustSettle, error) {
	filter := bson.M{
		"_id": adjustmentID,
	}
	adjustment, err := s.dao.Get(ctx, filter)
	if err != nil {
		return model.OrderAdjustSettle{}, err
	}
	return adjustment, nil
}

// List 获取订单调整结算记录列表
func (s *orderAdjustSettleService) List(ctx context.Context, req types.OrderAdjustSettleListReq) ([]model.OrderAdjustSettle, int64, error) {
	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 20
	}

	// 构建查询条件
	filter := bson.M{}

	list, total, err := s.dao.ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// ListByBuyer 获取订单调整结算记录列表
func (s *orderAdjustSettleService) ListByBuyer(ctx context.Context, req types.OrderAdjustSettleListReq) ([]model.OrderAdjustSettle, int64, error) {
	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 20
	}

	// 构建查询条件
	filter := bson.M{}

	// 采购商ID筛选
	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err == nil {
		filter["buyer_id"] = buyerID
	}

	list, total, err := s.dao.ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// List 获取订单调整结算记录列表
func (s *orderAdjustSettleService) ListBySupplier(ctx context.Context, req types.OrderAdjustSettleListReq) ([]model.OrderAdjustSettle, int64, error) {
	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 20
	}

	// 构建查询条件
	filter := bson.M{}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err == nil {
		filter["supplier_id"] = supplierID
	}

	list, total, err := s.dao.ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}

// Confirm 确认订单调整结算记录
func (s *orderAdjustSettleService) Confirm(ctx context.Context, adjustmentID primitive.ObjectID) error {
	// 获取调价记录
	adjustment, err := s.Get(ctx, adjustmentID)
	if err != nil {
		return err
	}

	order, err := s.orderService.Get(ctx, adjustment.OrderID)
	if err != nil {
		return err
	}

	if order.OrderStatus != model.OrderStatusTypeToReceive {
		return xerr.NewErr(xerr.ErrParamError, nil, "只有待收货的订单才能【确认】进行调整结算")
	}

	// 检查状态
	if adjustment.Status != model.OrderAdjustSettleStatusDraft {
		return xerr.NewErr(xerr.ErrParamError, nil, "只有草稿状态的调整结算记录才能确认")
	}

	milli := time.Now().UnixMilli()
	// 更新状态为已确认
	update := bson.M{
		"$set": bson.M{
			"status":       model.OrderAdjustSettleStatusConfirmed,
			"confirmed_at": milli,
			"updated_at":   milli,
		},
	}

	filter := bson.M{"_id": adjustmentID}
	err = s.dao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	// 发送MNS退款通知，延迟5秒处理
	mnsSendService.NewMNSClient().SendOrderAdjustSettleRefund(adjustmentID, 5)

	return nil
}

// Close 关闭订单调整结算记录
func (s *orderAdjustSettleService) Close(ctx context.Context, adjustmentID primitive.ObjectID) error {
	// 获取调价记录
	adjustment, err := s.Get(ctx, adjustmentID)
	if err != nil {
		return err
	}

	// 检查状态
	if adjustment.Status != model.OrderAdjustSettleStatusDraft {
		return xerr.NewErr(xerr.ErrParamError, nil, "只有草稿状态的调价记录才能取消")
	}

	// 更新状态为已取消
	update := bson.M{
		"$set": bson.M{
			"status":     model.OrderAdjustSettleStatusClosed,
			"updated_at": time.Now().UnixMilli(),
		},
	}

	filter := bson.M{"_id": adjustmentID}
	err = s.dao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

// GetByOrderID 根据订单ID获取订单调整结算记录
func (s *orderAdjustSettleService) GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.OrderAdjustSettle, error) {
	filter := bson.M{
		"order_id": orderID,
	}

	adjustment, err := s.dao.Get(ctx, filter)
	if err != nil {
		return model.OrderAdjustSettle{}, err
	}

	return adjustment, nil
}

// ProcessRefund 处理MNS退款通知
func (s *orderAdjustSettleService) ProcessRefund(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("ProcessRefund error:%v", err)
			return
		}
	}()

	var data model.MNSOrderAdjustSettle
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	adjustSettle, err := s.Get(ctx, data.AdjustSettleID)
	if err != nil {
		return err
	}

	// 检查状态，只有已确认的调整结算记录才能执行退款
	if adjustSettle.Status != model.OrderAdjustSettleStatusConfirmed {
		zap.S().Warnf("调整结算记录%s状态不是已确认，无法执行退款", adjustSettle.ID.Hex())
		return nil
	}

	// 检查状态是否已经是退款中或已完成
	if adjustSettle.Status == model.OrderAdjustSettleStatusRefunding ||
		adjustSettle.Status == model.OrderAdjustSettleStatusCompleted {
		zap.S().Warnf("调整结算记录%s状态为%s，跳过退款", adjustSettle.ID.Hex(), adjustSettle.Status)
		return nil
	}

	// 易宝退款
	err = s.DoRefundYee(ctx, adjustSettle)
	if err != nil {
		return err
	}
	return nil
}

// DoRefundYee 执行易宝退款
func (s *orderAdjustSettleService) DoRefundYee(ctx context.Context, adjustSettle model.OrderAdjustSettle) error {
	milli := time.Now().UnixMilli()
	// 计算总退款金额 - 调整结算记录中的差额（负数表示需要退款）
	refundAmount := adjustSettle.TotalAmount
	if refundAmount <= 0 {
		zap.S().Warnf("调整结算记录%s退款金额%d小于等于0，跳过退款", adjustSettle.ID.Hex(), refundAmount)
		return nil
	}

	// 获取父订单信息用于易宝退款
	order, err := s.orderService.Get(ctx, adjustSettle.OrderID)
	if err != nil {
		zap.S().Errorf("获取订单信息失败：%v", err)
		return err
	}

	parentOrder, err := s.parentOrderS.Get(ctx, order.ParentOrderID)
	if err != nil {
		zap.S().Errorf("获取父订单信息失败：%v", err)
		return err
	}

	// 构建易宝退款请求
	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/trade/refund")
	yopRequest.IsvPriKey = s.yeePay.ReqPriKey
	yopRequest.AppId = s.yeePay.AppID

	reqRefundRequestID := util.NewUUIDNum()
	yopRequest.AddParam("refundRequestId", reqRefundRequestID) // 商户退款请求号

	parentMerchantNo := parentOrder.YeeWechatResult.ParentMerchantNo
	yopRequest.AddParam("parentMerchantNo", parentMerchantNo)

	var merchantNo string
	var oriOrderID string

	if parentOrder.PayMethod == model.PayMethodTypeYeeWechat {
		merchant, err := s.yeeMerchantS.GetBySupplier(ctx, adjustSettle.SupplierID)
		if err != nil {
			return err
		}
		for _, sub := range parentOrder.YeeWechatResult.NotifySubOrderList {
			if sub.MerchantNo == merchant.MerchantNo {
				merchantNo = sub.MerchantNo
				oriOrderID = sub.OrderId
				break
			}
		}
	}

	if parentOrder.PayMethod == model.PayMethodTypeYeeBalance {
		merchantNo = parentMerchantNo
		oriOrderID = parentOrder.YeeWechatResult.OrderID
	}

	yopRequest.AddParam("orderId", oriOrderID) // 收款交易对应的商户收款请求号
	yopRequest.AddParam("merchantNo", merchantNo)

	paidAmountStr := util.DealMoneyToYuanStr(refundAmount)
	yopRequest.AddParam("refundAmount", paidAmountStr) // 订单金额。单位为元，精确到小数点后两位

	yopRequest.AddParam("description", "订单调整结算退款") // 退款订单说明

	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayTradeRefundAdjustSettle) // 接收支付结果的通知地址

	bytes, err := json.Marshal(yopRequest.Params)
	zap.S().Infof("请求：%s", string(bytes))

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if err != nil {
		return err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	zap.S().Infof("退款信息：%s", string(marshal))

	var r model.YeeRefundRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.Code != "OPR00000" {
		zap.S().Errorf("退款信息：%s", string(yopResp.Content))
		return xerr.NewErr(xerr.ErrParamError, nil, "退款异常")
	}

	res := model.YeeRefundResult{
		OrderId:          oriOrderID,
		RefundRequestId:  reqRefundRequestID,
		ParentMerchantNo: parentMerchantNo,
		MerchantNo:       merchantNo,
		RefundAmount:     refundAmount,
		Status:           "PROCESSING",
	}

	// 先更新状态为退款中
	err = s.dao.UpdateOne(ctx, bson.M{"_id": adjustSettle.ID}, bson.M{
		"$set": bson.M{
			"status":        model.OrderAdjustSettleStatusRefunding,
			"refund_result": res,
			"updated_at":    milli,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

// YeeNotifyRefundAdjustSettle 易宝调整结算退款回调通知处理
func (s *orderAdjustSettleService) YeeNotifyRefundAdjustSettle(ctx context.Context, notify model.YeeTradeRefundNotify) error {
	zap.S().Infof("收到易宝调整结算退款回调通知：%+v", notify)

	// 根据退款结果中的RefundRequestId查找对应的调整结算记录
	filter := bson.M{
		"refund_result.refund_request_id": notify.RefundRequestId,
	}

	adjustSettle, err := s.dao.Get(ctx, filter)
	if err != nil {
		zap.S().Errorf("根据退款请求ID查找调整结算记录失败：%s, error: %v", notify.RefundRequestId, err)
		return err
	}

	// 检查是否已经处理过
	if adjustSettle.Status == model.OrderAdjustSettleStatusCompleted {
		zap.S().Infof("调整结算记录%s已经处理成功，跳过", adjustSettle.ID.Hex())
		return nil
	}

	milli := time.Now().UnixMilli()

	// 构建更新字段
	update := bson.M{
		"refund_result.notify_payment_method":      notify.PaymentMethod,
		"refund_result.notify_refund_success_date": notify.RefundSuccessDate,
		"refund_result.status":                     notify.Status,
		"refund_result.notify_error_message":       notify.ErrorMessage,
		"refund_result.notify_unique_refund_no":    notify.UniqueOrderNo,
		"updated_at":                               milli,
	}

	// 根据回调状态更新调整结算记录状态
	if notify.Status == "SUCCESS" {
		update["status"] = model.OrderAdjustSettleStatusCompleted
		zap.S().Infof("订单调整结算退款成功,调整记录ID：%s", adjustSettle.ID.Hex())
	} else if notify.Status == "FAIL" {
		update["status"] = model.OrderAdjustSettleStatusFailed
		zap.S().Errorf("调整结算退款失败,调整记录ID：%s，错误信息：%s",
			adjustSettle.ID.Hex(), notify.RefundRequestId, notify.ErrorMessage)
	}

	// 更新调整结算记录
	err = s.dao.UpdateOne(ctx, bson.M{"_id": adjustSettle.ID}, bson.M{
		"$set": update,
	})
	if err != nil {
		zap.S().Errorf("更新调整结算记录退款状态失败：%v", err)
		return err
	}

	return nil
}

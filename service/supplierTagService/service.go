package supplierTagService

import (
	"base/dao"
	"base/dao/supplierTagDao"
	"base/global"
	"base/model"
	"base/service/supplierService"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ServiceInterface 分类标签
type ServiceInterface interface {
	Create(ctx context.Context, title, color string) error
	UpdateTitle(ctx context.Context, id primitive.ObjectID, title string, color string) error

	Get(ctx context.Context, id primitive.ObjectID) (model.SupplierTag, error)
	List(ctx context.Context) ([]model.SupplierTag, error)
	ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.SupplierTag, error)
	Delete(ctx context.Context, id primitive.ObjectID) error
}

type supplierTagService struct {
	mdb       *mongo.Database
	rdb       *redis.Client
	sTagDao   supplierTagDao.DaoInt
	supplierS supplierService.ServiceInterface
}

func (s supplierTagService) ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.SupplierTag, error) {
	var list []model.SupplierTag
	if len(ids) < 1 {
		return nil, nil
	}
	for _, id := range ids {
		tag, err := s.Get(ctx, id)
		if err != nil {
			continue
		}
		if tag.Title != "" {
			list = append(list, tag)

		}
	}
	return list, nil
}

func (s supplierTagService) Get(ctx context.Context, id primitive.ObjectID) (model.SupplierTag, error) {
	m := get(s.rdb, id)
	if m == "" {
		i, err := s.sTagDao.Get(ctx, bson.M{"_id": id})
		if err != nil {
			return model.SupplierTag{}, err
		}
		set(s.rdb, i)
		return i, nil
	}
	return model.SupplierTag{ID: id, Title: m}, nil
}

func (s supplierTagService) Create(ctx context.Context, title, color string) error {
	data := model.SupplierTag{
		ID:    primitive.NewObjectID(),
		Title: title,
		Color: color,
		//CreatedAt: time.Now().UnixMilli(),
	}

	err := s.sTagDao.Create(ctx, data)
	return err
}

func (s supplierTagService) List(ctx context.Context) ([]model.SupplierTag, error) {
	filter := bson.M{}
	list, err := s.sTagDao.List(ctx, filter)
	return list, err
}

func (s supplierTagService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	err := s.supplierS.CheckTag(ctx, id)
	if err != nil {
		return err
	}

	err = s.sTagDao.Delete(ctx, filter)
	if err != nil {
		return err
	}
	del(s.rdb, id)
	return err
}

func (s supplierTagService) UpdateTitle(ctx context.Context, id primitive.ObjectID, title string, color string) error {
	del(s.rdb, id)

	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		update := bson.M{
			"$set": bson.M{
				"title": title,
				"color": color,
			},
		}
		err := s.sTagDao.Update(ctx, bson.M{"_id": id}, update)
		if err != nil {
			return nil, err
		}

		err = s.supplierS.UpdateTag(sessCtx, id, title, color)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

func NewSupplierTagService() ServiceInterface {
	return supplierTagService{
		mdb:       global.MDB,
		rdb:       global.RDBDefault,
		sTagDao:   dao.SupplierTagDao,
		supplierS: supplierService.NewSupplierService(),
	}
}

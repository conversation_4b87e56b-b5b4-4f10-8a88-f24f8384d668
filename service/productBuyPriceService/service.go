package productBuyPriceService

import (
	"base/dao"
	"base/dao/productBuyPriceDao"
	"base/global"
	"base/model"
	"base/service/orderService"
	"context"
	"errors"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ServiceInterface 采购价服务接口
type ServiceInterface interface {
	Upsert(ctx context.Context, quality model.OrderQuality) error
	UpdateRefreshStatus(ctx context.Context, productID primitive.ObjectID, dayAt int64, stockUpNo int) error
	List(ctx context.Context, filter bson.M) ([]model.ProductBuyPrice, error)
}

type productBuyPriceService struct {
	rdb                *redis.Client
	productBuyPriceDao productBuyPriceDao.DaoInt
	orderS             orderService.ServiceInterface
}

// NewProductBuyPriceService 创建采购价服务
func NewProductBuyPriceService() ServiceInterface {
	return productBuyPriceService{
		rdb:                global.RDBDefault,
		productBuyPriceDao: dao.ProductBuyPriceDao,
		orderS:             orderService.NewOrderService(),
	}
}

// Upsert 更新或创建采购价
func (s productBuyPriceService) Upsert(ctx context.Context, quality model.OrderQuality) error {
	filter := bson.M{
		"product_id":        quality.ProductID,
		"sku_id_code":       quality.SkuIDCode,
		"stock_up_no":       quality.StockUpNo,
		"stock_up_day_time": quality.StockUpDayTime,
	}
	get, err := s.productBuyPriceDao.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	now := time.Now().UnixMilli()

	orderIDs := make([]primitive.ObjectID, 0, len(quality.OrderList))
	for _, temp := range quality.OrderList {
		orderIDs = append(orderIDs, temp.OrderID)
	}
	orders, err := s.orderS.ListByOrderIDs(ctx, orderIDs)
	if err != nil {
		return err
	}

	var orderProductList []model.PerOrderData

	var totalAmount int
	var totalNum int

	var totalUnitAmount int

	for _, order := range orders {
		for _, productOrder := range order.ProductList {
			if productOrder.ProductID == quality.ProductID && productOrder.SkuIDCode == quality.SkuIDCode {
				item := model.PerOrderData{
					OrderID:  order.ID,
					TotalNum: productOrder.Num,
					Price:    productOrder.Price,
				}
				if productOrder.IsCheckWeight {
					// 计重
					item.UnitPrice = productOrder.ProductRoughWeightUnitPriceKG
				} else {
					// 不计重
					item.UnitPrice = productOrder.Price
				}
				orderProductList = append(orderProductList, item)

				totalAmount += productOrder.ProductAmount
				totalNum += productOrder.Num

				totalUnitAmount += item.UnitPrice * productOrder.Num
			}
		}
	}

	var averagePrice, averageUnitPrice int

	averagePrice = totalAmount / totalNum
	averageUnitPrice = totalUnitAmount / totalNum

	var averageBuyPrice, averageBuyUnitPrice, profitAmount int
	var profitPercent float64

	if quality.QualityNum != 0 {
		averageBuyPrice = quality.Amount / quality.QualityNum
	}

	var totalSortWeight int
	var totalStandardWeight int

	for _, temp := range quality.OrderList {
		totalStandardWeight += temp.DueWeight
		if !temp.HasSort {
			// 存在未分拣的，该商品采购价直接先归零
			totalSortWeight = 0
			break
		}
		totalSortWeight += temp.SortWeight
	}

	if quality.IsCheckWeight {
		if totalSortWeight != 0 {

			weight := decimal.NewFromInt(int64(totalSortWeight)).Div(decimal.NewFromInt(int64(1000)))
			amountDe := decimal.NewFromInt(int64(quality.Amount))

			averageBuyUnitPrice = int(amountDe.Div(weight).IntPart())
		}

	} else {
		if quality.QualityNum != 0 {
			averageBuyUnitPrice = quality.Amount / quality.QualityNum
		}
	}

	if averageBuyUnitPrice != 0 {
		profitAmount = averageUnitPrice - averageBuyUnitPrice
		profit := decimal.NewFromInt(int64(profitAmount))
		saleUnitPrice := decimal.NewFromInt(int64(averageUnitPrice))
		profitPercent, _ = profit.Div(saleUnitPrice).Mul(decimal.NewFromInt(100)).Round(2).Float64()
	}

	if get.ID == primitive.NilObjectID {
		//	 新建
		data := model.ProductBuyPrice{
			ID:                  primitive.NewObjectID(),
			ServicePointID:      quality.ServicePointID,
			ProductID:           quality.ProductID,
			SkuIDCode:           quality.SkuIDCode,
			SkuName:             quality.SkuName,
			CategoryIDs:         quality.CategoryIDs,
			ProductTitle:        quality.ProductTitle,
			SupplierID:          quality.SupplierID,
			SupplierSimpleName:  quality.SupplierName,
			AveragePrice:        averagePrice,     // 价格
			AverageUnitPrice:    averageUnitPrice, // 单价
			OrderList:           orderProductList,
			RoughWeight:         quality.PerRoughWeight,
			TotalStandardWeight: totalStandardWeight,
			TotalSortWeight:     totalSortWeight,
			IsCheckWeight:       quality.IsCheckWeight,
			TotalNum:            quality.QualityDueNum,
			BuyNum:              quality.QualityNum,
			BuyAmount:           quality.Amount,
			AverageBuyPrice:     averageBuyPrice,     // 采购
			AverageBuyUnitPrice: averageBuyUnitPrice, // 采购
			ProfitAmount:        profitAmount,        // 利润
			ProfitPercent:       profitPercent,       // 利润
			StockUpNo:           quality.StockUpNo,
			StockUpDayTime:      quality.StockUpDayTime,
			RefreshStatus:       0,
			CreatedAt:           now,
			UpdatedAt:           now,
		}

		err = s.productBuyPriceDao.Create(ctx, data)
		if err != nil {
			return err
		}
	}

	if get.ID != primitive.NilObjectID {
		// 更新
		err = s.productBuyPriceDao.Update(ctx, bson.M{"_id": get.ID}, bson.M{"$set": bson.M{
			"average_price":          averagePrice,
			"average_unit_price":     averageUnitPrice,
			"total_num":              quality.QualityDueNum, // 采购总价
			"total_standard_weight":  totalStandardWeight,   // 采购总价
			"total_sort_weight":      totalSortWeight,       // 采购总价
			"buy_num":                quality.QualityNum,
			"buy_amount":             quality.Amount,
			"order_list":             orderProductList,
			"average_buy_price":      averageBuyPrice,
			"average_buy_unit_price": averageBuyUnitPrice,
			"profit_amount":          profitAmount,
			"profit_percent":         profitPercent,
			"refresh_status":         0,
			"updated_at":             now,
		}})
		if err != nil {
			return err
		}
	}

	return nil
}

// List 查询采购价列表
func (s productBuyPriceService) List(ctx context.Context, filter bson.M) ([]model.ProductBuyPrice, error) {
	list, err := s.productBuyPriceDao.Find(ctx, filter)
	if err != nil {
		return nil, nil
	}
	return list, nil
}

// UpdateRefreshStatus 更新刷新状态
func (s productBuyPriceService) UpdateRefreshStatus(ctx context.Context, productID primitive.ObjectID, dayAt int64, stockUpNo int) error {
	filter := bson.M{
		"product_id":        productID,
		"stock_up_no":       stockUpNo,
		"stock_up_day_time": dayAt,
	}

	update := bson.M{
		"refresh_status": 1,
	}
	err := s.productBuyPriceDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

package productService

import (
	"base/model"
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

// 缓存
var cacheUpdate = "productUpdate:"

func getUpdate(r *redis.Client, id primitive.ObjectID) model.Product {
	key := cacheUpdate + id.Hex()
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := r.Get(ctx, key).Bytes()
		if err != nil {
			zap.S().Error("get err")
			return model.Product{}
		}
		var i model.Product
		err = json.Unmarshal(bytes, &i)
		if err != nil {
			zap.S().Error("unmarshal,", err)
			return model.Product{}
		}
		return i
	}
	return model.Product{}
}

func setUpdate(r *redis.Client, info model.Product) {
	key := cacheUpdate + info.ID.Hex()

	bytes, err := json.Marshal(info)
	if err != nil {
		zap.S().Error("set marshal,", err)
		return
	}
	// 30 天过期
	r.Set(context.Background(), key, bytes, time.Hour*24*10)
}

func delUpdate(r *redis.Client, id primitive.ObjectID) {
	r.Del(context.Background(), cacheUpdate+id.Hex())
}

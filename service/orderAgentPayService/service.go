package orderAgentPayService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/orderAgentPayDao"
	"base/dao/promotionsubsidydao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"base/service/buyerBalanceOrderService"
	"base/service/orderAdjustSettleService"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/parentOrderService"
	"base/service/servicePointService"
	"base/service/stationService"
	"base/service/supplierService"
	"base/service/yeeMerchantService"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"sync"
	"time"

	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// 平台商编
var merchantPlatformNo = "10090765586"

var deliverLock sync.Mutex

var divideLock sync.Mutex

// ServiceInterface 分账
type ServiceInterface interface {
	ToAgentPayNormalOrder(ctx context.Context, orderID primitive.ObjectID) error
	ToAgentPayDebtOrder(ctx context.Context, debtID primitive.ObjectID) error
	ToAgentPayDeliver(ctx context.Context, parentID primitive.ObjectID) error

	SingleOrderAgentPay(ctx context.Context, orderID primitive.ObjectID) (model.OrderAgentPay, error)
	SingleOrderAgentPayForDebt(ctx context.Context, order model.OrderDebt) (model.OrderAgentPay, error)

	SingleOrderAgentPayDeliver(ctx context.Context, parentOrderID primitive.ObjectID) error // 配送费代付
	//SingleOrderAgentPayDeliverManual(ctx context.Context, id primitive.ObjectID) error
	//SingleOrderAgentPayAdjust(ctx context.Context, orderID primitive.ObjectID) error // 单笔代付--对完成的订单

	UpdateAgentPayResult(ctx context.Context, id primitive.ObjectID, ps model.AgentPayResult) error
	UpdateOrderStatus(ctx context.Context, BizOrderNo string) error
	//BackProductCommission(order model.Order) (int, int, int)
	//BackProductCommission2(order model.Order, refunds []model.OrderRefund) (int, int, int)

	//CheckSupplierAmount(ctx context.Context, bizOrderNo string) error

	YeeDivide(ctx context.Context, order model.OrderAgentPay) error
	YeeDivideComplete(ctx context.Context, order model.OrderAgentPay) error
	YeeCheckDivideFlat(ctx context.Context, id primitive.ObjectID) error
	YeeCheckDivideEnd(ctx context.Context, id primitive.ObjectID) error

	List(ctx context.Context, filter bson.M) ([]model.OrderAgentPay, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.OrderAgentPay, error)
	GetDebtByOrder(ctx context.Context, orderID primitive.ObjectID) (model.OrderAgentPay, error)
	GetOrderByOrder(ctx context.Context, orderID primitive.ObjectID) (model.OrderAgentPay, error)
}

type orderAgentPayService struct {
	mdb *mongo.Database
	rdb *redis.Client
	l   *zap.SugaredLogger

	yee          *global.YeePayInfo
	yeeMerchantS yeeMerchantService.ServiceInterface

	//PlatformPayCommission float64 // 平台平台支付费率
	allInPayOrderS  payModule.OrderService
	authenticationS authenticationService.ServiceInterface

	pointS    servicePointService.ServiceInterface
	stationS  stationService.ServiceInterface
	supplierS supplierService.ServiceInterface

	// 父单
	parentOrderS        parentOrderService.ServiceInterface
	refundS             orderRefundService.ServiceInterface
	debtS               orderDebtService.ServiceInterface
	orderS              orderService.ServiceInterface
	orderAdjustS        orderAdjustSettleService.ServiceInt
	OrderAgentPayDao    orderAgentPayDao.DaoInt
	PromotionSubsidyDao promotionsubsidydao.DaoInt
}

// NewOrderAgentPayService 创建订单代付服务
func NewOrderAgentPayService() ServiceInterface {
	return orderAgentPayService{
		mdb:          global.MDB,
		rdb:          global.RDBDefault,
		l:            global.OrderLogger.Sugar(),
		yee:          global.YeePay,
		yeeMerchantS: yeeMerchantService.NewYeeMerchantService(),

		allInPayOrderS:  payModule.NewOrderS(),
		authenticationS: authenticationService.NewAuthenticationService(),

		pointS:    servicePointService.NewServicePointService(),
		stationS:  stationService.NewStationService(),
		supplierS: supplierService.NewSupplierService(),

		parentOrderS:        parentOrderService.NewParentOrderService(),
		refundS:             orderRefundService.NewOrderRefundService(),
		debtS:               orderDebtService.NewOrderDebtService(),
		orderS:              orderService.NewOrderService(),
		orderAdjustS:        orderAdjustSettleService.NewService(),
		OrderAgentPayDao:    dao.OrderAgentPayDao,
		PromotionSubsidyDao: dao.PromotionSubsidyDao,
	}
}

func (s orderAgentPayService) SingleOrderAgentPay(ctx context.Context, orderID primitive.ObjectID) (model.OrderAgentPay, error) {
	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		s.l.Errorf("SingleOrderAgentPay 查询订单%v错误%v", orderID.Hex(), err)
		return model.OrderAgentPay{}, err
	}

	agentPay, err := s.OrderAgentPayDao.Get(ctx, bson.M{
		"order_id":       orderID,
		"agent_pay_type": 1, // 普通订单
	})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		s.l.Errorf("SingleOrderAgentPay 查询是否存在代付单%s错误%v", orderID.Hex(), err)
		return model.OrderAgentPay{}, err
	}
	if agentPay.ID != primitive.NilObjectID {
		s.l.Infof("SingleOrderAgentPay 已经存在%s", orderID.Hex())
		return agentPay, nil
	}

	parentOrder, err := s.parentOrderS.Get(ctx, order.ParentOrderID)
	if err != nil {
		return model.OrderAgentPay{}, err
	}

	// 服务仓
	pointMerchant, err := s.yeeMerchantS.GetYeeByPoint(ctx, order.ServicePointID)
	if err != nil {
		return model.OrderAgentPay{}, err
	}

	// 供应商
	supplierMerchant, err := s.yeeMerchantS.GetBySupplier(ctx, order.SupplierID)
	if err != nil {
		return model.OrderAgentPay{}, err
	}

	// 售后退款
	refunds, err := s.refundS.ListAfterSaleRefund(ctx, order.ID)
	if err != nil {
		zap.S().Errorf("代付查询退款订单异常，订单号：%s,异常:%s", order.ID.Hex(), err.Error())
		return model.OrderAgentPay{}, err
	}

	// 结算-品控退款和补差
	orderDebt, err := s.debtS.GetByOrderID(ctx, orderID)
	if err != nil {
		zap.S().Errorf("查询结算订单异常，订单号：%s,异常:%s", order.ID.Hex(), err.Error())
		return model.OrderAgentPay{}, err
	}

	// 售后
	var refundAfterSaleProductAmount int
	for _, re := range refunds {
		if re.RefundType == model.RefundTypeAfterSale && re.AuditStatus == model.AuditStatusTypePass {
			refundAfterSaleProductAmount += re.AuditAmount
		}
	}

	hasQualityRefundProduct, hasRefundServiceFee := backRealHasQualityRefundProduct(&orderDebt)

	// 优惠券退款金额-品控退款
	totalQualityRefundCouponAmount := dealDebtInfo(&orderDebt)
	// 优惠券退款金额-售后退款
	totalAfterSaleRefundCouponAmount := dealAfterSaleRefundCoupon(refunds, &order)

	// 总退款
	hasRefundTotalAmount := hasQualityRefundProduct + refundAfterSaleProductAmount + hasRefundServiceFee
	// 优惠券补贴
	var promotionSubsidyAmount int
	promotionSubsidyAmount = order.CouponSplitAmount - totalQualityRefundCouponAmount - totalAfterSaleRefundCouponAmount

	orderAdjust, err := s.orderAdjustS.GetByOrderID(ctx, orderID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		zap.S().Errorf("查询结算订单异常，订单号：%s,异常:%s", order.ID.Hex(), err.Error())
		return model.OrderAgentPay{}, err
	}

	// 订单调价退款
	hasRefundTotalAmount += orderAdjust.TotalAmount

	// 服务仓：服务费
	serviceAmount := order.TotalServiceFee - hasRefundServiceFee

	finalProductAmount := order.ProductTotalAmount - order.CouponSplitAmount - refundAfterSaleProductAmount - hasQualityRefundProduct - orderAdjust.TotalAmount

	// 平台：商品交易服务费
	platformServiceFee, err := backPlatformServiceFee(finalProductAmount)
	if err != nil {
		return model.OrderAgentPay{}, err
	}

	supplierAmount := finalProductAmount - platformServiceFee

	supplierMerchantMerchantNo := supplierMerchant.MerchantNo
	//if order.SupplierID.Hex() == "67c53f69c7374310352b529f" && order.CreatedAt <= 1741600680000 {
	//	// 2025-03-10 17:58:00
	//	supplierMerchantMerchantNo = "10091015557"
	//}

	servicePointMerchantNo := pointMerchant.MerchantNo
	//if order.CreatedAt <= 1741833000000 {
	//	// 2025-03-13 10:30:00
	//	servicePointMerchantNo = "10090790089"
	//}

	yeeDivideResult := model.YeeDivideResult{
		ParentMerchantNo:       parentOrder.YeeWechatResult.ParentMerchantNo,
		MerchantNo:             supplierMerchantMerchantNo,
		ServicePointMerchantNo: servicePointMerchantNo,
	}

	if order.PayMethod == model.PayMethodTypeYeeWechat {
		for _, sub := range parentOrder.YeeWechatResult.NotifySubOrderList {
			if sub.MerchantNo == supplierMerchantMerchantNo {
				yeeDivideResult.OriOrderId = sub.OrderId
				yeeDivideResult.OriUniqueOrderNo = sub.UniqueOrderNo
				break
			}
		}
	}

	if order.PayMethod == model.PayMethodTypeYeeBalance {
		yeeDivideResult.OriOrderId = parentOrder.YeeWechatResult.OrderID
		yeeDivideResult.OriUniqueOrderNo = parentOrder.YeeWechatResult.UniqueOrderNo
	}

	now := time.Now().UnixMilli()
	data := model.OrderAgentPay{
		ID:                               primitive.NewObjectID(),
		OrderID:                          order.ID,
		BuyerID:                          order.BuyerID,
		UserID:                           order.UserID,
		BuyerName:                        order.BuyerName,
		PayMethod:                        order.PayMethod,
		AgentPayType:                     model.AgentPayTypeNormal,
		ParentOrderID:                    order.ParentOrderID,
		OrderPaidAmount:                  order.PaidAmount,
		OrderSplitAmount:                 order.PaidAmount - hasRefundTotalAmount, // 优惠券补贴
		ReceiverBizUserID:                order.ReceiverBizUserID,
		OriPayBizUserID:                  order.PayBizUserID,
		OriBizOrderNo:                    parentOrder.BizOrderNo,
		OriOrderNo:                       parentOrder.BizOrderNoResult.OrderNo,
		SupplierID:                       order.SupplierID,
		SupplierAmount:                   supplierAmount,
		SupplierName:                     order.SupplierName,
		SupplierFeeAmount:                platformServiceFee, // 商品交易服务费
		ServicePointID:                   order.ServicePointID,
		ServicePointName:                 order.ServicePointName,
		ServiceAmount:                    serviceAmount,
		ServiceFeeAmount:                 0,
		SupplierLevel:                    order.SupplierLevel,
		StationID:                        order.StationID,
		StationName:                      order.StationName,
		StationServiceAmount:             0,
		StationServiceFeeAmount:          0,
		YeeDivideResult:                  yeeDivideResult,
		TotalPromotionSubsidyAmount:      promotionSubsidyAmount,
		TotalAfterSaleRefundCouponAmount: totalAfterSaleRefundCouponAmount,
		TotalQualityRefundCouponAmount:   totalQualityRefundCouponAmount,
		CreatedAt:                        now,
		UpdatedAt:                        now,
	}
	err = s.OrderAgentPayDao.Create(ctx, data)
	if err != nil {
		s.l.Errorf("创建代付单信息错误%v", err)
		return model.OrderAgentPay{}, err
	}

	return data, nil
}

func dealDebtInfo(debt *model.OrderDebt) int {
	var totalQualityRefundCouponAmount int
	for _, settle := range debt.SettleProductList {
		if settle.SettleResultType == model.SettleResultTypeRefund {
			//	品控退款，涉及优惠券
			totalQualityRefundCouponAmount += settle.RefundCouponAmount
		}
	}
	return totalQualityRefundCouponAmount
}

func dealAfterSaleRefundCoupon(refunds []model.OrderRefund, order *model.Order) int {
	mProductOrder := make(map[primitive.ObjectID]model.ProductOrder)
	for _, productOrder := range order.ProductList {
		mProductOrder[productOrder.ProductID] = productOrder
	}

	var totalRefundCouponAmount int

	for _, refund := range refunds {
		if refund.RefundType == model.RefundTypeAfterSale && refund.AuditStatus == model.AuditStatusTypePass {
			pOrder := mProductOrder[refund.ProductID]
			pOrderAmountDe := decimal.NewFromInt(int64(pOrder.ProductAmount))
			pOrderCouponSplitAmount := decimal.NewFromInt(int64(pOrder.CouponSplitAmount))

			auditAmountDe := decimal.NewFromInt(int64(refund.AuditAmount))

			percentDe := auditAmountDe.Div(pOrderAmountDe)
			totalRefundCouponAmount += int(pOrderCouponSplitAmount.Mul(percentDe).Round(0).IntPart())
		}
	}
	return totalRefundCouponAmount
}

func backRealHasQualityRefundProduct(debt *model.OrderDebt) (int, int) {
	// 返回商品和服务费退款
	realRefundProduct := debt.RefundTotalProductAmount - debt.OffsetProductAmount

	return realRefundProduct, debt.RefundTotalServiceFee
}

func (s orderAgentPayService) SingleOrderAgentPayForDebt(ctx context.Context, order model.OrderDebt) (model.OrderAgentPay, error) {
	agentPay, err := s.OrderAgentPayDao.Get(ctx, bson.M{
		"order_id":       order.OrderID,
		"agent_pay_type": model.AgentPayTypeDebt,
	})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		s.l.Errorf("SingleOrderAgentPayForDebt 查询是否存在代付单%s错误%v", order.ID.Hex(), err)
		return model.OrderAgentPay{}, err
	}
	if agentPay.ID != primitive.NilObjectID {
		s.l.Errorf("SingleOrderAgentPayForDebt 已经代付%s", order.ID.Hex())
		return model.OrderAgentPay{}, xerr.NewErr(xerr.ErrParamError, nil, "已经代付")
	}

	// 中心仓
	pointMerchant, err := s.yeeMerchantS.GetYeeByPoint(ctx, order.ServicePointID)
	if err != nil {
		return model.OrderAgentPay{}, err
	}

	// 供应商
	supplierMerchant, err := s.yeeMerchantS.GetBySupplier(ctx, order.SupplierID)
	if err != nil {
		return model.OrderAgentPay{}, err
	}

	// 平台：商品交易服务费
	finalProductAmount := order.TotalProductAmount

	platformServiceFee, err := backPlatformServiceFee(finalProductAmount)
	if err != nil {
		return model.OrderAgentPay{}, err
	}

	supplierAmount := finalProductAmount - platformServiceFee

	yeeDivideResult := model.YeeDivideResult{
		ParentMerchantNo:       order.YeeWechatResult.ParentMerchantNo,
		MerchantNo:             supplierMerchant.MerchantNo,
		ServicePointMerchantNo: pointMerchant.MerchantNo,
	}

	// 单笔交易
	yeeDivideResult.OriOrderId = order.YeeWechatResult.OrderID
	yeeDivideResult.OriUniqueOrderNo = order.YeeWechatResult.UniqueOrderNo

	paidAmount := order.TotalProductAmount

	now := time.Now().UnixMilli()
	data := model.OrderAgentPay{
		ID:                      primitive.NewObjectID(),
		OrderID:                 order.OrderID,
		BuyerID:                 order.BuyerID,
		BuyerName:               order.BuyerName,
		PayMethod:               order.PayMethod,
		AgentPayType:            model.AgentPayTypeDebt,
		OrderPaidAmount:         paidAmount,
		OrderSplitAmount:        paidAmount,
		SupplierID:              order.SupplierID,
		SupplierAmount:          supplierAmount,
		SupplierName:            order.SupplierName,
		SupplierFeeAmount:       platformServiceFee, // 商品交易服务费
		ServicePointID:          order.ServicePointID,
		ServicePointName:        order.ServicePointName,
		ServiceFeeAmount:        0,
		StationServiceAmount:    0,
		StationServiceFeeAmount: 0,
		YeeDivideResult:         yeeDivideResult,
		CreatedAt:               now,
		UpdatedAt:               now,
	}

	err = s.OrderAgentPayDao.Create(ctx, data)
	if err != nil {
		s.l.Errorf("创建补差-代付单信息错误%v", err)
		return model.OrderAgentPay{}, err
	}

	return data, nil
}

func (s orderAgentPayService) YeeDivide(ctx context.Context, order model.OrderAgentPay) error {
	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/divide/apply")

	yopRequest.AddParam("merchantNo", order.YeeDivideResult.MerchantNo) // 收款商户编号

	yopRequest.AddParam("orderId", order.YeeDivideResult.OriOrderId) // 商户请求收款的交易单号

	divideRequestId := util.NewUUIDNum()
	yopRequest.AddParam("divideRequestId", divideRequestId) // 商户分账请求号

	yopRequest.AddParam("parentMerchantNo", order.YeeDivideResult.ParentMerchantNo) // 交易发起方商编
	yopRequest.AddParam("uniqueOrderNo", order.YeeDivideResult.OriUniqueOrderNo)    // 收款交易对应在易宝的收款单号
	//yopRequest.AddParam("isUnfreezeResidualAmount", "TRUE")                         // 是否解冻收单商户剩余可用金额

	divideDetail := make([]map[string]string, 0)

	// 中心仓：仓配费和服务费
	pointAmount := order.ServiceAmount + order.WarehouseLoadAmount
	if pointAmount > 0 {
		str := util.DealMoneyToYuanStr(pointAmount)
		item := map[string]string{
			"amount":           str,
			"ledgerNo":         order.YeeDivideResult.ServicePointMerchantNo, // 分账接收方编号-中心仓
			"ledgerType":       "MERCHANT2MERCHANT",                          // 分账给商户
			"divideDetailDesc": "仓配费和服务费",
		}
		divideDetail = append(divideDetail, item)
	}

	// 平台：交易服务费：0.6%
	if order.SupplierFeeAmount > 0 {
		// 平台交易服务费
		str := util.DealMoneyToYuanStr(order.SupplierFeeAmount)
		item := map[string]string{
			"amount":           str,
			"ledgerNo":         order.YeeDivideResult.ParentMerchantNo, // 分账接收方编号-平台商
			"ledgerType":       "MERCHANT2MERCHANT",                    // 分账给商户
			"divideDetailDesc": "商品交易服务费",
		}
		divideDetail = append(divideDetail, item)
	}

	if order.PayMethod == model.PayMethodTypeYeeBalance {
		//	 供应商
		str := util.DealMoneyToYuanStr(order.SupplierAmount)
		item := map[string]string{
			"amount":           str,
			"ledgerNo":         order.YeeDivideResult.MerchantNo, // 分账接收方编号-供应商
			"ledgerType":       "MERCHANT2MERCHANT",              // 分账给商户
			"divideDetailDesc": "供应商商品",
		}
		divideDetail = append(divideDetail, item)

		yopRequest.AddParam("merchantNo", order.YeeDivideResult.ParentMerchantNo) // 收款商户编号
	}

	if order.PayMethod == model.PayMethodTypeYeeWechat && pointAmount == 0 && order.SupplierFeeAmount == 0 {
		// 不调用
		update := bson.M{
			"yee_divide_result.status": "SUCCESS",
			"note":                     "跳过分账，直接完结分账",
		}
		err := s.OrderAgentPayDao.UpdateOne(ctx, bson.M{
			"_id": order.ID,
		}, bson.M{
			"$set": update,
		})
		if err != nil {
			return err
		}

		if order.AgentPayType == model.AgentPayTypeNormal {
			err = s.orderS.UpdateOne(ctx, bson.M{
				"_id": order.OrderID,
			}, bson.M{
				"$set": bson.M{
					"has_agent_pay": true,
				},
			})
			if err != nil {
				zap.S().Errorf("订单分账，更新订单分账状态异常：%s", err.Error())
			}
		}

		return nil
	}

	divideDetailM, err := json.Marshal(divideDetail)
	if err != nil {
		return err
	}

	yopRequest.AddParam("divideDetail", string(divideDetailM)) // 分账详情

	yopResponse, err := s.yee.DoRequest(yopRequest)
	if err != nil {
		return err
	}

	marshal, err := json.Marshal(yopResponse.Result)
	if err != nil {
		return err
	}
	m := make(map[string]string)
	err = json.Unmarshal(marshal, &m)
	if err != nil {
		return err
	}

	if m["code"] != "OPR00000" {
		zap.S().Errorf("分账异常:%s,分账单ID:%s", m["message"], order.ID.Hex())
		return xerr.NewErr(xerr.ErrParamError, nil, m["message"])
	}

	var resList []model.YeeDivideDetailRes

	err = json.Unmarshal([]byte(m["divideDetail"]), &resList)
	if err != nil {
		return err
	}

	var divideDetailRes []model.YeeDivideDetail
	for _, re := range resList {

		fenInt := util.DealMoneyFloatToFenInt(re.Amount)
		item := model.YeeDivideDetail{
			DivideDetailNo:   re.DivideDetailNo,
			LedgerNo:         re.LedgerNo,
			Amount:           fenInt,
			DivideDetailDesc: re.DivideDetailDesc,
		}
		divideDetailRes = append(divideDetailRes, item)
	}

	update := bson.M{
		"yee_divide_result.divide_request_id":   m["divideRequestId"],
		"yee_divide_result.status":              m["status"],
		"yee_divide_result.divide_detail":       divideDetailRes,
		"yee_divide_result.create_date":         m["createDate"],
		"yee_divide_result.divide_success_date": m["divideSuccessDate"],
	}
	err = s.OrderAgentPayDao.UpdateOne(ctx, bson.M{
		"_id": order.ID,
	}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	if m["status"] == "PROCESSING" {
		mnsSendService.NewMNSClient().SendDivideFlatCheck(ctx, order.ID)
	}

	if order.AgentPayType == model.AgentPayTypeNormal {
		err = s.orderS.UpdateOne(ctx, bson.M{
			"_id": order.OrderID,
		}, bson.M{
			"$set": bson.M{
				"has_agent_pay": true,
			},
		})
		if err != nil {
			zap.S().Errorf("订单分账，更新订单分账状态异常：%s", err.Error())
		}
	}

	return nil
}

// YeeDivideComplete 完结分账-仅微信支付
func (s orderAgentPayService) YeeDivideComplete(ctx context.Context, order model.OrderAgentPay) error {
	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/divide/complete")

	yopRequest.AddParam("merchantNo", order.YeeDivideResult.MerchantNo) // 收款商户编号

	yopRequest.AddParam("orderId", order.YeeDivideResult.OriOrderId) // 商户请求收款的交易单号

	divideRequestId := util.NewUUIDNum()
	yopRequest.AddParam("divideRequestId", divideRequestId) // 商户分账请求号

	yopRequest.AddParam("parentMerchantNo", order.YeeDivideResult.ParentMerchantNo) // 交易发起方商编
	yopRequest.AddParam("uniqueOrderNo", order.YeeDivideResult.OriUniqueOrderNo)    // 收款交易对应在易宝的收款单号
	yopRequest.AddParam("divideDetailDesc", "供应商完结分账")

	yopResponse, err := s.yee.DoRequest(yopRequest)
	if err != nil {
		return err
	}

	marshal, err := json.Marshal(yopResponse.Result)
	if err != nil {
		return err
	}

	var r model.YeeDivideCompleteRes
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.Code != "OPR00000" {
		zap.S().Errorf("完结分账-分账异常:%s,分账单ID:%s", r.Message, order.ID.Hex())
		return xerr.NewErr(xerr.ErrParamError, nil, r.Message)
	}

	update := bson.M{
		"yee_divide_result.complete_divide_request_id":   divideRequestId,
		"yee_divide_result.complete_divide_status":       r.DivideStatus,
		"yee_divide_result.complete_create_date":         r.CreateDate,
		"yee_divide_result.complete_divide_success_date": r.DivideSuccessDate,
	}
	err = s.OrderAgentPayDao.UpdateOne(ctx, bson.M{
		"_id": order.ID,
	}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	mnsSendService.NewMNSClient().SendDivideEndCheck(ctx, order.ID)

	return nil
}

func (s orderAgentPayService) YeeCheckDivideFlat(ctx context.Context, id primitive.ObjectID) error {
	agentPay, err := s.Get(ctx, id)
	if err != nil {
		return err
	}

	merchantNo := agentPay.YeeDivideResult.MerchantNo

	if agentPay.PayMethod == model.PayMethodTypeYeeBalance {
		merchantNo = agentPay.YeeDivideResult.ParentMerchantNo
	}

	divideQuery, err := s.YeeDivideQuery(ctx, agentPay.YeeDivideResult.CompleteDivideRequestId, merchantNo, agentPay.YeeDivideResult.OriOrderId)
	if err != nil {
		return err
	}

	// 普通订单和补差

	if divideQuery["status"] == "SUCCESS" && agentPay.YeeDivideResult.Status != "SUCCESS" {

		var resList []model.YeeDivideDetailRes

		err = json.Unmarshal([]byte(divideQuery["divideDetail"]), &resList)
		if err != nil {
			return err
		}

		var divideDetailRes []model.YeeDivideDetail

		for _, re := range resList {

			fenInt := util.DealMoneyFloatToFenInt(re.Amount)

			item := model.YeeDivideDetail{
				DivideDetailNo:   re.DivideDetailNo,
				LedgerNo:         re.LedgerNo,
				Amount:           fenInt,
				DivideDetailDesc: re.DivideDetailDesc,
			}
			divideDetailRes = append(divideDetailRes, item)
		}

		update := bson.M{}
		if divideQuery["divideType"] == "FLAT_DIVIDE" {
			update["yee_divide_result.status"] = divideQuery["status"]
			update["yee_divide_result.divide_detail"] = divideDetailRes
			update["yee_divide_result.create_date"] = divideQuery["createDate"]
			update["yee_divide_result.divide_success_date"] = divideQuery["divideSuccessDate"]

			err = s.OrderAgentPayDao.UpdateOne(ctx, bson.M{
				"_id": agentPay.ID,
			}, bson.M{
				"$set": update,
			})
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (s orderAgentPayService) YeeCheckDivideEnd(ctx context.Context, id primitive.ObjectID) error {
	agentPay, err := s.Get(ctx, id)
	if err != nil {
		return err
	}

	merchantNo := agentPay.YeeDivideResult.MerchantNo

	divideQuery, err := s.YeeDivideQuery(ctx, agentPay.YeeDivideResult.CompleteDivideRequestId, merchantNo, agentPay.YeeDivideResult.OriOrderId)
	if err != nil {
		return err
	}

	// 普通订单和补差
	if divideQuery["status"] == "SUCCESS" && len(agentPay.YeeDivideResult.CompleteDivideDetail) == 0 {

		var resList []model.YeeDivideDetailRes

		err = json.Unmarshal([]byte(divideQuery["divideDetail"]), &resList)
		if err != nil {
			return err
		}

		var divideDetailRes []model.YeeDivideDetail

		for _, re := range resList {

			fenInt := util.DealMoneyFloatToFenInt(re.Amount)

			item := model.YeeDivideDetail{
				DivideDetailNo:   re.DivideDetailNo,
				LedgerNo:         re.LedgerNo,
				Amount:           fenInt,
				DivideDetailDesc: re.DivideDetailDesc,
			}
			divideDetailRes = append(divideDetailRes, item)
		}

		update := bson.M{}

		if divideQuery["divideType"] == "END_DIVIDE" {
			update["yee_divide_result.complete_divide_status"] = divideQuery["status"]
			update["yee_divide_result.complete_divide_detail"] = divideDetailRes
			update["yee_divide_result.complete_create_date"] = divideQuery["createDate"]
			update["yee_divide_result.complete_divide_success_date"] = divideQuery["divideSuccessDate"]
			err = s.OrderAgentPayDao.UpdateOne(ctx, bson.M{
				"_id": agentPay.ID,
			}, bson.M{
				"$set": update,
			})
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (s orderAgentPayService) YeeDivideQuery(ctx context.Context, divideRequestId, merchantNo, oriOrderId string) (map[string]string, error) {
	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/divide/query")

	yopRequest.AddParam("merchantNo", merchantNo) // 收款商户编号

	yopRequest.AddParam("orderId", oriOrderId) // 商户请求收款的交易单号

	yopRequest.AddParam("divideRequestId", divideRequestId) // 商户分账请求号

	yopResponse, err := s.yee.DoRequest(yopRequest)
	if err != nil {
		return nil, err
	}

	marshal, err := json.Marshal(yopResponse.Result)
	if err != nil {
		return nil, err
	}
	m := make(map[string]string)
	err = json.Unmarshal(marshal, &m)
	if err != nil {
		return nil, err
	}

	if m["code"] != "OPR00000" {
		return nil, xerr.NewErr(xerr.ErrParamError, nil, m["message"])
	}

	return m, nil
}

// SingleOrderAgentPayDeliver 配送费代付
func (s orderAgentPayService) SingleOrderAgentPayDeliver(ctx context.Context, parentOrderID primitive.ObjectID) error {
	parentOrder, err := s.parentOrderS.Get(ctx, parentOrderID)
	if err != nil {
		return err
	}
	//	不存在，取消代付
	if errors.Is(err, mongo.ErrNoDocuments) {
		s.l.Errorf("SingleOrderAgentPayDeliver 查询不存在支付订单%s，不进行配送费代付，错误%v", parentOrderID.Hex(), err.Error())
		return nil
	}
	if err != nil {
		s.l.Errorf("SingleOrderAgentPayDeliver 查询订单%v错误%v", parentOrderID.Hex(), err)
		return err
	}

	deliverAmount := parentOrder.DeliverFeeRes.FinalDeliverFee

	if deliverAmount == 0 {
		s.l.Infof("SingleOrderAgentPayDeliver FinalDeliverFee等于0,跳过%s", parentOrderID.Hex())
		return nil
	}

	agentPay, err := s.OrderAgentPayDao.Get(ctx, bson.M{
		"parent_order_id": parentOrderID,
		"agent_pay_type":  model.AgentPayTypeDeliver, // 配送费
	})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		s.l.Errorf("SingleOrderAgentPayDeliver 查询是否存在代付单，parent_order_id：%s错误%v", parentOrderID.Hex(), err)
		return err
	}
	if agentPay.ID != primitive.NilObjectID {
		s.l.Warnf("SingleOrderAgentPayDeliver 已经代付%s", parentOrderID.Hex())
		return nil
	}

	pointMerchant, err := s.yeeMerchantS.GetYeeByPoint(ctx, parentOrder.ServicePointID)
	if err != nil {
		return err
	}

	yeeDivideResult := model.YeeDivideResult{
		ParentMerchantNo: parentOrder.YeeWechatResult.ParentMerchantNo,
		//MerchantNo:             supplierMerchant.MerchantNo,
		ServicePointMerchantNo: pointMerchant.MerchantNo,
	}

	if parentOrder.PayMethod == model.PayMethodTypeYeeWechat {
		for _, sub := range parentOrder.YeeWechatResult.NotifySubOrderList {
			if sub.MerchantNo == pointMerchant.MerchantNo {
				yeeDivideResult.OriOrderId = sub.OrderId
				yeeDivideResult.OriUniqueOrderNo = sub.UniqueOrderNo
				break
			}
		}
	}

	if parentOrder.PayMethod == model.PayMethodTypeYeeBalance {
		yeeDivideResult.OriOrderId = parentOrder.YeeWechatResult.OrderID
		yeeDivideResult.OriUniqueOrderNo = parentOrder.YeeWechatResult.UniqueOrderNo
	}

	now := time.Now().UnixMilli()
	data := model.OrderAgentPay{
		ID:               primitive.NewObjectID(),
		BuyerID:          parentOrder.BuyerID,
		UserID:           parentOrder.UserID,
		AgentPayType:     model.AgentPayTypeDeliver,
		PayMethod:        parentOrder.PayMethod,
		ParentOrderID:    parentOrderID,
		OrderPaidAmount:  deliverAmount,
		OrderSplitAmount: deliverAmount,
		DeliverAmount:    deliverAmount,
		DeliverFeeAmount: 0,
		ServicePointID:   parentOrder.ServicePointID,
		YeeDivideResult:  yeeDivideResult,
		CreatedAt:        now,
		UpdatedAt:        now,
	}
	err = s.OrderAgentPayDao.Create(ctx, data)
	if err != nil {
		s.l.Errorf("创建代付单信息错误%v", err)
		return err
	}

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/divide/apply")

	yopRequest.AddParam("merchantNo", data.YeeDivideResult.ServicePointMerchantNo) // 收款商户编号

	yopRequest.AddParam("orderId", data.YeeDivideResult.OriOrderId) // 商户请求收款的交易单号

	divideRequestId := util.NewUUIDNum()
	yopRequest.AddParam("divideRequestId", divideRequestId) // 商户分账请求号

	yopRequest.AddParam("parentMerchantNo", data.YeeDivideResult.ParentMerchantNo) // 交易发起方商编
	yopRequest.AddParam("uniqueOrderNo", data.YeeDivideResult.OriUniqueOrderNo)    // 收款交易对应在易宝的收款单号
	//yopRequest.AddParam("isUnfreezeResidualAmount", "TRUE")                         // 是否解冻收单商户剩余可用金额

	divideDetail := make([]map[string]string, 0)

	// 中心仓：仓配费和服务费
	str := util.DealMoneyToYuanStr(deliverAmount)
	item := map[string]string{
		"amount":           str,
		"ledgerNo":         data.YeeDivideResult.ServicePointMerchantNo, // 分账接收方编号
		"ledgerType":       "MERCHANT2MERCHANT",                         // 分账给商户
		"divideDetailDesc": data.ParentOrderID.Hex() + "配送费",
	}
	divideDetail = append(divideDetail, item)

	if parentOrder.PayMethod == model.PayMethodTypeYeeBalance {
		yopRequest.AddParam("merchantNo", data.YeeDivideResult.ParentMerchantNo) // 收款商户编号
	}

	divideDetailM, err := json.Marshal(divideDetail)
	if err != nil {
		return err
	}

	yopRequest.AddParam("divideDetail", string(divideDetailM)) // 分账详情

	yopResponse, err := s.yee.DoRequest(yopRequest)
	if err != nil {
		return err
	}

	marshal, err := json.Marshal(yopResponse.Result)
	if err != nil {
		return err
	}
	m := make(map[string]string)
	err = json.Unmarshal(marshal, &m)
	if err != nil {
		return err
	}

	if m["code"] != "OPR00000" {
		zap.S().Errorf("分账异常:%s,分账单ID:%s", m["message"], data.ID.Hex())
		return xerr.NewErr(xerr.ErrParamError, nil, m["message"])
	}

	var resList []model.YeeDivideDetailRes

	err = json.Unmarshal([]byte(m["divideDetail"]), &resList)
	if err != nil {
		return err
	}

	var divideDetailRes []model.YeeDivideDetail
	for _, re := range resList {

		fenInt := util.DealMoneyFloatToFenInt(re.Amount)
		item := model.YeeDivideDetail{
			DivideDetailNo:   re.DivideDetailNo,
			LedgerNo:         re.LedgerNo,
			Amount:           fenInt,
			DivideDetailDesc: re.DivideDetailDesc,
		}
		divideDetailRes = append(divideDetailRes, item)
	}

	update := bson.M{
		"yee_divide_result.divide_request_id":   m["divideRequestId"],
		"yee_divide_result.status":              m["status"],
		"yee_divide_result.divide_detail":       divideDetailRes,
		"yee_divide_result.create_date":         m["createDate"],
		"yee_divide_result.divide_success_date": m["divideSuccessDate"],
	}
	err = s.OrderAgentPayDao.UpdateOne(ctx, bson.M{
		"_id": data.ID,
	}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	if m["status"] == "PROCESSING" {
		mnsSendService.NewMNSClient().SendDivideFlatCheck(ctx, data.ID)
	}

	return nil
}

func (s orderAgentPayService) ToAgentPayNormalOrder(ctx context.Context, orderID primitive.ObjectID) error {
	divideLock.Lock()
	defer divideLock.Unlock()

	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("ToAgentPay error:%v", err)
			return
		}
	}()

	order, err := s.orderS.Get(ctx, orderID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		s.l.Errorf("查询不存在订单%s，直接不进行代付，错误%v", orderID, err)
		return nil
	}
	if err != nil {
		return err
	}

	_ = order

	refunds, err := s.refundS.ListAfterSaleRefund(ctx, orderID)
	if err != nil {
		return err
	}
	var doNextPay bool // 执行下次分账检查
	for _, refund := range refunds {
		if (refund.AuditStatus == model.AuditStatusTypeDoing || (refund.AuditStatus == model.AuditStatusTypePass && refund.YeeRefundResult.Status != "SUCCESS")) && refund.IsWithdraw == false {
			//	 审核过，但是还没有退款，或者退款失败
			s.l.Infof("审核过，但是还没有退款，或者退款失败，订单%s，退款单%s", orderID, refund.ID.Hex())
			doNextPay = true
			break
		}
	}

	if doNextPay {
		s.l.Infof("执行下次代付检查%v", orderID)
		// 6小时后，换成秒
		seconds := time.Now().Add(time.Hour * 6).Unix()
		seconds = seconds - time.Now().Unix()

		mnsSendService.NewMNSClient().SendNormalOrderDivide(ctx, orderID, seconds)
		return nil
	}

	agentPay, err := s.SingleOrderAgentPay(ctx, orderID)
	if err != nil {
		s.l.Errorf("代付订单:%s错误:%v", orderID, err)
		return err
	}
	if agentPay.YeeDivideResult.Status != "" {
		return nil
	}

	// agentPay, err := s.GetOrderByOrder(ctx, orderID)
	// if err != nil {
	// 	return
	// }

	//return nil

	err = s.YeeDivide(ctx, agentPay)
	if err != nil {
		return err
	}

	// 执行完结分账
	if agentPay.PayMethod == model.PayMethodTypeYeeWechat {
		orderAgentPay, err := s.Get(ctx, agentPay.ID)
		if err != nil {
			s.l.Errorf("代付订单-完结分账:%s错误:%v", agentPay.ID.Hex(), err)
			return err
		}
		err = s.YeeDivideComplete(ctx, orderAgentPay)
		if err != nil {
			return err
		}
	}

	// 订单利润核算
	if order.CreatedAt > 1751299200000 {
		// 2025-07-01 00:00:00  之后的订单
		mnsSendService.NewMNSClient().SendOrderFinalSettle(ctx, orderID)
	}

	return nil
}

func (s orderAgentPayService) ToAgentPayDeliver(ctx context.Context, parentID primitive.ObjectID) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("ToAgentPayDeliver error:%v", err)
			return
		}
	}()
	deliverLock.Lock()
	defer deliverLock.Unlock()

	s.l.Infof("ToAgentPayDeliver--parentID:%s", parentID.Hex())

	//order, err := s.orderS.Get(ctx, orderID)
	//if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
	//	return err
	//}
	//
	//if errors.Is(err, mongo.ErrNoDocuments) {
	//	s.l.Errorf("配送费分账::%s订单不存在:%v", orderID.Hex(), err)
	//	return nil
	//}

	err := s.SingleOrderAgentPayDeliver(ctx, parentID)
	if err != nil {
		s.l.Errorf("代付订单parentOrderID::%s错误:%v", parentID.Hex(), err)
		return err
	}

	return nil
}

func (s orderAgentPayService) ToAgentPayDebtOrder(ctx context.Context, debtID primitive.ObjectID) error {
	divideLock.Lock()
	defer divideLock.Unlock()

	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("ToAgentPayForDebt error:%v", err)
			return
		}
	}()

	debt, err := s.debtS.Get(ctx, debtID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		s.l.Errorf("查询不存在补差订单%s，直接不进行代付，错误%v", debt.ID.Hex(), err)
		return nil
	}
	if err != nil {
		return err
	}

	s.l.Infof("ToAgentPayForDebt 执行补差订单分账,订单ID%s，补差单ID%s", debt.OrderID.Hex(), debtID)

	agentPay, err := s.SingleOrderAgentPayForDebt(ctx, debt)
	if err != nil {
		s.l.Errorf("代付订单:%s错误:%v", debtID, err.Error())
		return nil
	}

	// 执行分账
	err = s.YeeDivide(ctx, agentPay)
	if err != nil {
		return err
	}

	// 执行完结分账
	if agentPay.PayMethod == model.PayMethodTypeYeeWechat {
		orderAgentPay, err := s.Get(ctx, agentPay.ID)
		if err != nil {
			s.l.Errorf("代付订单-完结分账:%s错误:%v", orderAgentPay.ID.Hex(), err)
			return err
		}
		err = s.YeeDivideComplete(ctx, orderAgentPay)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s orderAgentPayService) UpdateAgentPayResult(ctx context.Context, id primitive.ObjectID, ps model.AgentPayResult) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"$set": bson.M{
			"agent_pay_result": ps,
		},
	}
	err := s.OrderAgentPayDao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s orderAgentPayService) List(ctx context.Context, filter bson.M) ([]model.OrderAgentPay, error) {
	list, err := s.OrderAgentPayDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s orderAgentPayService) Get(ctx context.Context, id primitive.ObjectID) (model.OrderAgentPay, error) {
	agentPay, err := s.OrderAgentPayDao.Get(ctx, bson.M{
		"_id": id,
	})
	if err != nil {
		return model.OrderAgentPay{}, err
	}

	return agentPay, nil
}
func (s orderAgentPayService) GetDebtByOrder(ctx context.Context, orderID primitive.ObjectID) (model.OrderAgentPay, error) {
	agentPay, err := s.OrderAgentPayDao.Get(ctx, bson.M{
		"order_id":       orderID,
		"agent_pay_type": model.AgentPayTypeDebt,
	})
	if err != nil {
		return model.OrderAgentPay{}, err
	}

	return agentPay, nil
}

func (s orderAgentPayService) GetOrderByOrder(ctx context.Context, orderID primitive.ObjectID) (model.OrderAgentPay, error) {
	agentPay, err := s.OrderAgentPayDao.Get(ctx, bson.M{
		"order_id":       orderID,
		"agent_pay_type": model.AgentPayTypeNormal,
	})
	if err != nil {
		return model.OrderAgentPay{}, err
	}

	return agentPay, nil
}

func (s orderAgentPayService) UpdateOrderStatus(ctx context.Context, BizOrderNo string) error {
	agentPay, err := s.OrderAgentPayDao.Get(ctx, bson.M{"biz_order_no": BizOrderNo})
	if err != nil {
		s.l.Errorf("回调根据BizOrderNo查询代付单:%v错误%v", BizOrderNo, err)
		return err
	}
	now := time.Now().UnixMilli()

	err = s.OrderAgentPayDao.UpdateOne(ctx, bson.M{"biz_order_no": BizOrderNo}, bson.M{"$set": bson.M{
		"agent_pay_result.pay_status": "success",
		"agent_pay_result.status":     "OK",
		"updated_at":                  now,
	}})
	if err != nil {
		return err
	}

	//var createdAt int64

	//1 普通订单 2 补差订单
	if agentPay.AgentPayType == model.AgentPayTypeNormal {
		err = s.orderS.UpdateOne(ctx, bson.M{"_id": agentPay.OrderID}, bson.M{"$set": bson.M{
			"has_agent_pay": true,
		}})
		if err != nil {
			return err
		}
		parentOrder, err := s.parentOrderS.Get(ctx, agentPay.ParentOrderID)
		if err != nil {
			return err
		}
		if parentOrder.DeliverFeeRes.FinalDeliverFee > 0 {
			//	 代付配送费
			//mnsSendService.NewMNSClient().SendAgentPayDeliver(parentOrder.ID)
		}
		//createdAt = parentOrder.CreatedAt
	}

	if agentPay.AgentPayType == model.AgentPayTypeDebt {
		//debt, err := s.debtS.GetByOrderID(ctx, agentPay.OrderID)
		//if err != nil {
		//	return err
		//}
		//
		//createdAt = debt.CreatedAt
		err = s.debtS.UpdateOne(ctx, bson.M{"order_id": agentPay.OrderID}, bson.M{"$set": bson.M{
			"has_agent_pay": true,
		}})
		if err != nil {
			return err
		}
	}

	if agentPay.RebateServiceFeeAmount > 0 {
		err = buyerBalanceOrderService.NewBuyerBalanceOrderService().CreateRebate(ctx, agentPay.BuyerID, agentPay.ID, agentPay.OrderID, agentPay.RebateServiceFeeAmount)
		if err == nil {
			//	 短信通知服务费返利
			//user, _ := userService.NewUserService().Get(ctx, agentPay.UserID)
			//

			//messageService.NewCaptchaService().SendServiceFeeRebate(user.Mobile, createdAt, agentPay.RebateServiceFeeAmount)

			//messageService.NewMessageService().AddRebateNotify(agentPay.BuyerID)
		}

	}
	return nil
}

//
//func (s orderAgentPayService) CheckSupplierAmount(ctx context.Context, bizOrderNo string) error {
//	agentPay, err := s.OrderAgentPayDao.Get(ctx, bson.M{"biz_order_no": bizOrderNo})
//	if err != nil {
//		return err
//	}
//	splitRes, _ := payModule.NewOrderS().GetOrderSplitRuleListDetailS(pays.GetOrderSplitRuleListDetailReq{
//		BizOrderNo: bizOrderNo,
//	})
//
//	var splitAmount, splitAmountFee int
//	for _, detail := range splitRes.SplitRuleListDetail {
//		splitAmount += detail.Amount
//		splitAmountFee += detail.Fee
//	}
//	originAmount := agentPay.SupplierAmount
//	actualSupplierAmount := agentPay.OrderSplitAmount - splitAmount - agentPay.SupplierFeeAmount
//	if actualSupplierAmount == originAmount {
//		return nil
//	}
//	if agentPay.BuyerName == "enjoy__322" {
//		return nil
//	}
//
//	if bizOrderNo == "2e57797b-4f4a-4a8e-b227-8d4e342c10aa" || bizOrderNo == "c90e2a23-5c70-49af-ac61-487547b147d1" ||
//		bizOrderNo == "74a8bdf1-ce68-4f10-b98e-6f47c2245e1a" {
//		return nil
//	}
//
//	if actualSupplierAmount != originAmount {
//		fmt.Printf("需要更新\n")
//		//return nil
//	}
//
//	err = s.OrderAgentPayDao.UpdateOne(ctx, bson.M{"biz_order_no": bizOrderNo}, bson.M{"$set": bson.M{
//		"supplier_amount": actualSupplierAmount,
//	}})
//	if err != nil {
//		return err
//	}
//
//	return nil
//}

// ApplyPromotionSubsidy 申请营销补贴
func (s orderAgentPayService) ApplyPromotionSubsidy(ctx context.Context, agentPay *model.OrderAgentPay) error {

	subsidyRequestID := util.NewUUIDNum()

	data := model.PromotionSubsidy{
		ID:               primitive.NewObjectID(),
		OrderID:          agentPay.OrderID,
		YeeOrderID:       agentPay.YeeDivideResult.OriOrderId,
		UniqueOrderNo:    agentPay.YeeDivideResult.OriUniqueOrderNo,
		SubsidyRequestID: subsidyRequestID,
		AssumeMerchantNo: merchantPlatformNo,
		ParentMerchantNo: merchantPlatformNo,
		MerchantNo:       agentPay.YeeDivideResult.MerchantNo,
		SubsidyAmount:    agentPay.TotalPromotionSubsidyAmount,
		Memo:             "优惠券补贴",
		Status:           model.PromotionSubsidyStatusPending,
		CreatedAt:        time.Now().UnixMilli(),
		UpdatedAt:        time.Now().UnixMilli(),
	}
	err := s.PromotionSubsidyDao.Create(ctx, data)
	if err != nil {
		return err
	}

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/promtion/subsidy/apply")

	yopRequest.AddParam("orderId", data.YeeOrderID)
	yopRequest.AddParam("uniqueOrderNo", data.UniqueOrderNo)
	yopRequest.AddParam("subsidyRequestId", data.SubsidyRequestID)
	yopRequest.AddParam("assumeMerchantNo", merchantPlatformNo) // 固定出资方商编
	yopRequest.AddParam("parentMerchantNo", merchantPlatformNo) // 固定发起方商编
	yopRequest.AddParam("merchantNo", data.MerchantNo)

	subsidyAmount := util.DealMoneyToYuanStr(data.SubsidyAmount)

	yopRequest.AddParam("subsidyAmount", subsidyAmount)
	yopRequest.AddParam("memo", "优惠券补贴") // 固定描述

	yopResponse, err := s.yee.DoRequest(yopRequest)
	if err != nil {
		s.l.Errorf("申请营销补贴失败:%v", err)
		return err
	}

	marshal, err := json.Marshal(yopResponse.Result)
	if err != nil {
		return err
	}
	var yopRes model.YeePromotionSubsidyApplyRes
	err = json.Unmarshal(marshal, &yopRes)
	if err != nil {
		return err
	}

	if yopRes.Code != "OPR00000" {
		s.l.Errorf("申请营销补贴异常:%s", yopRes.Message)
		return xerr.NewErr(xerr.ErrParamError, nil, yopRes.Message)
	}

	return nil
}

// GetPromotionSubsidyByOrder 查询营销补贴
func (s orderAgentPayService) GetPromotionSubsidyByOrder(ctx context.Context, orderID primitive.ObjectID,
) (model.PromotionSubsidy, error) {
	promotionSubsidy, err := s.PromotionSubsidyDao.Get(ctx, bson.M{"order_id": orderID})
	if err != nil {
		return model.PromotionSubsidy{}, err
	}
	return promotionSubsidy, nil
}

// QueryPromotionSubsidy 查询营销补贴
func (s orderAgentPayService) QueryPromotionSubsidy(ctx context.Context, orderID primitive.ObjectID,
) (model.PromotionSubsidyRes, error) {
	promotionSubsidy, err := s.GetPromotionSubsidyByOrder(ctx, orderID)
	if err != nil {
		return model.PromotionSubsidyRes{}, err
	}

	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/promtion/subsidy/query")

	yopRequest.AddParam("orderId", promotionSubsidy.YeeOrderID)
	yopRequest.AddParam("subsidyRequestId", promotionSubsidy.SubsidyRequestID)
	yopRequest.AddParam("parentMerchantNo", promotionSubsidy.ParentMerchantNo)
	yopRequest.AddParam("merchantNo", promotionSubsidy.MerchantNo)

	yopResponse, err := s.yee.DoRequest(yopRequest)
	if err != nil {
		s.l.Errorf("查询营销补贴失败:%v", err)
		return model.PromotionSubsidyRes{}, err
	}

	marshal, err := json.Marshal(yopResponse.Result)
	if err != nil {
		return model.PromotionSubsidyRes{}, err
	}
	var yopRes model.PromotionSubsidyRes
	err = json.Unmarshal(marshal, &yopRes)
	if err != nil {
		return model.PromotionSubsidyRes{}, err
	}

	if yopRes.Code != "OPR00000" {
		s.l.Errorf("查询营销补贴异常:%s", yopRes.Message)
		return model.PromotionSubsidyRes{}, xerr.NewErr(xerr.ErrParamError, nil, yopRes.Message)
	}

	if promotionSubsidy.Status == model.PromotionSubsidyStatusPending {
		if yopRes.Status == "SUCCESS" {
			err = s.PromotionSubsidyDao.UpdateOne(ctx, bson.M{"_id": promotionSubsidy.ID}, bson.M{"$set": bson.M{
				"status": model.PromotionSubsidyStatusSuccess,
			}})
			if err != nil {
				return model.PromotionSubsidyRes{}, err
			}
		}

		if yopRes.Status == "FAIL" {
			s.l.Errorf("查询营销补贴失败:%s", yopRes.Message)
			return model.PromotionSubsidyRes{}, xerr.NewErr(xerr.ErrParamError, nil, yopRes.Message)
		}
	}

	return yopRes, nil
}

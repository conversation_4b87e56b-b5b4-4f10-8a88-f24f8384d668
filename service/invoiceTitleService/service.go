package invoiceTitleService

import (
	"base/dao"
	"base/dao/invoiceTitleDao"
	"base/global"
	"base/model"
	"base/service/aesService"
	"base/types"
	"base/util"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type ServiceInterface interface {
	Upsert(ctx context.Context, buyer model.Buyer, req types.InvoiceTitleUpsert) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.InvoiceTitle, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.InvoiceTitle, int64, error)
}

type invoiceTitleService struct {
	rdb             *redis.Client
	invoiceTitleDao invoiceTitleDao.DaoInt
	aesS            aesService.ServiceInterface
}

func NewInvoiceTitleService() ServiceInterface {
	return invoiceTitleService{
		rdb:             global.RDBDefault,
		invoiceTitleDao: dao.InvoiceTitleDao,
		aesS:            aesService.NewAesService(),
	}
}

func (s invoiceTitleService) Upsert(ctx context.Context, buyer model.Buyer, req types.InvoiceTitleUpsert) error {
	now := time.Now().UnixMilli()

	data := model.InvoiceTitle{
		ID:               primitive.NewObjectID(),
		BuyerID:          buyer.ID,
		InvoiceTitleType: req.InvoiceTitleType,
		InvoiceTitle:     req.InvoiceTitle,
		TaxNumber:        req.TaxNumber,
		Address:          req.Address,
		PhoneNumber:      req.PhoneNumber,
		BankName:         req.BankName,
		BankAccount:      req.BankAccount,
		Remark:           req.Remark,
		CreatedAt:        now,
		UpdatedAt:        now,
	}

	if data.BankAccount != "" {
		en, err := s.aesS.En(data.BankAccount)
		if err != nil {
			return err
		}
		data.BankAccount = en
	}

	if data.PhoneNumber != "" {
		en, err := s.aesS.En(data.PhoneNumber)
		if err != nil {
			return err
		}
		data.PhoneNumber = en
	}

	if len(req.ID) == 24 {
		//	编辑
		id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
		if err != nil {
			return err
		}
		byID, err := s.GetByID(ctx, id)
		if err != nil {
			return err
		}
		data.ID = byID.ID
		data.CreatedAt = byID.CreatedAt
		err = s.invoiceTitleDao.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": data})
		if err != nil {
			return err
		}
	} else {
		//	新增
		err := s.invoiceTitleDao.Create(ctx, data)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s invoiceTitleService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"deleted_at": time.Now().UnixMilli(),
	}
	err := s.invoiceTitleDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s invoiceTitleService) GetByID(ctx context.Context, id primitive.ObjectID) (model.InvoiceTitle, error) {
	filter := bson.M{
		"_id": id,
	}
	fee, err := s.invoiceTitleDao.Get(ctx, filter)
	if err != nil {
		return model.InvoiceTitle{}, err
	}
	return fee, nil
}

func (s invoiceTitleService) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.InvoiceTitle, int64, error) {
	list, count, err := s.invoiceTitleDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

package yeeMerchantService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/authenticationDao"
	"base/dao/yeeMerchantDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/withdrawApplyOrderService"
	"base/types"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"io"
	"net/http"
	"os"
	"time"

	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"github.com/yop-platform/yop-go-sdk/yop/client"
	"github.com/yop-platform/yop-go-sdk/yop/constants"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"github.com/yop-platform/yop-go-sdk/yop/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	Create(ctx context.Context, mobile string, req types.AuthenticationReq, userID, objectID primitive.ObjectID, objectType model.ObjectType, mType pays.MemberType) error
	CreateByStation(ctx context.Context, mobile string, userID, objectID primitive.ObjectID) error
	CreateBySupplier(ctx context.Context, userMobile string, supplier model.Supplier) error
	CreateByPoint(ctx context.Context, point model.ServicePoint) error

	CreateYeeByBuyer(ctx context.Context, buyerID primitive.ObjectID) (model.YeeMerchant, error)

	CheckParam(ctx context.Context, req types.AuthenticationReq) error
	// CreateIndividual 个人会员
	CreateIndividual(ctx context.Context, mobile string, userID, objectID primitive.ObjectID, objectType model.ObjectType, mType pays.MemberType) error
	Update(ctx context.Context, req types.YeeMerchantUpdateReq) error
	UpdateBySecondPoint(ctx context.Context, req types.YeeMerchantUpdateReq) error
	YeeUpload(ctx context.Context, yeeMerchant model.YeeMerchant, object string) error

	YeeRegisterQuery(ctx context.Context, id primitive.ObjectID) (model.QueryRes, error)
	YeeRegisterQueryByPoint(ctx context.Context, id primitive.ObjectID) (model.QueryRes, error)

	UpdateBuyerCreditCode(ctx context.Context, id primitive.ObjectID, creditCode string) error
	UpdateCus(ctx context.Context, filter, update bson.M) error
	UpdateReApply(ctx context.Context, req types.AuthenticationReq, objectID primitive.ObjectID, objectType model.ObjectType) error
	UpdateHasSubmitPayOcr(ctx context.Context, id primitive.ObjectID, picType pays.PicType) error
	UpdateByBizUserID(ctx context.Context, bizUserID string) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.YeeMerchant, error)
	GetByApplicationNo(ctx context.Context, no string) (model.YeeMerchant, error)
	GetByObject(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType) (model.Authentication, error)
	GetByUserAndObject(ctx context.Context, userID primitive.ObjectID, objectType model.ObjectType) (model.Authentication, error)
	GetByBuyer(ctx context.Context, id primitive.ObjectID) (model.Authentication, error)
	GetYeeByBuyer(ctx context.Context, id primitive.ObjectID) (model.YeeMerchant, error)
	GetYeeByPoint(ctx context.Context, id primitive.ObjectID) (model.YeeMerchant, error)
	GetBuyerByUser(ctx context.Context, userID primitive.ObjectID) (model.Authentication, error)
	GetByUserAndEnv(ctx context.Context, userID primitive.ObjectID, env model.ObjectType) (model.Authentication, error)
	GetByBizUserID(ctx context.Context, bizUserID string) (model.Authentication, error)
	GetBySupplier(ctx context.Context, id primitive.ObjectID) (model.YeeMerchant, error)
	GetByStation(ctx context.Context, id primitive.ObjectID) (model.Authentication, error)
	BindBankByStation(ctx context.Context, id primitive.ObjectID, mobile, cardNo string) error
	SetRealNameByStation(ctx context.Context, id primitive.ObjectID, name, identityNo string) error
	GetByWarehouse(ctx context.Context, id primitive.ObjectID) (model.Authentication, error)
	CheckBySupplier(ctx context.Context, id primitive.ObjectID) (bool, error)
	CheckByWarehouse(ctx context.Context, id primitive.ObjectID) (bool, error)
	ListAll(ctx context.Context) ([]model.Authentication, error)
	ListByStation(ctx context.Context, ids []primitive.ObjectID) ([]model.Authentication, error)

	UpdateBindMobileStatus(ctx context.Context, id primitive.ObjectID, mobile string, verify bool) error
	//	NotifySetCompany 设置企业信息回调
	NotifySetCompany(ctx context.Context, res allinpay.VerifyResult)
	// NotifyOcrComparisonResult 影印件核对结果异步通知
	NotifyOcrComparisonResult(ctx context.Context, res allinpay.OcrComparisonResult)
	// NotifySignAcctProtocol 提现协议
	//NotifySignAcctProtocol(ctx context.Context, res allinpay.SignAcctProtocolResult)

	QueryApplyBindAcctForWechat(ctx context.Context, bizUserId string) (pays.ApplyBindAcctRes, error) //  绑定小程序支付标识--查询

	ListBankCard(ctx context.Context, bizUserID string) ([]pays.BindBankInfo, error)

	RegisterMerchant(ctx context.Context, merchant model.YeeMerchant) error
	RegisterMerchantForMicro(ctx context.Context, merchant model.YeeMerchant) error

	MerchantWechatAuthApply(ctx context.Context, merchant model.YeeMerchant) error
	MerchantWechatAuthQuery(ctx context.Context, merchant model.YeeMerchant) (model.WechatAuthQueryRes, error)
	MerchantWechatAuthCancel(ctx context.Context, supplierID primitive.ObjectID) error
	MerchantAuthQuery(ctx context.Context, id primitive.ObjectID) (model.MerAuthQueryRes, error)

	AccountOpen(ctx context.Context, buyerID primitive.ObjectID) error
	AccountQuery(ctx context.Context, buyerID primitive.ObjectID) (model.YeeAccountQueryRes, error)
	AccountBookRechargeQuery(ctx context.Context, buyerID primitive.ObjectID) (model.YeeAccountRechargeQueryRes, error)
	AccountBookRefund(ctx context.Context, buyerID primitive.ObjectID) error

	AccountWithDrawCardQuery(ctx context.Context, merchant model.YeeMerchant) (model.AccountWithDrawCardQueryRes, error)
	AccountWithDrawCardBind(ctx context.Context, merchant model.YeeMerchant, accountNo, bankCode string) (model.AccountWithDrawCardBindRes, error)
	InitWithDrawCardBind(ctx context.Context) error
	AccountWithDraw(ctx context.Context, merchant model.YeeMerchant, amount int, bankAccountNo string) error
	AccountWithDrawQuery(ctx context.Context, merchantNo, orderNo string) error

	BalanceQuery(ctx context.Context, merchant model.YeeMerchant) (model.YeeBalanceQueryRes, error)
	BalanceAccountQuery(ctx context.Context, merchant model.YeeMerchant) (model.YeeBalanceAccountQueryRes, error)
	BalanceAccountByNo(ctx context.Context, merchantNo string) (model.YeeBalanceAccountQueryRes, error)

	MerchantWechatConfigAdd(ctx context.Context, supplierID primitive.ObjectID) error

	RegisterResultNotify(ctx context.Context, res map[string]string) error

	ProductFeeQuery(ctx context.Context, mer model.YeeMerchant) (any, error)
	ProductFeeUpdate(ctx context.Context, mer model.YeeMerchant) error

	TransferToMarket(ctx context.Context, amount int) error
	TransferQuery(ctx context.Context, orderNo string) error
}

type yeeMerchantService struct {
	rdb               *redis.Client
	yeePay            *global.YeePayInfo
	authenticationDao authenticationDao.DaoInt

	yeeMerchantDao yeeMerchantDao.DaoInt

	withdrawApplyOrderS withdrawApplyOrderService.ServiceInterface

	// 支付 会员
	payMember payModule.MemberService
}

func NewYeeMerchantService() ServiceInterface {
	return yeeMerchantService{
		rdb:                 global.RDBDefault,
		yeePay:              global.YeePay,
		authenticationDao:   dao.AuthenticationDao,
		yeeMerchantDao:      dao.YeeMerchantDao,
		withdrawApplyOrderS: withdrawApplyOrderService.NewWithdrawApplyOrderService(),
		payMember:           payModule.NewMember(),
	}
}

// ApplyBindAcctForWechat 绑定小程序支付标识
func (s yeeMerchantService) ApplyBindAcctForWechat(ctx context.Context, bizUserId, openID string) error {
	res, err := s.payMember.ApplyBindAcctS(pays.ApplyBindAcctReq{
		BizUserId:     bizUserId,
		OperationType: "set",
		Acct:          openID,
		AcctType:      "weChatMiniProgram",
	})
	if err == xerr.XerrPayAccHasBind {
		//	 更新状态即可
		update := bson.M{
			"$set": bson.M{
				"is_pay_mini_acc": true,
			},
			"$addToSet": bson.M{
				"pay_mini_acc_list": openID,
			},
		}
		err = s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": bizUserId}, update)
		if err != nil {
			zap.S().Errorf("更新绑定微信支付标识错误，%v", err)
			return err
		}
		return nil
	}
	if err != nil {
		return err
	}
	if res.Result == "OK" {
		//	 成功
		update := bson.M{
			"$set": bson.M{
				"is_pay_mini_acc": true,
			},
			"$addToSet": bson.M{
				"pay_mini_acc_list": openID,
			},
		}
		err = s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": bizUserId}, update)
		if err != nil {
			zap.S().Errorf("更新绑定微信支付标识错误，%v", err)
			return err
		}

		return nil
	}
	zap.S().Errorf("绑定微信支付标识错误，%v", err)

	return nil
}

// QueryApplyBindAcctForWechat 绑定小程序支付标识--查询
func (s yeeMerchantService) QueryApplyBindAcctForWechat(ctx context.Context, bizUserId string) (pays.ApplyBindAcctRes, error) {
	// 创建会员
	res, err := s.payMember.ApplyBindAcctS(pays.ApplyBindAcctReq{
		BizUserId:     bizUserId,
		OperationType: "query",
	})
	if err != nil {
		return pays.ApplyBindAcctRes{}, err
	}
	return res, nil
}

func (s yeeMerchantService) CreateIndividual(ctx context.Context, mobile string, userID, objectID primitive.ObjectID, objectType model.ObjectType, mType pays.MemberType) error {
	data := model.Authentication{
		ID:             primitive.NewObjectID(),
		UserID:         userID,
		ObjectType:     objectType,
		ObjectID:       objectID,
		Mobile:         mobile,
		IsMobileVerify: false,
		MemberType:     pays.MemberTypeIndividual, // 个人
		CreatedAt:      time.Now().UnixMilli(),
	}

	// 创建会员
	res, err := s.payMember.CreateMemberS(pays.CreateMemberReq{
		BizUserId:  util.NewUUID(),
		MemberType: mType,
		Source:     pays.SourceMobile,
		ExtendParam: map[string]interface{}{
			"buyer_id": objectID.Hex(),
		}})
	if err != nil {
		return err
	}

	data.PayUserID = res.UserID
	data.PayBizUserId = res.BizUserId

	err = s.authenticationDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) NotifySetCompany(ctx context.Context, res allinpay.VerifyResult) {
	update := bson.M{
		"result":             res.Result,
		"account_set_result": res.AccountSetResult,
		"check_time":         res.CheckTime,
		"fail_reason":        res.FailReason,
		"remark":             res.Remark,
	}

	err := s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{"$set": update})
	if err != nil {
		zap.S().Error("设置企业信息异步通知更新失败：", err)
	}

}

//
//func (s authenticationService) NotifySignAcctProtocol(ctx context.Context, res allinpay.SignAcctProtocol) {
//	update := bson.M{
//		"sign_result":      res.Result,
//		"acct_protocol_no": res.AcctProtocolNo,
//	}
//
//	err := s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{"$set": update})
//	if err != nil {
//		zap.S().Error("更新提现协议编号失败：", err)
//	}
//
//}

func (s yeeMerchantService) NotifyOcrComparisonResult(ctx context.Context, res allinpay.OcrComparisonResult) {
	update := bson.M{
		"result_info":   res.ResultInfo,
		"req_serial_no": res.ReqSerialNo,
	}

	if res.OcrRegnumComparisonResult != 0 {
		update["ocr_regnum_comparison_result"] = res.OcrRegnumComparisonResult
	}

	if res.OcrIdcardComparisonResult != 0 {
		update["ocr_idcard_comparison_result"] = res.OcrIdcardComparisonResult
	}

	err := s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{"$set": update})
	if err != nil {
		zap.S().Error("设置企业信息异步通知更新失败：", err)
	}

}

func (s yeeMerchantService) GetByID(ctx context.Context, id primitive.ObjectID) (model.YeeMerchant, error) {
	get, err := s.yeeMerchantDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.YeeMerchant{}, err
	}
	return get, nil
}

func (s yeeMerchantService) GetByApplicationNo(ctx context.Context, no string) (model.YeeMerchant, error) {
	get, err := s.yeeMerchantDao.Get(ctx, bson.M{"application_no": no})
	if err != nil {
		return model.YeeMerchant{}, err
	}
	return get, nil
}

func (s yeeMerchantService) UpdateUnbindMobileStatus(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{"_id": id}

	update := bson.M{
		"is_mobile_verify": false,
		"mobile":           "",
	}

	err := s.authenticationDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) UpdateBindMobileStatus(ctx context.Context, id primitive.ObjectID, mobile string, verify bool) error {
	filter := bson.M{"_id": id}

	update := bson.M{
		"is_mobile_verify": verify,
		"mobile":           mobile,
	}

	err := s.authenticationDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) Update(ctx context.Context, req types.YeeMerchantUpdateReq) error {
	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		return err
	}

	err = checkUpdate(req)
	if err != nil {
		return err
	}

	update := bson.M{
		"merchant_subject_info.sign_type":                req.MerchantSubjectInfo.SignType,
		"merchant_subject_info.licence_no":               req.MerchantSubjectInfo.LicenceNo,
		"merchant_subject_info.sign_name":                req.MerchantSubjectInfo.SignName,
		"merchant_subject_info.short_name":               req.MerchantSubjectInfo.ShortName,
		"merchant_subject_info.license_img":              req.MerchantSubjectInfo.LicenseImg,
		"merchant_subject_info.licence_valid_date_begin": req.MerchantSubjectInfo.LicenceValidDateBegin,
		"merchant_subject_info.licence_valid_date_end":   req.MerchantSubjectInfo.LicenceValidDateEnd,
		//"merchant_subject_info.hand_licence_img": req.MerchantSubjectInfo.HandLicenceImg,

		"merchant_corporation_info.legal_licence_type":             "ID_CARD",
		"merchant_corporation_info.legal_name":                     req.MerchantCorporationInfo.LegalName,
		"merchant_corporation_info.legal_licence_no":               req.MerchantCorporationInfo.LegalLicenceNo,
		"merchant_corporation_info.legal_licence_front_img":        req.MerchantCorporationInfo.LegalLicenceFrontImg,
		"merchant_corporation_info.legal_licence_back_img":         req.MerchantCorporationInfo.LegalLicenceBackImg,
		"merchant_corporation_info.legal_licence_valid_date_begin": req.MerchantCorporationInfo.LegalLicenceValidDateBegin,
		"merchant_corporation_info.legal_licence_valid_date_end":   req.MerchantCorporationInfo.LegalLicenceValidDateEnd,

		"settlement_account_info.bank_card_no": req.SettlementAccountInfo.BankCardNo,
		"settlement_account_info.bank_code":    req.SettlementAccountInfo.BankCode,

		"business_address_info.province": req.BusinessAddressInfo.Province,
		"business_address_info.city":     req.BusinessAddressInfo.City,
		"business_address_info.district": req.BusinessAddressInfo.District,
		"business_address_info.address":  req.BusinessAddressInfo.Address,

		"merchant_contact_info.contact_email": req.MerchantContactInfo.ContactEmail,

		"business_role": model.BusinessRoleSETTLEDMERCHANT,
		"updated_at":    time.Now().UnixMilli(),
	}

	if req.MerchantSubjectInfo.SignType == model.SignTypeENTERPRISE {
		//	企业
		update["merchant_subject_info.enterprise_open_img"] = req.MerchantSubjectInfo.EnterpriseOpenImg
		update["settlement_account_info.bank_account_type"] = model.BankAccountTypeENTERPRISEACCOUNT
	}

	if req.MerchantSubjectInfo.SignType == model.SignTypeINDIVIDUAL {
		//	个体工商户
		update["merchant_subject_info.individual_bank_card_img"] = req.MerchantSubjectInfo.IndividualBankCardImg
		update["settlement_account_info.bank_account_type"] = model.BankAccountTypeDEBITCARD
	}

	filter := bson.M{"_id": id}

	err = s.yeeMerchantDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) UpdateBySecondPoint(ctx context.Context, req types.YeeMerchantUpdateReq) error {
	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		return err
	}

	//err = checkUpdate(req)
	//if err != nil {
	//	return err
	//}

	update := bson.M{
		//"merchant_subject_info.sign_type":                req.MerchantSubjectInfo.SignType,
		//"merchant_subject_info.licence_no":               req.MerchantSubjectInfo.LicenceNo,
		"merchant_subject_info.sign_name":  req.MerchantCorporationInfo.LegalName,
		"merchant_subject_info.short_name": req.MerchantSubjectInfo.ShortName,
		//"merchant_subject_info.license_img":              req.MerchantSubjectInfo.LicenseImg,
		//"merchant_subject_info.licence_valid_date_begin": req.MerchantSubjectInfo.LicenceValidDateBegin,
		//"merchant_subject_info.licence_valid_date_end":   req.MerchantSubjectInfo.LicenceValidDateEnd,
		//"merchant_subject_info.hand_licence_img": req.MerchantSubjectInfo.HandLicenceImg,

		"merchant_corporation_info.legal_licence_type":             "ID_CARD",
		"merchant_corporation_info.legal_name":                     req.MerchantCorporationInfo.LegalName,
		"merchant_corporation_info.legal_licence_no":               req.MerchantCorporationInfo.LegalLicenceNo,
		"merchant_corporation_info.legal_licence_front_img":        req.MerchantCorporationInfo.LegalLicenceFrontImg,
		"merchant_corporation_info.legal_licence_back_img":         req.MerchantCorporationInfo.LegalLicenceBackImg,
		"merchant_corporation_info.legal_licence_valid_date_begin": req.MerchantCorporationInfo.LegalLicenceValidDateBegin,
		"merchant_corporation_info.legal_licence_valid_date_end":   req.MerchantCorporationInfo.LegalLicenceValidDateEnd,

		"settlement_account_info.bank_card_no": req.SettlementAccountInfo.BankCardNo,
		"settlement_account_info.bank_code":    req.SettlementAccountInfo.BankCode,

		"business_address_info.province": req.BusinessAddressInfo.Province,
		"business_address_info.city":     req.BusinessAddressInfo.City,
		"business_address_info.district": req.BusinessAddressInfo.District,
		"business_address_info.address":  req.BusinessAddressInfo.Address,

		"merchant_contact_info.contact_email": req.MerchantContactInfo.ContactEmail,

		"business_role": model.BusinessRoleMicroMERCHANT,
		"updated_at":    time.Now().UnixMilli(),
	}

	update["merchant_subject_info.individual_bank_card_img"] = req.MerchantSubjectInfo.IndividualBankCardImg
	update["settlement_account_info.bank_account_type"] = model.BankAccountTypeDEBITCARD

	filter := bson.M{"_id": id}

	err = s.yeeMerchantDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func checkUpdate(req types.YeeMerchantUpdateReq) error {
	subject := req.MerchantSubjectInfo
	if subject.SignType != model.SignTypeENTERPRISE && subject.SignType != model.SignTypeINDIVIDUAL {
		return xerr.NewErr(xerr.ErrParamError, nil, "商户签约类型错误")
	}

	if subject.SignName == "" || subject.ShortName == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写主体名称和商户简称")
	}

	if subject.LicenseImg.Name == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请上传营业执照图片")
	}

	if subject.SignType == model.SignTypeENTERPRISE {
		//	 企业
		if subject.EnterpriseOpenImg.Name == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "企业类型，请上传开户信息图片")
		}
	}
	if subject.SignType == model.SignTypeINDIVIDUAL {
		//	 企业
		if subject.IndividualBankCardImg.Name == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "个体工商户类型，请上传银行卡图片")
		}
	}

	legal := req.MerchantCorporationInfo
	if legal.LegalName == "" || legal.LegalLicenceNo == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写法人信息")
	}

	if legal.LegalLicenceFrontImg.Name == "" || legal.LegalLicenceBackImg.Name == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请上传身份证图片")
	}

	if req.MerchantContactInfo.ContactEmail == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写联系人邮箱")
	}

	addr := req.BusinessAddressInfo
	if addr.Province == "" || addr.City == "" || addr.District == "" || addr.Address == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写地址信息")
	}

	account := req.SettlementAccountInfo

	if account.BankCardNo == "" || account.BankCode == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写账户账号和总行编号")
	}

	return nil
}

func (s yeeMerchantService) UpdateBuyerCreditCode(ctx context.Context, id primitive.ObjectID, creditCode string) error {
	filter := bson.M{"_id": id}
	now := time.Now().UnixMilli()

	update := bson.M{
		"company.credit_code": creditCode,
		"updated_at":          now,
	}

	err := s.authenticationDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) UpdateByBizUserID(ctx context.Context, bizUserID string) error {

	return nil
}

func (s yeeMerchantService) UpdateHasSubmitPayOcr(ctx context.Context, id primitive.ObjectID, picType pays.PicType) error {
	filter := bson.M{"_id": id}
	update := bson.M{}

	switch picType {
	case pays.PicTypeBusinessLicense:
		update["company.has_ocr_regnum_comparison"] = true
	case pays.PicTypeIDCardFront:
		update["company.legal.has_ocr_idcard_comparison_front"] = true
	case pays.PicTypeIDCardBack:
		update["company.legal.has_ocr_idcard_comparison_back"] = true
	default:
		return xerr.NewErr(xerr.ErrParamError, nil, "PicType参数错误")

	}

	err := s.authenticationDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) UpdateCus(ctx context.Context, filter, update bson.M) error {
	err := s.authenticationDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) GetByObject(ctx context.Context, objectID primitive.ObjectID, objectType model.ObjectType) (model.Authentication, error) {
	filter := bson.M{
		"object_type": objectType,
		"object_id":   objectID,
	}
	authentication, err := s.authenticationDao.Get(ctx, filter)
	if err != nil {
		return model.Authentication{}, err
	}
	return authentication, nil
}

func (s yeeMerchantService) GetByUserAndObject(ctx context.Context, userID primitive.ObjectID, objectType model.ObjectType) (model.Authentication, error) {
	filter := bson.M{
		"object_type": objectType,
		"user_id":     userID,
	}
	authentication, err := s.authenticationDao.Get(ctx, filter)
	if err != nil {
		return model.Authentication{}, err
	}
	return authentication, nil
}

func (s yeeMerchantService) Create(ctx context.Context, mobile string, req types.AuthenticationReq, userID, objectID primitive.ObjectID, objectType model.ObjectType, mType pays.MemberType) error {
	now := time.Now().UnixMilli()
	company := model.Company{
		BusinessLicenseImg: req.BusinessLicenseImg,
		CompanyType:        req.CompanyType,
		Legal: model.Legal{
			LegalPhone:     req.LegalPhone,
			IdCardFrontImg: req.LegalIdCardFrontImg,
			IdCardBackImg:  req.LegalIdCardBackImg,
		},
	}
	aType := model.AccountTypePublic
	if req.CompanyType == model.CompanyTypePerson {
		// 个体工商户
		aType = model.AccountTypeSelf
		if len(req.BankReservedMobile) != 11 {
			return xerr.NewErr(xerr.ErrParamError, nil, "个体工商户需要填写银行账户预留手机号")
		}
	}
	if req.CompanyType == model.CompanyTypeCo {
		//	企业
		if req.BankName == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "公司需要填写开户支行名称和支付行号")
		}
		if len(req.UnionBank) != 12 {
			return xerr.NewErr(xerr.ErrParamError, nil, "公司需要12位支付行号")
		}
	}

	bankAccount := model.BankAccount{
		AccountType: aType,
		CardNumber:  req.CardNumber,
		//对公
		//ParentBankName: "",
		BankName:  req.BankName,
		UnionBank: req.UnionBank,
		//对私
		BankReservedMobile: req.BankReservedMobile,
		//Bankcard:   ,
		BankcardImg: req.BankcardImg,
	}

	// 创建会员
	createMember, err := s.payMember.CreateMemberS(pays.CreateMemberReq{
		BizUserId:  util.NewUUID(),
		MemberType: mType,
		Source:     pays.SourceMobile,
		ExtendParam: map[string]interface{}{
			"object_id":   objectID.Hex(),
			"object_type": objectType,
		}})
	if err != nil {
		return err
	}

	data := model.Authentication{
		ID:           primitive.NewObjectID(),
		UserID:       userID,
		ObjectType:   objectType,
		ObjectID:     objectID,
		Mobile:       mobile,
		MemberType:   mType,
		PayUserID:    createMember.UserID,
		PayBizUserId: createMember.BizUserId,
		Company:      company,
		BankAccount:  bankAccount,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	err = s.authenticationDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) CreateByStation(ctx context.Context, mobile string, userID, objectID primitive.ObjectID) error {
	byStation, err := s.GetByStation(ctx, objectID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if byStation.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "已存在会员支付信息")
	}

	now := time.Now().UnixMilli()
	// 创建会员
	mType := pays.MemberTypeIndividual
	createMember, err := s.payMember.CreateMemberS(pays.CreateMemberReq{
		BizUserId:  util.NewUUID(),
		MemberType: mType, // 个人
		Source:     pays.SourceMobile,
		ExtendParam: map[string]interface{}{
			"object_id":   objectID.Hex(),
			"object_type": model.ObjectTypeStation,
		}})
	if err != nil {
		return err
	}

	data := model.Authentication{
		ID:           primitive.NewObjectID(),
		UserID:       userID,
		ObjectType:   model.ObjectTypeStation,
		ObjectID:     objectID,
		Mobile:       mobile,
		MemberType:   mType,
		PayUserID:    createMember.UserID,
		PayBizUserId: createMember.BizUserId,
		//Company:      company,
		//BankAccount:  bankAccount,
		CreatedAt: now,
		UpdatedAt: now,
	}

	err = s.authenticationDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) CreateByPoint(ctx context.Context, point model.ServicePoint) error {
	bySupplier, err := s.GetYeeByPoint(ctx, point.ID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if bySupplier.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "已存在入网信息")
	}

	now := time.Now().UnixMilli()

	merchantContactInfo := model.MerchantContactInfo{
		ContactName:   point.ContactUser,
		ContactMobile: point.ContactMobile,
		ContactEmail:  "<EMAIL>",
	}
	data := model.YeeMerchant{
		ID:                  primitive.NewObjectID(),
		UserID:              point.UserID,
		ObjectType:          model.ObjectTypeServicePoint,
		ObjectID:            point.ID,
		BusinessRole:        model.BusinessRoleMicroMERCHANT, // 个人
		ParentMerchantNo:    "***********",
		MerchantContactInfo: merchantContactInfo,
		CreatedAt:           now,
		UpdatedAt:           now,
	}

	err = s.yeeMerchantDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) CreateBySupplier(ctx context.Context, userMobile string, supplier model.Supplier) error {
	bySupplier, err := s.GetBySupplier(ctx, supplier.ID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if bySupplier.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "已存在入网信息")
	}

	now := time.Now().UnixMilli()

	merchantContactInfo := model.MerchantContactInfo{
		ContactName:   supplier.ContactUser,
		ContactMobile: userMobile,
		ContactEmail:  "<EMAIL>",
	}
	data := model.YeeMerchant{
		ID:                  primitive.NewObjectID(),
		UserID:              supplier.UserID,
		ObjectType:          model.ObjectTypeSupplier,
		ObjectID:            supplier.ID,
		BusinessRole:        model.BusinessRoleSETTLEDMERCHANT,
		ParentMerchantNo:    "***********",
		MerchantContactInfo: merchantContactInfo,
		CreatedAt:           now,
		UpdatedAt:           now,
	}

	err = s.yeeMerchantDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) CreateYeeByBuyer(ctx context.Context, buyerID primitive.ObjectID) (model.YeeMerchant, error) {
	now := time.Now().UnixMilli()
	data := model.YeeMerchant{
		ID: primitive.NewObjectID(),
		//UserID:     userID,
		ObjectType: model.ObjectTypeBuyer,
		ObjectID:   buyerID,
		//BusinessRole: model.BusinessRoleSETTLEDMERCHANT,
		ParentMerchantNo: "",
		CreatedAt:        now,
		UpdatedAt:        now,
	}

	err := s.yeeMerchantDao.Create(ctx, data)
	if err != nil {
		return model.YeeMerchant{}, err
	}
	return data, nil
}

func (s yeeMerchantService) CheckParam(ctx context.Context, req types.AuthenticationReq) error {
	if req.CompanyType == model.CompanyTypePerson {
		// 个体工商户
		if len(req.BankReservedMobile) != 11 {
			return xerr.NewErr(xerr.ErrParamError, nil, "个体工商户需要填写银行账户预留手机号")
		}
	}
	if req.CompanyType == model.CompanyTypeCo {
		//	企业
		if req.BankName == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "公司需要填写开户支行名称和支付行号")
		}
		if len(req.UnionBank) != 12 {
			return xerr.NewErr(xerr.ErrParamError, nil, "公司需要12位支付行号")
		}
	}
	return nil
}

func (s yeeMerchantService) UpdateReApply(ctx context.Context, req types.AuthenticationReq, objectID primitive.ObjectID, objectType model.ObjectType) error {
	authentication, err := s.GetByObject(ctx, objectID, objectType)
	if err != nil {
		return err
	}
	now := time.Now().UnixMilli()

	authentication.UpdatedAt = now

	authentication.Company.BusinessLicenseImg = req.BusinessLicenseImg
	authentication.Company.CompanyType = req.CompanyType
	authentication.Company.Legal.LegalPhone = req.LegalPhone
	authentication.Company.Legal.IdCardFrontImg = req.LegalIdCardFrontImg
	authentication.Company.Legal.IdCardBackImg = req.LegalIdCardBackImg

	aType := model.AccountTypePublic
	if req.CompanyType == model.CompanyTypePerson {
		// 个体工商户
		aType = model.AccountTypeSelf
		if len(req.BankReservedMobile) != 11 {
			return xerr.NewErr(xerr.ErrParamError, nil, "个体工商户需要填写银行账户预留手机号")
		}
	}
	if req.CompanyType == model.CompanyTypeCo {
		//	企业
		if req.BankName == "" {
			return xerr.NewErr(xerr.ErrParamError, nil, "企业需要填写开户支行名称和支付行号")
		}
		if len(req.UnionBank) != 12 {
			return xerr.NewErr(xerr.ErrParamError, nil, "企业需要12位支付行号")
		}
	}

	authentication.BankAccount.AccountType = aType
	authentication.BankAccount.CardNumber = req.CardNumber
	authentication.BankAccount.BankName = req.BankName
	authentication.BankAccount.UnionBank = req.UnionBank
	authentication.BankAccount.BankReservedMobile = req.BankReservedMobile
	authentication.BankAccount.BankcardImg = req.BankcardImg

	err = s.authenticationDao.Update(ctx, bson.M{"_id": authentication.ID}, bson.M{"$set": authentication})
	if err != nil {
		return err
	}
	return nil
}

func (s yeeMerchantService) CheckBySupplier(ctx context.Context, id primitive.ObjectID) (bool, error) {
	i, err := s.GetBySupplier(ctx, id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	if i.ID == primitive.NilObjectID {
		return false, nil
	}
	return true, nil
}

func (s yeeMerchantService) CheckByWarehouse(ctx context.Context, id primitive.ObjectID) (bool, error) {
	i, err := s.GetByWarehouse(ctx, id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	if i.ID == primitive.NilObjectID {
		return false, nil
	}
	return true, nil
}

func (s yeeMerchantService) GetBySupplier(ctx context.Context, id primitive.ObjectID) (model.YeeMerchant, error) {
	filter := bson.M{
		"object_type": model.ObjectTypeSupplier,
		"object_id":   id,
	}
	data, err := s.yeeMerchantDao.Get(ctx, filter)
	if err != nil {
		return model.YeeMerchant{}, err
	}

	return data, nil
}

func (s yeeMerchantService) GetYeeByBuyer(ctx context.Context, id primitive.ObjectID) (model.YeeMerchant, error) {
	filter := bson.M{
		"object_type": model.ObjectTypeBuyer,
		"object_id":   id,
	}
	data, err := s.yeeMerchantDao.Get(ctx, filter)
	if err != nil {
		return model.YeeMerchant{}, err
	}

	return data, nil
}

func (s yeeMerchantService) GetYeeByPoint(ctx context.Context, id primitive.ObjectID) (model.YeeMerchant, error) {
	filter := bson.M{
		"object_type": model.ObjectTypeServicePoint,
		"object_id":   id,
	}
	data, err := s.yeeMerchantDao.Get(ctx, filter)
	if err != nil {
		return model.YeeMerchant{}, err
	}

	return data, nil
}

func (s yeeMerchantService) GetByBuyer(ctx context.Context, id primitive.ObjectID) (model.Authentication, error) {
	object, err := s.GetByObject(ctx, id, model.ObjectTypeBuyer)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

func (s yeeMerchantService) GetBuyerByUser(ctx context.Context, userID primitive.ObjectID) (model.Authentication, error) {
	filter := bson.M{
		"object_type": model.ObjectTypeBuyer,
		"user_id":     userID,
	}
	authentication, err := s.authenticationDao.Get(ctx, filter)
	if err != nil {
		return model.Authentication{}, err
	}
	return authentication, nil
}

func (s yeeMerchantService) GetByUserAndEnv(ctx context.Context, userID primitive.ObjectID, env model.ObjectType) (model.Authentication, error) {
	filter := bson.M{
		"user_id":     userID,
		"object_type": env,
	}
	object, err := s.authenticationDao.Get(ctx, filter)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

func (s yeeMerchantService) GetByBizUserID(ctx context.Context, bizUserID string) (model.Authentication, error) {
	filter := bson.M{
		"pay_biz_user_id": bizUserID,
	}
	object, err := s.authenticationDao.Get(ctx, filter)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

func (s yeeMerchantService) GetByStation(ctx context.Context, id primitive.ObjectID) (model.Authentication, error) {
	object, err := s.GetByObject(ctx, id, model.ObjectTypeStation)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

// SetRealNameByStation 实名认证
func (s yeeMerchantService) SetRealNameByStation(ctx context.Context, id primitive.ObjectID, name, identityNo string) error {
	authentication, err := s.GetByStation(ctx, id)
	if err != nil {
		return err
	}

	req := pays.SetRealNameReq{
		BizUserId:  authentication.PayBizUserId,
		Name:       name,
		IdentityNo: identityNo,
	}
	res, err := s.payMember.SetRealNameS(req)
	if err != nil {
		marshal, _ := json.Marshal(req)
		zap.S().Errorf("站点：%s,实名错误：%s,实名信息：%s，", id.Hex(), err.Error(), string(marshal))
		return err
	}
	_ = res

	update := bson.M{
		"real_name":   name,
		"identity_no": identityNo,
	}
	err = s.UpdateCus(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s yeeMerchantService) BindBankByStation(ctx context.Context, id primitive.ObjectID, mobile, cardNo string) error {
	auth, err := s.GetByStation(ctx, id)
	if err != nil {
		return err
	}

	if auth.IndividualBankcardResult == "ok" {
		return xerr.NewErr(xerr.ErrParamError, nil, "已绑定，请勿重复绑定")
	}

	req := pays.ApplyBindBankCardReq{
		BizUserId:    auth.PayBizUserId,
		CardNo:       cardNo,
		Phone:        mobile,
		CardCheck:    8,
		Name:         auth.RealName,
		IdentityType: 1,
		IdentityNo:   auth.IdentityNo,
	}
	res, err := s.payMember.ApplyBindBankCardS(req)
	if err != nil {
		marshal, _ := json.Marshal(req)
		zap.S().Errorf("BindBank 站点：%s,绑定银行卡请求错误：%s,请求信息：%s，", id.Hex(), err.Error(), string(marshal))
		return err
	}
	_ = res

	update := bson.M{
		"individual_bankcard_no":          cardNo,
		"individual_bank_reserved_mobile": mobile,
		"individual_bankcard_result":      "ok",
	}
	err = s.UpdateCus(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	return nil
}

func (s yeeMerchantService) GetByWarehouse(ctx context.Context, id primitive.ObjectID) (model.Authentication, error) {
	object, err := s.GetByObject(ctx, id, model.ObjectTypeWarehouse)
	if err != nil {
		return model.Authentication{}, err
	}
	return object, nil
}

func (s yeeMerchantService) ListAll(ctx context.Context) ([]model.Authentication, error) {
	list, _, err := s.authenticationDao.List(ctx, bson.M{}, 1, 999)

	return list, err
}

func (s yeeMerchantService) ListByStation(ctx context.Context, ids []primitive.ObjectID) ([]model.Authentication, error) {
	filter := bson.M{
		"object_type": model.ObjectTypeStation,
		"object_id": bson.M{
			"$in": ids,
		},
	}

	list, err := s.authenticationDao.ListAll(ctx, filter)
	if err != nil {
		return nil, err
	}

	return list, err
}

func (s yeeMerchantService) ListBankCard(ctx context.Context, bizUserID string) ([]pays.BindBankInfo, error) {
	res, err := s.payMember.QueryBankCardS(pays.QueryBankCardReq{
		BizUserId: bizUserID,
	})
	if err != nil {
		return nil, err
	}

	return res.BindCardList, nil
}

func (s yeeMerchantService) YeeUpload(ctx context.Context, yeeMerchant model.YeeMerchant, object string) error {
	var priKey = s.yeePay.ReqPriKey
	var yopRequest = request.NewYopRequest(constants.POST_HTTP_METHOD, "/yos/v1.0/sys/merchant/qual/upload")
	yopRequest.AppId = s.yeePay.AppID
	yopRequest.IsvPriKey = priKey

	baseImgUrl := "https://image.guoshut.top/"

	var path string
	var key string

	if object == "license" {
		path = baseImgUrl + yeeMerchant.MerchantSubjectInfo.LicenseImg.Name
		key = "merchant_subject_info.licence_url"
	}

	if object == "idCardFront" {
		path = baseImgUrl + yeeMerchant.MerchantCorporationInfo.LegalLicenceFrontImg.Name
		key = "merchant_corporation_info.legal_licence_front_url"
	}

	if object == "idCardBack" {
		path = baseImgUrl + yeeMerchant.MerchantCorporationInfo.LegalLicenceBackImg.Name
		key = "merchant_corporation_info.legal_licence_back_url"
	}

	if object == "bank" {
		path = baseImgUrl + yeeMerchant.MerchantSubjectInfo.IndividualBankCardImg.Name
		key = "merchant_subject_info.open_account_licence_url"
	}

	resp, err := http.Get(path)
	if err != nil {
		return err
	}

	tmpFile, err := os.CreateTemp("", "yeeUpload*.png")
	if err != nil {
		return err
	}

	//tmpFile, err := os.Create("a.jpg")
	//if err != nil {
	//	return err
	//}
	//
	// 将图片内容写入到临时文件中
	_, err = io.Copy(tmpFile, resp.Body)
	if err != nil {
		return err
	}

	//	// 将文件指针重置到文件开始处
	_, err = tmpFile.Seek(0, 0)
	if err != nil {
	}

	//defer tmpFile.Close()

	//tmpFile, err := os.Open("tt.png")
	//if err != nil {
	//	return err
	//}

	stat, err := tmpFile.Stat()
	if err != nil {
		return err
	}

	zap.S().Infof("size :%d", stat.Size())

	_ = stat

	defer tmpFile.Close()

	//return nil

	yopRequest.AddFile("merQual", tmpFile)

	yopResp, err := client.DefaultClient.Request(yopRequest)
	if nil != err {
		return err
	}

	var r model.UploadRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.ReturnCode != "REG00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	zap.S().Infof("响应：%v", r)

	fileUrl := r.MerQualUrl

	update := bson.M{
		key: fileUrl,
		//"merchant_subject_info.licence_url": fileUrl,
		//"merchant_subject_info.open_account_licence_url": fileUrl,
		//"merchant_corporation_info.legal_licence_front_url": fileUrl,
		//"merchant_corporation_info.legal_licence_back_url": fileUrl,
		"updated_at": time.Now().UnixMilli(),
	}

	//if byID.MerchantSubjectInfo.SignType == model.SignTypeENTERPRISE {
	//	//	企业
	//	update["merchant_subject_info.enterprise_open_img"] = req.MerchantSubjectInfo.EnterpriseOpenImg
	//	update["settlement_account_info.bank_account_type"] = model.BankAccountTypeENTERPRISEACCOUNT
	//}
	//if byID.MerchantSubjectInfo.SignType == model.SignTypeINDIVIDUAL {
	//	//	个体工商户
	//	update["merchant_subject_info.individual_bank_card_img"] = req.MerchantSubjectInfo.IndividualBankCardImg
	//	update["settlement_account_info.bank_account_type"] = model.BankAccountTypeDEBITCARD
	//}

	filter := bson.M{"_id": yeeMerchant.ID}

	err = s.yeeMerchantDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

// RegisterMerchant 个体工商户
func (s yeeMerchantService) RegisterMerchant(ctx context.Context, merchant model.YeeMerchant) error {
	var yopRequest = request.NewYopRequest("POST", "/rest/v2.0/mer/register/saas/merchant")

	requestNo := util.NewUUIDNum()

	if merchant.RequestNo != "" {
		requestNo = merchant.RequestNo
	}

	yopRequest.AddParam("requestNo", requestNo)
	yopRequest.AddParam("businessRole", "SETTLED_MERCHANT") // 入驻商户
	yopRequest.AddParam("parentMerchantNo", "***********")  // 平台商

	// 商户主体信息
	merchantSubjectInfo := map[string]interface{}{
		"signType":              merchant.MerchantSubjectInfo.SignType,
		"licenceNo":             merchant.MerchantSubjectInfo.LicenceNo,
		"licenceUrl":            merchant.MerchantSubjectInfo.LicenceUrl,
		"signName":              merchant.MerchantSubjectInfo.SignName,
		"shortName":             merchant.MerchantSubjectInfo.ShortName,
		"openAccountLicenceUrl": merchant.MerchantSubjectInfo.OpenAccountLicenceUrl,
	}
	marshalSubject, err := json.Marshal(merchantSubjectInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("merchantSubjectInfo", string(marshalSubject))

	//  商户法人信息
	merchantCorporationInfo := map[string]interface{}{
		"legalName":            merchant.MerchantCorporationInfo.LegalName,
		"legalLicenceType":     "ID_CARD",
		"legalLicenceNo":       merchant.MerchantCorporationInfo.LegalLicenceNo,
		"legalLicenceFrontUrl": merchant.MerchantCorporationInfo.LegalLicenceFrontUrl,
		"legalLicenceBackUrl":  merchant.MerchantCorporationInfo.LegalLicenceBackUrl,
	}
	marshalMerchantCorporationInfo, err := json.Marshal(merchantCorporationInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("merchantCorporationInfo", string(marshalMerchantCorporationInfo))

	// 商户联系人信息
	merchantContactInfo := map[string]interface{}{
		"contactName":   merchant.MerchantContactInfo.ContactName,
		"contactMobile": merchant.MerchantContactInfo.ContactMobile,
		"contactEmail":  merchant.MerchantContactInfo.ContactEmail,
	}
	merchantContactInfoM, err := json.Marshal(merchantContactInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("merchantContactInfo", string(merchantContactInfoM))

	// 经营地址
	businessAddressInfo := map[string]interface{}{
		"province": merchant.BusinessAddressInfo.Province,
		"city":     merchant.BusinessAddressInfo.City,
		"district": merchant.BusinessAddressInfo.District,
		"address":  merchant.BusinessAddressInfo.Address, // 使用营业执照地址信息
	}
	businessAddressInfoM, err := json.Marshal(businessAddressInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("businessAddressInfo", string(businessAddressInfoM))

	// 结算账户信息
	settlementAccountInfo := map[string]interface{}{
		"settlementDirection": "ACCOUNT",
		"bankAccountType":     merchant.SettlementAccountInfo.BankAccountType,
		"bankCardNo":          merchant.SettlementAccountInfo.BankCardNo,
		"bankCode":            merchant.SettlementAccountInfo.BankCode,
	}
	settlementAccountInfoM, err := json.Marshal(settlementAccountInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("settlementAccountInfo", string(settlementAccountInfoM))

	// 商户回调地址
	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayMerchantRegister)

	// 开通产品信息
	productInfo := []map[string]interface{}{
		{
			"productCode":   "MINI_PROGRAM_WECHAT_OFFLINE",
			"rateType":      "SINGLE_PERCENT",
			"percentRate":   "0.25",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "PREPAID_REAL",      // 预付实扣
		},
		{
			"productCode":   "D1",
			"rateType":      "SINGLE_FIXED",
			"fixedRate":     "0",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "REAL_TIME",         // 实收
		},
		{
			"productCode":   "ENTERPRISE_WITHDRAW_STANDARD_TWOHOURS", // 企业账户提现标准版_2小时到账
			"rateType":      "SINGLE_FIXED",
			"fixedRate":     "0",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "REAL_TIME",         // 实收
		},
		{
			"productCode":   "ENTERPRISE_WITHDRAW_STANDARD_TOMORROW", // 企业账户提现标准版 次日到账
			"rateType":      "SINGLE_FIXED",
			"fixedRate":     "0",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "REAL_TIME",         // 实收
		},
		{
			"productCode":   "ENTERPRISE_WITHDRAW_STANDARD_REALTIME", // 企业账户提现标准版 实时到账
			"rateType":      "SINGLE_FIXED",
			"fixedRate":     "0",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "REAL_TIME",         // 实收
		},
	}
	productInfoM, err := json.Marshal(productInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("productInfo", string(productInfoM))

	functionService := []string{"SHARE"} // 功能服务  开通分账服务必传分账场景

	functionServiceM, err := json.Marshal(functionService)
	if err != nil {
		return err
	}

	yopRequest.AddParam("functionService", string(functionServiceM))

	functionServiceQualificationInfo := map[string]interface{}{
		"shareScene": "FZ_FJ003",
	} // 服务资质信息

	functionServiceQualificationInfoM, err := json.Marshal(functionServiceQualificationInfo)
	if err != nil {
		return err
	}

	yopRequest.AddParam("functionServiceQualificationInfo", string(functionServiceQualificationInfoM))

	// 产品资质   入驻商户不需要填写
	//productQualificationInfo := map[string]interface{}{
	//	"internetType":         "MOBILE", // 非面对面形态
	//	"terminalType":         "WECHANT_APPLET",
	//	"terminalName":         "果蔬团服务",
	//	"terminalTestAccount":  "***********", //
	//	"terminalTestPassword": "221122",      //
	//}
	//productQualificationInfoM, err := json.Marshal(productQualificationInfo)
	//if err != nil {
	//	return err
	//}
	//yopRequest.AddParam("productQualificationInfo", string(productQualificationInfoM))

	yopRequest.AddParam("businessNotifyUrl", global.BackHost+global.NotifyUrlYeePayMerchantBusiness)

	bytes, err := json.Marshal(yopRequest)
	if err != nil {
		return err
	}

	zap.S().Infof("请求:%s", string(bytes))

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}

	var r model.RegisterRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.ReturnCode != "NIG00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	err = s.yeeMerchantDao.Update(ctx, bson.M{"_id": merchant.ID}, bson.M{
		"$set": bson.M{
			"request_no":         r.RequestNo,
			"merchant_no":        r.MerchantNo,
			"application_no":     r.ApplicationNo,
			"application_status": r.ApplicationStatus,
		},
	})

	if err != nil {
		return err
	}

	return nil
}

// RegisterMerchantForMicro 个人
func (s yeeMerchantService) RegisterMerchantForMicro(ctx context.Context, merchant model.YeeMerchant) error {
	var yopRequest = request.NewYopRequest("POST", "/rest/v2.0/mer/register/saas/micro")

	requestNo := util.NewUUIDNum()

	if merchant.RequestNo != "" {
		requestNo = merchant.RequestNo
	}

	yopRequest.AddParam("requestNo", requestNo)
	yopRequest.AddParam("businessRole", "SETTLED_MERCHANT") // 入驻商户
	yopRequest.AddParam("parentMerchantNo", "***********")  // 平台商

	// 商户主体信息
	merchantSubjectInfo := map[string]interface{}{
		"signName":  merchant.MerchantSubjectInfo.SignName,  // 姓名
		"shortName": merchant.MerchantSubjectInfo.ShortName, //
	}
	marshalSubject, err := json.Marshal(merchantSubjectInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("merchantSubjectInfo", string(marshalSubject))

	//  商户法人信息
	merchantCorporationInfo := map[string]interface{}{
		//"legalName":            merchant.MerchantCorporationInfo.LegalName,
		"legalLicenceType":     "ID_CARD",
		"legalLicenceNo":       merchant.MerchantCorporationInfo.LegalLicenceNo,
		"legalLicenceFrontUrl": merchant.MerchantCorporationInfo.LegalLicenceFrontUrl,
		"legalLicenceBackUrl":  merchant.MerchantCorporationInfo.LegalLicenceBackUrl,
		"mobile":               merchant.MerchantContactInfo.ContactMobile,
	}
	marshalMerchantCorporationInfo, err := json.Marshal(merchantCorporationInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("merchantCorporationInfo", string(marshalMerchantCorporationInfo))

	// 商户联系人信息
	merchantContactInfo := map[string]interface{}{
		"contactName":   merchant.MerchantContactInfo.ContactName,
		"contactMobile": merchant.MerchantContactInfo.ContactMobile,
		"contactEmail":  merchant.MerchantContactInfo.ContactEmail,
	}
	merchantContactInfoM, err := json.Marshal(merchantContactInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("merchantContactInfo", string(merchantContactInfoM))

	// 经营地址
	businessAddressInfo := map[string]interface{}{
		"province": merchant.BusinessAddressInfo.Province,
		"city":     merchant.BusinessAddressInfo.City,
		"district": merchant.BusinessAddressInfo.District,
		"address":  merchant.BusinessAddressInfo.Address, // 使用营业执照地址信息
	}
	businessAddressInfoM, err := json.Marshal(businessAddressInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("businessAddressInfo", string(businessAddressInfoM))

	// 结算账户信息
	settlementAccountInfo := map[string]interface{}{
		"settlementDirection": "ACCOUNT",
		"bankAccountType":     "DEBIT_CARD",
		"bankCardNo":          merchant.SettlementAccountInfo.BankCardNo,
		"bankCode":            merchant.SettlementAccountInfo.BankCode,
	}
	settlementAccountInfoM, err := json.Marshal(settlementAccountInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("settlementAccountInfo", string(settlementAccountInfoM))

	// 商户回调地址
	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayMerchantRegister)

	// 开通产品信息
	productInfo := []map[string]interface{}{
		{
			"productCode":   "MINI_PROGRAM_WECHAT_OFFLINE",
			"rateType":      "SINGLE_PERCENT",
			"percentRate":   "0.25",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "PREPAID_REAL",      // 预付实扣
		},
		{
			"productCode":   "D1",
			"rateType":      "SINGLE_FIXED",
			"fixedRate":     "0",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "REAL_TIME",         // 实收
		},
		{
			"productCode":   "ENTERPRISE_WITHDRAW_STANDARD_TWOHOURS", // 企业账户提现标准版_2小时到账
			"rateType":      "SINGLE_FIXED",
			"fixedRate":     "0",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "REAL_TIME",         // 实收
		},
		{
			"productCode":   "ENTERPRISE_WITHDRAW_STANDARD_TOMORROW", // 企业账户提现标准版 次日到账
			"rateType":      "SINGLE_FIXED",
			"fixedRate":     "0",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "REAL_TIME",         // 实收
		},
		{
			"productCode":   "ENTERPRISE_WITHDRAW_STANDARD_REALTIME", // 企业账户提现标准版 实时到账
			"rateType":      "SINGLE_FIXED",
			"fixedRate":     "0",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "REAL_TIME",         // 实收
		},
	}
	productInfoM, err := json.Marshal(productInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("productInfo", string(productInfoM))

	functionService := []string{"SHARE"} // 功能服务  开通分账服务必传分账场景

	functionServiceM, err := json.Marshal(functionService)
	if err != nil {
		return err
	}

	yopRequest.AddParam("functionService", string(functionServiceM))

	functionServiceQualificationInfo := map[string]interface{}{
		"shareScene": "FZ_FJ003",
	} // 服务资质信息

	functionServiceQualificationInfoM, err := json.Marshal(functionServiceQualificationInfo)
	if err != nil {
		return err
	}

	yopRequest.AddParam("functionServiceQualificationInfo", string(functionServiceQualificationInfoM))

	// 产品资质   入驻商户不需要填写
	//productQualificationInfo := map[string]interface{}{
	//	"internetType":         "MOBILE", // 非面对面形态
	//	"terminalType":         "WECHANT_APPLET",
	//	"terminalName":         "果蔬团服务",
	//	"terminalTestAccount":  "***********", //
	//	"terminalTestPassword": "221122",      //
	//}
	//productQualificationInfoM, err := json.Marshal(productQualificationInfo)
	//if err != nil {
	//	return err
	//}
	//yopRequest.AddParam("productQualificationInfo", string(productQualificationInfoM))

	yopRequest.AddParam("businessNotifyUrl", global.BackHost+global.NotifyUrlYeePayMerchantBusiness)

	bytes, err := json.Marshal(yopRequest)
	if err != nil {
		return err
	}

	zap.S().Infof("请求:%s", string(bytes))

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}

	var r model.RegisterRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.ReturnCode != "NIG00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	err = s.yeeMerchantDao.Update(ctx, bson.M{"_id": merchant.ID}, bson.M{
		"$set": bson.M{
			"request_no":         r.RequestNo,
			"merchant_no":        r.MerchantNo,
			"application_no":     r.ApplicationNo,
			"application_status": r.ApplicationStatus,
		},
	})

	if err != nil {
		return err
	}

	return nil
}

// RegisterResultNotify 入网结果通知
func (s yeeMerchantService) RegisterResultNotify(ctx context.Context, res map[string]string) error {
	yeeMerchant, err := s.GetByApplicationNo(ctx, res["applicationNo"])
	if err != nil {
		return err
	}
	_ = yeeMerchant

	update := bson.M{
		"application_status": res["applicationStatus"],
	}

	if res["applicationStatus"] == "COMPLETED" {
		update["audit_opinion"] = res["auditOpinion"]
	}

	if res["auditOpinion"] != "" {
		update["auditOpinion"] = ""
	}

	if res["progressDescription"] != "" {
		update["progress_description"] = res["progressDescription"]
	}

	if res["agreementSignUrl"] != "" {
		update["agreement_sign_url"] = res["agreementSignUrl"]
	}

	if res["intentionAuthUrl"] != "" {
		update["intention_auth_url"] = res["intentionAuthUrl"]
	}

	if res["merchantNo"] != "" {
		update["merchant_no"] = res["merchantNo"]
	}

	err = s.yeeMerchantDao.Update(ctx, bson.M{"_id": yeeMerchant.ID}, bson.M{
		"$set": update,
	})

	if err != nil {
		return err
	}

	return nil
}

func (s yeeMerchantService) YeeRegisterQuery(ctx context.Context, id primitive.ObjectID) (model.QueryRes, error) {
	byID, err := s.GetBySupplier(ctx, id)
	if err != nil {
		return model.QueryRes{}, err
	}
	_ = byID

	var yopRequest = request.NewYopRequest("GET", "/rest/v2.0/mer/register/query")
	yopRequest.IsvPriKey = s.yeePay.ReqPriKey
	yopRequest.AppId = s.yeePay.AppID

	yopRequest.AddParam("merchantNo", byID.MerchantNo)

	yopResp, err := client.DefaultClient.Request(yopRequest)
	if nil != err {
		return model.QueryRes{}, err
	}
	// yopResp.Result为请求结果
	_ = yopResp

	var r model.QueryRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.QueryRes{}, err
	}
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.QueryRes{}, err
	}

	if r.ReturnCode != "NIG00000" {
		return model.QueryRes{}, xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	//err = s.yeeMerchantDao.Update(ctx, bson.M{"_id": id}, bson.M{
	//	"request_no": requestNo,
	//})
	//if err != nil {
	//	return err
	//}

	return r, err
}

func (s yeeMerchantService) YeeRegisterQueryByPoint(ctx context.Context, id primitive.ObjectID) (model.QueryRes, error) {
	byID, err := s.GetYeeByPoint(ctx, id)
	if err != nil {
		return model.QueryRes{}, err
	}
	_ = byID

	var yopRequest = request.NewYopRequest("GET", "/rest/v2.0/mer/register/query")

	yopRequest.AddParam("merchantNo", byID.MerchantNo)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.QueryRes{}, err
	}
	// yopResp.Result为请求结果
	_ = yopResp

	var r model.QueryRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.QueryRes{}, err
	}
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.QueryRes{}, err
	}

	if r.ReturnCode != "NIG00000" {
		return model.QueryRes{}, xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	//err = s.yeeMerchantDao.Update(ctx, bson.M{"_id": id}, bson.M{
	//	"request_no": requestNo,
	//})
	//if err != nil {
	//	return err
	//}

	return r, err
}

// MerchantWechatAuthApply 微信实名认证申请单-申请
func (s yeeMerchantService) MerchantWechatAuthApply(ctx context.Context, merchant model.YeeMerchant) error {
	//merchant, err := s.GetBySupplier(ctx, supplierID)
	//if err != nil {
	//	return err
	//}

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/mer/merchant/wechatauth/apply")
	yopRequest.IsvPriKey = s.yeePay.ReqPriKey
	yopRequest.AppId = s.yeePay.AppID

	requestNo := util.NewUUIDNum()
	yopRequest.AddParam("requestNo", requestNo)
	yopRequest.AddParam("subMerchantNo", merchant.MerchantNo)                         // 待认证商户对应的易宝商户编号
	yopRequest.AddParam("applicantType", "LEGAL")                                     // LEGAL：法人
	yopRequest.AddParam("applicantPhone", merchant.MerchantContactInfo.ContactMobile) // 申请人手机号  用于接收微信支付的重要管理信息及日常操作验证码

	if merchant.MerchantCorporationInfo.LegalLicenceValidDateBegin == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "缺失身份证有效期")
	}
	identificationValidDate := []string{merchant.MerchantCorporationInfo.LegalLicenceValidDateBegin, merchant.MerchantCorporationInfo.LegalLicenceValidDateEnd} // 功能服务  开通分账服务必传分账场景
	identificationValidDateM, err := json.Marshal(identificationValidDate)
	if err != nil {
		return err
	}
	yopRequest.AddParam("identificationValidDate", string(identificationValidDateM))

	// 主体注册地址
	yopRequest.AddParam("companyAddress", merchant.BusinessAddressInfo.Address) // 主体注册地址

	//yopRequest.AddParam("identificationType", "IDENTIFICATION_TYPE_IDCARD") // 法人证件类型
	//yopRequest.AddParam("identificationFrontCopy", "")                      // 法人证件正面
	//yopRequest.AddParam("identificationBackCopy", "")                       // 法人证件反面

	//yopRequest.AddParam("identificationAddress", " ") // 法人证件居住地址 1、主体类型为企业时，需要填写

	yopRequest.AddParam("reportFee", "XIANXIA") // 报备费率

	// 主体证件照片
	yopRequest.AddParam("certCopy", merchant.MerchantSubjectInfo.LicenceUrl) // 主体类型为企业或个体：上传统一信用代码证照片；

	if merchant.MerchantSubjectInfo.LicenceValidDateBegin == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "缺失营业执照有效期")
	}

	licenceValidDate := []string{merchant.MerchantSubjectInfo.LicenceValidDateBegin, merchant.MerchantSubjectInfo.LicenceValidDateEnd} // 营业执照有效期
	licenceValidDateM, err := json.Marshal(licenceValidDate)
	if err != nil {
		return err
	}
	yopRequest.AddParam("licenceValidDate", string(licenceValidDateM))

	yopRequest.AddParam("certType", "CERTIFICATE_TYPE_2389") // 登记证书类型

	//yopRequest.AddParam("owner", true)                       // 法人是否为受益人  bool 主体类型为企业时，需要填写 若法人是最终受益人，则填写：true。
	//yopRequest.AddParam("uboInfoList", "")                       // 受益人信息列表(UBO)  仅主体类型为企业需要填写。

	yopRequest.AddParam("channelId", "270836225") // 渠道编号

	bytes, err := json.Marshal(yopRequest.Params)
	zap.S().Infof("请求：%s", string(bytes))

	yopResp, err := client.DefaultClient.Request(yopRequest)
	if nil != err {
		return err
	}

	var r model.WechatAuthApplyRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}

	zap.S().Infof("响应：%s", string(marshal))
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	err = s.yeeMerchantDao.Update(ctx, bson.M{"_id": merchant.ID}, bson.M{
		"$set": bson.M{
			// 更新 微信实名认证的申请单编号
			// wechat_auth_apply_id   申请单编号
			// wechat_auth_apply_state  申请单状态 (非审核通过，查询申请单）
			"wechat_auth_apply_id":    r.ApplymentID,
			"wechat_auth_apply_state": "WAITTING_FOR_AUDIT",
		},
	})
	if err != nil {
		return err
	}

	return err
}

// MerchantWechatAuthQuery 微信实名认证申请单-查询
func (s yeeMerchantService) MerchantWechatAuthQuery(ctx context.Context, merchant model.YeeMerchant) (model.WechatAuthQueryRes, error) {
	//merchant, err := s.GetBySupplier(ctx, supplierID)
	//if err != nil {
	//	return model.WechatAuthQueryRes{}, err
	//}

	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/mer/merchant/wechatauth/query")
	yopRequest.IsvPriKey = s.yeePay.ReqPriKey
	yopRequest.AppId = s.yeePay.AppID

	yopRequest.AddParam("applymentId", merchant.WechatAuthApplyID)
	yopRequest.AddParam("subMerchantNo", merchant.MerchantNo)
	yopRequest.AddParam("reportFee", "XIANXIA") // 报备费率

	//requestNo := util.NewUUIDNum()
	//yopRequest.AddParam("requestNo", requestNo) // 请求号

	yopResp, err := client.DefaultClient.Request(yopRequest)
	if nil != err {
		return model.WechatAuthQueryRes{}, err
	}

	var r model.WechatAuthQueryRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.WechatAuthQueryRes{}, err
	}

	zap.S().Infof("响应：%s", string(marshal))
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.WechatAuthQueryRes{}, err
	}

	//if r.ReturnCode != "REG00000" {
	//	return xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	//}

	//err = s.yeeMerchantDao.Update(ctx, bson.M{"_id": id}, bson.M{
	//	"request_no": requestNo,
	//})
	//if err != nil {
	//	return err
	//}

	return r, err
}

// MerchantAuthQuery 商户授权状态查询
func (s yeeMerchantService) MerchantAuthQuery(ctx context.Context, id primitive.ObjectID) (model.MerAuthQueryRes, error) {
	yeeMerchant, err := s.GetBySupplier(ctx, id)
	if err != nil {
		return model.MerAuthQueryRes{}, err
	}

	var yopRequest = request.NewYopRequest("GET", "/rest/v2.0/mer/auth/state/query")
	yopRequest.IsvPriKey = s.yeePay.ReqPriKey
	yopRequest.AppId = s.yeePay.AppID

	yopRequest.AddParam("merchantNo", yeeMerchant.MerchantNo)
	yopRequest.AddParam("feeType", "XIANXIA")
	//yopRequest.AddParam("pageNum", "1")
	//yopRequest.AddParam("pageSize", "10")

	yopResp, err := client.DefaultClient.Request(yopRequest)
	if nil != err {
		return model.MerAuthQueryRes{}, err
	}

	var r model.MerAuthQueryRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.MerAuthQueryRes{}, err
	}
	zap.S().Infof("响应：%s", string(marshal))
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.MerAuthQueryRes{}, err
	}

	//if r.ReturnCode != "REG00000" {
	//	return xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	//}

	//err = s.yeeMerchantDao.Update(ctx, bson.M{"_id": id}, bson.M{
	//	"request_no": requestNo,
	//})
	//if err != nil {
	//	return err
	//}

	return r, err
}

// MerchantWechatAuthCancel   微信实名认证申请单-撤销   提交申请单后需要修改信息时，或者申请单审核结果为”已驳回“时，均需要先调用撤销申请单接口。
func (s yeeMerchantService) MerchantWechatAuthCancel(ctx context.Context, supplierID primitive.ObjectID) error {
	byID, err := s.GetBySupplier(ctx, supplierID)
	if err != nil {
		return err
	}
	_ = byID

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/mer/merchant/wechatauth/cancel")

	yopRequest.AddParam("applymentId", byID.WechatAuthApplyID)
	yopRequest.AddParam("subMerchantNo", byID.MerchantNo)
	yopRequest.AddParam("reportFee", "XIANXIA")

	requestNo := util.NewUUIDNum()
	yopRequest.AddParam("requestNo", requestNo) // 请求号

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}

	var r model.WechatAuthCancelRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	zap.S().Infof("响应：%s", string(marshal))
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	return err
}

// MerchantWechatConfigAdd   支付appID配置
func (s yeeMerchantService) MerchantWechatConfigAdd(ctx context.Context, supplierID primitive.ObjectID) error {
	merchant, err := s.GetBySupplier(ctx, supplierID)
	if err != nil {
		return err
	}

	var yopRequest = request.NewYopRequest("POST", "/rest/v2.0/aggpay/wechat-config/add")
	yopRequest.IsvPriKey = s.yeePay.ReqPriKey
	yopRequest.AppId = s.yeePay.AppID

	requestNo := util.NewUUIDNum()
	yopRequest.AddParam("requestNo", requestNo) // 请求号

	//yopRequest.AddParam("parentMerchantNo", "***********")
	yopRequest.AddParam("parentMerchantNo", merchant.MerchantNo)
	yopRequest.AddParam("merchantNo", merchant.MerchantNo)
	yopRequest.AddParam("reportMerchantNo", merchant.MerchantNo)

	//tradeAuthDirList := []string{}
	//
	//tradeAuthDirListM, err := json.Marshal(tradeAuthDirList)
	//if err != nil {
	//	return err
	//}
	//
	//yopRequest.AddParam("tradeAuthDirList", string(tradeAuthDirListM))

	appIdList := []map[string]string{{
		"appId":     "wx831b39de46824a82",
		"appIdType": "MINI_PROGRAM",
	}}
	appIdListM, err := json.Marshal(appIdList)
	if err != nil {
		return err
	}
	yopRequest.AddParam("appIdList", string(appIdListM)) // 支付appId目录列表

	yopResp, err := client.DefaultClient.Request(yopRequest)
	if nil != err {
		return err
	}

	var r model.WechatAuthCancelRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	zap.S().Infof("响应：%s", string(marshal))
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	//if r.ReturnCode != "REG00000" {
	//	return xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	//}

	//err = s.yeeMerchantDao.Update(ctx, bson.M{"_id": id}, bson.M{
	//	"request_no": requestNo,
	//})
	//if err != nil {
	//	return err
	//}

	return err
}

func (s yeeMerchantService) AccountOpen(ctx context.Context, buyerID primitive.ObjectID) error {
	merchant, err := s.GetYeeByBuyer(ctx, buyerID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		//	不存在
		merchant, err = s.CreateYeeByBuyer(ctx, buyerID)
		if err != nil {
			return err
		}
		err = nil
	}
	if err != nil {
		return err
	}

	_ = merchant

	var yopRequest = request.NewYopRequest("POST", "/rest/v2.0/account/account-manage/account/open")

	merchantAccountBookNo := util.NewUUIDNum()
	var params = map[string]any{
		"parentMerchantNo":      "***********",
		"merchantNo":            "***********",
		"merchantAccountBookNo": merchantAccountBookNo,
	}

	yopRequest.Content = utils.ParseToJsonStr(params)

	//yopRequest.AddParam("accountBookType", "PERSON")   // 记账薄类型（绑卡列表不为空时，该项必填 ）
	//yopRequest.AddParam("certificateType", "ID_CARD")
	//yopRequest.AddParam("certificateNo", "522427199802027579")
	//yopRequest.AddParam("certificateNo", "522427199802027579")

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}

	var r model.YeeAccountOpenRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	zap.S().Infof("响应：%s", string(marshal))
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.ReturnCode != "AM00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	err = s.yeeMerchantDao.Update(ctx, bson.M{"_id": merchant.ID}, bson.M{
		"$set": bson.M{
			"account_yp_account_book_no":       r.YpAccountBookNo,
			"account_merchant_account_book_no": r.MerchantAccountBookNo,
			"account_status":                   r.Status,
		},
	})
	if err != nil {
		return err
	}

	return err
}

func (s yeeMerchantService) AccountQuery(ctx context.Context, buyerID primitive.ObjectID) (model.YeeAccountQueryRes, error) {
	merchant, err := s.GetYeeByBuyer(ctx, buyerID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return model.YeeAccountQueryRes{}, nil
	}
	if err != nil {
		return model.YeeAccountQueryRes{}, err
	}

	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/account/account-manage/account/query")

	yopRequest.AddParam("parentMerchantNo", "***********")
	yopRequest.AddParam("merchantNo", "***********")
	yopRequest.AddParam("ypAccountBookNo", merchant.AccountYpAccountBookNo)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.YeeAccountQueryRes{}, err
	}

	var r model.YeeAccountQueryRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.YeeAccountQueryRes{}, err
	}
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.YeeAccountQueryRes{}, err
	}

	if r.ReturnCode == "" {
		return model.YeeAccountQueryRes{}, nil
	}

	if r.ReturnCode != "AM00000" {
		return model.YeeAccountQueryRes{}, xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	r.BalanceInt = util.DealMoneyFloatToFenInt(r.Balance)
	r.FreezeBalanceInt = util.DealMoneyFloatToFenInt(r.FreezeBalance)

	return r, err
}

//
//func (s yeeMerchantService) AccountUpdate(ctx context.Context, buyerID primitive.ObjectID) (model.YeeAccountUpdateRes, error) {
//	merchant, err := s.GetYeeByBuyer(ctx, buyerID)
//	if err != nil {
//		return model.YeeAccountUpdateRes{}, err
//	}
//
//	_ = merchant
//
//	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/account/account-manage/account/modify")
//	yopRequest.IsvPriKey = s.yeePay.ReqPriKey
//	yopRequest.AppId = s.yeePay.AppID
//
//	yopRequest.AddParam("parentMerchantNo", "***********")
//	yopRequest.AddParam("merchantNo", "***********")
//	yopRequest.AddParam("ypAccountBookNo", merchant.AccountYpAccountBookNo)
//	yopRequest.AddParam("merchantAccountBookName", "刘国磊")
//
//	yopResp, err := client.DefaultClient.Request(yopRequest)
//	if nil != err {
//		return model.YeeAccountUpdateRes{}, err
//	}
//
//	var r model.YeeAccountUpdateRes
//
//	marshal, err := json.Marshal(yopResp.Result)
//	if err != nil {
//		return model.YeeAccountUpdateRes{}, err
//	}
//	zap.S().Infof("响应：%s", string(marshal))
//	err = json.Unmarshal(marshal, &r)
//	if err != nil {
//		return model.YeeAccountUpdateRes{}, err
//	}
//
//	if r.ReturnCode != "AM00000" {
//		return model.YeeAccountUpdateRes{}, xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
//	}
//
//	return r, err
//}

// AccountBookRechargeQuery 批量查询记账簿来账流水
func (s yeeMerchantService) AccountBookRechargeQuery(ctx context.Context, buyerID primitive.ObjectID) (model.YeeAccountRechargeQueryRes, error) {
	merchant, err := s.GetYeeByBuyer(ctx, buyerID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return model.YeeAccountRechargeQueryRes{}, nil
	}

	if err != nil {
		return model.YeeAccountRechargeQueryRes{}, err
	}

	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/account/recharge/account-book/query")

	yopRequest.AddParam("parentMerchantNo", "***********")
	yopRequest.AddParam("merchantNo", "***********")
	yopRequest.AddParam("ypAccountBookNo", merchant.AccountYpAccountBookNo)

	now := time.Now()

	format := "2006-01-02 15:04:05"
	begin := now.AddDate(0, 0, -30).Format(format)
	end := now.Format(format)

	yopRequest.AddParam("queryStartDate", begin)
	yopRequest.AddParam("queryEndDate", end)
	yopRequest.AddParam("pageNo", 1)
	yopRequest.AddParam("pageSize", 30)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.YeeAccountRechargeQueryRes{}, err
	}

	var r model.YeeAccountRechargeQueryRes

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.YeeAccountRechargeQueryRes{}, err
	}
	zap.S().Infof("响应：%s", string(marshal))
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.YeeAccountRechargeQueryRes{}, err
	}

	if r.ReturnCode != "UA00000" {
		return model.YeeAccountRechargeQueryRes{}, xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	return r, err
}

func (s yeeMerchantService) AccountBookRefund(ctx context.Context, buyerID primitive.ObjectID) error {
	merchant, err := s.GetYeeByBuyer(ctx, buyerID)
	if err != nil {
		return err
	}

	_ = merchant

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/account/account-book/refund")

	yopRequest.AddParam("parentMerchantNo", "***********")
	yopRequest.AddParam("merchantNo", "***********")
	//yopRequest.AddParam("ypAccountBookNo", merchant.AccountYpAccountBookNo)

	num := util.NewUUIDNum()
	yopRequest.AddParam("merchantRefundRequestNo", num) // 商户退款请求号
	yopRequest.AddParam("originalOrderNo", "1dc258f0e59c4b57b900e22d39ae8e58")
	yopRequest.AddParam("refundAmount", 1)
	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayRefundAccountBookCharge)
	yopRequest.AddParam("bankPostscrip", "果蔬团余额充值退款")

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	zap.S().Infof("响应：%s", string(marshal))

	return err
}

func (s yeeMerchantService) BalanceQuery(ctx context.Context, merchant model.YeeMerchant) (model.YeeBalanceQueryRes, error) {
	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/account/balance/query")

	yopRequest.AddParam("merchantNo", merchant.MerchantNo)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.YeeBalanceQueryRes{}, err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.YeeBalanceQueryRes{}, err
	}

	var r model.YeeBalanceQueryRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.YeeBalanceQueryRes{}, err
	}

	if r.ReturnCode != "UA00000" {
		return model.YeeBalanceQueryRes{}, xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	r.BalanceInt = util.DealMoneyFloatToFenInt(r.Balance)

	return r, err
}

func (s yeeMerchantService) BalanceAccountQuery(ctx context.Context, merchant model.YeeMerchant) (model.YeeBalanceAccountQueryRes, error) {
	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/account/accountinfos/query")

	yopRequest.AddParam("merchantNo", merchant.MerchantNo)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.YeeBalanceAccountQueryRes{}, err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.YeeBalanceAccountQueryRes{}, err
	}
	zap.S().Infof("响应：%s", string(marshal))
	var r model.YeeBalanceAccountQueryRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.YeeBalanceAccountQueryRes{}, err
	}

	if r.ReturnCode != "UA00000" {
		return model.YeeBalanceAccountQueryRes{}, xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	for i := 0; i < len(r.AccountInfoList); i++ {
		r.AccountInfoList[i].BalanceInt = util.DealMoneyFloatToFenInt(r.AccountInfoList[i].Balance)
	}

	return r, err
}

func (s yeeMerchantService) BalanceAccountByNo(ctx context.Context, merchantNo string) (model.YeeBalanceAccountQueryRes, error) {
	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/account/accountinfos/query")

	yopRequest.AddParam("merchantNo", merchantNo)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.YeeBalanceAccountQueryRes{}, err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.YeeBalanceAccountQueryRes{}, err
	}
	zap.S().Infof("响应：%s", string(marshal))
	var r model.YeeBalanceAccountQueryRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.YeeBalanceAccountQueryRes{}, err
	}

	if r.ReturnCode != "UA00000" {
		return model.YeeBalanceAccountQueryRes{}, xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	for i := 0; i < len(r.AccountInfoList); i++ {
		r.AccountInfoList[i].BalanceInt = util.DealMoneyFloatToFenInt(r.AccountInfoList[i].Balance)
	}

	return r, err
}

func (s yeeMerchantService) AccountWithDrawCardQuery(ctx context.Context, merchant model.YeeMerchant) (model.AccountWithDrawCardQueryRes, error) {
	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/account/withdraw/card/query")

	yopRequest.AddParam("merchantNo", merchant.MerchantNo)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.AccountWithDrawCardQueryRes{}, err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.AccountWithDrawCardQueryRes{}, err
	}

	var r model.AccountWithDrawCardQueryRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.AccountWithDrawCardQueryRes{}, err
	}

	if r.ReturnCode != "UA00000" {
		return model.AccountWithDrawCardQueryRes{}, xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	return r, err
}

func (s yeeMerchantService) AccountWithDrawCardBind(ctx context.Context, merchant model.YeeMerchant, accountNo, bankCode string) (model.AccountWithDrawCardBindRes, error) {
	for _, i := range merchant.WithdrawCardList {
		if i.AccountNo == accountNo {
			return model.AccountWithDrawCardBindRes{}, xerr.NewErr(xerr.ErrParamError, nil, "改卡已经绑定，请联系修改")
		}
	}

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/account/withdraw/card/bind")
	yopRequest.AddParam("merchantNo", merchant.MerchantNo)

	bankCardType := "DEBIT_CARD"
	yopRequest.AddParam("bankCardType", bankCardType)
	yopRequest.AddParam("accountNo", accountNo)
	yopRequest.AddParam("bankCode", bankCode) // 开户行编码

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.AccountWithDrawCardBindRes{}, err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.AccountWithDrawCardBindRes{}, err
	}

	var r model.AccountWithDrawCardBindRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return model.AccountWithDrawCardBindRes{}, err
	}

	if r.ReturnCode != "UA00000" {
		return model.AccountWithDrawCardBindRes{}, xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	withdrawCardList := merchant.WithdrawCardList

	card := model.WithdrawCard{
		AccountNo:    accountNo,
		BankCardType: bankCardType,
		BankCode:     bankCode,
		BindId:       r.BindId,
	}
	withdrawCardList = append(withdrawCardList, card)

	err = s.yeeMerchantDao.Update(ctx, bson.M{
		"_id": merchant.ID,
	}, bson.M{
		"$set": bson.M{
			"withdraw_card_list": withdrawCardList,
		},
	})
	if err != nil {
		return model.AccountWithDrawCardBindRes{}, err
	}

	return r, err
}

func (s yeeMerchantService) AccountWithDraw(ctx context.Context, merchant model.YeeMerchant, amount int, bankAccountNo string) error {
	err := s.withdrawApplyOrderS.CheckExistOrder(ctx, merchant.ObjectID, merchant.ObjectType)
	if err != nil {
		return err
	}

	milli := time.Now().UnixMilli()
	data := model.WithdrawApplyOrder{
		ID:           primitive.NewObjectID(),
		Amount:       amount,
		ObjectType:   merchant.ObjectType,
		ObjectID:     merchant.ObjectID,
		Fee:          0,
		ValidateType: 0,
		BankCardNo:   bankAccountNo,
		PayStatus:    model.PayStatusTypePending,
		CreatedAt:    milli,
		UpdatedAt:    milli,
	}

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/account/withdraw/order")
	yopRequest.AddParam("parentMerchantNo", merchant.ParentMerchantNo)
	yopRequest.AddParam("merchantNo", merchant.MerchantNo)

	requestNo := util.NewUUIDNum()
	yopRequest.AddParam("requestNo", requestNo)
	yopRequest.AddParam("receiveType", "REAL_TIME") // 到账类型 实时

	amountFloat := util.DealMoneyToYuan(amount)

	yopRequest.AddParam("orderAmount", amountFloat) // number

	yopRequest.AddParam("bankAccountNo", bankAccountNo) // 提现账号
	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayWithdraw)

	remark := "果蔬团商户提现"
	if merchant.ObjectType == model.ObjectTypeServicePoint {
		remark = "中心仓提现"
	}

	yopRequest.AddParam("remark", remark)              // 银行附言
	yopRequest.AddParam("feeDeductType", "OUTSIDE")    // 手续费收取方式
	yopRequest.AddParam("accountType", "FUND_ACCOUNT") // 账户类型 资金账户

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}

	var r model.AccountWithDrawRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.ReturnCode != "UA00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	yeeResult := model.YeeWithdrawResult{
		OrderNo:   r.OrderNo,
		RequestNo: requestNo,
		Status:    r.Status,
	}

	data.YeeWithdrawResult = yeeResult

	err = s.withdrawApplyOrderS.CreateData(ctx, data)
	if err != nil {
		return err
	}

	return err
}

func (s yeeMerchantService) AccountWithDrawQuery(ctx context.Context, merchantNo, orderNo string) error {
	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/account/withdraw/system/query")
	yopRequest.AddParam("merchantNo", merchantNo)

	//requestNo := util.NewUUIDNum()
	//yopRequest.AddParam("requestNo", requestNo)
	yopRequest.AddParam("orderNo", orderNo)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}

	_ = yopResp

	return err
}

func (s yeeMerchantService) InitWithDrawCardBind(ctx context.Context) error {
	filter := bson.M{
		"object_type":        2,
		"application_status": "COMPLETED",
	}

	merchants, err := s.yeeMerchantDao.ListAll(ctx, filter)
	if err != nil {
		return err
	}

	for _, merchant := range merchants {
		zap.S().Infof("简称：%s", merchant.MerchantSubjectInfo.ShortName)

		cardQueryRes, err := s.AccountWithDrawCardQuery(ctx, merchant)
		if err != nil {
			return err
		}
		if len(cardQueryRes.BankCardAccountList) > 0 {
			zap.S().Infof("ok:%s", merchant.MerchantSubjectInfo.ShortName)
			continue
		}

		s.AccountWithDrawCardBind(ctx, merchant, merchant.SettlementAccountInfo.BankCardNo, merchant.SettlementAccountInfo.BankCode)

	}

	return nil
}

func (s yeeMerchantService) ProductFeeQuery(ctx context.Context, mer model.YeeMerchant) (any, error) {
	_ = ctx

	var yopRequest = request.NewYopRequest("GET", "/rest/v2.0/mer/product/fee/query")

	yopRequest.AddParam("parentMerchantNo", mer.ParentMerchantNo)
	yopRequest.AddParam("merchantNo", mer.MerchantNo)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return nil, err
	}

	_ = yopResp

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return nil, err
	}

	var m map[string]string
	err = json.Unmarshal(marshal, &m)
	if err != nil {
		return nil, err
	}

	var l []map[string]string
	err = json.Unmarshal([]byte(m["productInfo"]), &l)
	if err != nil {
		return nil, err
	}

	return l, err
}

func (s yeeMerchantService) ProductFeeUpdate(ctx context.Context, mer model.YeeMerchant) error {
	_ = ctx

	var yopRequest = request.NewYopRequest("POST", "/rest/v2.0/mer/product/fee/modify")

	requestNo := util.NewUUIDNum()
	yopRequest.AddParam("requestNo", requestNo)
	yopRequest.AddParam("parentMerchantNo", mer.ParentMerchantNo)
	yopRequest.AddParam("merchantNo", mer.MerchantNo)
	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeeProductModify)

	productInfo := []map[string]interface{}{
		{
			"productCode":   "MINI_PROGRAM_WECHAT_OFFLINE",
			"rateType":      "SINGLE_PERCENT",
			"percentRate":   "0.25",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "PREPAID_REAL",      // 预付实扣
		},
		{
			"productCode":   "D1",
			"rateType":      "SINGLE_FIXED",
			"fixedRate":     "0",
			"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
			"paymentMethod": "REAL_TIME",         // 实收
		},
		//{
		//	"productCode":   "ENTERPRISE_WITHDRAW_STANDARD_TWOHOURS", // 企业账户提现标准版_2小时到账
		//	"rateType":      "SINGLE_FIXED",
		//	"fixedRate":     "0",
		//	"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
		//	"paymentMethod": "REAL_TIME",         // 实收
		//},
		//{
		//	"productCode":   "ENTERPRISE_WITHDRAW_STANDARD_TOMORROW", // 企业账户提现标准版 次日到账
		//	"rateType":      "SINGLE_FIXED",
		//	"fixedRate":     "0",
		//	"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
		//	"paymentMethod": "REAL_TIME",         // 实收
		//},
		//{
		//	"productCode":   "ENTERPRISE_WITHDRAW_STANDARD_REALTIME", // 企业账户提现标准版 实时到账
		//	"rateType":      "SINGLE_FIXED",
		//	"fixedRate":     "0",
		//	"undertaker":    "PLATFORM_MERCHANT", // 后期改为 平台商 承担  PLATFORM_MERCHANT
		//	"paymentMethod": "REAL_TIME",         // 实收
		//},
	}
	productInfoM, err := json.Marshal(productInfo)
	if err != nil {
		return err
	}
	yopRequest.AddParam("productInfo", string(productInfoM))

	return nil

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}

	_ = yopResp

	zap.S().Infof("更改结果：%s", yopResp.Result)

	return err
}

// TransferToMarket 资金转营销账号
func (s yeeMerchantService) TransferToMarket(ctx context.Context, amount int) error {
	_ = ctx
	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/account/transfer/b2b/order")
	yopRequest.AddParam("parentMerchantNo", "***********") // 平台商

	requestNo := util.NewUUIDNum()
	yopRequest.AddParam("requestNo", requestNo)

	yopRequest.AddParam("fromMerchantNo", "***********") // 转出方商户编号
	yopRequest.AddParam("toMerchantNo", "***********")   // 转入方商户编号
	yopRequest.AddParam("toAccountType", "MARKET_ACCOUNT")

	yopRequest.AddParam("receiveType", "REAL_TIME") // 到账类型 实时

	amountFloat := util.DealMoneyToYuan(amount)
	yopRequest.AddParam("orderAmount", amountFloat) // number

	yopRequest.AddParam("usage", "营销账号")            // 用途
	yopRequest.AddParam("feeChargeSide", "OUTSIDE") // 手续费承担方

	yopRequest.AddParam("usage", "营销账号") // 用途

	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayTransfer) // 通知地址

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}

	var r model.TransferRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.ReturnCode != "UA00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.ReturnMsg)
	}

	return err
}

// TransferQuery 转账信息查询
func (s yeeMerchantService) TransferQuery(ctx context.Context, orderNo string) error {
	_ = ctx
	var yopRequest = request.NewYopRequest("GET", "/rest/v1.0/account/transfer/system/query")
	yopRequest.AddParam("requestNo", "10d7ef412201419c98ce11adc3e26912")
	//yopRequest.AddParam("orderNo", orderNo)

	yopRequest.AddParam("merchantNo", "***********") // 转出方商户编号

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}

	zap.S().Infof("转账信息：%s", string(marshal))

	return err
}

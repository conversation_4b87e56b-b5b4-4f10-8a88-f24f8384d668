package divisionService

//
//type Service interface {
//	ListProvince() ([]division.Division, error)
//	GetDetail(code int) ([]division.Division, error)
//	DealGroupDivision(province, city, county int) ([]division.Division, error)
//	ListNextByCode(code int) ([]division.Division, error)
//}
//
//type divisionService struct {
//	d *division.RedisAdapter
//}
//
//func (s divisionService) ListNextByCode(code int) ([]division.Division, error) {
//	divisions, err := s.d.ListNextDivision(code)
//	if err != nil {
//		return nil, xerr.NewErr(xerr.ErrParamError, err, err.Error())
//	}
//	return divisions, nil
//}
//
//func (s divisionService) ListProvince() ([]division.Division, error) {
//	divisions := s.d.ListProvince()
//	return divisions, nil
//}
//
//func (s divisionService) DealGroupDivision(province, city, county int) ([]division.Division, error) {
//	if county != 0 {
//		divisions, err := s.GetDetail(county)
//		return divisions, err
//	}
//	if city != 0 {
//		divisions, err := s.GetDetail(city)
//		return divisions, err
//	}
//	if province != 0 {
//		divisions, err := s.GetDetail(province)
//		return divisions, err
//	}
//
//	return nil, xerr.NewErr(xerr.ErrParamError, nil, "团购行政区域参数缺失")
//}
//
//func (s divisionService) GetDetail(code int) ([]division.Division, error) {
//	divisions, err := s.d.GetDivisionDetail(code)
//	if err != nil {
//		return nil, xerr.NewErr(xerr.ErrParamError, err, err.Error())
//	}
//	return divisions, nil
//}
//
//func NewDivisionService() Service {
//	return &divisionService{
//		d: global.Division,
//	}
//}

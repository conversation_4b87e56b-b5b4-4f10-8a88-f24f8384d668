package swipeService

import (
	"base/dao"
	"base/dao/swipeDao"
	"base/model"
	"base/types"
	"base/util"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, req types.SwipeCreate) (primitive.ObjectID, error)
	Update(ctx context.Context, req types.SwipeUpdate) error
	UpdateSort(ctx context.Context, req types.SwipeUpdateSort) error
	List(ctx context.Context, page, limit int64) ([]model.Swipe, int64, error)
	ListALl(ctx context.Context, visible bool) ([]model.Swipe, error)
	ListVisible(ctx context.Context) ([]model.Swipe, error)
	Del(ctx context.Context, Id primitive.ObjectID) error
	Get(ctx context.Context, id primitive.ObjectID) (model.Swipe, error)
}

type swipeService struct {
	db swipeDao.DaoInt
}

func (s swipeService) Get(ctx context.Context, id primitive.ObjectID) (model.Swipe, error) {
	get, err := s.db.Get(ctx, bson.M{"_id": id})
	return get, err
}

func NewSwipeService() ServiceInterface {
	return swipeService{
		db: dao.SwipeDao,
	}
}

func (s swipeService) Create(ctx context.Context, req types.SwipeCreate) (primitive.ObjectID, error) {
	now := time.Now().UnixMilli()

	count, err := s.db.Count(ctx)
	if err != nil {
		return [12]byte{}, err
	}

	swipe := model.Swipe{
		ID:        primitive.NewObjectID(),
		Sort:      int(count + 1),
		Visible:   req.Visible,
		Type:      req.Type,
		Url:       req.Url,
		Img:       req.Img,
		CreatedAt: now,
	}

	err = s.db.Create(ctx, swipe)
	if err != nil {
		return primitive.NilObjectID, err
	}
	return swipe.ID, nil
}

func (s swipeService) Update(ctx context.Context, req types.SwipeUpdate) error {
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		return err
	}

	return s.db.UpdateInfo(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{
		"sort":       req.Sort,
		"visible":    req.Visible,
		"type":       req.Type,
		"url":        req.Url,
		"img":        req.Img,
		"updated_at": time.Now().UnixMilli(),
	}})
}

func (s swipeService) UpdateSort(ctx context.Context, req types.SwipeUpdateSort) error {
	for _, sort := range req.List {
		id, err := util.ConvertToObject(sort.ID)
		if err != nil {
			return err
		}
		err = s.db.UpdateInfo(ctx, bson.M{"_id": id}, bson.M{"$set": bson.M{
			"sort": sort.Sort,
		}})
		if err != nil {
			return err
		}
	}
	return nil
}

func (s swipeService) ListALl(ctx context.Context, visible bool) ([]model.Swipe, error) {
	filter := bson.M{
		"visible": visible,
	}
	list, err := s.db.ListByCus(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s swipeService) ListVisible(ctx context.Context) ([]model.Swipe, error) {
	list, err := s.db.ListByCus(ctx, bson.M{
		"visible": true,
	})
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s swipeService) List(ctx context.Context, page, limit int64) ([]model.Swipe, int64, error) {
	list, i, err := s.db.List(ctx, bson.M{}, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s swipeService) Del(ctx context.Context, Id primitive.ObjectID) error {
	return s.db.Delete(ctx, Id)
}

package fruitClassService

import (
	"base/dao"
	"base/dao/fruitClassDao"
	"base/global"
	"base/model"
	"base/service/productService"
	"base/util"
	"context"
	"errors"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInterface 分类标签
type ServiceInterface interface {
	Create(ctx context.Context, categoryID primitive.ObjectID, name string) error
	UpdateName(ctx context.Context, id primitive.ObjectID, name string) error
	Get(ctx context.Context, id primitive.ObjectID) (model.FruitClass, error)
	List(ctx context.Context, categoryID primitive.ObjectID) ([]model.FruitClass, error)
	Delete(ctx context.Context, id primitive.ObjectID) error
	CreateFruitClassBatch(ctx context.Context, categoryID string) error
}

type fruitClassService struct {
	mdb      *mongo.Database
	rdb      *redis.Client
	fruitDao fruitClassDao.DaoInt
	productS productService.ServiceInterface
}

func NewFruitClassService() ServiceInterface {
	return fruitClassService{
		mdb:      global.MDB,
		rdb:      global.RDBDefault,
		fruitDao: dao.FruitClassDao,
		productS: productService.NewProductService(),
	}
}

func (s fruitClassService) Get(ctx context.Context, id primitive.ObjectID) (model.FruitClass, error) {
	m := get(s.rdb, id)
	if m.ID == primitive.NilObjectID {
		data, err := s.fruitDao.Get(ctx, bson.M{
			"_id": id,
		})
		if err != nil {
			return model.FruitClass{}, err
		}
		set(s.rdb, data)
		return data, nil
	}
	return m, nil
}

func (s fruitClassService) Create(ctx context.Context, categoryID primitive.ObjectID, name string) error {
	name = strings.TrimSpace(name)
	data := model.FruitClass{
		ID:         primitive.NewObjectID(),
		CategoryID: categoryID,
		Name:       name,
		CreatedAt:  time.Now().UnixMilli(),
	}
	filter := bson.M{
		"category_id": categoryID,
		"name":        name,
	}
	get, err := s.fruitDao.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if get.ID != primitive.NilObjectID {
		return err
	}

	err = s.fruitDao.Create(ctx, data)
	return err
}

func (s fruitClassService) List(ctx context.Context, categoryID primitive.ObjectID) ([]model.FruitClass, error) {
	filter := bson.M{
		"category_id": categoryID,
	}
	list, err := s.fruitDao.List(ctx, filter)
	return list, err
}

func (s fruitClassService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	err := s.productS.CheckFruitClass(ctx, id)
	if err != nil {
		return err
	}

	err = s.fruitDao.Delete(ctx, filter)
	return err
}

func (s fruitClassService) UpdateName(ctx context.Context, id primitive.ObjectID, name string) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"$set": bson.M{
			"name": name,
		},
	}

	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		err = s.productS.UpdateFruitClass(sessCtx, id, name)
		if err != nil {
			return nil, err
		}

		err = s.fruitDao.Update(ctx, filter, update)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	del(s.rdb, id)
	return nil
}

// CreateFruitClassBatch 新建水果等级
func (s fruitClassService) CreateFruitClassBatch(ctx context.Context, categoryID string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("CreateFruitClass error:%v", err)
			return
		}
	}()

	list := []string{"A级", "B级", "C级"}
	id, err := util.ConvertToObjectWithNote(categoryID, "")
	if err != nil {
		return err
	}

	for _, name := range list {
		err = s.Create(ctx, id, name)
		if err != nil {
			return err
		}
	}

	return nil
}

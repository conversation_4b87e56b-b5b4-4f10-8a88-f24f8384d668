package sysService

import (
	"base/global"
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"log"
)

type ServiceInterface interface {
	// UpsertDescImg 详情图之后
	UpsertDescImg(ctx context.Context, list []string) error
	ListDescImg(ctx context.Context) ([]string, error)
	SysTest(msg string) error
}

type sysService struct {
	rdb *redis.Client
}

func NewSysService() ServiceInterface {
	return sysService{
		rdb: global.RDBDefault,
	}
}

func (s sysService) SysTest(msg string) error {
	log.Println("SysTest:::", msg)
	return nil
}

func (s sysService) UpsertDescImg(ctx context.Context, list []string) error {
	bytes, err := json.Marshal(list)
	if err != nil {
		return err
	}
	s.rdb.Set(ctx, "descImg", bytes, 0)
	return nil
}

func (s sysService) ListDescImg(ctx context.Context) ([]string, error) {
	var list []string
	if s.rdb.Exists(ctx, "descImg").Val() > 0 {
		bytes, err := s.rdb.Get(ctx, "descImg").Bytes()
		if err != nil {
			return nil, err
		}
		err = json.Unmarshal(bytes, &list)
		if err != nil {
			return nil, err
		}
	}
	return list, nil
}

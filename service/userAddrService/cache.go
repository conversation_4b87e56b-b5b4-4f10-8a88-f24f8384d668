package userAddrService

import (
	"base/model"
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

var cache = "address:"

func get(r *redis.Client, id primitive.ObjectID) model.Address {
	key := cache + id.Hex()
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := r.Get(ctx, key).Bytes()
		if err != nil {
			zap.S().Error("get err")
			return model.Address{}
		}
		var i model.Address
		err = json.Unmarshal(bytes, &i)
		if err != nil {
			zap.S().Error("unmarshal,", err)
			return model.Address{}
		}
		return i
	}
	return model.Address{}
}

func set(r *redis.Client, info model.Address) {
	key := cache + info.ID.Hex()

	bytes, err := json.Marshal(info)
	if err != nil {
		zap.S().Error("set marshal,", err)
		return
	}
	r.Set(context.Background(), key, bytes, 0)
}

func del(r *redis.Client, id primitive.ObjectID) {
	r.Del(context.Background(), cache+id.Hex())
}

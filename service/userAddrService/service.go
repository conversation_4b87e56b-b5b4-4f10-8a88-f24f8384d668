package userAddrService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/userAddrDao"
	"base/global"
	"base/model"
	"base/service/messageService"
	"base/service/userService"
	"base/types"
	"base/util"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"strconv"
	"strings"
	"time"
)

type ServiceInterface interface {
	Add(ctx context.Context, req *types.UserAddrCreate, buyerID primitive.ObjectID) error
	InitByBuyer(ctx context.Context, info model.Buyer) error
	List(ctx context.Context, userId primitive.ObjectID) ([]model.Address, error)
	Update(ctx context.Context, req *types.UserAddrUpdate) error
	UpdateAudit(ctx context.Context, req types.UpdateAddrAdminReq) error
	UpdateLocation(ctx context.Context, req types.UpdateAddrLocationReq) error
	UpdateLogisticsUnitFee(ctx context.Context, id primitive.ObjectID, amount int) error
	//UpdateDeliverFree(ctx context.Context, deliverFreeBegin, deliverFreeEnd int64) error
	//UpdateCus(ctx context.Context, filter, update bson.M) error
	//UpdateMany(ctx context.Context, filter, update bson.M) error
	Del(ctx context.Context, id primitive.ObjectID) error
	DelByID(ctx context.Context, id primitive.ObjectID) error
	Get(ctx context.Context, id primitive.ObjectID) (model.Address, error)
	GetDefaultByUserID(ctx context.Context, userID primitive.ObjectID) (model.Address, error)
	GeoCoder(ctx context.Context, lat, lng float64) (model.GeoCoder, error)
	ListByCus(ctx context.Context, filter bson.M) ([]model.Address, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Address, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	Audit(ctx context.Context, address model.Address, point model.ServicePoint, req types.UserAddrAuditReq) error
	AssignPoint(ctx context.Context, info model.Address, point model.ServicePoint, station model.Station) error
}

type userAddrService struct {
	db    userAddrDao.UserAddrDaoInt
	rdb   *redis.Client
	msg   messageService.ServiceInterface
	userS userService.ServiceInterface
}

func NewUserAddrService() ServiceInterface {
	return userAddrService{
		db:    dao.UserAddrDao,
		rdb:   global.RDBDefault,
		msg:   messageService.NewMessageService(),
		userS: userService.NewUserService(),
	}
}

func (s userAddrService) GeoCoder(ctx context.Context, lat, lng float64) (model.GeoCoder, error) {
	_ = ctx
	url := "https://apis.map.qq.com/ws/geocoder/v1"

	latStr := strconv.FormatFloat(lat, 'f', 6, 64)
	lngStr := strconv.FormatFloat(lng, 'f', 6, 64)
	location := latStr + "," + lngStr
	params := map[string]string{
		"key":      "UHDBZ-KIBCT-RKSXH-VBQUD-YSEU6-ANF2H",
		"location": location,
	}
	var r model.GeoCoder
	response, err := util.NewResty().Get(url, params, &r)
	if err != nil {
		zap.S().Errorf("geoCoder err: %v", err.Error())
		return model.GeoCoder{}, err
	}
	_ = response

	if len(r.Result.AdInfo.AdCode) == 6 {
		adCode := r.Result.AdInfo.AdCode
		adInfo := r.Result.AdInfo
		adInfo.ProvinceCode = adCode[0:2]
		adInfo.CityCode = adCode[0:4]
		adInfo.DistrictCode = adCode[0:6]
		r.Result.AdInfo = adInfo
	}

	return r, nil
}

func (s userAddrService) Get(ctx context.Context, id primitive.ObjectID) (model.Address, error) {
	m := get(s.rdb, id)
	if m.ID == primitive.NilObjectID {
		address, err := s.db.Get(ctx, bson.M{"_id": id})
		if err != nil {
			return model.Address{}, err
		}
		set(s.rdb, address)
		return address, nil
	}
	return m, nil
}

func (s userAddrService) GetDefaultByUserID(ctx context.Context, userID primitive.ObjectID) (model.Address, error) {
	filter := bson.M{
		"user_id":    userID,
		"is_default": true,
	}
	address, err := s.db.Get(ctx, filter)
	if err != nil {
		return model.Address{}, err
	}
	return address, nil
}

// Add 添加收货地址
func (s userAddrService) Add(ctx context.Context, req *types.UserAddrCreate, buyerID primitive.ObjectID) error {
	err := checkAddr(req.Address, req.Contact.Name, req.Location)
	if err != nil {
		return err
	}
	now := time.Now().UnixMilli()

	data := model.Address{
		ID:        primitive.NewObjectID(),
		BuyerID:   buyerID,
		Address:   util.DealWrap(req.Address),
		Contact:   req.Contact,
		Location:  req.Location,
		IsDefault: false,
		//Entity:             req.Entity,
		//ApplyReason:        req.ApplyReason,
		AuditStatus: model.AuditStatusTypePass,
		//ShopHeadImg:        req.ShopHeadImg,
		//BusinessLicenseImg: req.BusinessLicenseImg,
		LogisticsNote: "",
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	err = s.db.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

// InitByBuyer 会员审核初始化
func (s userAddrService) InitByBuyer(ctx context.Context, info model.Buyer) error {
	now := time.Now().UnixMilli()

	contact := model.Contact{
		Name:   info.ContactUser,
		Mobile: info.ContactMobile,
	}
	data := model.Address{
		ID:                      primitive.NewObjectID(),
		UserID:                  info.UserID,
		BuyerID:                 info.ID,
		Address:                 util.DealWrap(info.Address),
		Contact:                 contact,
		Location:                info.Location,
		IsDefault:               true,
		ApplyReason:             info.ApplyReason,
		Note:                    info.AddressNote,
		Entity:                  info.Entity,
		ServicePointID:          info.ServicePointID,
		ServicePointName:        info.ServicePointName,
		StationID:               info.StationID,
		StationName:             info.StationName,
		DeliverType:             info.DeliverType,
		DeliverFee:              info.DeliverFee,
		SubsidyAmount:           info.SubsidyAmount,
		SubsidyPercent:          info.SubsidyPercent,
		DeliverFreeBegin:        info.DeliverFreeBegin,
		DeliverFreeEnd:          info.DeliverFreeEnd,
		LogisticsNote:           info.LogisticsNote,
		LogisticsUnitFee:        info.LogisticsUnitFee,
		InstantDeliver:          info.InstantDeliver,
		ServiceFee:              info.ServiceFee,
		ServiceFeeRebatePercent: info.ServiceFeeRebatePercent,
		AuditStatus:             model.AuditStatusTypePass,
		AuditFailReason:         "",
		ShopHeadImg:             info.ShopHeadImg,
		BusinessLicenseImg:      info.BusinessLicenseImg,
		//UserType:                info.UserType,
		CreatedAt: now,
		UpdatedAt: now,
	}

	err := s.db.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func checkAddr(addr, contactUser string, location model.Location) error {
	if len(addr) == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写详细地址")
	}
	if len(contactUser) == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写联系人")
	}
	if location.Latitude == 0 || location.Longitude == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请选择定位")
	}
	return nil
}

// List 用户地址列表
func (s userAddrService) List(ctx context.Context, userId primitive.ObjectID) ([]model.Address, error) {
	return s.db.GetByUserID(ctx, userId)
}

// ListByCus 用户地址列表
func (s userAddrService) ListByCus(ctx context.Context, filter bson.M) ([]model.Address, error) {
	list, err := s.db.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s userAddrService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Address, int64, error) {
	list, count, err := s.db.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s userAddrService) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

// Update 修改收货地址
func (s userAddrService) Update(ctx context.Context, req *types.UserAddrUpdate) error {
	err := checkAddr(req.Address, req.Contact.Name, req.Location)
	if err != nil {
		return err
	}

	id, err := primitive.ObjectIDFromHex(req.ID)
	if err != nil {
		return xerr.NewErr(xerr.ErrNoDocument, nil, "收货地址不存在，请刷新页面后重试")
	}

	address, err := s.Get(ctx, id)
	if err != nil {
		return err
	}
	now := time.Now().UnixMilli()
	address.Address = util.DealWrap(req.Address)

	address.Contact = req.Contact
	address.Location = req.Location
	//address.Entity = req.Entity
	//address.ApplyReason = req.ApplyReason
	address.AuditStatus = model.AuditStatusTypePass
	//address.ShopHeadImg = req.ShopHeadImg
	//address.BusinessLicenseImg = req.BusinessLicenseImg
	address.UpdatedAt = now

	err = s.db.Update(ctx, address)
	if err != nil {
		return err
	}

	del(s.rdb, id)

	return nil
}

// UpdateCus 修改收货地址
//func (s userAddrService) UpdateCus(ctx context.Context, filter, update bson.M) error {
//	return s.db.UpdateCus(ctx, filter, update)
//}

//func (s userAddrService) UpdateMany(ctx context.Context, filter, update bson.M) error {
//	return s.db.UpdateMany(ctx, filter, update)
//}

func (s userAddrService) Del(ctx context.Context, id primitive.ObjectID) error {
	err := s.db.Del(ctx, id)
	if err != nil {
		return err
	}

	del(s.rdb, id)
	return nil
}

func (s userAddrService) DelByID(ctx context.Context, id primitive.ObjectID) error {
	err := s.db.DelByID(ctx, id)
	if err != nil {
		return err
	}
	del(s.rdb, id)

	return nil
}

func (s userAddrService) Audit(ctx context.Context, info model.Address, point model.ServicePoint, req types.UserAddrAuditReq) error {
	milli := time.Now().UnixMilli()
	update := bson.M{
		"audit_status": req.AuditStatus,
		"updated_at":   milli,
	}

	if req.AuditStatus == model.AuditStatusTypeNotPass {
		update["audit_fail_reason"] = req.AuditFailReason
	}

	if req.AuditStatus == model.AuditStatusTypePass {
		//update["service_point_id"] = point.ID
		//update["service_point_name"] = point.Name
		update["deliver_type"] = req.DeliverType
		update["deliver_fee"] = req.DeliverFee
		//update["subsidy_amount"] = 50000
		//update["subsidy_percent"] = 50
		update["note"] = req.AddressNote
		update["deliver_free_begin"] = req.DeliverFreeBegin
		update["deliver_free_end"] = req.DeliverFreeEnd
		update["audit_fail_reason"] = ""

		update["logistics_note"] = req.LogisticsNote
		update["logistics_unit_fee"] = req.LogisticsUnitFee

		update["instant_deliver"] = req.InstantDeliver
		update["service_fee"] = req.ServiceFee
		update["service_fee_rebate_percent"] = req.ServiceFeeRebatePercent
		update["entity"] = req.Entity
		update["user_type"] = req.UserType
	}

	err := s.db.UpdateCus(ctx, bson.M{"_id": info.ID}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, info.ID)

	return nil
}

func (s userAddrService) AssignPoint(ctx context.Context, info model.Address, point model.ServicePoint, station model.Station) error {
	milli := time.Now().UnixMilli()
	update := bson.M{
		"is_assign_service_point": true,
		"service_point_id":        point.ID,
		"service_point_name":      point.Name,
		"station_id":              station.ID,
		"station_name":            station.Name,
		"updated_at":              milli,
	}

	err := s.db.UpdateCus(ctx, bson.M{"_id": info.ID}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, info.ID)

	return nil
}

func (s userAddrService) UpdateAudit(ctx context.Context, req types.UpdateAddrAdminReq) error {
	milli := time.Now().UnixMilli()
	update := bson.M{
		"updated_at": milli,
	}

	//update["service_point_id"] = point.ID
	//update["service_point_name"] = point.Name

	update["deliver_type"] = req.DeliverType
	update["deliver_fee"] = req.DeliverFee
	update["deliver_subsidy_amount"] = req.DeliverSubsidyAmount
	update["deliver_subsidy_percent"] = req.DeliverSubsidyPercent
	update["note"] = req.AddressNote
	update["deliver_free_begin"] = req.DeliverFreeBegin
	update["deliver_free_end"] = req.DeliverFreeEnd

	update["logistics_note"] = req.LogisticsNote
	update["logistics_unit_fee"] = req.LogisticsUnitFee

	update["instant_deliver"] = req.InstantDeliver
	update["service_fee"] = req.ServiceFee
	update["service_fee_rebate_percent"] = req.ServiceFeeRebatePercent

	update["entity"] = req.Entity
	update["user_type"] = req.UserType

	addressID, err := util.ConvertToObjectWithCtx(ctx, req.AddressID)
	if err != nil {
		return err
	}

	err = s.db.UpdateCus(ctx, bson.M{"_id": addressID}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, addressID)

	return nil
}

func (s userAddrService) UpdateLocation(ctx context.Context, req types.UpdateAddrLocationReq) error {
	// check

	addr := strings.TrimSpace(req.Address)
	if addr == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写详细地址")
	}

	loc := req.Location
	if loc.Longitude == 0 || loc.Latitude == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写经纬度")
	}

	if loc.Address == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写定位地址")
	}

	type UpdateAddrLocationReq struct {
		AddressID string         `json:"address_id"`
		Address   string         `bson:"address" json:"address"`   // 具体地址
		Location  model.Location `bson:"location" json:"location"` // 地址经纬度
	}

	milli := time.Now().UnixMilli()
	update := bson.M{
		"updated_at": milli,
	}

	update["address"] = addr
	update["location"] = req.Location

	addressID, err := util.ConvertToObjectWithCtx(ctx, req.AddressID)
	if err != nil {
		return err
	}

	err = s.db.UpdateCus(ctx, bson.M{"_id": addressID}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, addressID)

	return nil
}

func (s userAddrService) UpdateLogisticsUnitFee(ctx context.Context, id primitive.ObjectID, amount int) error {
	milli := time.Now().UnixMilli()
	update := bson.M{
		"updated_at": milli,
	}

	update["logistics_unit_fee"] = amount

	err := s.db.UpdateCus(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, id)

	return nil
}

//
//func (s userAddrService) UpdateDeliverFree(ctx context.Context, deliverFreeBegin, deliverFreeEnd int64) error {
//	milli := time.Now().UnixMilli()
//	update := bson.M{
//		"deliver_free_begin": deliverFreeBegin,
//		"deliver_free_end":   deliverFreeEnd,
//		"updated_at":         milli,
//	}
//
//	err := s.db.UpdateCus(ctx, bson.M{}, bson.M{"$set": update})
//	if err != nil {
//		return err
//	}
//
//	del(s.rdb, id)
//
//	return nil
//}

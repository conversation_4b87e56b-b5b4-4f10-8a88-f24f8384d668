package indexPartProductService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/indexPartProductApplyDao"
	"base/dao/indexPartProductDao"
	"base/global"
	"base/model"
	"base/service/productHistoryService"
	"base/service/productService"
	"context"
	"errors"
	"time"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	Update(ctx context.Context, id primitive.ObjectID, pIDs []primitive.ObjectID, operate string) error
	UpdateByStation(ctx context.Context, indexPartID, supplierID primitive.ObjectID, pIDs []primitive.ObjectID) error
	UpdateSort(ctx context.Context, id primitive.ObjectID, pIDs []primitive.ObjectID) error
	RemoveFromPart(ctx context.Context, id primitive.ObjectID, productID primitive.ObjectID) error
	List(ctx context.Context, id primitive.ObjectID, page, limit int64) ([]model.IndexPartProduct, int64, error)
	ListByCus(ctx context.Context, filter bson.M) ([]model.IndexPartProduct, error)

	Apply(ctx context.Context, indexPartID, productID, supplierID primitive.ObjectID, applyPriceList []model.PerPrice) error
	//ApplyCheck(ctx context.Context, indexPartID, productID primitive.ObjectID) error
	ApplyList(ctx context.Context, filter bson.M) ([]model.IndexPartProductApply, error)
	ApplyAudit(ctx context.Context, id primitive.ObjectID, auditStatus model.AuditStatusType, auditNote string) error

	DownProduct(ctx context.Context, productID primitive.ObjectID, isDel bool) error
	UpProduct(ctx context.Context, productID primitive.ObjectID) error
}

type indexPartProductService struct {
	mdb                      *mongo.Database
	rdb                      *redis.Client
	indexPartProductDao      indexPartProductDao.DaoInt
	indexPartProductApplyDao indexPartProductApplyDao.DaoInt
	productS                 productService.ServiceInterface
	productHistoryS          productHistoryService.ServiceInterface
}

func NewIndexPartProductService() ServiceInterface {
	return indexPartProductService{
		mdb:                      global.MDB,
		rdb:                      global.RDBDefault,
		indexPartProductDao:      dao.IndexPartProductDao,
		indexPartProductApplyDao: dao.IndexPartProductApplyDao,
		productS:                 productService.NewProductService(),
		productHistoryS:          productHistoryService.NewProductHistoryService(),
	}
}

func (s indexPartProductService) DownProduct(ctx context.Context, productID primitive.ObjectID, isDel bool) error {
	filter := bson.M{
		"product_id": productID,
	}

	list, err := s.indexPartProductDao.List(ctx, filter)
	if err != nil {
		return err
	}
	err = s.indexPartProductDao.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}

	if isDel {
		return nil
	}

	for _, v := range list {
		if v.IndexPartID.Hex() == "666a4f7c999621192a9a0fc2" {
			// 益禾堂
			continue
		}
		s.productHistoryS.NotifyDownSale(ctx, model.HistoryTypePart, v.IndexPartID, productID)

		// 重排
		listAll, _, err := s.indexPartProductDao.ListByPage(ctx, bson.M{
			"index_part_id": v.IndexPartID,
		}, 1, 100)
		if err != nil {
			return err
		}
		if len(listAll) < 1 {
			return nil
		}

		for i, data := range listAll {
			err = s.indexPartProductDao.UpdateOne(ctx, bson.M{
				"index_part_id": v.IndexPartID,
				"product_id":    data.ProductID,
			}, bson.M{"$set": bson.M{
				"sort": i,
			}})
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func (s indexPartProductService) UpProduct(ctx context.Context, productID primitive.ObjectID) error {
	list, err := s.productHistoryS.ListUpSale(ctx, model.HistoryTypePart, productID)
	if err != nil {
		return err
	}

	for _, v := range list {
		get, err := s.indexPartProductDao.Get(ctx, bson.M{
			"index_part_id": v.ObjectID,
			"product_id":    productID,
		})
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			return err
		}

		if get.ID == primitive.NilObjectID {
			data := model.IndexPartProduct{
				ID:          primitive.NewObjectID(),
				IndexPartID: v.ObjectID,
				ProductID:   productID,
				Sort:        100,
				CreatedAt:   time.Now().UnixMilli(),
			}
			err = s.indexPartProductDao.Create(ctx, data)
			if err != nil {
				zap.S().Errorf("topicService UpProduct错误%v", err)
			}
		}

		pList, err := s.indexPartProductDao.List(ctx, bson.M{
			"index_part_id": v.ObjectID,
		})
		if err != nil {
			return err
		}

		index := 1

		for _, product := range pList {
			if product.ProductID == productID {
				index = 1
			} else {
				index++
			}

			s.indexPartProductDao.UpdateOne(ctx, bson.M{
				"_id": product.ID,
			}, bson.M{
				"$set": bson.M{
					"sort": index,
				},
			})
		}
	}
	return nil
}

func (s indexPartProductService) Update(ctx context.Context, id primitive.ObjectID, pIDs []primitive.ObjectID, operate string) error {
	if operate == "add" {
		err := s.indexPartProductDao.DeleteMany(ctx, bson.M{
			"index_part_id": id,
			"product_id": bson.M{
				"$in": pIDs,
			},
		})
		if err != nil {
			return err
		}

		listByIDs, err := s.productS.ListByIDs(ctx, pIDs)
		if err != nil {
			return err
		}
		mSupplier := make(map[primitive.ObjectID]primitive.ObjectID)
		for _, i := range listByIDs {
			mSupplier[i.ID] = i.SupplierID
		}

		now := time.Now().UnixMilli()
		var list []model.IndexPartProduct
		for i, d := range pIDs {
			data := model.IndexPartProduct{
				ID:          primitive.NewObjectID(),
				IndexPartID: id,
				ProductID:   d,
				SupplierID:  mSupplier[d],
				Sort:        i + 1,
				CreatedAt:   now,
			}
			list = append(list, data)
		}
		err = s.indexPartProductDao.CreateMany(ctx, list)
		if err != nil {
			return err
		}

		return nil
	}

	if operate == "remove" {
		//	移除
		filter := bson.M{
			"index_part_id": id,
			"product_id": bson.M{
				"$in": pIDs,
			},
		}
		err := s.indexPartProductDao.DeleteMany(ctx, filter)
		if err != nil {
			return err
		}

		// 重排 - 使用批量更新优化
		list, _, err := s.indexPartProductDao.ListByPage(ctx, bson.M{
			"index_part_id": id,
		}, 1, 100)
		if err != nil {
			return err
		}
		if len(list) < 1 {
			return nil
		}

		for i, data := range list {
			err = s.indexPartProductDao.UpdateOne(ctx, bson.M{
				"index_part_id": id,
				"product_id":    data.ProductID,
			}, bson.M{"$set": bson.M{
				"sort": i,
			}})
			if err != nil {
				return err
			}
		}

		return nil
	}

	return nil
}

func (s indexPartProductService) UpdateByStation(ctx context.Context, indexPartID, supplierID primitive.ObjectID, pIDs []primitive.ObjectID) error {
	filterDel := bson.M{
		"index_part_id": indexPartID,
		"supplier_id":   supplierID,
	}
	err := s.indexPartProductDao.DeleteMany(ctx, filterDel)
	if err != nil {
		return err
	}

	if len(pIDs) < 1 {
		return nil
	}

	now := time.Now().UnixMilli()
	var list []model.IndexPartProduct
	for i, d := range pIDs {
		data := model.IndexPartProduct{
			ID:          primitive.NewObjectID(),
			IndexPartID: indexPartID,
			ProductID:   d,
			SupplierID:  supplierID,
			Sort:        i + 1,
			CreatedAt:   now,
			UpdatedAt:   now,
		}
		list = append(list, data)
	}
	err = s.indexPartProductDao.CreateMany(ctx, list)
	if err != nil {
		return err
	}

	return nil
}

func (s indexPartProductService) AddToPart(ctx context.Context, id primitive.ObjectID, productID primitive.ObjectID) error {
	p, err := s.indexPartProductDao.Get(ctx, bson.M{
		"index_part_id": id,
		"product_id":    productID,
	})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if p.ID != primitive.NilObjectID {
		//	已存在
		return nil
	}

	count, err := s.indexPartProductDao.Count(ctx, bson.M{
		"index_part_id": id,
	})
	if err != nil {
		return err
	}
	now := time.Now().UnixMilli()

	data := model.IndexPartProduct{
		ID:          primitive.NewObjectID(),
		IndexPartID: id,
		ProductID:   productID,
		Sort:        int(count + 1),
		CreatedAt:   now,
	}

	err = s.indexPartProductDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartProductService) RemoveFromPart(ctx context.Context, id primitive.ObjectID, productID primitive.ObjectID) error {
	product, err := s.productS.Get(ctx, productID)
	if err != nil {
		return err
	}

	filter := bson.M{
		"index_part_id": id,
		"product_id":    productID,
	}
	err = s.indexPartProductDao.Delete(ctx, filter)
	if err != nil {
		return err
	}

	if product.OriginPrice != 0 {
		err = s.productS.UpdatePricePart(ctx, productID, product.OriginPrice, 0, 0)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s indexPartProductService) UpdateSort(ctx context.Context, id primitive.ObjectID, pIDs []primitive.ObjectID) error {
	if len(pIDs) < 1 {
		return nil
	}

	// 使用BulkWrite批量更新，替代原来的逐一更新，提升性能
	operations := make([]mongo.WriteModel, 0, len(pIDs))

	for i, productID := range pIDs {
		filter := bson.M{
			"index_part_id": id,
			"product_id":    productID,
		}
		update := bson.M{
			"$set": bson.M{
				"sort": i,
			},
		}

		operation := mongo.NewUpdateOneModel()
		operation.SetFilter(filter)
		operation.SetUpdate(update)
		operations = append(operations, operation)
	}

	return s.indexPartProductDao.BulkWrite(ctx, operations)
}

func (s indexPartProductService) List(ctx context.Context, id primitive.ObjectID, page, limit int64) ([]model.IndexPartProduct, int64, error) {
	filter := bson.M{
		"index_part_id": id,
	}
	list, count, err := s.indexPartProductDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s indexPartProductService) ListByCus(ctx context.Context, filter bson.M) ([]model.IndexPartProduct, error) {
	list, err := s.indexPartProductDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s indexPartProductService) Apply(ctx context.Context, indexPartID, productID, supplierID primitive.ObjectID, applyPriceList []model.PerPrice) error {
	partProduct, err := s.indexPartProductDao.Get(ctx, bson.M{
		"index_part_id": indexPartID,
		"product_id":    productID})
	if errors.Is(err, mongo.ErrNoDocuments) {
		err = nil
	}
	if err != nil {
		return err
	}
	if partProduct.ProductID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "该商品已存在此专区")
	}

	partProductApply, err := s.indexPartProductApplyDao.Get(ctx, bson.M{
		"index_part_id": indexPartID,
		"product_id":    productID})
	if errors.Is(err, mongo.ErrNoDocuments) {
		err = nil
	}
	if err != nil {
		return err
	}
	if partProductApply.ProductID != primitive.NilObjectID && partProductApply.AuditStatus == model.AuditStatusTypeDoing {
		return xerr.NewErr(xerr.ErrParamError, nil, "该商品在申请中")
	}

	now := time.Now().UnixMilli()

	data := model.IndexPartProductApply{
		ID:             primitive.NewObjectID(),
		SupplierID:     supplierID,
		IndexPartID:    indexPartID,
		ProductID:      productID,
		ApplyPriceList: applyPriceList,
		AuditStatus:    model.AuditStatusTypeDoing,
		AuditNote:      "",
		CreatedAt:      now,
		UpdatedAt:      now,
	}
	err = s.indexPartProductApplyDao.Create(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

//
//func (s indexPartProductService) ApplyCheck(ctx context.Context, indexPartID, productID primitive.ObjectID) (bool, error) {
//	//partProduct, err := s.indexPartProductDao.Get(ctx, bson.M{
//	//	"index_part_id": indexPartID,
//	//	"product_id":    productID})
//	//if errors.Is(err, mongo.ErrNoDocuments) {
//	//	err = nil
//	//}
//	//if err != nil {
//	//	return false, err
//	//}
//	//if partProduct.ProductID == primitive.NilObjectID {
//	//	return true, nil
//	//}
//
//	return false, nil
//}

func (s indexPartProductService) ApplyList(ctx context.Context, filter bson.M) ([]model.IndexPartProductApply, error) {
	list, err := s.indexPartProductApplyDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s indexPartProductService) ApplyAudit(ctx context.Context, id primitive.ObjectID, auditStatus model.AuditStatusType, auditNote string) error {
	apply, err := s.indexPartProductApplyDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}
	if apply.AuditStatus != model.AuditStatusTypeDoing {
		return xerr.NewErr(xerr.ErrParamError, nil, "该申请已审核")
	}

	now := time.Now().UnixMilli()

	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"audit_status": auditStatus,
		"audit_note":   auditNote,
		"updated_at":   now,
	}
	err = s.indexPartProductApplyDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}

	if auditStatus == model.AuditStatusTypePass {
		//	 添加到专区
		err = s.AddToPart(ctx, apply.IndexPartID, apply.ProductID)
		if err != nil {
			return err
		}
	}

	return nil
}

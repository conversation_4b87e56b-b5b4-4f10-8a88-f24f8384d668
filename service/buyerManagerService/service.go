package buyerManagerService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/buyerManagerDao"
	"base/global"
	"base/model"
	"context"
	"errors"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, userID primitive.ObjectID, userName string) error
	Update(ctx context.Context, id primitive.ObjectID, userName string) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, page, limit int64) ([]model.BuyerManager, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	GetByBuyer(ctx context.Context, buyerID primitive.ObjectID) (model.BuyerManager, error)
}

type buyerManagerService struct {
	mdb             *mongo.Database
	rdb             *redis.Client
	buyerManagerDao buyerManagerDao.DaoInt
}

func NewBuyerManagerService() ServiceInterface {
	return buyerManagerService{
		mdb:             global.MDB,
		rdb:             global.RDBDefault,
		buyerManagerDao: dao.BuyerLinkUserDao,
	}
}

func (s buyerManagerService) Create(ctx context.Context, buyerID primitive.ObjectID, userName string) error {
	get, err := s.buyerManagerDao.Get(ctx, bson.M{"buyer_id": buyerID})
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	_ = get
	if errors.Is(err, mongo.ErrNoDocuments) {
		// 新建
		now := time.Now().UnixMilli()
		data := model.BuyerManager{
			ID:        primitive.NewObjectID(),
			UserID:    primitive.NilObjectID,
			BuyerID:   buyerID,
			UserName:  userName,
			CreatedAt: now,
			UpdatedAt: now,
		}
		err = s.buyerManagerDao.Create(ctx, data)
		if err != nil {
			return err
		}
		return nil
	}

	return xerr.NewErr(xerr.ErrParamError, nil, "已存在")
}

func (s buyerManagerService) List(ctx context.Context, page, limit int64) ([]model.BuyerManager, int64, error) {
	filter := bson.M{}
	list, i, err := s.buyerManagerDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s buyerManagerService) GetByBuyer(ctx context.Context, buyerID primitive.ObjectID) (model.BuyerManager, error) {
	filter := bson.M{
		"buyer_id": buyerID,
	}
	i, err := s.buyerManagerDao.Get(ctx, filter)
	if err != nil {
		return model.BuyerManager{}, err
	}
	return i, nil
}

func (s buyerManagerService) Count(ctx context.Context, filter bson.M) (int64, error) {
	i, err := s.buyerManagerDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return i, nil
}

func (s buyerManagerService) Update(ctx context.Context, id primitive.ObjectID, userName string) error {
	filter := bson.M{
		"_id": id,
	}
	now := time.Now().UnixMilli()

	update := bson.M{
		"user_name":  userName,
		"updated_at": now,
	}
	err := s.buyerManagerDao.Update(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s buyerManagerService) Delete(ctx context.Context, userID primitive.ObjectID) error {
	filter := bson.M{
		"buyer_id": userID,
	}
	err := s.buyerManagerDao.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

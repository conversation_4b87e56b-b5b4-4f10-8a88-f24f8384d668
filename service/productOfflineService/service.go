package productOfflineService

import (
	"base/global"
	"base/model"
	"base/service/productService"
	"base/util"
	"context"
	"encoding/json"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"strconv"
)

var productOfflineListKey = "productOfflineListKey:"

type ServiceInterface interface {
	ListByDay(ctx context.Context, t int64) ([]OfflineInfo, error)
	OfflineCheck(ctx context.Context, content string) error
}

type productOfflineService struct {
	mdb      *mongo.Database
	rdb      *redis.Client
	productS productService.ServiceInterface
}

func NewProductOfflineService() ServiceInterface {
	return productOfflineService{
		mdb:      global.MDB,
		rdb:      global.RDBDefault,
		productS: productService.NewProductService(),
	}
}

func (s productOfflineService) ListByDay(ctx context.Context, t int64) ([]OfflineInfo, error) {
	key := productOfflineListKey + strconv.FormatInt(t, 10)
	lRange := s.rdb.LRange(ctx, key, 0, -1).Val()
	l := make([]OfflineInfo, 0)
	for _, str := range lRange {
		var temp OfflineInfo

		err := json.Unmarshal([]byte(str), &temp)
		l = append(l, temp)

		if err != nil {
			return nil, err
		}
	}

	return l, nil
}

func (s productOfflineService) OfflineCheck(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("OfflineCheck error:%v", err)
			return
		}
	}()

	var data model.MNSProduct
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	product, err := s.productS.Get(ctx, data.ProductID)
	if err != nil {
		zap.S().Errorf("OfflineCheck error:%v", err)
		return nil
	}

	zeroTimestamp, err := util.DayStartZeroTimestamp(data.OfflineAt)
	if err != nil {
		zap.S().Errorf("OfflineCheck error:%v", err)
		return nil
	}

	if !product.Sale && product.AuditStatus == model.AuditStatusTypePass {
		//	半小时后依然下架
		p := OfflineInfo{
			ProductID:    product.ID,
			ProductTitle: product.Title,
			SupplierID:   product.SupplierID,
			SupplierName: product.SupplierSimpleName,
			OfflineAt:    data.OfflineAt,
		}

		// zeroTimestamp 转string
		zeroTimestampStr := strconv.FormatInt(zeroTimestamp, 10)

		key := productOfflineListKey + zeroTimestampStr

		marshal, err := json.Marshal(p)
		if err != nil {
			zap.S().Errorf("OfflineCheck error:%v", err)
			return nil
		}

		s.rdb.LPush(ctx, key, string(marshal))
	}

	return nil
}

type OfflineInfo struct {
	ProductID    primitive.ObjectID `json:"product_id"`
	ProductTitle string             `json:"product_title"`
	OfflineAt    int64              `json:"offline_at"`
	SupplierID   primitive.ObjectID `json:"supplier_id"`
	SupplierName string             `json:"supplier_name"`
}

package withdrawService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/authenticationDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

// ServiceInterface 提现
type ServiceInterface interface {
	// SignAcctProtocol 账户提现协议签约
	SignAcctProtocolBuyer(ctx context.Context, data model.Authentication) (string, error)

	SignAcctProtocol(ctx context.Context, auth model.Authentication, signType string) (string, error)
	// SignAcctProtocolQuery 账户提现协议签约-查询
	SignAcctProtocolQuery(ctx context.Context, data model.Authentication) (string, error)
	// UpdateSignAcctProtocolFrontBack 主动更新提现协议
	UpdateSignAcctProtocolFrontBack(ctx context.Context, bizUserId, result, AcctProtocolNo string) error
	// NotifySignAcctProtocol 提现协议
	NotifySignAcctProtocol(ctx context.Context, res allinpay.SignAcctProtocolResult)
	NotifySignAcctProtocolPersonal(ctx context.Context, res allinpay.SignAcctProtocolResult)
}

type withdrawService struct {
	rdb               *redis.Client
	authenticationDao authenticationDao.DaoInt
	authenticationS   authenticationService.ServiceInterface
	// 支付 会员
	payMember payModule.MemberService
}

func (s withdrawService) UpdateSignAcctProtocolFrontBack(ctx context.Context, bizUserId, result, acctProtocolNo string) error {
	update := bson.M{
		"sign_result":      result,
		"acct_protocol_no": acctProtocolNo,
	}

	err := s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": bizUserId}, bson.M{"$set": update})
	if err != nil {
		zap.S().Error("更新提现协议编号失败：", err)
		return err
	}
	return nil
}

type VerifyResult struct {
	BizUserId        string `json:"bizUserId"`
	Result           int    `json:"result"`
	AccountSetResult int    `json:"accountSetResult"`
	CheckTime        string `json:"checkTime"`
	FailReason       string `json:"failReason"`
	Remark           string `json:"remark"`
}

func (s withdrawService) NotifySignAcctProtocol(ctx context.Context, res allinpay.SignAcctProtocolResult) {
	auth, err := s.authenticationS.GetByBizUserID(ctx, res.BizUserId)
	if err != nil {
		zap.S().Errorf("NotifySignAcctProtocol 查询错误：%s", res.BizUserId)
		return
	}

	if auth.MemberType == pays.MemberTypeCompany {
		update := bson.M{
			"sign_result":      res.Result,
			"sign_acct_name":   res.SignAcctName,
			"acct_protocol_no": res.AcctProtocolNo,
		}

		err := s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{"$set": update})
		if err != nil {
			zap.S().Error("更新提现协议编号失败：", err)
		}
		return
	}

	if auth.MemberType == pays.MemberTypeIndividual {
		update := bson.M{
			"individual_sign_result":      res.Result,
			"individual_sign_acct_name":   res.SignAcctName,
			"individual_acct_protocol_no": res.AcctProtocolNo,
		}

		err := s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{"$set": update})
		if err != nil {
			zap.S().Error("更新提现协议编号失败：", err)
		}
	}

}

func (s withdrawService) NotifySignAcctProtocolPersonal(ctx context.Context, res allinpay.SignAcctProtocolResult) {
	update := bson.M{
		"sign_personal.sign_result":      res.Result,
		"sign_personal.sign_acct_name":   res.SignAcctName,
		"sign_personal.acct_protocol_no": res.AcctProtocolNo,
	}

	err := s.authenticationDao.Update(ctx, bson.M{"pay_biz_user_id": res.BizUserId}, bson.M{"$set": update})
	if err != nil {
		zap.S().Error("更新提现协议编号失败：", err)
	}

}

// SignAcctProtocol 账户提现签约
func (s withdrawService) SignAcctProtocol(ctx context.Context, data model.Authentication, signType string) (string, error) {
	var name string
	if data.MemberType == pays.MemberTypeIndividual {
		return "", xerr.NewErr(xerr.ErrParamError, nil, "个人暂时不支持")
	}

	url := global.BackUrlSignAcctProtocol
	if data.MemberType == pays.MemberTypeCompany {
		if data.Company.CompanyType == model.CompanyTypeCo {
			//	公司---对公
			name = data.Company.CompanyName
			if signType == "personal" {
				name = data.Company.Legal.LegalName
				url = global.BackUrlSignAcctProtocolPersonal
			}
		}
		if data.Company.CompanyType == model.CompanyTypePerson {
			//	个体工商户--默认对私
			name = data.Company.Legal.LegalName
		}
	}
	res, err := s.payMember.SignAcctProtocolS(pays.SignAcctProtocolReq{
		BizUserId:    data.PayBizUserId,
		SignAcctName: name,
		JumpPageType: 2, // 2 小程序页面
		//JumpUrl:      url, //
		BackUrl: global.BackHost + url,
		//NoContractUrl:
		Source: pays.SourceMobile,
	})
	if err != nil {
		zap.S().Error("提现签约请求失败：", err)
		return "", err
	}

	return res.Url, nil
}

func (s withdrawService) SignAcctProtocolBuyer(ctx context.Context, data model.Authentication) (string, error) {
	name := data.RealName

	res, err := s.payMember.SignAcctProtocolS(pays.SignAcctProtocolReq{
		BizUserId:    data.PayBizUserId,
		SignAcctName: name,
		JumpPageType: 2, // 2 小程序页面
		//JumpUrl:      url, //
		BackUrl: global.BackHost + global.BackUrlSignAcctProtocol,
		//NoContractUrl:
		Source: pays.SourceMobile,
	})
	if err != nil {
		zap.S().Error("提现签约请求失败：", err)
		return "", err
	}

	return res.Url, nil
}

// SignAcctProtocolQuery 账户提现签约-查询
func (s withdrawService) SignAcctProtocolQuery(ctx context.Context, data model.Authentication) (string, error) {
	res, err := s.payMember.SignContractQueryS(pays.SignContractQueryReq{
		BizUserId:    data.PayBizUserId,
		SignAcctName: data.SignAcctName,
		JumpPageType: 2, // 2 小程序页面
		//JumpUrl:      url, //
		Source: pays.SourceMobile,
	})
	if err != nil {
		zap.S().Error("提现签约查询错误：", err)
		return "", err
	}
	return res.Url, nil
}

func NewWithdrawService() ServiceInterface {
	return withdrawService{
		rdb:               global.RDBDefault,
		authenticationDao: dao.AuthenticationDao,
		authenticationS:   authenticationService.NewAuthenticationService(),
		payMember:         payModule.NewMember(),
	}
}

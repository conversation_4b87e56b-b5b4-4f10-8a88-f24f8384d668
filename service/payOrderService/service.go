package payOrderService

import (
	"base/dao"
	"base/dao/payOrderDao"
	"base/global"
	"base/model"
	"base/payModule"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// ServiceInterface 支付单
type ServiceInterface interface {
	Create(ctx context.Context, data model.PayOrder) error
	Get(ctx context.Context, id primitive.ObjectID) (model.PayOrder, error)
	GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.PayOrder, error)
	CloseByBizOrderNo(ctx context.Context, bizOrderNo string) error
	QueryBizOrderStatus(ctx context.Context, orderNo, bizOrderNo string) (pays.GetOrderStatusRes, error)
}

type payOrderService struct {
	rdb            *redis.Client
	l              *zap.SugaredLogger
	payOrderDao    payOrderDao.DaoInt
	allInPayOrderS payModule.OrderService
}

func NewPayOrderService() ServiceInterface {
	return payOrderService{
		rdb:            global.RDBDefault,
		l:              global.OrderLogger.Sugar(),
		payOrderDao:    dao.PayOrderDao,
		allInPayOrderS: payModule.NewOrderS(),
	}
}

func (s payOrderService) GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.PayOrder, error) {
	filter := bson.M{
		"biz_order_no": bizOrderNo,
	}

	payOrder, err := s.payOrderDao.Get(ctx, filter)
	if err != nil {
		s.l.Error("根据bizOrderNo查询错误", err)
		return model.PayOrder{}, err
	}
	return payOrder, nil
}

func (s payOrderService) Create(ctx context.Context, data model.PayOrder) error {
	err := s.payOrderDao.Create(ctx, data)

	return err
}

func (s payOrderService) Get(ctx context.Context, id primitive.ObjectID) (model.PayOrder, error) {
	filter := bson.M{
		"_id": id,
	}
	payOrder, err := s.payOrderDao.Get(ctx, filter)
	if err != nil {
		return model.PayOrder{}, err
	}
	return payOrder, nil
}

// QueryBizOrderStatus 查询支付订单状态
func (s payOrderService) QueryBizOrderStatus(ctx context.Context, orderNo, bizOrderNo string) (pays.GetOrderStatusRes, error) {
	req := pays.GetOrderStatusReq{
		OrderNo:    orderNo,
		BizOrderNo: bizOrderNo,
	}
	res, err := s.allInPayOrderS.GetOrderStatusS(req)
	if err != nil {
		return pays.GetOrderStatusRes{}, err
	}
	return res, nil
}

// CloseByBizOrderNo 关闭云商通订单
func (s payOrderService) CloseByBizOrderNo(ctx context.Context, bizOrderNo string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("CloseByBizOrderNo error:%v", err)
			return
		}
	}()

	if bizOrderNo == "695c4a07-2a42-43c5-b464-67aa8961000d" || bizOrderNo == "55d69f72-8228-4f55-bd7f-869b253e965c" {
		s.l.Infof("CloseByBizOrderNo 跳过%s", bizOrderNo)
		return nil
	}

	req := pays.CloseOrderReq{
		BizOrderNo: bizOrderNo,
	}
	res, err := s.allInPayOrderS.CloseOrderS(req)
	if err != nil {
		s.l.Errorf("更新支付串信息，关闭原云商通订单%s错误", bizOrderNo)
		return nil
	}

	if res.CloseResult != 1 {
		s.l.Errorf("关闭云商通订单%s失败,原因%s", bizOrderNo, res.ErrorMessage)
	}
	return nil
}

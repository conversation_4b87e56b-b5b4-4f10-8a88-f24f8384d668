package fileservice

import (
	"base/model"
	"base/service/orderService"
	"base/service/ossService"
	"base/util"
	"bytes"
	"context"
	"fmt"
	"time"

	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

var hasExportCache = "hasExportCache"

// ProductSaleStatsExport 商品销售统计导出
func (s *fileService) ProductSaleStatsExport(ctx context.Context, content string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("ProductSaleStatsExport %v", err)
		}
	}()

	var data model.MNSProductSaleStatsExportTime
	err := util.DecodeMNSContent(content, &data)
	if err != nil {
		return err
	}

	key := fmt.Sprintf("%s:%d:%d", hasExportCache, data.BeginTime, data.EndTime)
	exists, err := s.rdb.Exists(ctx, key).Result()
	if err != nil {
		return err
	}
	if exists == 1 {
		return nil
	}

	s.rdb.Set(ctx, key, true, time.Second*10)

	filter := bson.M{
		"created_at": bson.M{
			"$gte": data.BeginTime,
			"$lte": data.EndTime,
		},
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		return err
	}

	productStatsMap := make(map[primitive.ObjectID]*ProductStats)

	for _, order := range orders {
		for _, p := range order.ProductList {
			if stats, exists := productStatsMap[p.ProductID]; exists {
				stats.OrderCount += p.Num
				stats.ShipCount += p.SortNum
			} else {
				productStatsMap[p.ProductID] = &ProductStats{
					ProductID:    p.ProductID,
					SupplierName: order.SupplierName,
					ProductTitle: p.ProductTitle,
					Price:        p.Price,
					OrderCount:   p.Num,
					ShipCount:    p.SortNum,
				}
			}
		}
	}

	bufferFile, err := exportExcel(&productStatsMap)
	if err != nil {
		return err
	}

	now := time.Now()

	dir := "filecenter"
	pathSuffix := now.Format("20060102T150405")

	objectName := dir + "/productstats/" + pathSuffix + ".xlsx"

	err = ossService.NewOssService().UploadDeliverNote(objectName, bufferFile)
	if err != nil {
		zap.S().Errorf("上传oss失败：%v", err.Error())
		return err
	}

	beginTime := time.UnixMilli(data.BeginTime)
	endTime := time.UnixMilli(data.EndTime)
	fileName := fmt.Sprintf("商品销售统计_%d月%d日_%d月%d日.xlsx",
		beginTime.Month(), beginTime.Day(),
		endTime.Month(), endTime.Day())

	s.SaveFileInfo(ctx, fileName, objectName)

	return nil
}

// ProductStats 商品统计
type ProductStats struct {
	ProductID    primitive.ObjectID `json:"product_id"`
	SupplierName string             `json:"supplier_name"` // 供应商名称
	ProductTitle string             `json:"product_title"` // 商品标题
	Price        int                `json:"price"`         // 单价
	OrderCount   int                `json:"order_count"`   // 订单数量
	ShipCount    int                `json:"ship_count"`    // 发货数量
}

// ExcelTest 测试导出Excel
func exportExcel(productStatsMap *map[primitive.ObjectID]*ProductStats) (*bytes.Buffer, error) {
	// 创建新的Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 设置标题
	f.SetCellValue("Sheet1", "A1", "商品销售统计")
	// 合并A1到F1单元格
	f.MergeCell("Sheet1", "A1", "F1")
	// 设置标题样式（居中、加粗、字体大小）
	titleStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Font: &excelize.Font{
			Bold: true,
			Size: 18,
		},
	})
	if err != nil {
		zap.S().Errorf("设置标题样式失败：%v", err.Error())
		return nil, err
	}
	f.SetCellStyle("Sheet1", "A1", "F1", titleStyle)

	// 设置表头样式（居中）
	headerStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		zap.S().Errorf("设置表头样式失败：%v", err.Error())
		return nil, err
	}

	// 设置表头
	headers := []string{"序号", "供应商", "商品名称", "单价", "订单数量", "发货数量"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c2", 'A'+i)
		f.SetCellValue("Sheet1", cell, header)
		f.SetCellStyle("Sheet1", cell, cell, headerStyle)
	}

	// 设置列宽
	f.SetColWidth("Sheet1", "A", "A", 8)  // 序号列宽
	f.SetColWidth("Sheet1", "B", "B", 20) // 供应商列宽
	f.SetColWidth("Sheet1", "C", "C", 30) // 商品名称列宽
	f.SetColWidth("Sheet1", "D", "D", 12) // 单价列宽
	f.SetColWidth("Sheet1", "E", "E", 12) // 订单数量列宽
	f.SetColWidth("Sheet1", "F", "F", 12) // 发货数量列宽

	// 设置内容居中样式
	contentStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	if err != nil {
		zap.S().Errorf("设置内容居中样式失败：%v", err.Error())
		return nil, err
	}

	// 写入数据
	rowIndex := 3
	index := 1
	for _, stats := range *productStatsMap {

		priceFmt := util.DealMoneyToYuan(stats.Price)

		f.SetCellValue("Sheet1", fmt.Sprintf("A%d", rowIndex), index)
		f.SetCellValue("Sheet1", fmt.Sprintf("B%d", rowIndex), stats.SupplierName)
		f.SetCellValue("Sheet1", fmt.Sprintf("C%d", rowIndex), stats.ProductTitle)
		f.SetCellValue("Sheet1", fmt.Sprintf("D%d", rowIndex), priceFmt)
		f.SetCellValue("Sheet1", fmt.Sprintf("E%d", rowIndex), stats.OrderCount)
		f.SetCellValue("Sheet1", fmt.Sprintf("F%d", rowIndex), stats.ShipCount)

		// 设置整行样式
		f.SetCellStyle("Sheet1", fmt.Sprintf("A%d", rowIndex), fmt.Sprintf("F%d", rowIndex), contentStyle)
		rowIndex++
		index++
	}

	// 添加总计行
	totalRow := rowIndex + 1

	// 计算总订单数和总发货数
	var totalOrderCount, totalShipCount int
	for _, stats := range *productStatsMap {
		totalOrderCount += stats.OrderCount
		totalShipCount += stats.ShipCount
	}

	// 设置总计行样式（加粗）
	totalStyle, err := f.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Font: &excelize.Font{
			Bold: true,
		},
	})
	if err != nil {
		zap.S().Errorf("设置总计行样式失败：%v", err.Error())
		return nil, err
	}

	// 写入总计行
	f.SetCellValue("Sheet1", fmt.Sprintf("A%d", totalRow), "总计")
	f.MergeCell("Sheet1", fmt.Sprintf("A%d", totalRow), fmt.Sprintf("D%d", totalRow))
	f.SetCellValue("Sheet1", fmt.Sprintf("E%d", totalRow), totalOrderCount)
	f.SetCellValue("Sheet1", fmt.Sprintf("F%d", totalRow), totalShipCount)
	f.SetCellStyle("Sheet1", fmt.Sprintf("A%d", totalRow), fmt.Sprintf("F%d", totalRow), totalStyle)

	toBuffer, err := f.WriteToBuffer()
	if err != nil {
		zap.S().Errorf("写入总计行失败：%v", err.Error())
		return nil, err
	}

	return toBuffer, nil
}

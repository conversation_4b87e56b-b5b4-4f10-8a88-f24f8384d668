package integralAccountService

import (
	"base/dao"
	integralAccountDao "base/dao/integralAccountDao"
	"base/global"
	"base/model"
	"base/service/userService"
	"context"
	"errors"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, userID primitive.ObjectID) (primitive.ObjectID, error)
	//GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.IntegralAccount, error)
	GetByBuyer(ctx context.Context, buyerID primitive.ObjectID) (model.IntegralAccount, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralAccount, int64, error)
	UpdateNum(ctx context.Context, id primitive.ObjectID, num int) error
	UpdateStatus(ctx context.Context, content string) error
}

type integralAccountService struct {
	mdb                *mongo.Database
	rdb                *redis.Client
	IntegralAccountDao integralAccountDao.DaoInt
	UserS              userService.ServiceInterface
}

func NewIntegralAccountService() ServiceInterface {
	return integralAccountService{
		mdb:                global.MDB,
		rdb:                global.RDBDefault,
		IntegralAccountDao: dao.IntegralAccountDao,
		UserS:              userService.NewUserService(),
	}
}

func (s integralAccountService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralAccount, int64, error) {
	list, i, err := s.IntegralAccountDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s integralAccountService) Create(ctx context.Context, userID primitive.ObjectID) (primitive.ObjectID, error) {
	now := time.Now().UnixMilli()
	data := model.IntegralAccount{
		ID:        primitive.NewObjectID(),
		UserID:    userID,
		Num:       0,
		CreatedAt: now,
		UpdatedAt: now,
	}
	err := s.IntegralAccountDao.Create(ctx, data)
	if err != nil {
		return [12]byte{}, err
	}

	return data.ID, nil
}

func (s integralAccountService) GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.IntegralAccount, error) {
	filter := bson.M{
		"user_id": userID,
	}
	account, err := s.IntegralAccountDao.Get(ctx, filter)
	if errors.Is(err, mongo.ErrNoDocuments) {
		now := time.Now().UnixMilli()
		data := model.IntegralAccount{
			ID:        primitive.NewObjectID(),
			UserID:    userID,
			Num:       0,
			CreatedAt: now,
			UpdatedAt: now,
		}
		user, err := s.UserS.Get(ctx, userID)
		if err != nil {
			return model.IntegralAccount{}, err
		}
		_ = user

		err = s.IntegralAccountDao.Create(ctx, data)
		if err != nil {
			return model.IntegralAccount{}, err
		}
		return data, nil
	}
	if err != nil {
		return model.IntegralAccount{}, err
	}

	return account, nil
}

func (s integralAccountService) GetByBuyer(ctx context.Context, buyerID primitive.ObjectID) (model.IntegralAccount, error) {
	filter := bson.M{
		"buyer_id": buyerID,
	}
	account, err := s.IntegralAccountDao.Get(ctx, filter)
	if errors.Is(err, mongo.ErrNoDocuments) {
		now := time.Now().UnixMilli()
		data := model.IntegralAccount{
			ID: primitive.NewObjectID(),
			//UserID:    userID,
			BuyerID:   buyerID,
			Num:       0,
			CreatedAt: now,
			UpdatedAt: now,
		}
		//user, err := s.UserS.Get(ctx, userID)
		//if err != nil {
		//	return model.IntegralAccount{}, err
		//}
		//_ = user

		err = s.IntegralAccountDao.Create(ctx, data)
		if err != nil {
			return model.IntegralAccount{}, err
		}
		if err != nil {
			return model.IntegralAccount{}, err
		}
		return data, nil
	}
	if err != nil {
		return model.IntegralAccount{}, err
	}

	return account, nil
}

func (s integralAccountService) UpdateNum(ctx context.Context, id primitive.ObjectID, num int) error {
	update := bson.M{
		"num":        num,
		"updated_at": time.Now().UnixMilli(),
	}
	err := s.IntegralAccountDao.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}
func (s integralAccountService) UpdateStatus(ctx context.Context, content string) error {
	//defer func() {
	//	if err := recover(); err != nil {
	//		zap.S().Errorf("inviteService UpdateStatus error:%v", err)
	//		return
	//	}
	//}()
	//
	//var data = struct {
	//	InvitedUserID string `json:"invited_user_id"`
	//	InviteStatus  int    `json:"invite_status"`
	//}{}
	//err := util.DecodeMNSContent(content, &data)
	//if err != nil {
	//	return err
	//}
	//
	//if len(data.InvitedUserID) != 24 {
	//	zap.S().Errorf("UpdateStatus InvitedUserID id不符，id：%s,原数据：%s", data.InvitedUserID, content)
	//	return nil
	//}
	//
	//invitedUserID, err := util.ConvertToObjectWithNote(data.InvitedUserID, "")
	//if err != nil {
	//	return err
	//}
	//
	//filter := bson.M{
	//	"invited_user_id": invitedUserID,
	//}
	//
	//err = s.InviteDao.UpdateMany(ctx, filter, bson.M{"$set": bson.M{"invite_status": data.InviteStatus}})
	//if err != nil {
	//	zap.S().Errorf("更新代金券账户有效错误，err:%s", err.Error())
	//	return err
	//}

	return nil
}

package supplierCollectService

import (
	"base/dao"
	"base/dao/supplierCollectDao"
	"base/global"
	"base/model"
	"context"
	"errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

// ServiceInterface 分类标签
type ServiceInterface interface {
	Create(ctx context.Context, buyerID, supplierID primitive.ObjectID) error
	Exist(ctx context.Context, buyerID, supplierID primitive.ObjectID) (bool, error)
	Delete(ctx context.Context, ids []primitive.ObjectID) error
	List(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.SupplierCollect, int64, error)
	CountFansNumBySupplier(ctx context.Context, supplierID primitive.ObjectID) (int64, error)
}

type supplierCollectService struct {
	mdb         *mongo.Database
	sCollectDao supplierCollectDao.DaoInt
}

func NewSupplierCollectService() ServiceInterface {
	return supplierCollectService{
		mdb:         global.MDB,
		sCollectDao: dao.SupplierCollectDao,
	}
}

func (s supplierCollectService) Exist(ctx context.Context, buyerID, supplierID primitive.ObjectID) (bool, error) {
	filter := bson.M{"buyer_id": buyerID, "supplier_id": supplierID}
	collect, err := s.sCollectDao.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return false, err
	}
	return collect.ID != primitive.NilObjectID, nil
}

func (s supplierCollectService) Delete(ctx context.Context, ids []primitive.ObjectID) error {
	if len(ids) < 1 {
		return nil
	}
	err := s.sCollectDao.DeleteMany(ctx, bson.M{
		"_id": bson.M{"$in": ids},
	})
	if err != nil {
		return err
	}
	return nil
}

func (s supplierCollectService) Create(ctx context.Context, buyerID, supplierID primitive.ObjectID) error {
	filter := bson.M{"buyer_id": buyerID, "supplier_id": supplierID}
	collect, err := s.sCollectDao.Get(ctx, filter)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}

	if collect.ID != primitive.NilObjectID {
		err = s.sCollectDao.Delete(ctx, filter)
		if err != nil {
			return err
		}
		return nil
	}
	data := model.SupplierCollect{
		ID:         primitive.NewObjectID(),
		BuyerID:    buyerID,
		SupplierID: supplierID,
		CreatedAt:  time.Now().UnixMilli(),
	}

	err = s.sCollectDao.Create(ctx, data)
	return err
}

func (s supplierCollectService) List(ctx context.Context, buyerID primitive.ObjectID, page, limit int64) ([]model.SupplierCollect, int64, error) {
	filter := bson.M{
		"buyer_id": buyerID,
	}
	list, i, err := s.sCollectDao.ListByPage(ctx, filter, page, limit)
	return list, i, err
}

func (s supplierCollectService) CountFansNumBySupplier(ctx context.Context, supplierID primitive.ObjectID) (int64, error) {
	filter := bson.M{
		"supplier_id": supplierID,
	}
	i, err := s.sCollectDao.Count(ctx, filter)
	return i, err
}

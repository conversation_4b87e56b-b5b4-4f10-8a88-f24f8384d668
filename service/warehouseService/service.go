package warehouseService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/warehouseDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/allInPayUserService"
	"base/service/authenticationService"
	"base/service/bankAccountService"
	"base/service/entityService"
	"base/service/userService"
	"base/types"
	"base/util"
	"context"
	"errors"
	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

type ServiceInterface interface {
	List(filter bson.M, page, limit int64) ([]model.Warehouse, int64, error)
	ListCus(ctx context.Context, filter bson.M) ([]model.Warehouse, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.Warehouse, error)
	CheckExist(id primitive.ObjectID) error
	CheckOneExist(id primitive.ObjectID) error
	Create(ctx context.Context, req types.CreateWarehouseReq) (primitive.ObjectID, error)
	Update(ctx context.Context, id primitive.ObjectID, name, addr, note string, location model.Location) error
	GetByUser(ctx context.Context, userID primitive.ObjectID) (model.Warehouse, error)
	GetByRegion(ctx context.Context, id primitive.ObjectID) (model.Warehouse, error)
}

type warehouseService struct {
	mdb             *mongo.Database
	rdb             *redis.Client
	warehouseDao    warehouseDao.DaoInt
	userS           userService.ServiceInterface
	entityService   entityService.ServiceInterface
	bankAccountS    bankAccountService.ServiceInterface
	authenticationS authenticationService.ServiceInterface

	AllInPayS     payModule.MemberService
	AllInPayUserS allInPayUserService.ServiceInterface
}

func (s warehouseService) GetByRegion(ctx context.Context, id primitive.ObjectID) (model.Warehouse, error) {
	warehouse, err := s.warehouseDao.Get(ctx, bson.M{
		"region_id":  id,
		"deleted_at": 0,
	})
	if err != nil {
		return model.Warehouse{}, err
	}
	return warehouse, nil
}

func (s warehouseService) Create(ctx context.Context, req types.CreateWarehouseReq) (primitive.ObjectID, error) {
	userID, err := util.ConvertToObject(req.AuthenticationReq.UserID)
	if err != nil {
		return primitive.NilObjectID, err
	}

	user, err := s.userS.Get(ctx, userID)
	if err != nil {
		return primitive.NilObjectID, err
	}

	getByUser, err := s.GetByUser(ctx, userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return primitive.NilObjectID, err
	}

	if getByUser.ID != primitive.NilObjectID {
		return primitive.NilObjectID, xerr.NewErr(xerr.ErrParamError, nil, "创建失败，该用户已绑定有集中仓")
	}

	now := time.Now().UnixMilli()

	// 开启事务
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return primitive.NilObjectID, err
	}
	defer session.EndSession(ctx)

	data := model.Warehouse{
		ID:          primitive.NewObjectID(),
		UserID:      userID,
		Name:        req.Name,
		ContactUser: req.AuthenticationReq.ContactUserName,
		Location:    req.AuthenticationReq.Location,
		Address:     req.AuthenticationReq.Address,
		Note:        req.Note,
		CreatedAt:   now,
	}

	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 创建
		err = s.warehouseDao.Create(sessCtx, data)
		if err != nil {
			return nil, err
		}

		err = s.authenticationS.Create(sessCtx, user.Mobile, req.AuthenticationReq, userID, data.ID, model.ObjectTypeWarehouse, pays.MemberTypeCompany)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return primitive.NilObjectID, err
	}
	return data.ID, nil
}

func (s warehouseService) CheckExist(id primitive.ObjectID) error {
	filter := bson.M{
		"_id":        id,
		"deleted_at": 0,
	}
	count, err := s.warehouseDao.Count(context.Background(), filter)
	if err != nil {
		return err
	}
	if count != 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "部分集中仓不存在")
	}
	return nil
}

func (s warehouseService) CheckOneExist(id primitive.ObjectID) error {
	filter := bson.M{
		"_id":        id,
		"deleted_at": 0,
	}
	count, err := s.warehouseDao.Count(context.Background(), filter)
	if err != nil {
		return err
	}
	if count != 1 {
		return xerr.NewErr(xerr.ErrParamError, nil, "集中仓不存在")
	}
	return nil
}

func (s warehouseService) Update(ctx context.Context, id primitive.ObjectID, name, addr, note string, location model.Location) error {
	filter := bson.M{
		"_id": id,
	}
	update := bson.M{
		"$set": bson.M{
			"name":       name,
			"addr":       addr,
			"note":       note,
			"location":   location,
			"updated_at": time.Now().UnixMilli(),
		},
	}
	err := s.warehouseDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s warehouseService) Get(ctx context.Context, id primitive.ObjectID) (model.Warehouse, error) {
	m := get(s.rdb, id)
	if m.ID == primitive.NilObjectID {
		warehouse, err := s.warehouseDao.Get(ctx, bson.M{"_id": id})
		if err == mongo.ErrNoDocuments {
			return model.Warehouse{}, xerr.NewErr(xerr.ErrNoDocument, nil)
		}
		if err != nil {
			return model.Warehouse{}, err
		}
		set(s.rdb, warehouse)
		return warehouse, nil
	}
	return m, nil
}

func (s warehouseService) GetByUser(ctx context.Context, userID primitive.ObjectID) (model.Warehouse, error) {
	filter := bson.M{
		"user_id":    userID,
		"deleted_at": 0,
	}
	i, err := s.warehouseDao.Get(ctx, filter)
	if err != nil {
		return model.Warehouse{}, err
	}
	return i, nil
}

func (s warehouseService) List(filter bson.M, page, limit int64) ([]model.Warehouse, int64, error) {
	list, i, err := s.warehouseDao.List(context.Background(), filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s warehouseService) ListCus(ctx context.Context, filter bson.M) ([]model.Warehouse, error) {
	list, err := s.warehouseDao.ListCus(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func NewWarehouseServiceService() ServiceInterface {
	return warehouseService{
		mdb:             global.MDB,
		rdb:             global.RDBDefault,
		warehouseDao:    dao.WarehouseDao,
		userS:           userService.NewUserService(),
		entityService:   entityService.NewEntityService(),
		bankAccountS:    bankAccountService.NewBankCardService(),
		authenticationS: authenticationService.NewAuthenticationService(),

		AllInPayS:     payModule.NewMember(),
		AllInPayUserS: allInPayUserService.NewAllInPayUserService(),
	}
}

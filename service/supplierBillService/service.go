package supplierBillService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/supplierBillDao"
	"base/global"
	"base/model"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, data model.SupplierBill) error
	CheckExportLimit(ctx context.Context, buyerID primitive.ObjectID) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	GetByID(ctx context.Context, id primitive.ObjectID) (model.SupplierBill, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.SupplierBill, int64, error)
}

type supplierBillService struct {
	rdb             *redis.Client
	supplierBillDao supplierBillDao.DaoInt
}

func NewSupplierBillService() ServiceInterface {
	return supplierBillService{
		rdb:             global.RDBDefault,
		supplierBillDao: dao.SupplierBillDao,
	}
}

func (s supplierBillService) CheckExportLimit(ctx context.Context, buyerID primitive.ObjectID) error {
	//	每日只能导出10次
	key := "billExportDay:" + buyerID.Hex()
	count, err := s.rdb.Incr(ctx, key).Result()

	// 过期时间，当天
	now := time.Now()
	endTime := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 0, now.Location())
	//diff := int(endTime.Sub(now).Seconds())
	s.rdb.ExpireAt(ctx, key, endTime)

	if err != nil {
		return err
	}
	if count > 5 {
		return xerr.ErrBillExportLimit
	}
	_ = count

	return nil
}

func (s supplierBillService) Create(ctx context.Context, data model.SupplierBill) error {
	err := s.supplierBillDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierBillService) Delete(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	now := time.Now().UnixMilli()

	update := bson.M{
		"updated_at": now,
		"deleted_at": now,
	}
	err := s.supplierBillDao.UpdateOne(ctx, filter, bson.M{"$set": update})
	if err != nil {
		return err
	}
	return nil
}

func (s supplierBillService) GetByID(ctx context.Context, id primitive.ObjectID) (model.SupplierBill, error) {
	filter := bson.M{
		"_id": id,
	}
	fee, err := s.supplierBillDao.Get(ctx, filter)
	if err != nil {
		return model.SupplierBill{}, err
	}
	return fee, nil
}

func (s supplierBillService) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.SupplierBill, int64, error) {
	list, count, err := s.supplierBillDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

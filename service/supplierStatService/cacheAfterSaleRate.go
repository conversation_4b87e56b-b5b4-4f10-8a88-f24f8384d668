package supplierStatService

import (
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"time"
)

// 缓存
var cacheAfterSale = "supplierStatAfterSaleRate:"

func getAfterSale(r *redis.Client, supplierID primitive.ObjectID) float64 {
	key := cacheAfterSale + supplierID.Hex()
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		num, err := r.Get(ctx, key).Float64()
		if err != nil {
			zap.S().Error("get err")
			return 0
		}
		//var i model.SupplierStat
		//err = json.Unmarshal(bytes, &i)
		//if err != nil {
		//	zap.S().Error("unmarshal,", err)
		//	return model.SupplierStat{}
		//}
		return num
	}
	return 0
}

func setAfterSale(r *redis.Client, supplierID primitive.ObjectID, num float64) {
	key := cacheAfterSale + supplierID.Hex()

	//bytes, err := json.Marshal(info)
	//if err != nil {
	//	zap.S().Error("set marshal,", err)
	//	return
	//}
	r.Set(context.Background(), key, num, time.Hour*24*30)
}

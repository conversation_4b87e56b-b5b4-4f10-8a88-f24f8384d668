package supplierStatService

import (
	"base/global"
	"base/model"
	"base/service/commentService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/supplierCollectService"
	"base/service/supplierService"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

// ServiceInterface 供应商
type ServiceInterface interface {
	CalcDaily(ctx context.Context) error
	GetBySupplierID(ctx context.Context, id primitive.ObjectID) (model.SupplierStat, error)
	GetAfterSaleRate(ctx context.Context, supplierID primitive.ObjectID) (float64, error)
}

type supplierStatService struct {
	mdb              *mongo.Database
	rdb              *redis.Client
	supplierS        supplierService.ServiceInterface
	supplierCollectS supplierCollectService.ServiceInterface
	orderS           orderService.ServiceInterface
	orderRefundS     orderRefundService.ServiceInterface
	commentS         commentService.ServiceInterface
}

func NewSupplierStatService() ServiceInterface {
	return supplierStatService{
		mdb:              global.MDB,
		rdb:              global.RDBDefault,
		supplierS:        supplierService.NewSupplierService(),
		supplierCollectS: supplierCollectService.NewSupplierCollectService(),
		orderS:           orderService.NewOrderService(),
		orderRefundS:     orderRefundService.NewOrderRefundService(),
		commentS:         commentService.NewCommentService(),
	}
}

func (s supplierStatService) CalcDaily(ctx context.Context) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("supplierStatService CalcDaily，%v", err)
		}
	}()

	zap.S().Infof("计算,%s", time.Now().Format(time.RFC3339))

	// 计算
	suppliers, _, err := s.supplierS.List(bson.M{"deleted_at": 0}, 1, 100)
	if err != nil {
		return err
	}

	begin := time.Now().AddDate(0, 0, -30).UnixMilli()
	end := time.Now().UnixMilli()
	zap.S().Infof("计算,begin:%d", begin)

	for _, supplier := range suppliers {
		supplierID := supplier.ID
		fansNum, err := s.supplierCollectS.CountFansNumBySupplier(ctx, supplierID)
		if err != nil {
			zap.S().Errorf("CountFansNumBySupplier:%v", err.Error())
			return err
		}
		saleProductNumMonthly, err := s.orderS.CountSaleProductNumMonthly(ctx, supplierID, begin, end)
		if err != nil {
			zap.S().Errorf("CountSaleProductNumMonthly:%v", err.Error())
			return err
		}

		reBuyRateMonthly, err := s.orderS.CountReBuyRateMonthly(ctx, supplierID, begin, end)
		if err != nil {
			zap.S().Errorf("CountReBuyRateMonthly:%v", err.Error())
			return err
		}

		bySupplier, _, err := s.commentS.ListBySupplier(ctx, supplierID, model.AuditStatusTypePass, 1, 100)
		if err != nil {
			zap.S().Errorf("ListBySupplier:%v", err.Error())
			return err
		}

		// 售后
		countAfterSaleMonthly, err := s.orderRefundS.CountAfterSaleMonthly(ctx, supplierID, begin, end)
		if err != nil {
			zap.S().Errorf("countAfterSaleMonthly:%v", err.Error())
			return err
		}
		orderNumMonthly, err := s.orderS.CountOrderNumMonthly(ctx, supplierID, begin, end)
		if err != nil {
			zap.S().Errorf("orderNumMonthly:%v", err.Error())
			return err
		}

		afterSaleRate := calcAfterSaleRate(countAfterSaleMonthly, orderNumMonthly)

		commentStar := calcCommentStar(bySupplier, saleProductNumMonthly)
		now := time.Now().UnixMilli()
		// cache
		data := model.SupplierStat{
			SupplierID:     supplierID,
			SaleNumMonthly: int(saleProductNumMonthly),
			FansNum:        int(fansNum),
			ReBuyRate:      int(reBuyRateMonthly),
			Star:           commentStar,
			AfterSaleRate:  afterSaleRate,
			OrderNum:       int(orderNumMonthly),
			CreatedAt:      now,
			UpdatedAt:      now,
		}
		set(s.rdb, data)
	}

	return nil

}

func calcCommentStar(list []model.Comment, total int) int {
	if total == 0 {
		return 0
	}
	autoStar := (total - len(list)) * 50
	var totalStar int
	for _, comment := range list {
		totalStar += comment.ProductStar
	}
	return (autoStar + totalStar) / total
}

func calcAfterSaleRate(afterSaleNum, orderNum int64) float64 {
	if orderNum == 0 {
		return 0
	}
	afterSale := decimal.NewFromInt(afterSaleNum)
	order := decimal.NewFromInt(orderNum)
	f, _ := afterSale.Mul(decimal.NewFromInt(100)).Div(order).Round(1).Float64()
	//if err != nil {
	//
	//	return 0
	//}
	return f
}

func (s supplierStatService) GetBySupplierID(ctx context.Context, supplierID primitive.ObjectID) (model.SupplierStat, error) {
	stat := get(s.rdb, supplierID)

	return stat, nil
}

func (s supplierStatService) GetAfterSaleRate(ctx context.Context, supplierID primitive.ObjectID) (float64, error) {
	num := getAfterSale(s.rdb, supplierID)

	return num, nil
}

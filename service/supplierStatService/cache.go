package supplierStatService

import (
	"base/model"
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// 缓存
var cache = "supplierStat:"

func get(r *redis.Client, supplierID primitive.ObjectID) model.SupplierStat {
	key := cache + supplierID.Hex()
	ctx := context.Background()
	val := r.Exists(ctx, key).Val()
	if val > 0 {
		bytes, err := r.Get(ctx, key).Bytes()
		if err != nil {
			zap.S().Error("get err")
			return model.SupplierStat{}
		}
		var i model.SupplierStat
		err = json.Unmarshal(bytes, &i)
		if err != nil {
			zap.S().Error("unmarshal,", err)
			return model.SupplierStat{}
		}
		return i
	}
	return model.SupplierStat{}
}

func set(r *redis.Client, info model.SupplierStat) {
	key := cache + info.SupplierID.Hex()

	bytes, err := json.Marshal(info)
	if err != nil {
		zap.S().<PERSON>rror("set marshal,", err)
		return
	}
	r.Set(context.Background(), key, bytes, 0)
}

func del(r *redis.Client, id primitive.ObjectID) {
	r.Del(context.Background(), cache+id.Hex())
}

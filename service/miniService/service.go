package miniService

import (
	"base/core/config"
	"base/core/xerr"
	"base/global"
	"base/model"
	"base/util"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
	"io"
	"net/http"
	"net/url"
	"time"
)

//var accessTokenCache = "accessToken"
//var accessTokenAdminCache = "accessTokenAdmin"

// ServiceInterface 小程序服务
type ServiceInterface interface {
	GetOpenID(code string) (openID, sessionKey, unionID string, err error)
	GetMobile(code string) (string, error)
	GetAccessToken() (accessToken string, err error)
	SendUniformMsg(ctx context.Context, r model.UniformMessage) error
	SendUniformMsg222(ctx context.Context, openID string) error
	GetAppID(ctx context.Context) (string, error)
	GetUnlimitedQRCode(userID string) ([]byte, error)
	GetUnlimitedDeliverQRCode(scene string) ([]byte, error)
	GenerateUrlLink(code string) (url string, err error)

	ShipStatus(transactionID string) (int, error)
	ShipUploadInfo(openID, transactionID string, isDebt bool) error
}

type miniService struct {
	AppID          string
	Secret         string
	AccessTokenKey string
	rdb            *redis.Client
}

func NewMiniService() ServiceInterface {
	var appID string
	var appSecret string
	var accessTokenKey string
	c := config.Conf.Mini
	appID = c.AppID
	appSecret = c.AppSecret
	accessTokenKey = "accessToken"

	return miniService{
		AppID:          appID,
		Secret:         appSecret,
		AccessTokenKey: accessTokenKey,
		rdb:            global.RDBDefault,
	}
}

func (s miniService) GetAccessToken() (accessToken string, err error) {
	key := s.AccessTokenKey
	val := s.rdb.Exists(context.Background(), key).Val()
	if val > 0 {
		v := s.rdb.Get(context.Background(), key).Val()
		return v, nil
	}
	tokenRemote, _, err := s.getAccessTokenRemote()
	if err != nil {
		return "", err
	}
	result, err := s.rdb.Set(context.Background(), key, tokenRemote, time.Second*7000).Result()
	if err != nil {
		return "", err
	}
	_ = result

	return tokenRemote, nil
}

func (s miniService) getAccessTokenRemote() (accessToken string, expiresIn int, err error) {
	parse, err := url.Parse("https://api.weixin.qq.com/cgi-bin/token")

	values := url.Values{}
	values.Add("grant_type", "client_credential")
	values.Add("appid", s.AppID)
	values.Add("secret", s.Secret)

	parse.RawQuery = values.Encode()

	resp, err := http.Get(parse.String())
	if err != nil {
		return "", 0, err
	}
	var info AccessTokenInfo
	body, _ := io.ReadAll(resp.Body)
	err = json.Unmarshal(body, &info)
	if err != nil {
		return "", 0, err
	}
	return info.AccessToken, info.ExpiresIn, nil
}

type GetUnlimitedQRCodeResp struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
	Buffer  []byte `json:"buffer"`
}

func (s miniService) GetUnlimitedQRCode(userID string) ([]byte, error) {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return nil, err
	}

	parse, err := url.Parse("https://api.weixin.qq.com/wxa/getwxacodeunlimit")
	if err != nil {
		return nil, err
	}

	values := url.Values{}
	values.Add("access_token", accessToken)

	parse.RawQuery = values.Encode()

	scene := url.Values{}
	scene.Add("user_id", userID)

	b := map[string]interface{}{
		"page":       "pages/center/invite/index",
		"scene":      scene.Encode(),
		"width":      "280", // 默认430，二维码的宽度，单位 px，最小 280px，最大 1280px
		"check_path": true,  //
		//"check_path": false, //
		"env_version": "release", // 正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
		//"env_version": "develop", // 正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
		"is_hyaline": true, // 默认是false，是否需要透明底色，为 true 时，生成透明底色的小程序
	}

	var res GetUnlimitedQRCodeResp

	resp, err := util.NewResty().Post(parse.String(), b, &res)
	if err != nil {
		return nil, err
	}
	if res.Errcode != 0 {
		return nil, xerr.NewErr(xerr.ErrParamError, nil, res.Errmsg)
	}
	_ = resp

	return resp.Body(), nil

}

func (s miniService) GetUnlimitedDeliverQRCode(key string) ([]byte, error) {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return nil, err
	}

	parse, err := url.Parse("https://api.weixin.qq.com/wxa/getwxacodeunlimit")
	if err != nil {
		return nil, err
	}

	values := url.Values{}
	values.Add("access_token", accessToken)

	parse.RawQuery = values.Encode()

	scene := url.Values{}
	scene.Add("key", key)

	b := map[string]interface{}{
		"page":       "pages/servicePoint/deliverMan/index",
		"scene":      scene.Encode(),
		"width":      "280", // 默认430，二维码的宽度，单位 px，最小 280px，最大 1280px
		"check_path": true,  //
		//"check_path": false, //
		//"env_version": "release", // 正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
		"env_version": "release", // 正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
		"is_hyaline":  true,      // 默认是false，是否需要透明底色，为 true 时，生成透明底色的小程序
	}

	var res GetUnlimitedQRCodeResp

	resp, err := util.NewResty().Post(parse.String(), b, &res)
	if err != nil {
		return nil, err
	}
	if res.Errcode != 0 {
		return nil, xerr.NewErr(xerr.ErrParamError, nil, res.Errmsg)
	}
	_ = resp

	return resp.Body(), nil

}

// SendUniformMsg 下发统一消息
func (s miniService) SendUniformMsg(ctx context.Context, r model.UniformMessage) error {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return err
	}

	urlStr := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?access_token=%s", accessToken)
	b := map[string]interface{}{
		"touser": r.OpenID,
		"mp_template_msg": map[string]interface{}{
			"appid":       "wx115b52c2ef6e426c", // 公众号appid
			"template_id": r.TemplateID,
			//"url":         "",
			"miniprogram": map[string]interface{}{
				"appid":    r.MiniAppID,
				"pagepath": r.PagePath,
			},
			"data": r.Data,
		},
	}

	var res SendResp

	marshal, _ := json.Marshal(b)
	zap.S().Infof("统一下发消息：%s", string(marshal))

	resp, err := util.NewResty().Post(urlStr, b, &res)
	if err != nil {
		return err
	}
	_ = resp
	bytes, _ := json.Marshal(resp)
	zap.S().Infof("resp %s", string(bytes))
	if res.Errcode != 0 {
		zap.S().Errorf("res.Errmsg , %s", res.Errmsg)
		return xerr.NewErr(xerr.ErrParamError, nil, res.Errmsg)
	}
	return nil
}

// SendUniformMsg222 下发统一消息
func (s miniService) SendUniformMsg222(ctx context.Context, openID string) error {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return err
	}

	urlStr := fmt.Sprintf("https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=%s", accessToken)
	b := map[string]interface{}{
		"touser": openID,
		"mp_template_msg": map[string]interface{}{
			"appid":       "wx115b52c2ef6e426c", // 公众号appid
			"template_id": "4NL-dJ_Te3YSeT0iG-6TqFuHcTw_1uj-TgQST4FYEmE",
			//"url":         "",
			//"miniprogram": map[string]interface{}{
			//	"appid":    r.MiniAppID,
			//	"pagepath": r.PagePath,
			//},
			"data": map[string]interface{}{
				"thing16": map[string]string{
					"value": "张三",
				},
				"thing5": map[string]string{
					"value": "这是留言信息",
				},
			},
		},
	}

	/*
	   data := map[string]interface{}{
	   		"amount7": map[string]string{
	   			"value": amountStr,
	   		},
	   		"phrase3": map[string]string{
	   			"value": rType,
	   		},
	   		"time4": map[string]string{
	   			//"value": refund.RefundResult.PayDatetime,
	   			"value": rDatetime,
	   		},


	*/
	var res SendResp

	marshal, _ := json.Marshal(b)
	zap.S().Infof("统一下发消息：%s", string(marshal))

	resp, err := util.NewResty().Post(urlStr, b, &res)
	if err != nil {
		return err
	}
	_ = resp
	bytes, _ := json.Marshal(resp)
	zap.S().Infof("resp %s", string(bytes))
	if res.Errcode != 0 {
		zap.S().Errorf("res.Errmsg , %s", res.Errmsg)
		return xerr.NewErr(xerr.ErrParamError, nil, res.Errmsg)
	}
	return nil
}

type SendResp struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
}

func (s miniService) GetOpenID(code string) (openID, sessionKey, unionID string, err error) {
	url := "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code"
	urlPath := fmt.Sprintf(url, s.AppID, s.Secret, code)
	resp, err := http.Get(urlPath)
	defer resp.Body.Close()
	body, _ := io.ReadAll(resp.Body)

	type res struct {
		SessionKey string `json:"session_key"`
		UnionID    string `json:"unionid"`
		ErrMsg     string `json:"errmsg"`
		Openid     string `json:"openid"`
		ErrCode    int32  `json:"errcode"`
	}
	var r res
	err = json.Unmarshal(body, &r)
	if err != nil {
		return "", "", "", err
	}

	if r.ErrCode != 0 {
		switch r.ErrCode {
		case 40029:
			return "", "", "", xerr.NewErr(xerr.ErrLoginExpire, nil)
		default:
			return "", "", "", xerr.NewErr(xerr.ErrLoginExpire, nil)
		}
	}

	return r.Openid, r.SessionKey, r.UnionID, nil
}

type GenResp struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
	UrlLink string `json:"url_link"`
}

func (s miniService) GenerateUrlLink(code string) (url string, err error) {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return "", err
	}

	urlStr := fmt.Sprintf("https://api.weixin.qq.com/wxa/generate_urllink?access_token=%s", accessToken)

	unix := time.Now().Add(time.Hour * 20).Unix()

	b := map[string]interface{}{
		"expire_type": 0,
		"expire_time": unix,
		"env_version": "release",
		//"env_version": unix,
		//"env_version": "develop",
	}

	var res GenResp

	marshal, _ := json.Marshal(b)
	zap.S().Infof("generateUrlLink：%s", string(marshal))

	resp, err := util.NewResty().Post(urlStr, b, &res)
	if err != nil {
		return "", err
	}
	_ = resp
	bytes, _ := json.Marshal(resp)
	zap.S().Infof("resp %s", string(bytes))
	if res.Errcode != 0 {
		zap.S().Errorf("res.Errmsg , %s", res.Errmsg)
		return "", xerr.NewErr(xerr.ErrParamError, nil, res.Errmsg)
	}
	return res.UrlLink, nil
}

func (s miniService) GetMobile(code string) (string, error) {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return "", err
	}
	// post
	u := fmt.Sprintf("https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s", accessToken)

	bytes, err := util.DoHttp(u, http.MethodPost, nil, map[string]interface{}{"code": code})
	if err != nil {
		return "", err
	}

	var mobile mobileInfo
	err = json.Unmarshal(bytes, &mobile)
	if err != nil {
		return "", err
	}

	if mobile.ErrCode == -1 {
		return "", xerr.NewErr(xerr.ErrSysBusy, nil)
	}
	if mobile.ErrCode == 40029 {
		return "", xerr.NewErr(xerr.ErrParamError, nil, "code失效，请重试")
	}
	return mobile.PhoneInfo.PurePhoneNumber, nil
}

func (s miniService) GetAppID(ctx context.Context) (string, error) {

	return s.AppID, nil
}

type AccessTokenInfo struct {
	AccessToken string `json:"access_token"` // 凭证
	ExpiresIn   int    `json:"expires_in"`   // 凭证有效时间，单位：秒。目前是7200秒之内的值
}

type mobileInfo struct {
	ErrCode   int    `json:"errcode"`
	ErrMsg    string `json:"errmsg"`
	PhoneInfo struct {
		PhoneNumber     string `json:"phoneNumber"`
		PurePhoneNumber string `json:"purePhoneNumber"`
		CountryCode     string `json:"countryCode"`
	} `json:"phone_info"`
}

// ShipUploadInfo 上传发货信息
func (s miniService) ShipUploadInfo(openID, transactionID string, isDebt bool) error {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return err
	}

	parse, err := url.Parse("https://api.weixin.qq.com/wxa/sec/order/upload_shipping_info")
	if err != nil {
		return err
	}

	values := url.Values{}
	values.Add("access_token", accessToken)

	parse.RawQuery = values.Encode()

	formatTime := time.Now().Format(time.RFC3339)

	desc := "水果商品"
	logisticsType := 2

	if isDebt {
		desc = "补差订单支付成功通知，请忽略"
		logisticsType = 3
	}

	b := map[string]interface{}{
		"order_key": map[string]interface{}{
			"order_number_type": 2,             // 订单单号类型，用于确认需要上传详情的订单。枚举值1，使用下单商户号和商户侧单号；枚举值2，使用微信支付单号。
			"transaction_id":    transactionID, // 订单单号类型，用于确认需要上传详情的订单。枚举值1，使用下单商户号和商户侧单号；枚举值2，使用微信支付单号。
		},
		"logistics_type":   logisticsType,      // 1、实体物流配送采用快递公司进行实体物流配送形式 2、同城配送 3、虚拟商品，虚拟商品，例如话费充值，点卡等，无实体配送形式 4、用户自提
		"delivery_mode":    "UNIFIED_DELIVERY", //
		"is_all_delivered": true,
		"shipping_list": []map[string]interface{}{
			{"item_desc": desc},
		},
		"upload_time": formatTime,
		"payer": map[string]string{
			"openid": openID,
		},
	}

	var res shipRes
	resp, err := util.NewResty().Post(parse.String(), b, &res)
	if err != nil {
		return err
	}

	if res.ErrCode != 0 {
		zap.S().Errorf("发货信息上传异常%s", res.ErrMsg)
		return xerr.NewErr(xerr.ErrParamError, nil, res.ErrMsg)
	}
	_ = resp

	return nil

}

type shipRes struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

type shipInfoRes struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	Order   struct {
		TransactionID string `json:"transaction_id"`
		MerchantId    string `json:"merchant_id"`
		Description   string `json:"description"`
		OrderState    int    `json:"order_state"`  // 订单状态枚举：(1) 待发货；(2) 已发货；(3) 确认收货；(4) 交易完成；(5) 已退款。
		InComplaint   bool   `json:"in_complaint"` // 是否处在交易纠纷中。
	}
}

// ShipStatus 查询发货状态
func (s miniService) ShipStatus(transactionID string) (int, error) {
	accessToken, err := s.GetAccessToken()
	if err != nil {
		return 0, err
	}

	parse, err := url.Parse("https://api.weixin.qq.com/wxa/sec/order/get_order")
	if err != nil {
		return 0, err
	}

	values := url.Values{}
	values.Add("access_token", accessToken)

	parse.RawQuery = values.Encode()

	b := map[string]interface{}{
		"transaction_id": transactionID,
	}

	var res shipInfoRes

	resp, err := util.NewResty().Post(parse.String(), b, &res)
	if err != nil {
		return 0, err
	}
	if res.ErrCode != 0 {
		return 0, xerr.NewErr(xerr.ErrParamError, nil, res.ErrMsg)
	}
	_ = resp

	return res.Order.OrderState, nil

}

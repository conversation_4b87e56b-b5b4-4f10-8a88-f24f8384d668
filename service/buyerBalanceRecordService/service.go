package buyerBalanceRecordService

import (
	"base/dao"
	"base/dao/buyerBalanceRecordDao"
	"base/global"
	"base/model"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"time"
)

type ServiceInterface interface {
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.BuyerBalanceRecord, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.BuyerBalanceRecord, error)
	CreateRecord(ctx context.Context, buyerBalanceRecordType model.BuyerBalanceRecordType, buyerID, objectID primitive.ObjectID, amount int) error
}

type buyerBalanceRecordService struct {
	mdb                   *mongo.Database
	rdb                   *redis.Client
	buyerBalanceRecordDao buyerBalanceRecordDao.DaoInt
}

func NewBuyerBalanceRecordService() ServiceInterface {
	return buyerBalanceRecordService{
		mdb:                   global.MDB,
		rdb:                   global.RDBDefault,
		buyerBalanceRecordDao: dao.BuyerBalanceRecordDao,
	}
}

func (s buyerBalanceRecordService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.BuyerBalanceRecord, int64, error) {
	list, i, err := s.buyerBalanceRecordDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}

func (s buyerBalanceRecordService) List(ctx context.Context, filter bson.M) ([]model.BuyerBalanceRecord, error) {
	list, err := s.buyerBalanceRecordDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

// CreateRecord 记录
func (s buyerBalanceRecordService) CreateRecord(ctx context.Context, buyerBalanceRecordType model.BuyerBalanceRecordType, buyerID, objectID primitive.ObjectID, amount int) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("buyerBalanceRecordService CreateRecord error:%v", err)
			return
		}
	}()

	now := time.Now().UnixMilli()

	item := model.BuyerBalanceRecord{
		ID:                     primitive.NewObjectID(),
		BuyerID:                buyerID,
		ObjectID:               objectID,
		Amount:                 amount,
		BuyerBalanceRecordType: buyerBalanceRecordType,
		Note:                   "",
		CreatedAt:              now,
		UpdatedAt:              now,
	}

	err := s.buyerBalanceRecordDao.Create(ctx, item)
	if err != nil {
		return err
	}
	return nil
}

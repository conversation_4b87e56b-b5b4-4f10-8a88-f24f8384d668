package orderDebtService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/orderDebtDao"
	"base/global"
	"base/mnsSendService"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"base/service/buyerService"
	"base/service/couponStockService"
	"base/service/miniService"
	"base/service/orderService"
	"base/service/parentOrderService"
	"base/service/payAccountService"
	"base/service/payOrderService"
	"base/service/productCommissionService"
	"base/service/productImageService"
	"base/service/productService"
	"base/service/routeService"
	"base/service/servicePointService"
	"base/service/supplierService"
	"base/service/userAddrService"
	"base/service/warehouseService"
	"base/service/yeeMerchantService"
	"base/util"
	"context"
	"encoding/json"
	"errors"
	"sync"
	"time"

	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	"github.com/cnbattle/allinpay"
	"github.com/go-redis/redis/v8"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/yop-platform/yop-go-sdk/yop/request"
	"github.com/yop-platform/yop-go-sdk/yop/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

var refundLock sync.Mutex

// ServiceInterface 补差订单->重量多出--托管代收
type ServiceInterface interface {
	Create(ctx context.Context, order model.Order, pSettleList []model.ProductSettle) (model.OrderDebt, error)
	List(ctx context.Context, filter bson.M) ([]model.OrderDebt, error)
	ListWithOption(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]model.OrderDebt, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderDebt, int64, error)
	ListByBuyerID(ctx context.Context, buyerID primitive.ObjectID) ([]model.OrderDebt, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.OrderDebt, error)
	CheckByBuyer(ctx context.Context, buyerID primitive.ObjectID) (bool, error)
	ExistByBuyer(ctx context.Context, buyerID primitive.ObjectID) (bool, error)
	CountNotPaidByBuyer(ctx context.Context, buyerID primitive.ObjectID) (int64, error)
	GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.OrderDebt, error)
	GetByOutTradeNo(ctx context.Context, outTradeNo string) (model.OrderDebt, error)

	UpdatePayStatus(ctx context.Context, res allinpay.NotifyPay) error
	UpdateOne(ctx context.Context, filter, update bson.M) error

	ToPayJSAPI(ctx context.Context, debtID primitive.ObjectID, openID string) (model.WXPayResult, error)

	YeeAggTutelagePrePay(ctx context.Context, debtID primitive.ObjectID, openID, ip string) (model.YeeWechatResult, error)
	YeeAccountBookPay(ctx context.Context, debtID primitive.ObjectID) error

	RemoveDebtPayInfo(ctx context.Context, debtOrderID string) error

	DoShipSettleRefund(ctx context.Context, ids []primitive.ObjectID) error

	DoDebtPaidRefundYee(ctx context.Context, id primitive.ObjectID) error

	ShipUploadInfo(ctx context.Context, orderIDList []primitive.ObjectID) error

	FreeDebt(ctx context.Context, id primitive.ObjectID) error

	//	NotifyPayStatus 异步通知
	NotifyPayStatus(ctx context.Context, res allinpay.NotifyPay) error

	NotifyPay(ctx context.Context, content *payments.Transaction) error

	YeeNotifyTradeOrderPay(ctx context.Context, notify model.YeeTradeOrderPaySingleNotify) error
	YeeNotifyAccountBookPay(ctx context.Context, notify model.YeeTradeOrderPaySingleNotify) error

	YeeNotifyRefundQuality(ctx context.Context, notify model.YeeTradeRefundNotify) error

	YeeNotifyRefundDebtPaid(ctx context.Context, notify model.YeeTradeRefundNotify) error
}

type orderDebtService struct {
	mdb *mongo.Database
	rdb *redis.Client
	l   *zap.SugaredLogger

	yeePay       *global.YeePayInfo
	yeeMerchantS yeeMerchantService.ServiceInterface

	orderS   orderService.ServiceInterface
	productS productService.ServiceInterface

	addrS userAddrService.ServiceInterface

	routeFeeS     routeService.ServiceInterface
	servicePointS servicePointService.ServiceInterface
	warehouseS    warehouseService.ServiceInterface
	//servicePointCommissionS servicePointCommissionService.ServiceInterface
	productImageS      productImageService.ServiceInterface
	productCommissionS productCommissionService.ServiceInterface
	//供应商
	supplierS supplierService.ServiceInterface
	// 地址
	userAddrS userAddrService.ServiceInterface

	//	优惠
	couponStockS couponStockService.ServiceInterface

	authenticationS authenticationService.ServiceInterface

	mini miniService.ServiceInterface

	// 平台营销账户余额
	payAccountS payAccountService.ServiceInterface

	allInPayOrderS payModule.OrderService

	parentOrderS parentOrderService.ServiceInterface // 父单

	orderDebtDao orderDebtDao.DaoInt // 补差订单

	payOrderS payOrderService.ServiceInterface
}

// NewOrderDebtService 创建补差订单服务
func NewOrderDebtService() ServiceInterface {
	return orderDebtService{
		mdb: global.MDB,
		rdb: global.RDBDefault,
		l:   global.OrderLogger.Sugar(),

		yeePay:       global.YeePay,
		yeeMerchantS: yeeMerchantService.NewYeeMerchantService(),

		orderS:        orderService.NewOrderService(),
		productS:      productService.NewProductService(),
		addrS:         userAddrService.NewUserAddrService(),
		routeFeeS:     routeService.NewTransportFeeService(),
		servicePointS: servicePointService.NewServicePointService(),
		warehouseS:    warehouseService.NewWarehouseServiceService(),
		//servicePointCommissionS: servicePointCommissionService.NewPartnerCommissionService(),
		productImageS:      productImageService.NewProductImageService(),
		productCommissionS: productCommissionService.NewProductCommissionService(),
		supplierS:          supplierService.NewSupplierService(),
		userAddrS:          userAddrService.NewUserAddrService(),
		couponStockS:       couponStockService.NewService(),

		authenticationS: authenticationService.NewAuthenticationService(),

		mini: miniService.NewMiniService(),

		payAccountS: payAccountService.NewPayAccountService(), // 父单

		allInPayOrderS: payModule.NewOrderS(),
		orderDebtDao:   dao.OrderDebtDao,
		parentOrderS:   parentOrderService.NewParentOrderService(),
		payOrderS:      payOrderService.NewPayOrderService(),
	}
}

// Create 结算
func (s orderDebtService) Create(ctx context.Context, order model.Order, pSettleList []model.ProductSettle) (model.OrderDebt, error) {
	byOrderID, err := s.GetByOrderID(ctx, order.ID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.OrderDebt{}, err
	}

	if byOrderID.ID == primitive.NilObjectID {
		err = nil
	}

	_ = byOrderID

	if byOrderID.ID != primitive.NilObjectID {
		s.l.Warnf("已经对订单%s进行结算处理", order.ID.Hex())
		return model.OrderDebt{}, nil
	}

	now := time.Now().UnixMilli()

	debt := model.OrderDebt{
		ID:                primitive.NewObjectID(),
		OrderID:           order.ID,
		ParentOrderID:     order.ParentOrderID,
		BuyerID:           order.BuyerID,
		BuyerName:         order.BuyerName,
		PayStatus:         model.PayStatusTypeToPay,
		SupplierID:        order.SupplierID,
		SupplierName:      order.SupplierName,
		ServicePointID:    order.ServicePointID,
		ServicePointName:  order.ServicePointName,
		SettleProductList: pSettleList,
		ExpireAt:          0,
		CreatedAt:         now,
		UpdatedAt:         now,
	}

	for _, p := range pSettleList {
		if p.SettleResultType == model.SettleResultTypeDebt {
			// 商品补差金额
			debt.TotalProductAmount += p.DiffProductAmount
			debt.PaidProductAmount += p.DiffProductAmount
		}

		if p.SettleResultType == model.SettleResultTypeRefund {
			// 退款
			debt.RefundTotalProductAmount += p.DiffProductAmount
			// debt.RefundUnitTransportFee = p.UnitTransportFee
			// debt.RefundTotalTransportFee += p.TotalTransportFee
			debt.RefundTotalServiceFee += p.TotalServiceFee
		}
	}

	if debt.RefundTotalProductAmount > 0 {
		// 抵消-退款
		maxOffset := debt.RefundTotalProductAmount + debt.RefundTotalServiceFee
		//+ debt.RefundTotalServiceFee

		debt.OffsetProductAmount = min(debt.TotalProductAmount, maxOffset)
		debt.PaidProductAmount = debt.TotalProductAmount - debt.OffsetProductAmount
		if debt.PaidProductAmount == 0 {
			debt.PayStatus = model.PayStatusTypePaid
		}

		debt.RefundFinalAmount = debt.RefundTotalProductAmount + debt.RefundTotalServiceFee - debt.OffsetProductAmount
	}

	if debt.PaidProductAmount == 0 {
		debt.PayStatus = model.PayStatusTypePaid
	}

	if debt.RefundFinalAmount > 0 {
		yeeRefundResult := s.checkRefundInfo(ctx, debt)
		debt.RefundYeeRefundResult = yeeRefundResult
	}

	err = s.orderDebtDao.Create(ctx, debt)
	if err != nil {
		return model.OrderDebt{}, err
	}
	return debt, nil
}

func (s orderDebtService) checkRefundInfo(ctx context.Context, debt model.OrderDebt) model.YeeRefundResult {
	parentOrder, err := s.parentOrderS.Get(ctx, debt.ParentOrderID)
	if err != nil {
		return model.YeeRefundResult{}
	}

	var yeeRefund model.YeeRefundResult
	if parentOrder.PayMethod == model.PayMethodTypeYeeWechat {
		merchant, err := s.yeeMerchantS.GetBySupplier(ctx, debt.SupplierID)
		if err != nil {
			return model.YeeRefundResult{}
		}

		var merchantNo string
		var oriOrderID string
		for _, sub := range parentOrder.YeeWechatResult.NotifySubOrderList {
			if sub.MerchantNo == merchant.MerchantNo {
				merchantNo = sub.MerchantNo
				oriOrderID = sub.OrderId
				break
			}
		}

		yeeRefund.ParentMerchantNo = parentOrder.YeeWechatResult.ParentMerchantNo
		yeeRefund.MerchantNo = merchantNo
		yeeRefund.OrderId = oriOrderID
	}

	if parentOrder.PayMethod == model.PayMethodTypeYeeBalance {
		yeeRefund.ParentMerchantNo = parentOrder.YeeWechatResult.ParentMerchantNo
		yeeRefund.MerchantNo = parentOrder.YeeWechatResult.ParentMerchantNo
		yeeRefund.OrderId = parentOrder.YeeWechatResult.OrderID
	}

	return yeeRefund
}

func (s orderDebtService) DoShipSettleRefund(ctx context.Context, ids []primitive.ObjectID) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("DoAfterShipRefund error:%v", err)
			return
		}
	}()

	refundLock.Lock()
	defer refundLock.Unlock()

	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
		"refund_yee_refund_result.status": "", // 未执行过退款的
	}

	debtList, err := s.orderDebtDao.List(ctx, filter)
	if err != nil {
		return err
	}
	marshal, _ := json.Marshal(debtList)
	s.l.Infof("执行发货结算单退款：：：%v", string(marshal))

	//var processList []model.OrderRefund
	for _, d := range debtList {
		if d.RefundYeeRefundResult.Status != "" {
			s.l.Infof("退款单%s,已处理，跳过", d.ID.Hex())
			continue
		}

		zap.S().Infof("执行退款：%s", d.ID.Hex())

		err = s.DoRefundYee(ctx, d)
		if err != nil {
			s.l.Errorf("DoShipSettleRefund 执行退款错误%v，结算单ID%s，订单ID%s", err.Error(), d.ID.Hex(), d.OrderID.Hex())
			return err
		}
	}

	//if len(processList) > 0 {
	//	s.l.Infof("存在处理中订单：%v", processList)
	//	var refundIDs []primitive.ObjectID
	//	for _, r := range processList {
	//		refundIDs = append(refundIDs, r.ID)
	//	}
	//	mnsSendService.NewMNSClient().SendDoAfterShipRefundNext(refundIDs)
	//}

	s.l.Infof("DoAfterShipRefund success")
	return nil
}

// DoRefundYee 退款-易宝
func (s orderDebtService) DoRefundYee(ctx context.Context, order model.OrderDebt) error {
	milli := time.Now().UnixMilli()
	refundAmount := order.RefundFinalAmount

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/trade/refund")

	reqRefundRequestId := util.NewUUIDNum()
	yopRequest.AddParam("refundRequestId", reqRefundRequestId) // 商户退款请求号

	parentMerchantNo := order.RefundYeeRefundResult.ParentMerchantNo
	yopRequest.AddParam("parentMerchantNo", parentMerchantNo)

	merchantNo := order.RefundYeeRefundResult.MerchantNo
	oriOrderID := order.RefundYeeRefundResult.OrderId

	if order.PayMethod == model.PayMethodTypeYeeBalance {
		merchantNo = parentMerchantNo
		oriOrderID = order.RefundYeeRefundResult.OrderId
	}

	yopRequest.AddParam("orderId", oriOrderID) // 收款交易对应的商户收款请求号
	yopRequest.AddParam("merchantNo", merchantNo)

	paidAmount := refundAmount
	paidAmountStr := util.DealMoneyToYuanStr(paidAmount)
	yopRequest.AddParam("refundAmount", paidAmountStr) // 订单金额。单位为元，精确到小数点后两位

	description := "品控结算退款"
	notifyUrl := global.NotifyUrlYeePayTradeRefundQuality

	yopRequest.AddParam("description", description) // 退款订单说明

	yopRequest.AddParam("notifyUrl", global.BackHost+notifyUrl) // 接收支付结果的通知地址

	bytes, err := json.Marshal(yopRequest.Params)
	zap.S().Infof("请求：%s", string(bytes))

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}
	_ = yopResp

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	zap.S().Infof("退款信息：%s", string(marshal))

	var r model.YeeRefundRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.Code != "OPR00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.Message)
	}

	res := model.YeeRefundResult{
		OrderId:          oriOrderID,
		RefundRequestId:  reqRefundRequestId,
		ParentMerchantNo: parentMerchantNo,
		MerchantNo:       merchantNo,
		RefundAmount:     refundAmount,
		Status:           "PROCESSING",
	}

	err = s.orderDebtDao.UpdateOne(ctx, bson.M{"_id": order.ID}, bson.M{
		"$set": bson.M{
			"refund_yee_refund_result": res,
			"updated_at":               milli,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

// DoDebtPaidRefundYee 已支付补差退款-易宝
func (s orderDebtService) DoDebtPaidRefundYee(ctx context.Context, id primitive.ObjectID) error {
	debt, err := s.Get(ctx, id)
	if err != nil {
		return err
	}

	milli := time.Now().UnixMilli()
	// 补差支付金额
	refundAmount := debt.PaidProductAmount

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/trade/refund")

	reqRefundRequestId := util.NewUUIDNum()
	yopRequest.AddParam("refundRequestId", reqRefundRequestId) // 商户退款请求号

	parentMerchantNo := debt.YeeWechatResult.ParentMerchantNo
	yopRequest.AddParam("parentMerchantNo", parentMerchantNo)

	merchantNo := debt.YeeWechatResult.MerchantNo
	oriOrderID := debt.YeeWechatResult.OrderID

	//if order.PayMethod == model.PayMethodTypeYeeBalance {
	//	merchantNo = parentMerchantNo
	//	oriOrderID = order.RefundYeeRefundResult.OrderId
	//}

	yopRequest.AddParam("orderId", oriOrderID) // 收款交易对应的商户收款请求号
	yopRequest.AddParam("merchantNo", merchantNo)

	paidAmountStr := util.DealMoneyToYuanStr(refundAmount)
	yopRequest.AddParam("refundAmount", paidAmountStr) // 订单金额。单位为元，精确到小数点后两位

	notifyUrl := global.NotifyUrlYeePayDebtPaidRefund

	description := "补差已支付退款"
	yopRequest.AddParam("description", description) // 退款订单说明

	yopRequest.AddParam("notifyUrl", global.BackHost+notifyUrl) // 接收支付结果的通知地址

	bytes, err := json.Marshal(yopRequest.Params)
	zap.S().Infof("请求：%s", string(bytes))

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return err
	}
	_ = yopResp

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	zap.S().Infof("退款信息：%s", string(marshal))

	var r model.YeeRefundRes

	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.Code != "OPR00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.Message)
	}

	res := model.YeeRefundResult{
		OrderId:          oriOrderID,
		RefundRequestId:  reqRefundRequestId,
		ParentMerchantNo: parentMerchantNo,
		MerchantNo:       merchantNo,
		RefundAmount:     refundAmount,
		Status:           "PROCESSING",
	}

	err = s.orderDebtDao.UpdateOne(ctx, bson.M{"_id": id}, bson.M{
		"$set": bson.M{
			"paid_refund_yee_result": res,
			"updated_at":             milli,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

func (s orderDebtService) YeeNotifyRefundQuality(ctx context.Context, notify model.YeeTradeRefundNotify) error {
	filter := bson.M{
		"refund_yee_refund_result.refund_request_id": notify.RefundRequestId,
	}
	debt, err := s.orderDebtDao.Get(ctx, filter)
	if err != nil {
		return err
	}

	if debt.RefundYeeRefundResult.Status == "SUCCESS" {
		return nil
	}

	update := bson.M{
		"refund_yee_refund_result.notify_payment_method":      notify.PaymentMethod,
		"refund_yee_refund_result.notify_refund_success_date": notify.RefundSuccessDate,
		"refund_yee_refund_result.status":                     notify.Status,
		"refund_yee_refund_result.notify_error_message":       notify.ErrorMessage,
		"refund_yee_refund_result.notify_unique_refund_no":    notify.UniqueOrderNo,
	}

	if notify.Status == "SUCCESS" {
		milli := time.Now().UnixMilli()
		update["updated_at"] = milli
	}

	err = s.orderDebtDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})

	if err != nil {
		return err
	}

	if debt.PayMethod == model.PayMethodTypeYeeBalance {
		amount := debt.RefundFinalAmount
		mnsSendService.NewMNSClient().SendCreateBalanceRecord(model.BuyerBalanceRecordTypeOrderQuality, debt.BuyerID, debt.ID, amount)
	}

	mnsSendService.NewMNSClient().SendSyncQualityRefundData(debt.OrderID.Hex())

	return nil
}

func (s orderDebtService) YeeNotifyRefundDebtPaid(ctx context.Context, notify model.YeeTradeRefundNotify) error {
	filter := bson.M{
		"paid_refund_yee_result.refund_request_id": notify.RefundRequestId,
	}
	debt, err := s.orderDebtDao.Get(ctx, filter)
	if err != nil {
		return err
	}

	if debt.RefundYeeRefundResult.Status == "SUCCESS" {
		return nil
	}

	update := bson.M{
		"paid_refund_yee_result.notify_payment_method":      notify.PaymentMethod,
		"paid_refund_yee_result.notify_refund_success_date": notify.RefundSuccessDate,
		"paid_refund_yee_result.status":                     notify.Status,
		"paid_refund_yee_result.notify_error_message":       notify.ErrorMessage,
		"paid_refund_yee_result.notify_unique_refund_no":    notify.UniqueOrderNo,
	}

	if notify.Status == "SUCCESS" {
		milli := time.Now().UnixMilli()
		update["updated_at"] = milli
	}

	err = s.orderDebtDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})

	if err != nil {
		return err
	}

	//if debt.PayMethod == model.PayMethodTypeYeeBalance {
	//	amount := debt.RefundFinalAmount
	//	queueProduceService.NewQueueProduceService().SendCreateBalanceRecord(ctx, model.BuyerBalanceRecordTypeOrderQuality, debt.BuyerID, debt.ID, amount)
	//}

	//mnsSendService.NewMNSClient().SendSyncQualityRefundData(debt.OrderID.Hex())

	return nil
}

func (s orderDebtService) GetByBizOrderNo(ctx context.Context, bizOrderNo string) (model.OrderDebt, error) {
	filter := bson.M{
		"biz_order_no": bizOrderNo,
	}
	data, err := s.orderDebtDao.Get(ctx, filter)
	if err != nil {
		return model.OrderDebt{}, err
	}
	return data, nil
}

func (s orderDebtService) GetByOutTradeNo(ctx context.Context, outTradeNo string) (model.OrderDebt, error) {
	filter := bson.M{
		"wx_pay_result.out_trade_no": outTradeNo,
	}
	payOrder, err := s.orderDebtDao.Get(ctx, filter)
	if err != nil {
		return model.OrderDebt{}, err
	}
	return payOrder, nil
}

// NotifyPayStatus 回调
func (s orderDebtService) NotifyPayStatus(ctx context.Context, res allinpay.NotifyPay) error {
	// 只有成功才通知
	if res.Status == "OK" {
		//“OK”标识支付成功；
		err := s.UpdatePayStatus(ctx, res)
		if err != nil {
			s.l.Error("debt 更新支付成功状态错误：", err)
			return err
		}
	}

	s.l.Infof("NotifyPayStatus %v", res)

	return nil
}

func (s orderDebtService) NotifyPay(ctx context.Context, content *payments.Transaction) error {
	debt, err := s.GetByOutTradeNo(ctx, *content.OutTradeNo)
	if err != nil {
		return err
	}

	if debt.PayStatus == model.PayStatusTypePaid {
		return nil
	}

	update := bson.M{
		"wx_pay_result.transaction_id":   content.TransactionId,
		"wx_pay_result.trade_state":      content.TradeState,
		"wx_pay_result.trade_state_desc": content.TradeStateDesc,
		"wx_pay_result.bank_type":        content.BankType,
		"wx_pay_result.success_time":     content.SuccessTime,
		"wx_pay_result.payer_amount":     content.Amount.PayerTotal,
		"wx_pay_result.total_amount":     content.Amount.Total,
		"wx_pay_result.source":           content,
		"pay_status":                     model.PayStatusTypePaid,
	}

	//if *content.TradeState == "SUCCESS" {
	//	update["pay_status"] =
	//}

	zap.S().Infof("补差微信支付回调：%s,%v", debt.ID.Hex(), update)

	filter := bson.M{
		"wx_pay_result.out_trade_no": *content.OutTradeNo,
	}
	err = s.orderDebtDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	err = s.orderS.UpdateOne(ctx, bson.M{"_id": debt.OrderID}, bson.M{"$set": bson.M{
		//"has_debt_order":      true,
		"has_debt_order_paid": true,
	}})
	if err != nil {
		return err
	}

	// ids := []primitive.ObjectID{debt.OrderID}

	// UploadShipInfo(ctx, ids, true)

	return nil
}

// YeeNotifyTradeOrderPay 交易下单-回调
func (s orderDebtService) YeeNotifyTradeOrderPay(ctx context.Context, notify model.YeeTradeOrderPaySingleNotify) error {
	filter := bson.M{
		"yee_wechat_result.order_id": notify.OrderId,
	}

	orderDebt, err := s.orderDebtDao.Get(ctx, filter)
	if err != nil {
		return err
	}

	if orderDebt.YeeWechatResult.NotifyStatus == "SUCCESS" {
		return nil
	}

	payAmount := util.DealMoneyToFenInt(notify.PayAmount)
	orderAmount := util.DealMoneyToFenInt(notify.OrderAmount)

	update := bson.M{
		"yee_wechat_result.notify_pay_way":                  notify.PayWay,
		"yee_wechat_result.notify_pay_success_date":         notify.PaySuccessDate,
		"yee_wechat_result.notify_channel_order_id":         notify.ChannelOrderId,
		"yee_wechat_result.notify_status":                   notify.Status,
		"yee_wechat_result.notify_channel_trx_id":           notify.ChannelTrxId,
		"yee_wechat_result.notify_pay_amount":               payAmount,
		"yee_wechat_result.notify_order_amount":             orderAmount,
		"yee_wechat_result.notify_payer_user_id":            notify.PayerInfoFormat.UserID,
		"yee_wechat_result.notify_payer_yp_account_book_no": notify.PayerInfoFormat.YpAccountBookNo,
		//"yee_wechat_result.notify_sub_order_list":           notify.SubOrderInfoListFormat,
	}

	if notify.Status == "SUCCESS" {
		update["pay_status"] = model.PayStatusTypePaid
	}

	err = s.orderDebtDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	if notify.Status == "SUCCESS" {
		// 分账
		mnsSendService.NewMNSClient().SendDebtOrderDivide(ctx, orderDebt.ID)
	}

	return nil
}

// YeeNotifyAccountBookPay 记账簿支付
func (s orderDebtService) YeeNotifyAccountBookPay(ctx context.Context, notify model.YeeTradeOrderPaySingleNotify) error {
	filter := bson.M{
		"yee_wechat_result.order_id": notify.OrderId,
	}

	orderDebt, err := s.orderDebtDao.Get(ctx, filter)
	if err != nil {
		return err
	}

	if orderDebt.YeeWechatResult.NotifyStatus == "SUCCESS" {
		return nil
	}

	payAmount := util.DealMoneyToFenInt(notify.PayAmount)
	orderAmount := util.DealMoneyToFenInt(notify.OrderAmount)

	update := bson.M{
		"yee_wechat_result.notify_pay_way":                  notify.PayWay,
		"yee_wechat_result.notify_pay_success_date":         notify.PaySuccessDate,
		"yee_wechat_result.notify_channel_order_id":         notify.ChannelOrderId,
		"yee_wechat_result.notify_status":                   notify.Status,
		"yee_wechat_result.notify_channel_trx_id":           notify.ChannelTrxId,
		"yee_wechat_result.notify_pay_amount":               payAmount,
		"yee_wechat_result.notify_order_amount":             orderAmount,
		"yee_wechat_result.notify_payer_user_id":            notify.PayerInfoFormat.UserID,
		"yee_wechat_result.notify_payer_yp_account_book_no": notify.PayerInfoFormat.YpAccountBookNo,
		//"yee_wechat_result.notify_sub_order_list":           notify.SubOrderInfoListFormat,
	}

	if notify.Status == "SUCCESS" {
		update["pay_status"] = model.PayStatusTypePaid
	}

	err = s.orderDebtDao.UpdateOne(ctx, filter, bson.M{
		"$set": update,
	})
	if err != nil {
		return err
	}

	if orderDebt.PayMethod == model.PayMethodTypeYeeBalance {
		amount := orderDebt.TotalProductAmount
		mnsSendService.NewMNSClient().SendCreateBalanceRecord(model.BuyerBalanceRecordTypeOrderDebtPay, orderDebt.BuyerID, orderDebt.ID, amount)
	}

	if notify.Status == "SUCCESS" {
		// 分账
		mnsSendService.NewMNSClient().SendDebtOrderDivide(ctx, orderDebt.ID)
	}

	return nil
}

func (s orderDebtService) UpdatePayStatus(ctx context.Context, res allinpay.NotifyPay) error {
	// 回调更新
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)

	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		debt, err := s.GetByBizOrderNo(sessCtx, res.BizOrderNo)
		if err != nil {
			return nil, err
		}
		filter := bson.M{"_id": debt.ID}
		err = s.orderDebtDao.UpdateOne(sessCtx, filter, bson.M{"$set": bson.M{
			"pay_status":                      model.PayStatusTypePaid,
			"pay_result.status":               res.Status,
			"pay_result.amount":               res.Amount,
			"pay_result.channel_paytime":      res.ChannelPaytime,
			"pay_result.cusid":                res.Cusid,
			"pay_result.biz_order_no":         res.BizOrderNo,
			"pay_result.channel_fee":          res.ChannelFee,
			"pay_result.pay_interfacetrxcode": res.PayInterfacetrxcode,
			"pay_result.pay_datetime":         res.PayDatetime,
			"pay_result.acct":                 res.Acct,
		}})
		if err != nil {
			s.l.Error("更新补差订单状态错误:", err)
			return nil, err
		}
		err = s.orderS.UpdateOne(ctx, bson.M{"_id": debt.OrderID}, bson.M{"$set": bson.M{
			"has_debt_order":      true,
			"has_debt_order_paid": true,
		}})
		if err != nil {
			return nil, err
		}

		// 通知分账
		mnsSendService.NewMNSClient().SendDebtAgentPay(debt.ID.Hex())

		buyer, err := buyerService.NewBuyerService().Get(debt.BuyerID)
		if err != nil {
			return nil, err
		}

		mnsSendService.NewMNSClient().SendGenerateIntegral(model.RecordTypeOrderDebtPay, buyer.ID, debt.ID, debt.TotalProductAmount/100)

		if debt.PayMethod == model.PayMethodTypeBalance {
			//mnsSendService.NewMNSClient().SendCreateRecord(model.BuyerBalanceRecordTypeOrderDebtPay, debt.BuyerID, debt.ID, res.Amount)
		}

		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (s orderDebtService) FreeDebt(ctx context.Context, id primitive.ObjectID) error {
	debt, err := s.Get(ctx, id)
	if err != nil {
		return err
	}
	filter := bson.M{"_id": id}
	err = s.orderDebtDao.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
		"pay_status":        model.PayStatusTypePaid,
		"pay_result.status": "free",
	}})
	if err != nil {
		s.l.Error("更新补差订单状态错误:", err)
		return err
	}
	err = s.orderS.UpdateOne(ctx, bson.M{"_id": debt.OrderID}, bson.M{"$set": bson.M{
		"has_debt_order":      true,
		"has_debt_order_paid": true,
	}})
	if err != nil {
		return err
	}

	return nil
}

func (s orderDebtService) List(ctx context.Context, filter bson.M) ([]model.OrderDebt, error) {
	list, err := s.orderDebtDao.List(ctx, filter)
	return list, err
}

func (s orderDebtService) ListWithOption(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]model.OrderDebt, error) {
	list, err := s.orderDebtDao.ListWithOption(ctx, filter, opts)
	return list, err
}

func (s orderDebtService) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderDebt, int64, error) {
	list, count, err := s.orderDebtDao.ListByPage(ctx, filter, page, limit)
	return list, count, err
}

func (s orderDebtService) ListByBuyerID(ctx context.Context, buyerID primitive.ObjectID) ([]model.OrderDebt, error) {
	filter := bson.M{
		"buyer_id": buyerID,
	}
	list, err := s.orderDebtDao.List(ctx, filter)
	return list, err
}

func (s orderDebtService) UpdateOne(ctx context.Context, filter, update bson.M) error {
	err := s.orderDebtDao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s orderDebtService) Get(ctx context.Context, id primitive.ObjectID) (model.OrderDebt, error) {
	order, err := s.orderDebtDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.OrderDebt{}, err
	}
	return order, nil
}

func (s orderDebtService) CheckByBuyer(ctx context.Context, buyerID primitive.ObjectID) (bool, error) {
	orders, err := s.orderDebtDao.List(ctx, bson.M{
		"buyer_id": buyerID,
		"pay_status": bson.M{
			"$ne": model.PayStatusTypePaid,
		},
		"deleted_at": 0,
	})
	if err != nil {
		return false, err
	}

	t := time.Now().Add(-time.Hour * 48).UnixMilli()

	var f bool
	for _, v := range orders {
		if v.CreatedAt < t {
			f = true
			break
		}
	}
	return f, nil
}

func (s orderDebtService) ExistByBuyer(ctx context.Context, buyerID primitive.ObjectID) (bool, error) {
	orders, err := s.orderDebtDao.List(ctx, bson.M{
		"buyer_id": buyerID,
		"pay_status": bson.M{
			"$ne": model.PayStatusTypePaid,
		},
		"deleted_at": 0,
	})
	if err != nil {
		return false, err
	}

	return len(orders) > 0, nil
}

func (s orderDebtService) CountNotPaidByBuyer(ctx context.Context, buyerID primitive.ObjectID) (int64, error) {
	count, err := s.orderDebtDao.Count(ctx, bson.M{
		"buyer_id": buyerID,
		"pay_status": bson.M{
			"$ne": model.PayStatusTypePaid,
		},
		"deleted_at": 0,
	})
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s orderDebtService) GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.OrderDebt, error) {
	order, err := s.orderDebtDao.Get(ctx, bson.M{"order_id": orderID})
	if err != nil {
		return model.OrderDebt{}, err
	}
	return order, nil
}

// ToPayJSAPI 微信支付
func (s orderDebtService) ToPayJSAPI(ctx context.Context, debtID primitive.ObjectID, openID string) (model.WXPayResult, error) {
	debt, err := s.Get(ctx, debtID)
	if err != nil {
		return model.WXPayResult{}, err
	}
	if debt.PayStatus == model.PayStatusTypePaid {
		s.l.Infof("该订单已支付%s", debt.ID.Hex())
		return model.WXPayResult{}, xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
	}

	var hasExpire bool
	if debt.ExpireAt != 0 && debt.ExpireAt < time.Now().UnixMilli() {
		hasExpire = true
	}

	if !hasExpire && debt.WXPayResult.PayOpenID != "" && debt.WXPayResult.PaySign == openID {
		// 非过期
		return debt.WXPayResult, nil
	}

	svc := global.PayJSAPI

	amount := debt.TotalProductAmount

	expireAt := util.ExpirePayOrderTimeAT(global.OrderExpireMinute)

	tradeNo := util.NewUUIDNum()

	req := jsapi.PrepayRequest{
		Appid:       core.String("wx831b39de46824a82"),
		Mchid:       core.String("1694598262"),
		Description: core.String("商品"),
		OutTradeNo:  core.String(tradeNo),
		Attach:      core.String("订单"), // 选填
		NotifyUrl:   core.String(global.BackHost + global.NotifyUrlPayDebtOrder),
		Amount: &jsapi.Amount{
			Total: core.Int64(int64(amount)),
		},
		Payer: &jsapi.Payer{
			Openid: core.String(openID),
		},
	}
	resp, result, err := svc.PrepayWithRequestPayment(ctx, req)
	_ = result
	if err != nil {
		return model.WXPayResult{}, err
	}

	res := model.WXPayResult{
		OutTradeNo:     tradeNo,
		PayOpenID:      openID,
		PayFailMessage: "",
		PrepayId:       *resp.PrepayId,
		TotalAmount:    0,
		PayerAmount:    0,
		Appid:          *resp.Appid,
		TimeStamp:      *resp.TimeStamp,
		NonceStr:       *resp.NonceStr,
		Package:        *resp.Package,
		SignType:       *resp.SignType,
		PaySign:        *resp.PaySign,
	}

	respMarshal, err := json.Marshal(resp)
	if err != nil {
		return model.WXPayResult{}, err
	}
	zap.S().Warnf("支付信息：%s", string(respMarshal))

	err = s.UpdateOne(ctx, bson.M{"_id": debt.ID}, bson.M{
		"$set": bson.M{
			"wx_pay_result": res,
			"expire_at":     expireAt,
			"pay_method":    model.PayMethodTypeWechat,
			"pay_status":    model.PayStatusTypePending,
			"updated_at":    time.Now().UnixMilli(),
		},
	})

	//mnsSendService.NewMNSClient().SendCheckDebtPay(debt.ID.Hex())
	//
	//mnsSendService.NewMNSClient().SendRemoveDebtPayInfo(debt.ID.Hex())

	return res, nil
}

// YeeTradeOrder 交易下单
func (s orderDebtService) YeeTradeOrder(ctx context.Context, debtID primitive.ObjectID) (model.YeeWechatResult, error) {
	debt, err := s.Get(ctx, debtID)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	if debt.PayStatus == model.PayStatusTypePaid {
		return model.YeeWechatResult{}, xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
	}
	now := time.Now()
	milli := now.UnixMilli()

	amount := debt.PaidProductAmount

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/trade/order")

	reqOrderID := util.NewUUIDNum()

	yopRequest.AddParam("orderId", reqOrderID) // 商户收款请求号。可包含字母、数字、下划线；需保证在商户端不重复。合单收款场景中，此参数为合单收款请求号

	yopRequest.AddParam("parentMerchantNo", "***********")

	getBySupplier, err := s.yeeMerchantS.GetBySupplier(ctx, debt.SupplierID)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	merchantNo := getBySupplier.MerchantNo

	yopRequest.AddParam("merchantNo", merchantNo)

	paidAmountStr := util.DealMoneyToYuanStr(amount)

	yopRequest.AddParam("orderAmount", paidAmountStr) // 订单金额。单位为元，精确到小数点后两位
	yopRequest.AddParam("goodsName", "补差订单")
	yopRequest.AddParam("fundProcessType", "DELAY_SETTLE")
	yopRequest.AddParam("memo", "补差订单ID:"+debtID.Hex())

	addDate := now.AddDate(0, 0, 25)
	expiredTime := addDate.Format("2006-01-02 15:04:05")

	yopRequest.AddParam("expiredTime", expiredTime) // yyyy-MM-dd HH:mm:ss

	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayTradeOrderDebt) // 接收支付结果的通知地址

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.YeeWechatResult{}, err
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	var tradeRes model.TradeOrderRes
	err = json.Unmarshal(marshal, &tradeRes)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	dataRes := model.YeeWechatResult{
		OrderCreatedAt:   milli,
		OrderID:          tradeRes.OrderId,
		UniqueOrderNo:    tradeRes.UniqueOrderNo,
		Token:            tradeRes.Token,
		ParentMerchantNo: tradeRes.ParentMerchantNo,
		MerchantNo:       tradeRes.MerchantNo,
	}

	err = s.orderDebtDao.UpdateOne(ctx, bson.M{"_id": debtID}, bson.M{
		"$set": bson.M{
			"yee_wechat_result": tradeRes,
		},
	})
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	return dataRes, nil
}

// YeeAggTutelagePrePay 聚合托管下单
func (s orderDebtService) YeeAggTutelagePrePay(ctx context.Context, debtID primitive.ObjectID, openID, ip string) (model.YeeWechatResult, error) {
	debt, err := s.Get(ctx, debtID)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	if debt.YeeWechatResult.AggOpenID == openID {
		return debt.YeeWechatResult, nil
	}

	now := time.Now().UnixMilli()

	tradeOrderResult := debt.YeeWechatResult
	if debt.YeeWechatResult.OrderID == "" {
		tradeOrderResult, err = s.YeeTradeOrder(ctx, debtID)
		if err != nil {
			return model.YeeWechatResult{}, err
		}
	}

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/aggpay/tutelage/pre-pay")

	yopRequest.AddParam("payWay", "MINI_PROGRAM") // 小程序支付
	yopRequest.AddParam("channel", "WECHAT")
	yopRequest.AddParam("userIp", ip)
	yopRequest.AddParam("scene", "OFFLINE")

	yopRequest.AddParam("orderId", tradeOrderResult.OrderID)

	amount := debt.PaidProductAmount

	toYuan := util.DealMoneyToYuan(amount)

	// 商户收款请求号
	yopRequest.AddParam("orderAmount", toYuan) // 商户收款请求号
	yopRequest.AddParam("goodsName", "水果商品订单")

	yopRequest.AddParam("fundProcessType", "DELAY_SETTLE")
	yopRequest.AddParam("notifyUrl", global.BackHost+global.NotifyUrlYeePayAggTutelagePrePayDebt) // 接收支付结果的通知地址
	yopRequest.AddParam("limitCredit", "N")                                                       // N:借贷记卡均可支付
	yopRequest.AddParam("token", tradeOrderResult.Token)                                          // 下单接口返回的token
	yopRequest.AddParam("userId", openID)                                                         // 微信小程序为用户的openId

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return model.YeeWechatResult{}, nil
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	m := make(map[string]string)
	err = json.Unmarshal(marshal, &m)
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	tradeRes := tradeOrderResult
	tradeRes.AggOrderId = m["orderId"]
	tradeRes.AggUniqueOrderNo = m["uniqueOrderNo"]
	tradeRes.PrePayTn = m["prePayTn"]
	tradeRes.AppId = m["appId"]
	tradeRes.MiniProgramPath = m["miniProgramPath"]
	tradeRes.MiniProgramOrgId = m["miniProgramOrgId"]
	tradeRes.AggOpenID = openID
	tradeRes.AggCreatedAt = now

	err = s.orderDebtDao.UpdateOne(ctx, bson.M{"_id": debtID}, bson.M{
		"$set": bson.M{
			"pay_method":        model.PayMethodTypeYeeWechat,
			"yee_wechat_result": tradeRes,
		},
	})
	if err != nil {
		return model.YeeWechatResult{}, err
	}

	return tradeRes, nil
}

// YeeAccountBookPay 记账薄支付
func (s orderDebtService) YeeAccountBookPay(ctx context.Context, debtID primitive.ObjectID) error {
	debt, err := s.Get(ctx, debtID)
	if err != nil {
		return err
	}

	if debt.PayStatus == model.PayStatusTypePaid {
		s.l.Infof("该订单已支付%s", debtID.Hex())
		return xerr.NewErr(xerr.ErrOrder, nil, "该订单已支付")
	}

	if debt.PayStatus == model.PayStatusTypePending {
		s.l.Infof("该订单支付中%s", debtID.Hex())
		return xerr.NewErr(xerr.ErrOrder, nil, "该订单支付中")
	}

	var yopRequest = request.NewYopRequest("POST", "/rest/v1.0/account/enterprise/account-book-pay/order")

	amount := debt.PaidProductAmount

	toYuan := util.DealMoneyToYuan(amount)

	merchant, err := s.yeeMerchantS.GetYeeByBuyer(ctx, debt.BuyerID)
	if err != nil {
		return err
	}

	uuidNum := util.NewUUIDNum()

	parentMerchantNo := "***********"

	var params = map[string]any{
		"parentMerchantNo": parentMerchantNo,
		"merchantNo":       parentMerchantNo,                // 商户编号收单主体商编
		"ypAccountBookNo":  merchant.AccountYpAccountBookNo, // 记账薄编号
		"orderInfo": map[string]any{
			"orderId":         uuidNum, // 商户请求号，需要保持在商户下唯一
			"orderAmount":     toYuan,
			"fundProcessType": "DELAY_SETTLE",
			"goodsName":       "补差订单",
			"businessInfo":    debtID.Hex(),
			"notifyUrl":       global.BackHost + global.NotifyUrlYeePayAccountBookPayDebt,
		}, // 订单信息
	}

	yopRequest.Content = utils.ParseToJsonStr(params)

	yopResp, err := s.yeePay.DoRequest(yopRequest)
	if nil != err {
		return nil
	}

	marshal, err := json.Marshal(yopResp.Result)
	if err != nil {
		return err
	}
	var r model.YeeAccountBookPayRes
	err = json.Unmarshal(marshal, &r)
	if err != nil {
		return err
	}

	if r.Code != "UA00000" {
		return xerr.NewErr(xerr.ErrParamError, nil, r.Message)
	}

	payStatus := model.PayStatusTypePending
	if r.OrderStatus == "SUCCESS" {
		payStatus = model.PayStatusTypePaid
	}

	err = s.orderDebtDao.UpdateOne(ctx, bson.M{"_id": debtID}, bson.M{
		"$set": bson.M{
			"pay_method":                           model.PayMethodTypeYeeBalance,
			"yee_wechat_result.order_id":           r.OrderId,
			"yee_wechat_result.unique_order_no":    r.UniqueOrderNo,
			"yee_wechat_result.parent_merchant_no": parentMerchantNo,
			"pay_status":                           payStatus,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

func (s orderDebtService) RemoveDebtPayInfo(ctx context.Context, debtOrderID string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("RemoveDebtPayInfo error:%v", err)
			return
		}
	}()

	//id, err := util.ConvertToObjectWithNote(debtOrderID, "RemoveDebtPayInfo debtOrderID")
	//if err != nil {
	//	return err
	//}
	//
	//debt, err := s.Get(ctx, id)
	//if err != nil {
	//	return err
	//}
	//now := time.Now().UnixMilli()
	//if debt.ExpireAt > now {
	//	// 未过期，不移除debt支付信息
	//	s.l.Infof("补差订单%s未过期跳过，bizOrderNo:%s", debtOrderID, debt.BizOrderNo)
	//	return nil
	//}
	//
	//res, err := s.payOrderS.QueryBizOrderStatus(ctx, debt.PayResult.OrderNo, debt.BizOrderNo)
	//if errors.Is(err, xerr.XerrPayNoOrder) {
	//	s.l.Warnf("查询不到debt pay info信息")
	//	return nil
	//}
	//
	//if model.PayStatusType(res.OrderStatus) == model.PayStatusTypePaid {
	//	s.l.Infof("已经支付，无需替换debt pay info")
	//	return nil
	//}
	//
	//// 执行移除
	//marshal, _ := json.Marshal(debt.PayResult)
	//s.l.Infof("待移除debt原始支付串信息%v", string(marshal))
	//update := bson.M{
	//	"pay_result":   model.PayResult{},
	//	"biz_order_no": "",
	//	"pay_status":   model.PayStatusTypeToPay,
	//	"expire_at":    0,
	//	"updated_at":   now,
	//}
	//err = s.orderDebtDao.UpdateOne(ctx, bson.M{"_id": debt.ID}, bson.M{"$set": update})
	//if err != nil {
	//	s.l.Errorf("执行移除debt支付串失败%v", err)
	//	return err
	//}

	return nil
}

// ShipUploadInfo 上传发货信息
func (s orderDebtService) ShipUploadInfo(ctx context.Context, orderIDList []primitive.ObjectID) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("ShipUploadInfo error:%v", err)
			return
		}
	}()

	if len(orderIDList) < 1 {
		return nil
	}

	debts, err := s.List(ctx, bson.M{
		"order_id": bson.M{
			"$in": orderIDList,
		},
	})
	if err != nil {
		return err
	}

	for _, p := range debts {
		status, err := s.mini.ShipStatus(p.WXPayResult.TransactionID)
		if err != nil {
			return err
		}
		if status == 1 {
			err = s.mini.ShipUploadInfo(p.WXPayResult.PayOpenID, p.WXPayResult.TransactionID, true)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

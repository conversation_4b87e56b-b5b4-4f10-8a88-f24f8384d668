package orderQualityService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/orderQualityDao"
	"base/global"
	"base/model"
	"base/service/authenticationService"
	"base/service/orderService"
	"base/service/productBuyPriceService"
	"base/service/productCommissionService"
	"base/service/productImageService"
	"base/service/productService"
	"base/service/routeService"
	"base/service/servicePointCommissionService"
	"base/service/servicePointService"
	"base/service/supplierService"
	"base/service/userAddrService"
	"base/service/warehouseService"
	"base/types"
	"base/util"
	"context"
	"time"

	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInterface 订单
type ServiceInterface interface {
	CreateQuality(ctx context.Context, data model.OrderQuality) error
	UpdateQuality(ctx context.Context, id primitive.ObjectID, qualityNum int, reasonType int, reasonImg model.FileInfo, amount int) error
	Get(ctx context.Context, id primitive.ObjectID) (model.OrderQuality, error)
	GetByProduct(ctx context.Context, productID primitive.ObjectID, stockUpNo int, stockUpDayAt int64) (model.OrderQuality, error)
	ListQuality(ctx context.Context, stockUpDayTime int64) ([]model.OrderQuality, error)
	ListNotQuality(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) ([]model.OrderQuality, error)
	CountNotQuality(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) (int64, error)

	ListQualityAll(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) ([]model.OrderQuality, error)
	ListQualityAllByPoint(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) ([]model.OrderQuality, error)
	ListQualityHas(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) ([]model.OrderQuality, error)
	SearchQuality(ctx context.Context, stockUpDayTime int64, servicePointID, stationID primitive.ObjectID, content, QualityID string, qualityStatus int) ([]model.OrderQuality, error)
	ListSort(ctx context.Context, stockUpDayTime int64, hasSort bool, servicePointID primitive.ObjectID) ([]model.OrderQuality, error)
	ListNotSort(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) ([]model.OrderQuality, error)
	CountNotSort(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) (int64, error)

	SearchSort(ctx context.Context, stockUpDayTime int64, content string, sortStatus int, servicePointID primitive.ObjectID) ([]model.OrderQuality, error)
	UpdateSort(ctx context.Context, req types.OrderSortReq) error
	UpdateSortPhoto(ctx context.Context, qualityID, orderID primitive.ObjectID, photoList []model.FileInfo) error
	List(ctx context.Context, filter bson.M) ([]model.OrderQuality, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	UpdateMany(ctx context.Context, filter, update bson.M) error
	UpdateOrderToStockUp(ctx context.Context, orderIDList []primitive.ObjectID, ts int64, servicePointID primitive.ObjectID) error
}

type orderQualityService struct {
	mdb                     *mongo.Database
	rdb                     *redis.Client
	l                       *zap.SugaredLogger
	orderQualityDao         orderQualityDao.DaoInt
	orderS                  orderService.ServiceInterface
	productS                productService.ServiceInterface
	routeFeeS               routeService.ServiceInterface
	servicePointS           servicePointService.ServiceInterface
	warehouseS              warehouseService.ServiceInterface
	servicePointCommissionS servicePointCommissionService.ServiceInterface
	productImageS           productImageService.ServiceInterface
	productCommissionS      productCommissionService.ServiceInterface
	//供应商
	supplierS supplierService.ServiceInterface
	// 地址
	userAddrS userAddrService.ServiceInterface

	authenticationS authenticationService.ServiceInterface
}

func NewOrderQualityService() ServiceInterface {
	return orderQualityService{
		mdb:                     global.MDB,
		rdb:                     global.RDBDefault,
		l:                       global.OrderLogger.Sugar(),
		orderQualityDao:         dao.OrderQualityDao,
		orderS:                  orderService.NewOrderService(),
		productS:                productService.NewProductService(),
		routeFeeS:               routeService.NewTransportFeeService(),
		servicePointS:           servicePointService.NewServicePointService(),
		warehouseS:              warehouseService.NewWarehouseServiceService(),
		servicePointCommissionS: servicePointCommissionService.NewPartnerCommissionService(),
		productImageS:           productImageService.NewProductImageService(),
		productCommissionS:      productCommissionService.NewProductCommissionService(),
		supplierS:               supplierService.NewSupplierService(),
		userAddrS:               userAddrService.NewUserAddrService(),
	}
}

func (s orderQualityService) ListQualityAll(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) ([]model.OrderQuality, error) {
	filter := bson.M{
		"stock_up_day_time": stockUpDayTime,
		"service_point_id":  servicePointID,
		"supplier_level":    model.SupplierLevelPoint,
	}

	list, err := s.orderQualityDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderQualityService) ListQualityAllByPoint(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) ([]model.OrderQuality, error) {
	filter := bson.M{
		"stock_up_day_time": stockUpDayTime,
		"service_point_id":  servicePointID,
	}

	list, err := s.orderQualityDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderQualityService) ListQuality(ctx context.Context, stockUpDayTime int64) ([]model.OrderQuality, error) {
	filter := bson.M{
		"stock_up_day_time": stockUpDayTime,
		"quality_has":       false,
	}
	list, err := s.orderQualityDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderQualityService) ListNotQuality(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) ([]model.OrderQuality, error) {
	filter := bson.M{
		"stock_up_day_time": stockUpDayTime,
		"service_point_id":  servicePointID,
		"supplier_level":    model.SupplierLevelPoint,
		"quality_has":       false,
	}

	list, err := s.orderQualityDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderQualityService) CountNotQuality(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) (int64, error) {
	filter := bson.M{
		"stock_up_day_time": stockUpDayTime,
		"service_point_id":  servicePointID,
		"supplier_level":    model.SupplierLevelPoint,
		"quality_has":       false,
	}

	n, err := s.orderQualityDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return n, nil
}

func (s orderQualityService) Get(ctx context.Context, id primitive.ObjectID) (model.OrderQuality, error) {
	filter := bson.M{
		"_id": id,
	}
	data, err := s.orderQualityDao.Get(ctx, filter)
	if err != nil {
		return model.OrderQuality{}, err
	}
	return data, nil
}

func (s orderQualityService) GetByProduct(ctx context.Context, productID primitive.ObjectID, stockUpNo int, stockUpDayAt int64) (model.OrderQuality, error) {
	filter := bson.M{
		"product_id":        productID,
		"stock_up_day_time": stockUpDayAt,
		"stock_up_no":       stockUpNo,
	}
	data, err := s.orderQualityDao.Get(ctx, filter)
	if err != nil {
		return model.OrderQuality{}, err
	}
	return data, nil
}

func (s orderQualityService) ListQualityHas(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) ([]model.OrderQuality, error) {
	filter := bson.M{
		"stock_up_day_time": stockUpDayTime,
		"service_point_id":  servicePointID,
		"supplier_level":    model.SupplierLevelPoint,
		"quality_has":       true,
	}

	list, err := s.orderQualityDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderQualityService) SearchQuality(ctx context.Context, stockUpDayTime int64, servicePointID, stationID primitive.ObjectID, content, QualityID string, qualityStatus int) ([]model.OrderQuality, error) {
	filter := bson.M{
		"stock_up_day_time": stockUpDayTime,
		"service_point_id":  servicePointID,
		"supplier_level":    model.SupplierLevelPoint,
	}

	if stationID != primitive.NilObjectID {
		filter["supplier_level"] = model.SupplierLevelStation
		filter["station_id"] = stationID
	}

	if qualityStatus != 0 {
		filter["quality_has"] = false
		if qualityStatus == 2 {
			filter["quality_has"] = true
		}
	}
	if content != "" {
		filter["product_title"] = bson.M{
			"$regex": content,
		}
	}
	if content == "" && len(QualityID) == 24 {
		id, err := util.ConvertToObjectWithNote(QualityID, "")
		if err != nil {
			return nil, err
		}
		filter["_id"] = id
	}

	list, err := s.orderQualityDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}
func (s orderQualityService) ListSort(ctx context.Context, stockUpDayTime int64, hasSort bool, servicePointID primitive.ObjectID) ([]model.OrderQuality, error) {
	filter := bson.M{
		"stock_up_day_time": stockUpDayTime,
		"service_point_id":  servicePointID,
		"supplier_level":    model.SupplierLevelPoint,
		"sort_has":          hasSort,
	}

	list, err := s.orderQualityDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderQualityService) ListNotSort(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) ([]model.OrderQuality, error) {
	filter := bson.M{
		"stock_up_day_time":   stockUpDayTime,
		"service_point_id":    servicePointID,
		"supplier_level":      model.SupplierLevelPoint,
		"order_list.has_sort": false,
	}

	list, err := s.orderQualityDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderQualityService) CountNotSort(ctx context.Context, stockUpDayTime int64, servicePointID primitive.ObjectID) (int64, error) {
	filter := bson.M{
		"stock_up_day_time":   stockUpDayTime,
		"service_point_id":    servicePointID,
		"supplier_level":      model.SupplierLevelPoint,
		"order_list.has_sort": false,
	}
	n, err := s.orderQualityDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return n, nil
}

func (s orderQualityService) SearchSort(ctx context.Context, stockUpDayTime int64, content string, sortStatus int, servicePointID primitive.ObjectID) ([]model.OrderQuality, error) {
	filter := bson.M{
		"stock_up_day_time": stockUpDayTime,
		"service_point_id":  servicePointID,
	}
	if sortStatus != 0 {
		filter["sort_has"] = false
		if sortStatus == 2 {
			filter["sort_has"] = true
		}
	}
	if content != "" {
		filter["product_title"] = bson.M{
			"$regex": content,
		}
	}
	list, err := s.orderQualityDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s orderQualityService) CreateQuality(ctx context.Context, data model.OrderQuality) error {
	err := s.orderQualityDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s orderQualityService) List(ctx context.Context, filter bson.M) ([]model.OrderQuality, error) {
	ups, err := s.orderQualityDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	return ups, nil
}
func (s orderQualityService) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.orderQualityDao.Count(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

// UpdateOrderToStockUp  备货
func (s orderQualityService) UpdateOrderToStockUp(ctx context.Context, orderIDList []primitive.ObjectID, ts int64, servicePointID primitive.ObjectID) error {
	// 查询已存在品控单
	for _, id := range orderIDList {
		err := s.stockUpPerOrder(ctx, id, ts)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s orderQualityService) stockUpPerOrder(ctx context.Context, orderID primitive.ObjectID, ts int64) error {
	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		return err
	}
	if order.OrderStatus == model.OrderStatusTypeCancel || order.OrderStatus == model.OrderStatusTypeClosed {
		return xerr.NewErr(xerr.ErrParamError, nil, "存在订单已取消，请刷新")
	}

	now := time.Now().UnixMilli()
	for _, p := range order.ProductList {
		// 待入库重量,根据实际数量计算
		dw := p.Num * p.RoughWeight

		qualityList, err := s.orderQualityDao.List(ctx, bson.M{
			"stock_up_day_time": ts,
			"product_id":        p.ProductID,
			"sku_id_code":       p.SkuIDCode,
		})

		if err != nil {
			return err
		}

		stockUpNo := len(qualityList)

		var quality model.OrderQuality

		for _, q := range qualityList {
			// 存在品控单，可能存在多批次，只取未品控的
			if !q.QualityHas {
				quality = q
				break
			}
		}

		// 已存在品控单且未品控-合并，需要判断是否是同一个规格，不同规格需要新建品控单
		if quality.ID != primitive.NilObjectID && p.SkuIDCode == quality.SkuIDCode {
			//	 已有相同产品备货
			num := quality.QualityDueNum + p.Num
			d := model.PerOrderTemp{
				OrderID:            orderID,
				BuyerID:            order.BuyerID,
				DeliverType:        order.DeliverType,
				InstantDeliverType: order.InstantDeliverType,
				InstantDeliverName: order.InstantDeliverName,
				BuyerName:          order.BuyerName,
				Address:            order.Address,
				DueNum:             p.Num,
				DueWeight:          dw,
				SortNum:            0,
				SortWeight:         0,
				HasShip:            false,
				HasQuality:         false,
				HasSort:            false,
			}

			qualityDueChangeNum := p.Num
			// // sortNewOrderNum := 0
			// for _, v := range quality.OrderList {
			// 	if !v.HasQuality && quality.QualityHas {
			// 		qualityDueChangeNum += v.DueNum
			// 	}
			// 	// if !v.HasSort && quality.SortHas {
			// 	// 	sortNewOrderNum++
			// 	// }
			// }
			// // if quality.SortHas {
			// // 	sortNewOrderNum++
			// // }

			quality.OrderList = append(quality.OrderList, d)

			update := bson.M{
				"quality_due_num":        num,                 //	待入库数
				"quality_due_change_num": qualityDueChangeNum, // 品控变化数
				// "sort_new_order_num":     sortNewOrderNum,     // 新分拣订单
				"order_list": quality.OrderList,
			}
			err = s.orderQualityDao.UpdateOne(ctx, bson.M{"_id": quality.ID}, bson.M{"$set": update})
			if err != nil {
				return err
			}
		} else {
			// 不存在品控单-新建
			oList := []model.PerOrderTemp{{
				OrderID:            orderID,
				SecondPointID:      order.SecondPointID,
				SecondPointName:    order.SecondPointName,
				BuyerID:            order.BuyerID,
				DeliverType:        order.DeliverType,
				InstantDeliverType: order.InstantDeliverType,
				InstantDeliverName: order.InstantDeliverName,
				BuyerName:          order.BuyerName,
				Address:            order.Address,
				DueNum:             p.Num,
				DueWeight:          dw,
				SortNum:            0,
				SortWeight:         0,
				HasQuality:         false,
				HasSort:            false,
			}}

			stockUpNo++

			data := model.OrderQuality{
				ID:               primitive.NewObjectID(),
				SupplierID:       order.SupplierID,
				SupplierName:     order.SupplierName,
				ProductID:        p.ProductID,
				SkuIDCode:        p.SkuIDCode,
				SkuName:          p.SkuName,
				PerRoughWeight:   p.RoughWeight,
				IsCheckWeight:    p.IsCheckWeight,
				HasParam:         p.HasParam,
				ProductParamType: p.ProductParamType,
				StandardAttr:     p.StandardAttr,
				NonStandardAttr:  p.NonStandardAttr,
				CategoryIDs:      p.CategoryIDs,
				ProductTitle:     p.ProductTitle,
				ProductCover:     p.ProductCoverImg,
				OrderList:        oList,
				ServicePointID:   order.ServicePointID,
				ServicePointName: order.ServicePointName,
				SupplierLevel:    order.SupplierLevel,
				StationID:        order.StationID,
				StationName:      order.StationName,
				QualityDueNum:    p.Num,
				QualityNum:       0,
				QualityHas:       false,
				SortHas:          false,
				StockUpNo:        stockUpNo, // 批次
				StockUpDayTime:   ts,
				CreatedAt:        time.Now().UnixMilli(),
				PurchaseNote:     p.PurchaseNote,
				LinkBrandStatus:  p.LinkBrandStatus,
				LinkBrandID:      p.LinkBrandID,
				LinkBrandName:    p.LinkBrandName,
			}
			err = s.orderQualityDao.Create(ctx, data)
			if err != nil {
				return err
			}
		}
	}

	// 更新订单-待品控
	filter := bson.M{
		"_id": orderID,
	}
	status := model.OrderStatusTypeToQuality
	key := model.BackRecordKey(status)

	err = s.orderS.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
		"stock_up_day_time":          ts,     //	备货时间
		"order_status":               status, // 待品控
		"order_status_record." + key: now,    // 状态记录
	}})

	// "stock_up_no":                stockUpNo, //	订单层级移除批次 商品层级存在批次

	if err != nil {
		return err
	}

	return nil
}

func checkQuality(q model.OrderQuality, qualityNum int) error {
	if qualityNum > q.QualityDueNum {
		return xerr.NewErr(xerr.ErrParamError, nil, "品控数不可大于已应入库数")
	}
	var sortNum int
	for _, v := range q.OrderList {
		sortNum += v.SortNum
	}
	if qualityNum < sortNum {
		//return xerr.NewErr(xerr.ErrParamError, nil, "品控数不可小于已分拣数")
	}

	return nil
}

// UpdateQuality 品控
func (s orderQualityService) UpdateQuality(ctx context.Context, id primitive.ObjectID, qualityNum int, reasonType int, reasonImg model.FileInfo, amount int) error {
	// 查询已备货
	q, err := s.orderQualityDao.Get(ctx, bson.M{
		"_id": id,
	})
	if err != nil {
		return err
	}
	//mq := make(map[primitive.ObjectID]model.OrderQuality)
	//for _, i := range qList {
	//	mq[i.ProductID] = i
	//}
	err = checkQuality(q, qualityNum)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()
	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {

		orderList := make([]model.PerOrderTemp, 0)

		for _, temp := range q.OrderList {
			orderList = append(orderList, temp)
		}

		for i, _ := range orderList {
			orderList[i].HasQuality = true
		}

		if q.QualityDueNum == qualityNum {
			reasonType = 0
			reasonImg = model.FileInfo{}
		}

		err = s.orderQualityDao.UpdateOne(sessCtx, bson.M{"_id": id}, bson.M{"$set": bson.M{
			"quality_has":            true,
			"quality_num":            qualityNum,
			"amount":                 amount, // 采购总价
			"order_list":             orderList,
			"quality_due_change_num": 0,
			"reason_type":            reasonType,
			"reason_img":             reasonImg,
			"updated_at":             now,
		}})
		if err != nil {
			return nil, err
		}
		var oIDs []primitive.ObjectID
		for _, order := range q.OrderList {
			if !order.HasQuality {
				oIDs = append(oIDs, order.OrderID)
			}
		}

		if len(oIDs) < 1 {
			return nil, nil
		}

		filter := bson.M{
			"_id": bson.M{"$in": oIDs},
			"order_status": bson.M{
				"$lte": model.OrderStatusTypeToSort, // 小于分拣环节的
			},
		}

		status := model.OrderStatusTypeToSort
		key := model.BackRecordKey(status)
		err = s.orderS.UpdateMany(sessCtx, filter, bson.M{"$set": bson.M{
			"order_status":               model.OrderStatusTypeToSort, // 待分拣
			"order_status_record." + key: now,                         // 状态记录
		}})
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}

	err = productBuyPriceService.NewProductBuyPriceService().UpdateRefreshStatus(ctx, q.ProductID, q.StockUpDayTime, q.StockUpNo)
	if err != nil {
		return err
	}

	return nil
}

func (s orderQualityService) UpdateMany(ctx context.Context, filter, update bson.M) error {
	err := s.orderQualityDao.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

// UpdateSort 分拣
func (s orderQualityService) UpdateSort(ctx context.Context, req types.OrderSortReq) error {
	id, err := util.ConvertToObjectWithNote(req.ID, "备货信息ID")
	if err != nil {
		return err
	}
	q, err := s.orderQualityDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	// 校验
	err = checkSort(q, req)
	if err != nil {
		return err
	}

	oList := q.OrderList
	for _, order := range req.OrderList {
		oID, err := util.ConvertToObjectWithNote(order.OrderID, "req sort order id")
		if err != nil {
			return err
		}
		for i, perOrder := range oList {
			if perOrder.HasShip {
				// 跳过已发货
				continue
			}
			if perOrder.OrderID == oID {
				oList[i].SortNum = order.Num
				oList[i].HasSort = true
				// TODO
				//oList[i].SortUserID = multiUser.UserID
				//oList[i].SortUserName = multiUser.Note
				//oList[i].SortUserMobile = user.Mobile
				if q.IsCheckWeight {
					oList[i].SortWeight = order.Weight
				} else {
					oList[i].SortWeight = order.Num * q.PerRoughWeight
				}
			}
		}
	}
	var oIDs []primitive.ObjectID
	for _, order := range q.OrderList {
		if !order.HasShip {
			oIDs = append(oIDs, order.OrderID)
		}
	}
	now := time.Now().UnixMilli()

	if len(oIDs) < 1 {
		return nil
	}

	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 更新备货单
		err = s.orderQualityDao.UpdateOne(sessCtx, bson.M{"_id": id}, bson.M{"$set": bson.M{
			"order_list":         oList,
			"sort_has":           true,
			"sort_new_order_num": 0,
			"updated_at":         now,
		}})

		// 更新订单
		filter := bson.M{
			"_id": bson.M{"$in": oIDs},
			"order_status": bson.M{
				"$lte": model.OrderStatusTypeToShip, // 小于发货环节的
			},
		}
		status := model.OrderStatusTypeToShip
		key := model.BackRecordKey(status)

		err = s.orderS.UpdateMany(sessCtx, filter, bson.M{"$set": bson.M{
			"order_status":               status, // 待发货
			"order_status_record." + key: now,    // 状态记录
		}})
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}

	// 计算采购价
	q2, err := s.orderQualityDao.Get(ctx, bson.M{
		"_id": id,
	})
	if err != nil {
		return err
	}
	err = productBuyPriceService.NewProductBuyPriceService().Upsert(ctx, q2)
	if err != nil {
		return err
	}

	return nil
}

// UpdateSortPhoto 分拣图
func (s orderQualityService) UpdateSortPhoto(ctx context.Context, qualityID, orderID primitive.ObjectID, photoList []model.FileInfo) error {
	now := time.Now().UnixMilli()

	filter := bson.M{
		"_id":                 qualityID,
		"order_list.order_id": orderID,
	}
	err := s.orderQualityDao.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
		"order_list.$.photo_list": photoList,
		"updated_at":              now,
	}})

	if err != nil {
		return err
	}
	return nil
}

func checkSort(q model.OrderQuality, req types.OrderSortReq) error {
	for _, order := range q.OrderList {
		if !order.HasQuality {
			return xerr.NewErr(xerr.ErrParamError, nil, "不能分拣，该单品存在订单尚未完成品控")
		}
		if order.HasShip {
			for _, sort := range req.OrderList {
				if order.OrderID.Hex() == sort.OrderID {
					if order.SortNum != sort.Num {
						return xerr.NewErr(xerr.ErrParamError, nil, "已发货订单不能更改分拣数据【数量】")
					}
					if q.IsCheckWeight {
						if order.SortWeight != sort.Weight {
							return xerr.NewErr(xerr.ErrParamError, nil, "已发货订单不能更改分拣数据【重量】")
						}
					}
				}
			}
		}
	}

	mOriginOrder := make(map[string]model.PerOrderTemp)
	for _, order := range q.OrderList {
		mOriginOrder[order.OrderID.Hex()] = order
	}
	var count int
	mCheck := make(map[string]int)
	for _, i := range req.OrderList {
		if _, ok := mCheck[i.OrderID]; ok {
			return xerr.NewErr(xerr.ErrParamError, nil, "分拣错误，订单重复")
		}
		mCheck[i.OrderID] = 0
		if v, ok := mOriginOrder[i.OrderID]; ok {
			if i.Num > v.DueNum {
				zap.S().Errorf("分拣数量错误，应有数量：%d，请求数量：%d", v.DueNum, i.Num)
				return xerr.NewErr(xerr.ErrParamError, nil, "分拣错误，数量不能大于订单应有数量")
			}
			if q.IsCheckWeight {
				if i.Num != 0 && i.Weight == 0 {
					zap.S().Errorf("分拣重量不能为0")
					return xerr.NewErr(xerr.ErrParamError, nil, "分拣重量不能为0")
				}
				if i.Num == 0 && i.Weight != 0 {
					zap.S().Errorf("分拣数量等于0，重量不为0")
					return xerr.NewErr(xerr.ErrParamError, nil, "分拣数量等于0，重量只能为0")
				}
				//if i.Weight > v.DueWeight {
				//	zap.S().Errorf("分拣重量错误，应有重量%d，请求重量%d", v.DueWeight, i.Weight)
				//	return xerr.NewErr(xerr.ErrParamError, nil, "分拣重量错误，重量不能大于订单应有重量")
				//}
			}
		} else {
			reqBytes, _ := jsoniter.Marshal(req.OrderList)
			upBytes, _ := jsoniter.Marshal(q.OrderList)
			zap.S().Errorf("分拣订单存在归属错误%s，请求分拣订单列表%v，备货单订单列表%v", i.OrderID, string(reqBytes), string(upBytes))
			return xerr.NewErr(xerr.ErrParamError, nil, "分拣存在订单归属错误")
		}
		count += i.Num
	}

	if count != q.QualityNum {
		return xerr.NewErr(xerr.ErrParamError, nil, "分拣总数不等于品控总数")
	}
	return nil
}

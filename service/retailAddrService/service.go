package retailAddrService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/userAddrDao"
	"base/global"
	"base/model"
	"base/service/userService"
	"base/types"
	"base/util"
	"context"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

type ServiceInterface interface {
	Create(ctx context.Context, req *types.RetailAddrCreate, userID primitive.ObjectID) error
	GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.RetailAddress, error)
	Update(ctx context.Context, req *types.RetailAddrUpdate, userId primitive.ObjectID) error
}

type retailAddrService struct {
	db    userAddrDao.UserAddrDaoInt
	rdb   *redis.Client
	userS userService.ServiceInterface
}

func NewRetailAddrService() ServiceInterface {
	return retailAddrService{
		db:    dao.UserAddrDao,
		rdb:   global.RDBDefault,
		userS: userService.NewUserService(),
	}
}

func (s retailAddrService) Create(ctx context.Context, req *types.RetailAddrCreate, userID primitive.ObjectID) error {
	err := checkAddr(req.Address, req.Contact.Name, req.Contact.Mobile, req.Location)
	if err != nil {
		return err
	}
	now := time.Now().UnixMilli()

	data := model.RetailAddress{
		ID:        primitive.NewObjectID(),
		UserID:    userID,
		Address:   util.DealWrap(req.Address),
		Contact:   req.Contact,
		Location:  req.Location,
		CreatedAt: now,
		UpdatedAt: now,
	}

	set(s.rdb, data)

	return nil
}

func (s retailAddrService) GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.RetailAddress, error) {
	m := get(s.rdb, userID)
	return m, nil
}

// Update 修改收货地址
func (s retailAddrService) Update(ctx context.Context, req *types.RetailAddrUpdate, userID primitive.ObjectID) error {
	err := checkAddr(req.Address, req.Contact.Name, req.Contact.Mobile, req.Location)
	if err != nil {
		return err
	}

	address, err := s.GetByUserID(ctx, userID)
	if err != nil {
		return err
	}
	now := time.Now().UnixMilli()
	address.Address = util.DealWrap(req.Address)

	address.Contact = req.Contact
	address.Location = req.Location
	address.UpdatedAt = now

	set(s.rdb, address)

	return nil
}

func checkAddr(addr, contactUser, mobile string, location model.Location) error {
	if len(addr) == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写详细地址")
	}
	if len(contactUser) == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写收件人")
	}
	if len(mobile) != 11 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请填写手机号")
	}
	if location.Latitude == 0 || location.Longitude == 0 {
		return xerr.NewErr(xerr.ErrParamError, nil, "请选择定位")
	}
	return nil
}

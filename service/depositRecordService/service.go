package depositRecordService

import (
	"base/dao"
	"base/dao/depositRecordDao"
	"base/model"
	"context"
)

type ServiceInterface interface {
	Create(ctx context.Context, data model.DepositRecord) error
}

type depositRecordService struct {
	depositRecordDao depositRecordDao.DaoInt
}

func NewDepositRecordService() ServiceInterface {
	return depositRecordService{
		depositRecordDao: dao.DepositRecordDao,
	}
}

func (s depositRecordService) Create(ctx context.Context, data model.DepositRecord) error {
	err := s.depositRecordDao.Create(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Building the Application

```bash
go build -o main main.go
```

### Running the Application

```bash
./main
```

### Docker Build

```bash
docker build -t server .
```

### Development Setup

- Port: 11001 (configured in config.yaml)
- Configuration file: `config.yaml` (contains database, payment, and service configurations)
- Go module: Uses Go 1.21 with custom allinpay module replacement

## High-Level Architecture

This is a Go-based e-commerce backend server using Gin framework with the following key architectural components:

### Core Structure

- **Entry Point**: `main.go` initializes config, global services, DAOs, handlers, and starts server
- **Framework**: Gin web framework for HTTP routing
- **Database**: MongoDB for primary data storage, Redis for caching
- **Configuration**: YAML-based config in `config.yaml`

### Key Layers

1. **API Layer** (`/api`): HTTP endpoints organized by functional domains (admin, buyer, supplier, etc.)
2. **Handler Layer** (`/handler`): Route registration and middleware setup
3. **Service Layer** (`/service`): Business logic implementation with caching
4. **DAO Layer** (`/dao`): Data access objects for MongoDB operations
5. **Model Layer** (`/model`): Data structures and business entities

### Primary Business Domains

- **User Management**: Buyers, suppliers, admins, service points
- **Product Management**: Products, categories, inventory, auditing
- **Order Management**: Orders, payments, refunds, logistics
- **Financial**: Payments (AllinPay, YeePay), invoicing, commissions
- **Marketing**: Coupons, promotions, integrations

### External Integrations

- **Payment Systems**: AllinPay and YeePay payment gateways
- **Cloud Services**: Alibaba Cloud (OSS storage, OCR, SMS)
- **WeChat**: Mini-program and official account APIs

### Data Flow

1. HTTP requests → Handlers → Services → DAOs → MongoDB
2. Services implement caching strategies using Redis
3. Global initialization sets up database connections, payment clients, and logging

### Key Configuration Areas

- Database connections (MongoDB, Redis)
- Payment gateway credentials and certificates
- WeChat app configurations
- Alibaba Cloud service keys
- Logging and monitoring setup

### Development Patterns

- DAO pattern for data access with interface-based design
- Service layer with caching decorators
- Middleware for authentication, CORS, rate limiting
- Structured logging with separate log files for different concerns (sys, pay, order)

# IndexPartProductService 性能优化说明

## 性能问题

原始的 `UpdateSort` 方法存在严重的性能问题：

- 在 for 循环中逐一执行数据库更新操作
- 每次更新都是一次独立的数据库往返
- 当产品数量较多时，性能急剧下降

## 优化方案

### 1. BulkWrite 批量更新（推荐）

**性能最佳**，适用于大部分场景

```go
func (s indexPartProductService) updateSortBulkWrite(ctx context.Context, id primitive.ObjectID, pIDs []primitive.ObjectID) error {
    operations := make([]mongo.WriteModel, 0, len(pIDs))

    for i, productID := range pIDs {
        operation := mongo.NewUpdateOneModel()
        operation.SetFilter(bson.M{
            "index_part_id": id,
            "product_id":    productID,
        })
        operation.SetUpdate(bson.M{
            "$set": bson.M{"sort": i},
        })
        operations = append(operations, operation)
    }

    return s.indexPartProductDao.BulkWrite(ctx, operations)
}
```

### 2. UpdateMany 批量更新

适用于相同更新操作的场景

```go
// 注意：只适用于所有记录设置相同值的情况
filter := bson.M{
    "index_part_id": id,
    "product_id": bson.M{"$in": pIDs},
}
update := bson.M{"$set": bson.M{"sort": 0}}
return s.indexPartProductDao.UpdateMany(ctx, filter, update)
```

### 3. 事务批量更新

适用于需要强一致性的场景

```go
func (s indexPartProductService) updateSortWithTransaction(ctx context.Context, id primitive.ObjectID, pIDs []primitive.ObjectID) error {
    session, err := s.mdb.Client().StartSession()
    if err != nil {
        return err
    }
    defer session.EndSession(ctx)

    _, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
        // BulkWrite operations within transaction
        return nil, s.indexPartProductDao.BulkWrite(sessCtx, operations)
    })

    return err
}
```

## 性能对比

| 场景       | 原始方法         | BulkWrite      | 性能提升 |
| ---------- | ---------------- | -------------- | -------- |
| 10 个产品  | 10 次数据库调用  | 1 次数据库调用 | 5-10x    |
| 50 个产品  | 50 次数据库调用  | 1 次数据库调用 | 20-50x   |
| 100 个产品 | 100 次数据库调用 | 1 次数据库调用 | 50-100x  |

## 其他优化的方法

除了 `UpdateSort`，以下方法也进行了类似优化：

- `DownProduct`: 重排序逻辑优化
- `UpProduct`: 批量更新产品排序
- `Update` (remove 操作): 重排序优化

## 使用建议

1. **默认使用 BulkWrite**: 性能最佳，适用于大部分场景
2. **数据量小时**: 可以考虑简单的 UpdateMany
3. **需要事务**: 使用 updateSortWithTransaction
4. **监控性能**: 在生产环境中监控执行时间

## 注意事项

1. BulkWrite 有操作数量限制（通常是 100,000 个操作）
2. 大批量操作可能需要分批处理
3. 错误处理：BulkWrite 可能部分成功，需要检查 BulkWriteResult
4. 索引优化：确保相关字段有适当的数据库索引

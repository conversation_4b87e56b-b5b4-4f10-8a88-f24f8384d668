{"swagger": "2.0", "info": {"title": "订单最终结算 API", "description": "订单最终结算管理系统，支持订单最终结算记录的查询、统计等操作", "version": "1.0.0", "contact": {"name": "API Support", "email": "<EMAIL>"}}, "host": "api.example.com", "basePath": "/v1", "schemes": ["https", "http"], "consumes": ["application/json"], "produces": ["application/json"], "securityDefinitions": {"bearerAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header", "description": "Bearer token for authentication. Format: 'Bearer {token}'"}}, "security": [{"bearerAuth": []}], "tags": [{"name": "orderFinalSettle", "description": "订单最终结算相关接口"}], "paths": {"/api/order/final/settle/get/by/order": {"post": {"tags": ["orderFinalSettle"], "summary": "根据订单ID查询订单最终结算记录", "description": "根据指定的订单ID查询对应的最终结算记录详情", "operationId": "getOrderFinalSettleByOrderID", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "查询请求参数", "schema": {"$ref": "#/definitions/GetByOrderIDRequest"}}], "responses": {"200": {"description": "成功获取订单最终结算记录", "schema": {"$ref": "#/definitions/ApiResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/api/order/final/settle/list/by/supplier": {"post": {"tags": ["orderFinalSettle"], "summary": "根据供应商ID查询最终结算记录列表", "description": "根据供应商ID分页查询最终结算记录列表", "operationId": "listOrderFinalSettleBySupplierID", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "分页查询请求参数", "schema": {"$ref": "#/definitions/ListBySupplierIDRequest"}}], "responses": {"200": {"description": "成功获取最终结算记录列表", "schema": {"$ref": "#/definitions/PagedApiResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/api/order/final/settle/list/by/month": {"post": {"tags": ["orderFinalSettle"], "summary": "按月查询订单最终结算记录", "description": "根据月份时间戳和可选的供应商ID分页查询最终结算记录", "operationId": "listOrderFinalSettleByMonth", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "按月查询请求参数", "schema": {"$ref": "#/definitions/ListByMonthRequest"}}], "responses": {"200": {"description": "成功获取最终结算记录列表", "schema": {"$ref": "#/definitions/ListApiResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}, "/api/order/final/settle/stats/monthly": {"post": {"tags": ["orderFinalSettle"], "summary": "获取月度最终结算统计", "description": "根据月份时间戳和可选的供应商ID获取月度最终结算统计数据", "operationId": "getMonthlyOrderFinalSettleStats", "security": [{"bearerAuth": []}], "parameters": [{"name": "body", "in": "body", "required": true, "description": "统计查询请求参数", "schema": {"$ref": "#/definitions/GetMonthlyStatsRequest"}}], "responses": {"200": {"description": "成功获取月度统计数据", "schema": {"$ref": "#/definitions/MonthlyStatsResponse"}}, "400": {"description": "请求参数错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}, "500": {"description": "服务器内部错误", "schema": {"$ref": "#/definitions/ErrorResponse"}}}}}}, "definitions": {"GetByOrderIDRequest": {"type": "object", "required": ["order_id"], "properties": {"order_id": {"type": "string", "description": "订单ID", "example": "507f1f77bcf86cd799439011"}}}, "ListBySupplierIDRequest": {"type": "object", "required": ["supplier_id"], "properties": {"supplier_id": {"type": "string", "description": "供应商ID", "example": "507f1f77bcf86cd799439012"}, "page": {"type": "integer", "description": "页码，从1开始", "default": 1, "minimum": 1, "example": 1}, "limit": {"type": "integer", "description": "每页数量", "default": 20, "minimum": 1, "maximum": 100, "example": 20}}}, "ListByMonthRequest": {"type": "object", "properties": {"supplier_id": {"type": "string", "description": "供应商ID（可选）", "example": "507f1f77bcf86cd799439012"}, "month_timestamp": {"type": "integer", "format": "int64", "description": "月份时间戳（秒）", "example": 1640995200}, "page": {"type": "integer", "description": "页码，从1开始", "default": 1, "minimum": 1, "example": 1}, "limit": {"type": "integer", "description": "每页数量", "default": 20, "minimum": 1, "maximum": 100, "example": 20}}}, "GetMonthlyStatsRequest": {"type": "object", "properties": {"supplier_id": {"type": "string", "description": "供应商ID（可选）", "example": "507f1f77bcf86cd799439012"}, "month_timestamp": {"type": "integer", "format": "int64", "description": "月份时间戳（秒）", "example": 1640995200}}}, "OrderFinalSettle": {"type": "object", "properties": {"id": {"type": "string", "description": "最终结算记录ID", "example": "507f1f77bcf86cd799439011"}, "order_id": {"type": "string", "description": "关联订单ID", "example": "507f1f77bcf86cd799439012"}, "supplier_id": {"type": "string", "description": "供应商ID", "example": "507f1f77bcf86cd799439013"}, "supplier_name": {"type": "string", "description": "供应商名称", "example": "优质供应商A"}, "total_paid_amount": {"type": "integer", "description": "总支付金额（分）", "example": 100000}, "total_buy_product_cost_amount": {"type": "integer", "description": "总成本金额（分）", "example": 80000}, "total_adjust_settle_amount_profit": {"type": "integer", "description": "总调价结算金额（分）", "example": 5000}, "total_quality_refund_amount": {"type": "integer", "description": "品控退款金额（分）", "example": 2000}, "total_after_sale_refund_amount": {"type": "integer", "description": "售后退款金额（分）", "example": 3000}, "total_profit": {"type": "integer", "description": "总利润金额（分）", "example": 15000}, "profit_rate": {"type": "number", "format": "double", "description": "利润率", "example": 0.15}, "product_list": {"type": "array", "description": "商品最终结算明细", "items": {"$ref": "#/definitions/ProductFinalSettle"}}, "order_created_at": {"type": "integer", "format": "int64", "description": "订单创建时间戳（秒）", "example": 1640995200}, "remark": {"type": "string", "description": "备注", "example": "正常结算"}, "created_at": {"type": "integer", "format": "int64", "description": "创建时间戳（秒）", "example": 1640995200}, "updated_at": {"type": "integer", "format": "int64", "description": "更新时间戳（秒）", "example": 1640995200}}}, "ProductFinalSettle": {"type": "object", "properties": {"product_id": {"type": "string", "description": "商品ID", "example": "507f1f77bcf86cd799439014"}, "product_title": {"type": "string", "description": "商品标题", "example": "新鲜苹果"}, "product_cover_img": {"type": "string", "description": "商品封面图片URL", "example": "https://example.com/images/apple.jpg"}, "sku_id_code": {"type": "string", "description": "SKU编码", "example": "APPLE001"}, "sku_name": {"type": "string", "description": "SKU名称", "example": "红富士苹果 500g"}, "quantity": {"type": "integer", "description": "数量", "example": 10}, "unit_price": {"type": "integer", "description": "单价（分）", "example": 1000}, "revenue": {"type": "integer", "description": "收入金额（分）", "example": 10000}, "cost": {"type": "integer", "description": "成本金额（分）", "example": 8000}, "profit": {"type": "integer", "description": "利润金额（分）", "example": 2000}, "profit_rate": {"type": "number", "format": "double", "description": "利润率", "example": 0.2}, "remark": {"type": "string", "description": "备注", "example": "品质良好"}}}, "MonthlyStats": {"type": "object", "properties": {"total_orders": {"type": "integer", "description": "总订单数", "example": 100}, "total_paid_amount": {"type": "integer", "description": "总支付金额（分）", "example": 1000000}, "total_buy_product_cost_amount": {"type": "integer", "description": "总成本金额（分）", "example": 800000}, "total_adjust_settle_amount_profit": {"type": "integer", "description": "总调价结算金额（分）", "example": 50000}, "total_quality_refund_amount": {"type": "integer", "description": "总品控退款金额（分）", "example": 20000}, "total_after_sale_refund_amount": {"type": "integer", "description": "总售后退款金额（分）", "example": 30000}, "total_profit": {"type": "integer", "description": "总利润金额（分）", "example": 150000}, "average_profit_rate": {"type": "number", "format": "double", "description": "平均利润率", "example": 0.15}}}, "PagedData": {"type": "object", "properties": {"list": {"type": "array", "description": "数据列表", "items": {"$ref": "#/definitions/OrderFinalSettle"}}, "total": {"type": "integer", "description": "总数量", "example": 100}, "page": {"type": "integer", "description": "当前页码", "example": 1}, "limit": {"type": "integer", "description": "每页数量", "example": 20}}}, "ApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码，0表示成功", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"$ref": "#/definitions/OrderFinalSettle"}}}, "PagedApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码，0表示成功", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"$ref": "#/definitions/PagedData"}}}, "ListApiResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码，0表示成功", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"type": "array", "description": "数据列表", "items": {"$ref": "#/definitions/OrderFinalSettle"}}, "total": {"type": "integer", "description": "总数量", "example": 100}}}, "MonthlyStatsResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码，0表示成功", "example": 0}, "message": {"type": "string", "description": "响应消息", "example": "success"}, "data": {"$ref": "#/definitions/MonthlyStats"}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误状态码，非0表示失败", "example": 400}, "message": {"type": "string", "description": "错误消息", "example": "请求参数错误"}, "data": {"type": "object", "description": "错误详情", "example": null}}}}}
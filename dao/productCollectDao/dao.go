package productCollectDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.ProductCollect) error
	Get(ctx context.Context, filter bson.M) (model.ProductCollect, error)
	Delete(ctx context.Context, filter bson.M) error
	DeleteMany(ctx context.Context, filter bson.M) error
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ProductCollect, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type productCollectDao struct {
	db *mongo.Collection
}

func (s productCollectDao) Get(ctx context.Context, filter bson.M) (model.ProductCollect, error) {
	var data model.ProductCollect
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ProductCollect{}, err
	}
	return data, nil
}

func (s productCollectDao) Create(ctx context.Context, data model.ProductCollect) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s productCollectDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s productCollectDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s productCollectDao) DeleteMany(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s productCollectDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ProductCollect, int64, error) {
	var list []model.ProductCollect
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s productCollectDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func NewProductCollectDao(collect string) DaoInt {
	return productCollectDao{
		db: global.MDB.Collection(collect),
	}
}

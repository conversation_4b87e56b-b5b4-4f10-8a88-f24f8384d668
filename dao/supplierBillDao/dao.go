package supplierBillDao

import (
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.SupplierBill) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.SupplierBill, error)
	List(ctx context.Context, filter bson.M) ([]model.SupplierBill, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.SupplierBill, int64, error)
}

type supplierBillDao struct {
	db *mongo.Collection
}

// List 查询
func (s supplierBillDao) List(ctx context.Context, filter bson.M) ([]model.SupplierBill, error) {
	var list []model.SupplierBill
	//skip := (page - 1) * limit
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s supplierBillDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.SupplierBill, int64, error) {
	var list []model.SupplierBill
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s supplierBillDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierBillDao) Create(ctx context.Context, data model.SupplierBill) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierBillDao) Get(ctx context.Context, filter bson.M) (model.SupplierBill, error) {
	var data model.SupplierBill
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.SupplierBill{}, err
	}
	return data, nil
}

func NewSupplierBillDao(collect string) DaoInt {
	return supplierBillDao{
		db: global.MDB.Collection(collect),
	}
}

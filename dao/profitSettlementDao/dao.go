package profitSettlementDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 利润DAO接口
type DaoInt interface {
	Create(ctx context.Context, data model.ProfitSettlement) error
	Update(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.ProfitSettlement, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ProfitSettlement, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.ProfitSettlement, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type profitSettlementDao struct {
	db *mongo.Collection
}

// NewProfitSettlementDao 创建利润结算DAO
func NewProfitSettlementDao(collect string) DaoInt {
	return profitSettlementDao{
		db: global.MDB.Collection(collect),
	}
}

// Create 创建
func (s profitSettlementDao) Create(ctx context.Context, data model.ProfitSettlement) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

// Update 更新
func (s profitSettlementDao) Update(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

// Delete 删除
func (s profitSettlementDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

// Get 查询
func (s profitSettlementDao) Get(ctx context.Context, filter bson.M) (model.ProfitSettlement, error) {
	var data model.ProfitSettlement
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ProfitSettlement{}, err
	}
	return data, nil
}

// ListByPage 查询
func (s profitSettlementDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ProfitSettlement, int64, error) {
	var list []model.ProfitSettlement
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	opts.SetSort(bson.M{"created_at": -1})

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

// List 查询
func (s profitSettlementDao) List(ctx context.Context, filter bson.M) ([]model.ProfitSettlement, error) {
	var list []model.ProfitSettlement
	opts := options.Find()

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// Count 统计

func (s profitSettlementDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	return s.db.CountDocuments(ctx, filter)
}

package generalImgDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 通用图片
type DaoInt interface {
	Upsert(ctx context.Context, data model.GeneralImg) error
	Update(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.GeneralImg, error)
	Get(ctx context.Context, filter bson.M) (model.GeneralImg, error)
}

type generalImgDao struct {
	db *mongo.Collection
}

func (s generalImgDao) Upsert(ctx context.Context, data model.GeneralImg) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}

	return nil
}

func (s generalImgDao) Get(ctx context.Context, filter bson.M) (model.GeneralImg, error) {
	var data model.GeneralImg
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.GeneralImg{}, err
	}
	return data, nil
}

func (s generalImgDao) List(ctx context.Context, filter bson.M) ([]model.GeneralImg, error) {
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var list []model.GeneralImg
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s generalImgDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func NewGeneralImgDao(collect string) DaoInt {
	return generalImgDao{
		db: global.MDB.Collection(collect),
	}
}

package invoiceDao

import (
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Invoice) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.Invoice, error)
	List(ctx context.Context, filter bson.M) ([]model.Invoice, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Invoice, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	DeleteOne(ctx context.Context, filter bson.M) error
}

type invoiceDao struct {
	db *mongo.Collection
}

// List 查询
func (s invoiceDao) List(ctx context.Context, filter bson.M) ([]model.Invoice, error) {
	var list []model.Invoice
	//skip := (page - 1) * limit
	opts := options.Find()
	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s invoiceDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Invoice, int64, error) {
	var list []model.Invoice
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s invoiceDao) Count(ctx context.Context, filter bson.M) (int64, error) {

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s invoiceDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s invoiceDao) DeleteOne(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s invoiceDao) Create(ctx context.Context, data model.Invoice) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s invoiceDao) Get(ctx context.Context, filter bson.M) (model.Invoice, error) {
	var data model.Invoice
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Invoice{}, err
	}
	return data, nil
}

func NewInvoiceDao(collect string) DaoInt {
	return invoiceDao{
		db: global.MDB.Collection(collect),
	}
}

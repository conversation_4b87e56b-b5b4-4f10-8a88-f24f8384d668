package swipeDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, swipe model.Swipe) error
	Count(ctx context.Context) (int64, error)
	UpdateInfo(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Swipe, int64, error)
	ListByCus(ctx context.Context, filter bson.M) ([]model.Swipe, error)
	Get(ctx context.Context, filter bson.M) (model.Swipe, error)
}

type swipeDao struct {
	db *mongo.Collection
}

func NewSwipeDao(collect string) DaoInt {
	return swipeDao{
		db: global.MDB.Collection(collect),
	}
}

func (s swipeDao) Get(ctx context.Context, filter bson.M) (model.Swipe, error) {
	var data model.Swipe
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Swipe{}, err
	}
	return data, nil
}

func (s swipeDao) Create(ctx context.Context, swipe model.Swipe) error {
	_, err := s.db.InsertOne(ctx, swipe)
	if err != nil {
		return err
	}

	return nil
}

func (s swipeDao) UpdateInfo(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s swipeDao) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := s.db.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	return nil
}

func (s swipeDao) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Swipe, int64, error) {
	var list []model.Swipe
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "sort", Value: 1},
	}
	opts.SetSort(sort)
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s swipeDao) Count(ctx context.Context) (int64, error) {
	count, err := s.db.CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s swipeDao) ListByCus(ctx context.Context, filter bson.M) ([]model.Swipe, error) {
	var list []model.Swipe
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "sort", Value: 1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

package deliverAssignDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.DeliverAssign) error
	CreateMany(ctx context.Context, data []model.DeliverAssign) error
	Upsert(ctx context.Context, data model.DeliverAssign) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.DeliverAssign, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.DeliverAssign, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	List(ctx context.Context, filter bson.M) ([]model.DeliverAssign, error)
}

type deliverAssignDao struct {
	db *mongo.Collection
}

func NewDeliverAssignDao(collect string) DaoInt {
	return deliverAssignDao{
		db: global.MDB.Collection(collect),
	}
}

func (s deliverAssignDao) Create(ctx context.Context, data model.DeliverAssign) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s deliverAssignDao) CreateMany(ctx context.Context, data []model.DeliverAssign) error {
	var list []interface{}
	for _, datum := range data {
		list = append(list, datum)
	}

	_, err := s.db.InsertMany(ctx, list)
	if err != nil {
		return err
	}
	return nil
}

func (s deliverAssignDao) Upsert(ctx context.Context, data model.DeliverAssign) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}
	return nil
}

func (s deliverAssignDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s deliverAssignDao) Delete(ctx context.Context, filter bson.M) error {
	res, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (s deliverAssignDao) Get(ctx context.Context, filter bson.M) (model.DeliverAssign, error) {
	var data model.DeliverAssign
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.DeliverAssign{}, err
	}
	return data, nil
}

// ListByPage 查询
func (s deliverAssignDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.DeliverAssign, int64, error) {
	var list []model.DeliverAssign
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s deliverAssignDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

// List 查询
func (s deliverAssignDao) List(ctx context.Context, filter bson.M) ([]model.DeliverAssign, error) {
	var list []model.DeliverAssign

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

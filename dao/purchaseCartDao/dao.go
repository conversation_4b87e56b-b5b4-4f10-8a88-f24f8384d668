package purchaseCartDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	Add(ctx context.Context, list model.PurchaseCart) error
	Count(ctx context.Context, filter bson.M) (int64, error)
	Delete(ctx context.Context, filter bson.M) error
	DeleteMany(ctx context.Context, filter bson.M) error
	Insert(ctx context.Context, data model.PurchaseCart) error
	Update(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.PurchaseCart, error)
	GetByFilter(ctx context.Context, filter bson.M) (model.PurchaseCart, error)
}

type purchaseCartDao struct {
	db *mongo.Collection
}

func (s purchaseCartDao) Insert(ctx context.Context, data model.PurchaseCart) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s purchaseCartDao) GetByFilter(ctx context.Context, filter bson.M) (model.PurchaseCart, error) {
	var data model.PurchaseCart
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.PurchaseCart{}, err
	}
	return data, nil
}

func (s purchaseCartDao) List(ctx context.Context, filter bson.M) ([]model.PurchaseCart, error) {
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var list []model.PurchaseCart
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s purchaseCartDao) DeleteMany(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteMany(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (s purchaseCartDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s purchaseCartDao) Add(ctx context.Context, cart model.PurchaseCart) error {
	_, err := s.db.InsertOne(ctx, cart)
	if err != nil {
		return err
	}
	return nil
}

func (s purchaseCartDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s purchaseCartDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}

func (s purchaseCartDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	return count, err
}

func NewPurchaseCartDao(collect string) DaoInt {
	return purchaseCartDao{
		db: global.MDB.Collection(collect),
	}
}

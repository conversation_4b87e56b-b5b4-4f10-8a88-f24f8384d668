package indexPartDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 首页专区
type DaoInt interface {
	Create(ctx context.Context, data model.IndexPart) error
	Upsert(ctx context.Context, data model.IndexPart) error
	Get(ctx context.Context, filter bson.M) (model.IndexPart, error)
	List(ctx context.Context, filter bson.M) ([]model.IndexPart, error)
	Delete(ctx context.Context, filter bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type indexPartDao struct {
	db *mongo.Collection
}

func (s indexPartDao) Upsert(ctx context.Context, data model.IndexPart) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartDao) Create(ctx context.Context, data model.IndexPart) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartDao) Get(ctx context.Context, filter bson.M) (model.IndexPart, error) {
	var data model.IndexPart
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.IndexPart{}, err
	}
	return data, nil
}

func (s indexPartDao) List(ctx context.Context, filter bson.M) ([]model.IndexPart, error) {
	sort := bson.D{
		bson.E{Key: "sort", Value: 1},
	}
	opts := options.Find()
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var list []model.IndexPart
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s indexPartDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (s indexPartDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func NewIndexPartDao(collect string) DaoInt {
	return indexPartDao{
		db: global.MDB.Collection(collect),
	}
}

package supplierTagDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	Create(ctx context.Context, data model.SupplierTag) error
	Update(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.SupplierTag, error)
	List(ctx context.Context, filter bson.M) ([]model.SupplierTag, error)
}

type supplierTagDao struct {
	db *mongo.Collection
}

func (s supplierTagDao) Create(ctx context.Context, data model.SupplierTag) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierTagDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierTagDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierTagDao) Get(ctx context.Context, filter bson.M) (model.SupplierTag, error) {
	var data model.SupplierTag
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.SupplierTag{}, err
	}
	return data, nil
}

// List 查询
func (s supplierTagDao) List(ctx context.Context, filter bson.M) ([]model.SupplierTag, error) {
	var list []model.SupplierTag

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func NewSupplierTagDao(collect string) DaoInt {
	return supplierTagDao{
		db: global.MDB.Collection(collect),
	}
}

package productDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, product model.Product) error
	Count(ctx context.Context, filter bson.M) (int64, error)
	NumAllSupplier(ctx context.Context) ([]model.SupplierStats, error)
	NumBySupplier(ctx context.Context, id primitive.ObjectID) (model.SupplierStats, error)
	FilterCategoryBySupplier(ctx context.Context, saleStatus int, id primitive.ObjectID) ([]model.CategoryOnly, error)
	FilterCategory(ctx context.Context, saleStatus int) ([]model.CategoryOnly, error)
	Find(ctx context.Context, filter bson.M) ([]model.Product, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Product, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.Product, error)
	Update(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.Product, error)
}

type productDao struct {
	db *mongo.Collection
}

func (s productDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Product, int64, error) {
	var list []model.Product
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "updated_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s productDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s productDao) List(ctx context.Context, filter bson.M) ([]model.Product, error) {
	var list []model.Product
	//skip := (page - 1) * limit
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "updated_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s productDao) Get(ctx context.Context, filter bson.M) (model.Product, error) {
	var data model.Product
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Product{}, err
	}
	return data, nil
}

func NewProductDao(collect string) DaoInt {
	return productDao{
		db: global.MDB.Collection(collect),
	}
}

func (s productDao) Create(ctx context.Context, product model.Product) error {
	_, err := s.db.InsertOne(ctx, product)
	if err != nil {
		return err
	}
	return nil
}

func (s productDao) Find(ctx context.Context, filter bson.M) ([]model.Product, error) {
	var list []model.Product
	sort := bson.D{
		bson.E{Key: "updated_at", Value: -1},
	}
	opts := options.Find()
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s productDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s productDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateMany(ctx, filter, update)
	_ = res
	return err
}

func (s productDao) NumAllSupplier(ctx context.Context) ([]model.SupplierStats, error) {

	cursor, err := s.db.Aggregate(ctx, mongo.Pipeline{
		{{
			"$match", bson.M{
				"deleted_at": 0,
			},
		}},
		{
			{"$group", bson.M{
				"_id": "$supplier_id",
				"sale_true_count": bson.M{
					"$sum": bson.M{"$cond": bson.A{bson.M{"$eq": bson.A{"$sale", true}}, 1, 0}},
				},
				"sale_false_count": bson.M{
					"$sum": bson.M{"$cond": bson.A{bson.M{"$eq": bson.A{"$sale", false}}, 1, 0}},
				},
				"total_item_count": bson.M{"$sum": 1},
			}},
		},
	})
	if err != nil {
		return nil, err
	}

	defer cursor.Close(ctx)

	var supplierStatsList []model.SupplierStats
	if err := cursor.All(ctx, &supplierStatsList); err != nil {
		return nil, err
	}

	return supplierStatsList, nil
}

func (s productDao) NumBySupplier(ctx context.Context, id primitive.ObjectID) (model.SupplierStats, error) {

	cursor, err := s.db.Aggregate(ctx, mongo.Pipeline{
		{{
			"$match", bson.M{
				"supplier_id": id,
				"deleted_at":  0,
			},
		}},
		{
			{"$group", bson.M{
				"_id": "$supplier_id",
				"sale_true_count": bson.M{
					"$sum": bson.M{"$cond": bson.A{bson.M{"$eq": bson.A{"$sale", true}}, 1, 0}},
				},
				"sale_false_count": bson.M{
					"$sum": bson.M{"$cond": bson.A{bson.M{"$eq": bson.A{"$sale", false}}, 1, 0}},
				},
				"total_item_count": bson.M{"$sum": 1},
			}},
		},
	})
	if err != nil {
		return model.SupplierStats{}, err
	}

	defer cursor.Close(ctx)

	var supplierStatsList []model.SupplierStats
	if err := cursor.All(ctx, &supplierStatsList); err != nil {
		return model.SupplierStats{}, err
	}

	if len(supplierStatsList) < 1 {
		return model.SupplierStats{}, nil
	}

	return supplierStatsList[0], nil
}

func (s productDao) FilterCategoryBySupplier(ctx context.Context, saleStatus int, id primitive.ObjectID) ([]model.CategoryOnly, error) {
	filter := bson.M{
		"supplier_id": id,
		"deleted_at":  0,
	}
	if saleStatus != 0 {
		filter["sale"] = false
		if saleStatus == 1 {
			filter["sale"] = true
		}
	}

	opts := options.Find()

	projection := bson.D{
		{"category_ids", 1},
	}

	opts.SetProjection(projection)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var list []model.CategoryOnly

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s productDao) FilterCategory(ctx context.Context, saleStatus int) ([]model.CategoryOnly, error) {
	filter := bson.M{
		"deleted_at": 0,
	}
	if saleStatus != 0 {
		filter["sale"] = false
		if saleStatus == 1 {
			filter["sale"] = true
		}
	}

	opts := options.Find()

	projection := bson.D{
		{"category_ids", 1},
	}

	opts.SetProjection(projection)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var list []model.CategoryOnly

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

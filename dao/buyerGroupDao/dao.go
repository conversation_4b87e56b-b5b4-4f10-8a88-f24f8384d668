package buyerGroupDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 采购商交流群
type DaoInt interface {
	Create(ctx context.Context, data model.BuyerGroup) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.BuyerGroup, error)
	List(ctx context.Context, filter bson.M) ([]model.BuyerGroup, error)
	Delete(ctx context.Context, filter bson.M) error
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type buyerGroupDao struct {
	db *mongo.Collection
}

func (s buyerGroupDao) Create(ctx context.Context, data model.BuyerGroup) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s buyerGroupDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s buyerGroupDao) Get(ctx context.Context, filter bson.M) (model.BuyerGroup, error) {
	var data model.BuyerGroup
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.BuyerGroup{}, err
	}
	return data, nil
}

func (s buyerGroupDao) List(ctx context.Context, filter bson.M) ([]model.BuyerGroup, error) {
	sort := bson.D{
		bson.E{Key: "sort", Value: 1},
	}
	opts := options.Find()
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var list []model.BuyerGroup
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s buyerGroupDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (s buyerGroupDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func NewBuyerGroupDao(collect string) DaoInt {
	return buyerGroupDao{
		db: global.MDB.Collection(collect),
	}
}

package warehouseDao

import (
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Warehouse) error
	Update(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.Warehouse, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Warehouse, int64, error)
	ListCus(ctx context.Context, filter bson.M) ([]model.Warehouse, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type warehouseDao struct {
	db *mongo.Collection
}

func (s warehouseDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

// List 查询
func (s warehouseDao) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Warehouse, int64, error) {
	var list []model.Warehouse
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s warehouseDao) ListCus(ctx context.Context, filter bson.M) ([]model.Warehouse, error) {
	var list []model.Warehouse
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s warehouseDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s warehouseDao) Create(ctx context.Context, data model.Warehouse) error {
	_, err := s.db.InsertOne(context.Background(), data)
	if err != nil {
		return err
	}

	return nil
}

func (s warehouseDao) Get(ctx context.Context, filter bson.M) (model.Warehouse, error) {
	var data model.Warehouse
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Warehouse{}, err
	}
	return data, nil
}

func NewWarehouseDao(collect string) DaoInt {
	return warehouseDao{
		db: global.MDB.Collection(collect),
	}
}

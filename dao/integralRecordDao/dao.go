package integralRecordDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.IntegralRecord) error
	CreateMany(ctx context.Context, list []model.IntegralRecord) error
	DeleteMany(ctx context.Context, filter bson.M) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.IntegralRecord, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralRecord, int64, error)
	Get(ctx context.Context, filter bson.M) (model.IntegralRecord, error)
}

type integralRecordDao struct {
	db *mongo.Collection
}

func NewIntegralRecordDao(collect string) DaoInt {
	return integralRecordDao{
		db: global.MDB.Collection(collect),
	}
}

func (s integralRecordDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralRecord, int64, error) {
	var list []model.IntegralRecord
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s integralRecordDao) Get(ctx context.Context, filter bson.M) (model.IntegralRecord, error) {
	var data model.IntegralRecord
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.IntegralRecord{}, err
	}
	return data, nil
}

func (s integralRecordDao) List(ctx context.Context, filter bson.M) ([]model.IntegralRecord, error) {
	opts := options.Find()

	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	var list []model.IntegralRecord
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s integralRecordDao) Create(ctx context.Context, data model.IntegralRecord) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s integralRecordDao) DeleteMany(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteMany(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s integralRecordDao) CreateMany(ctx context.Context, list []model.IntegralRecord) error {
	var l []interface{}
	for _, record := range list {
		l = append(l, record)
	}
	_, err := s.db.InsertMany(ctx, l)
	if err != nil {
		return err
	}
	return nil
}

func (s integralRecordDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s integralRecordDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}

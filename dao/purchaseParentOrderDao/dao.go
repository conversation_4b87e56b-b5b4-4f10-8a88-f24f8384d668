package purchaseParentOrderDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 支付父单
type DaoInt interface {
	Create(ctx context.Context, data model.PurchaseParentOrder) error
	Get(ctx context.Context, filter bson.M) (model.PurchaseParentOrder, error)
	List(ctx context.Context, filter bson.M) ([]model.PurchaseParentOrder, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.PurchaseParentOrder, int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type purchaseParentOrderDao struct {
	db *mongo.Collection
}

func (s purchaseParentOrderDao) Create(ctx context.Context, data model.PurchaseParentOrder) error {
	_, err := s.db.InsertOne(ctx, data)
	return err
}

func (s purchaseParentOrderDao) Get(ctx context.Context, filter bson.M) (model.PurchaseParentOrder, error) {
	var data model.PurchaseParentOrder
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s purchaseParentOrderDao) List(ctx context.Context, filter bson.M) ([]model.PurchaseParentOrder, error) {
	var list []model.PurchaseParentOrder
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s purchaseParentOrderDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.PurchaseParentOrder, int64, error) {
	var list []model.PurchaseParentOrder
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s purchaseParentOrderDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s purchaseParentOrderDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}
func NewPurchaseParentOrderDao(collect string) DaoInt {
	return purchaseParentOrderDao{
		db: global.MDB.Collection(collect),
	}
}

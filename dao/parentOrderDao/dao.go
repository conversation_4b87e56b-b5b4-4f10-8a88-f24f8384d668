package parentOrderDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 支付父单
type DaoInt interface {
	Create(ctx context.Context, data model.ParentOrder) error
	Get(ctx context.Context, filter bson.M) (model.ParentOrder, error)
	List(ctx context.Context, filter bson.M) ([]model.ParentOrder, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ParentOrder, int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type parentOrderDao struct {
	db *mongo.Collection
}

func (s parentOrderDao) Create(ctx context.Context, data model.ParentOrder) error {
	_, err := s.db.InsertOne(ctx, data)
	return err
}

func (s parentOrderDao) Get(ctx context.Context, filter bson.M) (model.ParentOrder, error) {
	var data model.ParentOrder
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s parentOrderDao) List(ctx context.Context, filter bson.M) ([]model.ParentOrder, error) {
	var list []model.ParentOrder
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s parentOrderDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ParentOrder, int64, error) {
	var list []model.ParentOrder
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s parentOrderDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s parentOrderDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}
func NewParentOrderDao(collect string) DaoInt {
	return parentOrderDao{
		db: global.MDB.Collection(collect),
	}
}

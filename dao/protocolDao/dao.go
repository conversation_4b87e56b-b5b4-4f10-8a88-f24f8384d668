package protocolDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 平台协议
type DaoInt interface {
	Upsert(ctx context.Context, data model.Protocol) error
	DeleteOne(ctx context.Context, id primitive.ObjectID) error
	Update(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.Protocol, error)
	Get(ctx context.Context, filter bson.M) (model.Protocol, error)
}

type protocolDao struct {
	db *mongo.Collection
}

func (s protocolDao) Get(ctx context.Context, filter bson.M) (model.Protocol, error) {
	var data model.Protocol
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Protocol{}, err
	}
	return data, nil
}

func (s protocolDao) List(ctx context.Context, filter bson.M) ([]model.Protocol, error) {
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var list []model.Protocol
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s protocolDao) DeleteOne(ctx context.Context, id primitive.ObjectID) error {
	filter := bson.M{
		"_id": id,
	}
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s protocolDao) Upsert(ctx context.Context, data model.Protocol) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}
	return nil
}

func (s protocolDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func NewProtocolDao(collect string) DaoInt {
	return protocolDao{
		db: global.MDB.Collection(collect),
	}
}

package integralOrderDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.IntegralOrder) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	DeleteOne(ctx context.Context, filter bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.IntegralOrder, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralOrder, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	Get(ctx context.Context, filter bson.M) (model.IntegralOrder, error)
}

type integralOrderDao struct {
	db *mongo.Collection
}

func NewIntegralOrderDao(collect string) DaoInt {
	return integralOrderDao{
		db: global.MDB.Collection(collect),
	}
}

func (s integralOrderDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralOrder, int64, error) {
	var list []model.IntegralOrder
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s integralOrderDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s integralOrderDao) Get(ctx context.Context, filter bson.M) (model.IntegralOrder, error) {
	var data model.IntegralOrder
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.IntegralOrder{}, err
	}
	return data, nil
}

func (s integralOrderDao) List(ctx context.Context, filter bson.M) ([]model.IntegralOrder, error) {
	opts := options.Find()

	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	var list []model.IntegralOrder
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s integralOrderDao) Create(ctx context.Context, data model.IntegralOrder) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s integralOrderDao) DeleteOne(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s integralOrderDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s integralOrderDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}

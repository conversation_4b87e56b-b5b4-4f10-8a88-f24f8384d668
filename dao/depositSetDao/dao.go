package depositSetDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	Create(ctx context.Context, data model.DepositSet) error
	Update(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.DepositSet, error)
	List(ctx context.Context, filter bson.M) ([]model.DepositSet, error)
	Delete(ctx context.Context, filter bson.M) error
}

type depositSetDao struct {
	db *mongo.Collection
}

func (s depositSetDao) Get(ctx context.Context, filter bson.M) (model.DepositSet, error) {
	var i model.DepositSet
	err := s.db.FindOne(ctx, filter).Decode(&i)
	if err != nil {
		return model.DepositSet{}, err
	}
	return i, nil
}

func (s depositSetDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(context.Background(), filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s depositSetDao) List(ctx context.Context, filter bson.M) ([]model.DepositSet, error) {
	var list []model.DepositSet

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s depositSetDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (s depositSetDao) Create(ctx context.Context, data model.DepositSet) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func NewDepositSetDao(collect string) DaoInt {
	return depositSetDao{
		db: global.MDB.Collection(collect),
	}
}

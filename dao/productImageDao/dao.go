package productImageDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// DaoInt 商品镜像DAO接口
type DaoInt interface {
	Create(ctx context.Context, data model.ProductImage) error
	CreateMany(ctx context.Context, data []model.ProductImage) error
	Find(ctx context.Context, filter bson.M) ([]model.ProductImage, error)
	Get(ctx context.Context, filter bson.M) (model.ProductImage, error)
	UpdateOne(ctx context.Context, filter bson.M, update bson.M) error
}

type productImageDao struct {
	db *mongo.Collection
}

func (p productImageDao) Get(ctx context.Context, filter bson.M) (model.ProductImage, error) {
	var data model.ProductImage
	err := p.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ProductImage{}, err
	}
	return data, nil
}

func (p productImageDao) UpdateOne(ctx context.Context, filter bson.M, update bson.M) error {
	res, err := p.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (p productImageDao) Create(ctx context.Context, data model.ProductImage) error {
	res, err := p.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (p productImageDao) CreateMany(ctx context.Context, data []model.ProductImage) error {
	var list []interface{}
	for _, v := range data {
		list = append(list, v)
	}
	res, err := p.db.InsertMany(ctx, list)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (p productImageDao) Find(ctx context.Context, filter bson.M) ([]model.ProductImage, error) {
	var list []model.ProductImage

	cursor, err := p.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func NewProductImageDao(collect string) DaoInt {
	return productImageDao{
		db: global.MDB.Collection(collect),
	}
}

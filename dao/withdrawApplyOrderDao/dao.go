package withdrawApplyOrderDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.WithdrawApplyOrder) error
	Update(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.WithdrawApplyOrder, error)
	Get(ctx context.Context, filter bson.M) (model.WithdrawApplyOrder, error)
	Delete(ctx context.Context, filter bson.M) error
}

type withdrawApplyOrderDao struct {
	db *mongo.Collection
}

func (s withdrawApplyOrderDao) Get(ctx context.Context, filter bson.M) (model.WithdrawApplyOrder, error) {
	var data model.WithdrawApplyOrder
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s withdrawApplyOrderDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(context.Background(), filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s withdrawApplyOrderDao) List(ctx context.Context, filter bson.M) ([]model.WithdrawApplyOrder, error) {
	var list []model.WithdrawApplyOrder
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s withdrawApplyOrderDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (s withdrawApplyOrderDao) Create(ctx context.Context, data model.WithdrawApplyOrder) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func NewWithdrawApplyOrderDao(collect string) DaoInt {
	return withdrawApplyOrderDao{
		db: global.MDB.Collection(collect),
	}
}

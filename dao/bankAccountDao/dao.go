package bankAccountDao

import (
	"base/global"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	//Upsert(ctx context.Context, data model.BankAccount) error
	//GetByObject(objectType int, objectID primitive.ObjectID) (model.BankAccount, error)
}

type bankcardDao struct {
	db *mongo.Collection
}

//
//func (s bankcardDao) GetByObject(objectType int, objectID primitive.ObjectID) (model.BankAccount, error) {
//	var data model.BankAccount
//	filter := bson.M{
//		"object_type": objectType,
//		"object_id":   objectID,
//	}
//	a := objectID.Hex()
//	_ = a
//	err := s.db.FindOne(context.Background(), filter).Decode(&data)
//	if err != nil {
//		return model.BankAccount{}, err
//	}
//	return data, nil
//}
//
//func (s bankcardDao) Upsert(ctx context.Context, data model.BankAccount) error {
//	opts := options.Update().SetUpsert(true)
//	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
//	if err != nil {
//		return err
//	}
//	return nil
//}

func NewBankCardDao(collect string) DaoInt {
	return bankcardDao{
		db: global.MDB.Collection(collect),
	}
}

package topicDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, swipe model.Topic) error
	UpdateInfo(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, id primitive.ObjectID) error
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Topic, int64, error)
	ListByCus(ctx context.Context, filter bson.M) ([]model.Topic, error)
	Get(ctx context.Context, filter bson.M) (model.Topic, error)
}

type topicDao struct {
	db *mongo.Collection
}

func NewTopicDao(collect string) DaoInt {
	return topicDao{
		db: global.MDB.Collection(collect),
	}
}

func (s topicDao) Get(ctx context.Context, filter bson.M) (model.Topic, error) {
	var data model.Topic
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Topic{}, err
	}
	return data, nil
}

func (s topicDao) Create(ctx context.Context, swipe model.Topic) error {
	_, err := s.db.InsertOne(ctx, swipe)
	if err != nil {
		return err
	}

	return nil
}

func (s topicDao) UpdateInfo(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (s topicDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (s topicDao) Delete(ctx context.Context, id primitive.ObjectID) error {
	_, err := s.db.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	return nil
}

func (s topicDao) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Topic, int64, error) {
	var list []model.Topic
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s topicDao) ListByCus(ctx context.Context, filter bson.M) ([]model.Topic, error) {
	var list []model.Topic

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

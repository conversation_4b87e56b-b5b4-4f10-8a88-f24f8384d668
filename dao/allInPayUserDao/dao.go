package allInPayUserDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.AllInPayUser) error
	Update(ctx context.Context, filter, update bson.M) error
	GetByObject(objectType int, objectID primitive.ObjectID) (model.AllInPayUser, error)
	Get(ctx context.Context, filter bson.M) (model.AllInPayUser, error)
}

type allInPayUserDao struct {
	db *mongo.Collection
}

func (s allInPayUserDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s allInPayUserDao) GetByObject(objectType int, objectID primitive.ObjectID) (model.AllInPayUser, error) {
	var data model.AllInPayUser
	filter := bson.M{
		"object_type": objectType,
		"object_id":   objectID,
	}
	err := s.db.FindOne(context.Background(), filter).Decode(&data)
	if err != nil {
		return model.AllInPayUser{}, err
	}
	return data, nil
}

func (s allInPayUserDao) Get(ctx context.Context, filter bson.M) (model.AllInPayUser, error) {
	var data model.AllInPayUser
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.AllInPayUser{}, err
	}
	return data, nil
}

func (s allInPayUserDao) Create(ctx context.Context, data model.AllInPayUser) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}
	return nil
}

func NewAllInPayUserDao(collect string) DaoInt {
	return allInPayUserDao{
		db: global.MDB.Collection(collect),
	}
}

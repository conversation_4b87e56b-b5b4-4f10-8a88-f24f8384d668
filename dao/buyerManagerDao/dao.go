package buyerManagerDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 客户经理
type DaoInt interface {
	Create(ctx context.Context, data model.BuyerManager) error
	DeleteOne(ctx context.Context, filter bson.M) error
	Update(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.BuyerManager, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.BuyerManager, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	Get(ctx context.Context, filter bson.M) (model.BuyerManager, error)
}

type buyerManagerDao struct {
	db *mongo.Collection
}

func NewBuyerManagerDao(collect string) DaoInt {
	return buyerManagerDao{
		db: global.MDB.Collection(collect),
	}
}

func (s buyerManagerDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.BuyerManager, int64, error) {
	var list []model.BuyerManager
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s buyerManagerDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s buyerManagerDao) Get(ctx context.Context, filter bson.M) (model.BuyerManager, error) {
	var data model.BuyerManager
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.BuyerManager{}, err
	}
	return data, nil
}

func (s buyerManagerDao) List(ctx context.Context, filter bson.M) ([]model.BuyerManager, error) {
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	var list []model.BuyerManager
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s buyerManagerDao) DeleteOne(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s buyerManagerDao) Create(ctx context.Context, data model.BuyerManager) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s buyerManagerDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

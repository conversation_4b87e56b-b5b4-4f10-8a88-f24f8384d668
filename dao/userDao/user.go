package userDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type UserDao interface {
	Create(ctx context.Context, user model.User) error
	Update(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.User, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.User, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.User, error)
}

type userDao struct {
	db *mongo.Collection
}

func (s userDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.User, int64, error) {
	var list []model.User
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s userDao) List(ctx context.Context, filter bson.M) ([]model.User, error) {
	var list []model.User
	//skip := (page - 1) * limit
	opts := options.Find()

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s userDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s userDao) Create(ctx context.Context, user model.User) error {
	_, err := s.db.InsertOne(context.Background(), user)
	if err != nil {
		return err
	}

	return nil
}

func (s userDao) Get(ctx context.Context, filter bson.M) (model.User, error) {
	var u model.User
	err := s.db.FindOne(ctx, filter).Decode(&u)
	if err != nil {
		return model.User{}, err
	}
	return u, nil
}

func NewUserDao() UserDao {
	return userDao{
		db: global.MDB.Collection("user"),
	}
}

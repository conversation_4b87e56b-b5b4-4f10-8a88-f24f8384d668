package couponUserDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 优惠券用户DAO接口
type DaoInt interface {
	Create(ctx context.Context, data model.CouponUser) error
	Update(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.CouponUser, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.CouponUser, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	Get(ctx context.Context, filter bson.M) (model.CouponUser, error)
	DeleteOne(ctx context.Context, filter bson.M) error
}

type couponUserDao struct {
	db *mongo.Collection
}

// NewCouponUserDao 创建优惠券用户DAO
func NewCouponUserDao(collect string) DaoInt {
	return couponUserDao{
		db: global.MDB.Collection(collect),
	}
}

func (s couponUserDao) Get(ctx context.Context, filter bson.M) (model.CouponUser, error) {
	var data model.CouponUser
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.CouponUser{}, err
	}

	return data, nil
}

func (s couponUserDao) Create(ctx context.Context, category model.CouponUser) error {
	res, err := s.db.InsertOne(ctx, category)
	if err != nil {
		return err
	}
	_ = res

	return nil
}

func (s couponUserDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s couponUserDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s couponUserDao) List(ctx context.Context, where bson.M) ([]model.CouponUser, error) {
	list := make([]model.CouponUser, 0)
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}

	opts := &options.FindOptions{
		Sort: sort,
	}

	res, err := s.db.Find(ctx, where, opts)
	if err != nil {
		return list, err
	}

	if err = res.All(ctx, &list); err != nil {
		return list, err
	}

	return list, nil
}

func (s couponUserDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.CouponUser, int64, error) {
	var list []model.CouponUser
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s couponUserDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	i, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return i, nil
}

func (s couponUserDao) DeleteOne(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	return err
}

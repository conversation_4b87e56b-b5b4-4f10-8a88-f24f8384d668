package depositRecordDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	Create(ctx context.Context, data model.DepositRecord) error
	List(ctx context.Context, filter bson.M) ([]model.DepositRecord, error)
}

type depositRecordDao struct {
	db *mongo.Collection
}

func (s depositRecordDao) List(ctx context.Context, filter bson.M) ([]model.DepositRecord, error) {
	var list []model.DepositRecord

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s depositRecordDao) Create(ctx context.Context, data model.DepositRecord) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func NewDepositRecordDao(collect string) DaoInt {
	return depositRecordDao{
		db: global.MDB.Collection(collect),
	}
}

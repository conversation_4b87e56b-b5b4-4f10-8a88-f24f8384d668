package brandDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Brand) error
	Update(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.Brand, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Brand, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	Get(ctx context.Context, filter bson.M) (model.Brand, error)
	DeleteOne(ctx context.Context, filter bson.M) error
}

type brandDao struct {
	db *mongo.Collection
}

func NewBrandDao(collect string) DaoInt {
	return brandDao{
		db: global.MDB.Collection(collect),
	}
}

func (s brandDao) Get(ctx context.Context, filter bson.M) (model.Brand, error) {
	var data model.Brand
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Brand{}, err
	}
	return data, nil
}

func (s brandDao) Create(ctx context.Context, address model.Brand) error {
	res, err := s.db.InsertOne(ctx, address)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (s brandDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

func (s brandDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

func (s brandDao) List(ctx context.Context, filter bson.M) ([]model.Brand, error) {
	list := make([]model.Brand, 0)

	res, err := s.db.Find(ctx, filter)
	if err != nil {
		return list, err
	}

	if err = res.All(ctx, &list); err != nil {
		return list, err
	}

	return list, nil
}

// ListByPage 查询
func (s brandDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Brand, int64, error) {
	var list []model.Brand
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s brandDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s brandDao) DeleteOne(ctx context.Context, filter bson.M) error {
	one, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	_ = one

	return nil
}

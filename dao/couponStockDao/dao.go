package couponStockDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 优惠券批次
type DaoInt interface {
	Get(ctx context.Context, filter bson.M) (model.CouponStock, error)
	Create(ctx context.Context, category model.CouponStock) error
	Replace(ctx context.Context, data model.CouponStock) error
	Update(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.CouponStock, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.CouponStock, int64, error)
	DeleteOne(ctx context.Context, filter bson.M) error
}

type dao struct {
	db *mongo.Collection
}

// NewDao 创建优惠券批次dao
func NewDao(collect string) DaoInt {
	return dao{
		db: global.MDB.Collection(collect),
	}
}

func (s dao) Get(ctx context.Context, filter bson.M) (model.CouponStock, error) {
	var data model.CouponStock
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.CouponStock{}, err
	}

	return data, nil
}

func (s dao) Create(ctx context.Context, category model.CouponStock) error {
	res, err := s.db.InsertOne(ctx, category)
	if err != nil {
		return err
	}
	_ = res

	return nil
}

func (s dao) Replace(ctx context.Context, data model.CouponStock) error {
	_, err := s.db.ReplaceOne(ctx, bson.M{"_id": data.ID}, data)
	if err != nil {
		return err
	}
	return nil
}

func (s dao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s dao) List(ctx context.Context, where bson.M) ([]model.CouponStock, error) {
	list := make([]model.CouponStock, 0)
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}

	opts := &options.FindOptions{
		Sort: sort,
	}

	res, err := s.db.Find(ctx, where, opts)
	if err != nil {
		return list, err
	}

	if err = res.All(ctx, &list); err != nil {
		return list, err
	}

	return list, nil
}

func (s dao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.CouponStock, int64, error) {
	var list []model.CouponStock
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}
	return list, count, nil
}

func (s dao) DeleteOne(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	return err
}

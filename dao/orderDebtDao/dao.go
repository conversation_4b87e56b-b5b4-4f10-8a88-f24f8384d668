package orderDebtDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 补差订单
type DaoInt interface {
	Create(ctx context.Context, data model.OrderDebt) error
	CreateMany(ctx context.Context, data []model.OrderDebt) error
	Get(ctx context.Context, filter bson.M) (model.OrderDebt, error)
	List(ctx context.Context, filter bson.M) ([]model.OrderDebt, error)
	ListWithOption(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]model.OrderDebt, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderDebt, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	DeleteOne(ctx context.Context, filter bson.M) error
}

type orderDebtDao struct {
	db *mongo.Collection
}

func (s orderDebtDao) CreateMany(ctx context.Context, list []model.OrderDebt) error {
	data := make([]interface{}, len(list))
	for i, v := range list {
		data[i] = v
	}

	_, err := s.db.InsertMany(ctx, data)
	if err != nil {
		return err
	}

	return err
}

func (s orderDebtDao) Create(ctx context.Context, data model.OrderDebt) error {
	_, err := s.db.InsertOne(ctx, data)

	return err
}

func (s orderDebtDao) Get(ctx context.Context, filter bson.M) (model.OrderDebt, error) {
	var data model.OrderDebt
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s orderDebtDao) List(ctx context.Context, filter bson.M) ([]model.OrderDebt, error) {
	var list []model.OrderDebt
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s orderDebtDao) ListWithOption(ctx context.Context, filter bson.M, opts *options.FindOptions) ([]model.OrderDebt, error) {
	var list []model.OrderDebt
	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s orderDebtDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderDebt, int64, error) {
	var list []model.OrderDebt
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s orderDebtDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s orderDebtDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s orderDebtDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}

func (s orderDebtDao) DeleteOne(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	return err
}

func NewOrderDebtDao(collect string) DaoInt {
	return orderDebtDao{
		db: global.MDB.Collection(collect),
	}
}

package authenticationPersonDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 认证
type DaoInt interface {
	Create(ctx context.Context, data model.AuthenticationPerson) error
	Update(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.AuthenticationPerson, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.AuthenticationPerson, int64, error)
}

type authenticationPersonDao struct {
	db *mongo.Collection
}

func (s authenticationPersonDao) Create(ctx context.Context, data model.AuthenticationPerson) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationPersonDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationPersonDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s authenticationPersonDao) Get(ctx context.Context, filter bson.M) (model.AuthenticationPerson, error) {
	var data model.AuthenticationPerson
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.AuthenticationPerson{}, err
	}
	return data, nil
}

// List 查询
func (s authenticationPersonDao) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.AuthenticationPerson, int64, error) {
	var list []model.AuthenticationPerson
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func NewAuthenticationPersonDao(collect string) DaoInt {
	return authenticationPersonDao{
		db: global.MDB.Collection(collect),
	}
}

package orderAgentPayDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 代付
type DaoInt interface {
	Create(ctx context.Context, data model.OrderAgentPay) error
	CreateMany(ctx context.Context, data []model.OrderAgentPay) error
	Get(ctx context.Context, filter bson.M) (model.OrderAgentPay, error)
	List(ctx context.Context, filter bson.M) ([]model.OrderAgentPay, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderAgentPay, int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type orderAgentPayDao struct {
	db *mongo.Collection
}

func (s orderAgentPayDao) CreateMany(ctx context.Context, list []model.OrderAgentPay) error {
	data := make([]interface{}, len(list))
	for i, v := range list {
		data[i] = v
	}

	_, err := s.db.InsertMany(ctx, data)
	if err != nil {
		return err
	}

	return err
}

func (s orderAgentPayDao) Create(ctx context.Context, data model.OrderAgentPay) error {
	_, err := s.db.InsertOne(ctx, data)

	return err
}

func (s orderAgentPayDao) Get(ctx context.Context, filter bson.M) (model.OrderAgentPay, error) {
	var data model.OrderAgentPay
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s orderAgentPayDao) List(ctx context.Context, filter bson.M) ([]model.OrderAgentPay, error) {
	var list []model.OrderAgentPay
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s orderAgentPayDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderAgentPay, int64, error) {
	var list []model.OrderAgentPay
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s orderAgentPayDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s orderAgentPayDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}

func NewOrderAgentPayDao(collect string) DaoInt {
	return orderAgentPayDao{
		db: global.MDB.Collection(collect),
	}
}

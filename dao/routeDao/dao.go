package routeDao

import (
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Upsert(ctx context.Context, data model.Route) error
	Update(filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.Route, error)
	List(ctx context.Context, filter bson.M) ([]model.Route, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Route, int64, error)
}

type routeDao struct {
	db *mongo.Collection
}

// List 查询
func (s routeDao) List(ctx context.Context, filter bson.M) ([]model.Route, error) {
	var list []model.Route
	//skip := (page - 1) * limit
	opts := options.Find()
	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s routeDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Route, int64, error) {
	var list []model.Route
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s routeDao) Update(filter, update bson.M) error {
	_, err := s.db.UpdateOne(context.Background(), filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s routeDao) Upsert(ctx context.Context, data model.Route) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}
	return nil
}

func (s routeDao) Get(ctx context.Context, filter bson.M) (model.Route, error) {
	var data model.Route
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Route{}, err
	}
	return data, nil
}

func NewRouteDao(collect string) DaoInt {
	return routeDao{
		db: global.MDB.Collection(collect),
	}
}

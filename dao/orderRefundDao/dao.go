package orderRefundDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 补差订单
type DaoInt interface {
	Create(ctx context.Context, data model.OrderRefund) error
	CreateMany(ctx context.Context, data []model.OrderRefund) error
	Get(ctx context.Context, filter bson.M) (model.OrderRefund, error)
	List(ctx context.Context, filter bson.M) ([]model.OrderRefund, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderRefund, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type orderRefundDao struct {
	db *mongo.Collection
}

func (s orderRefundDao) CreateMany(ctx context.Context, list []model.OrderRefund) error {
	data := make([]interface{}, len(list))
	for i, v := range list {
		data[i] = v
	}

	_, err := s.db.InsertMany(ctx, data)
	if err != nil {
		return err
	}

	return err
}

func (s orderRefundDao) Create(ctx context.Context, data model.OrderRefund) error {
	_, err := s.db.InsertOne(ctx, data)

	return err
}

func (s orderRefundDao) Get(ctx context.Context, filter bson.M) (model.OrderRefund, error) {
	var data model.OrderRefund
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s orderRefundDao) List(ctx context.Context, filter bson.M) ([]model.OrderRefund, error) {
	var list []model.OrderRefund
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func (s orderRefundDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderRefund, int64, error) {
	var list []model.OrderRefund
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s orderRefundDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (s orderRefundDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s orderRefundDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}

func NewOrderRefundDao(collect string) DaoInt {
	return orderRefundDao{
		db: global.MDB.Collection(collect),
	}
}

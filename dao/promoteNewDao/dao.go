package promoteNewDao

import (
	"base/global"
	"base/model"
	"context"
	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.PromoteNew) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	DeleteOne(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.PromoteNew, error)
	List(ctx context.Context, filter bson.M) ([]model.PromoteNew, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.PromoteNew, int64, error)
}

type promoteNewDao struct {
	db *mongo.Collection
}

func NewPromoteNewDao(collect string) DaoInt {
	return promoteNewDao{
		db: global.MDB.Collection(collect),
	}
}

// List 查询
func (s promoteNewDao) List(ctx context.Context, filter bson.M) ([]model.PromoteNew, error) {
	var list []model.PromoteNew
	//skip := (page - 1) * limit
	opts := options.Find()
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s promoteNewDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.PromoteNew, int64, error) {
	var list []model.PromoteNew
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s promoteNewDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}
func (s promoteNewDao) DeleteOne(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s promoteNewDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s promoteNewDao) Create(ctx context.Context, data model.PromoteNew) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s promoteNewDao) Get(ctx context.Context, filter bson.M) (model.PromoteNew, error) {
	var data model.PromoteNew
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.PromoteNew{}, err
	}
	return data, nil
}

package versionDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 版本
type DaoInt interface {
	Create(ctx context.Context, data model.Version) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	Update(ctx context.Context, filter, update bson.M) error
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Version, int64, error)
	Get(ctx context.Context, filter bson.M) (model.Version, error)
}

type versionDao struct {
	db *mongo.Collection
}

func (s versionDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Version, int64, error) {
	var list []model.Version
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s versionDao) Get(ctx context.Context, filter bson.M) (model.Version, error) {
	var data model.Version
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Version{}, err
	}
	return data, nil
}

func (s versionDao) Create(ctx context.Context, data model.Version) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s versionDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s versionDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}

func NewVersionDao(collect string) DaoInt {
	return versionDao{
		db: global.MDB.Collection(collect),
	}
}

package integralProductDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.IntegralProduct) error
	Replace(ctx context.Context, data model.IntegralProduct) error
	UpdateOne(ctx context.Context, filter, update bson.M) error
	DeleteOne(ctx context.Context, filter bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.IntegralProduct, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralProduct, int64, error)
	Get(ctx context.Context, filter bson.M) (model.IntegralProduct, error)
}

type integralProductDao struct {
	db *mongo.Collection
}

func NewIntegralProductDao(collect string) DaoInt {
	return integralProductDao{
		db: global.MDB.Collection(collect),
	}
}

func (s integralProductDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.IntegralProduct, int64, error) {
	var list []model.IntegralProduct
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	sort := bson.D{
		//bson.E{Key: "created_at", Value: -1},
		bson.E{Key: "sort", Value: 1},
	}
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s integralProductDao) Get(ctx context.Context, filter bson.M) (model.IntegralProduct, error) {
	var data model.IntegralProduct
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.IntegralProduct{}, err
	}
	return data, nil
}

func (s integralProductDao) List(ctx context.Context, filter bson.M) ([]model.IntegralProduct, error) {
	opts := options.Find()

	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	var list []model.IntegralProduct
	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s integralProductDao) Create(ctx context.Context, data model.IntegralProduct) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s integralProductDao) Replace(ctx context.Context, data model.IntegralProduct) error {
	_, err := s.db.ReplaceOne(ctx, bson.M{"_id": data.ID}, data)
	if err != nil {
		return err
	}
	return nil
}

func (s integralProductDao) DeleteOne(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s integralProductDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s integralProductDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateMany(ctx, filter, update)
	return err
}

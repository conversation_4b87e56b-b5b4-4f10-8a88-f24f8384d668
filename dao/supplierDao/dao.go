package supplierDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Supplier) error
	Update(ctx context.Context, filter, update bson.M) error
	GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.Supplier, error)
	Get(ctx context.Context, filter bson.M) (model.Supplier, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Supplier, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.Supplier, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	UpdateMany(ctx context.Context, filter, update bson.M) error
}

type supplierDao struct {
	db *mongo.Collection
}

func (s supplierDao) Get(ctx context.Context, filter bson.M) (model.Supplier, error) {
	var data model.Supplier
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Supplier{}, err
	}
	return data, nil
}

func (s supplierDao) Create(ctx context.Context, data model.Supplier) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s supplierDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s supplierDao) GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.Supplier, error) {
	var data model.Supplier
	err := s.db.FindOne(ctx, bson.M{"user_id": userID}).Decode(&data)
	if err != nil {
		return model.Supplier{}, err
	}
	return data, nil
}

func (s supplierDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

// ListByPage 查询
func (s supplierDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Supplier, int64, error) {
	var list []model.Supplier
	//skip := (page - 1) * limit
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}

	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s supplierDao) List(ctx context.Context, filter bson.M) ([]model.Supplier, error) {
	var list []model.Supplier
	//skip := (page - 1) * limit
	sort := bson.D{
		bson.E{Key: "created_at", Value: 1},
	}

	opts := options.Find()
	opts.SetSort(sort)

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s supplierDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateMany(ctx, filter, update)
	_ = res
	return err
}

func NewSupplierDao(collect string) DaoInt {
	return supplierDao{
		db: global.MDB.Collection(collect),
	}
}

package servicePointDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.ServicePoint) error
	Update(ctx context.Context, filter, update bson.M) error
	GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.ServicePoint, error)
	Get(ctx context.Context, filter bson.M) (model.ServicePoint, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ServicePoint, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.ServicePoint, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
}

type servicePointDao struct {
	db *mongo.Collection
}

func (s servicePointDao) Get(ctx context.Context, filter bson.M) (model.ServicePoint, error) {
	var data model.ServicePoint
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ServicePoint{}, err
	}
	return data, nil
}

func (s servicePointDao) Create(ctx context.Context, data model.ServicePoint) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}

	return nil
}

func (s servicePointDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s servicePointDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s servicePointDao) GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.ServicePoint, error) {
	var data model.ServicePoint
	err := s.db.FindOne(ctx, bson.M{"user_id": userID}).Decode(&data)
	if err != nil {
		return model.ServicePoint{}, err
	}
	return data, nil
}

// List 查询
func (s servicePointDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.ServicePoint, int64, error) {
	var list []model.ServicePoint
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

// List 查询
func (s servicePointDao) List(ctx context.Context, filter bson.M) ([]model.ServicePoint, error) {
	var list []model.ServicePoint
	//skip := (page - 1) * limit
	opts := options.Find()

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}
func NewServicePointDao(collect string) DaoInt {
	return servicePointDao{
		db: global.MDB.Collection(collect),
	}
}

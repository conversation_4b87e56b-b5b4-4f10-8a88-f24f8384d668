package depositAccountDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	Create(ctx context.Context, data model.DepositAccount) error
	Update(ctx context.Context, filter, update bson.M) error
	Get(ctx context.Context, filter bson.M) (model.DepositAccount, error)
	List(ctx context.Context, filter bson.M) ([]model.DepositAccount, error)
	Delete(ctx context.Context, filter bson.M) error
}

type depositAccountDao struct {
	db *mongo.Collection
}

func (s depositAccountDao) Get(ctx context.Context, filter bson.M) (model.DepositAccount, error) {
	var data model.DepositAccount
	err := s.db.FindOne(ctx, filter).Decode(&data)
	return data, err
}

func (s depositAccountDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(context.Background(), filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s depositAccountDao) List(ctx context.Context, filter bson.M) ([]model.DepositAccount, error) {
	var list []model.DepositAccount

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s depositAccountDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(context.Background(), filter)
	if err != nil {
		return err
	}
	return nil
}

func (s depositAccountDao) Create(ctx context.Context, data model.DepositAccount) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func NewDepositAccountDao(collect string) DaoInt {
	return depositAccountDao{
		db: global.MDB.Collection(collect),
	}
}

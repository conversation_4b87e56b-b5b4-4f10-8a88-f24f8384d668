package userAddrDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type UserAddrDaoInt interface {
	Create(ctx context.Context, address model.Address) error
	Update(ctx context.Context, address model.Address) error
	UpdateCus(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	GetByUserID(ctx context.Context, userId primitive.ObjectID) ([]model.Address, error)
	List(ctx context.Context, filter bson.M) ([]model.Address, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Address, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	Get(ctx context.Context, filter bson.M) (model.Address, error)
	Del(ctx context.Context, id primitive.ObjectID) error
	DelByID(ctx context.Context, id primitive.ObjectID) error
}

type userAddrDao struct {
	db *mongo.Collection
}

func NewUserAddrDao(collect string) UserAddrDaoInt {
	return userAddrDao{
		db: global.MDB.Collection(collect),
	}
}

func (u userAddrDao) Get(ctx context.Context, filter bson.M) (model.Address, error) {
	var data model.Address
	err := u.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Address{}, err
	}
	return data, nil
}

func (u userAddrDao) Create(ctx context.Context, address model.Address) error {
	res, err := u.db.InsertOne(ctx, address)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

func (u userAddrDao) Update(ctx context.Context, address model.Address) error {
	_, err := u.db.UpdateOne(ctx, bson.M{"user_id": address.UserID, "_id": address.ID}, bson.M{"$set": address})
	if err != nil {
		return err
	}

	return nil
}

func (u userAddrDao) UpdateCus(ctx context.Context, filter, update bson.M) error {
	re, err := u.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = re

	return nil
}

func (u userAddrDao) UpdateMany(ctx context.Context, filter, update bson.M) error {
	_, err := u.db.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}

	return nil
}

func (u userAddrDao) GetByUserID(ctx context.Context, userId primitive.ObjectID) ([]model.Address, error) {
	list := make([]model.Address, 0)

	res, err := u.db.Find(ctx, bson.M{"user_id": userId})
	if err != nil {
		return list, err
	}

	if err = res.All(ctx, &list); err != nil {
		return list, err
	}

	return list, nil
}
func (u userAddrDao) List(ctx context.Context, filter bson.M) ([]model.Address, error) {
	list := make([]model.Address, 0)

	res, err := u.db.Find(ctx, filter)
	if err != nil {
		return list, err
	}

	if err = res.All(ctx, &list); err != nil {
		return list, err
	}

	return list, nil
}

// ListByPage 查询
func (s userAddrDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Address, int64, error) {
	var list []model.Address
	//skip := (page - 1) * limit
	sort := bson.D{
		bson.E{Key: "created_at", Value: -1},
	}

	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)
	opts.SetSort(sort)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func (s userAddrDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (u userAddrDao) Del(ctx context.Context, id primitive.ObjectID) error {
	_, err := u.db.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	return nil
}

func (u userAddrDao) DelByID(ctx context.Context, id primitive.ObjectID) error {
	_, err := u.db.DeleteOne(ctx, bson.M{"_id": id})
	if err != nil {
		return err
	}

	return nil
}

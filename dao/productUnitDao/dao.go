package productUnitDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.ProductUnit) error
	Upsert(ctx context.Context, data model.ProductUnit) error
	Update(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.ProductUnit, error)
	List(ctx context.Context, filter bson.M) ([]model.ProductUnit, error)
}

type productUnitDao struct {
	db *mongo.Collection
}

func (s productUnitDao) Upsert(ctx context.Context, data model.ProductUnit) error {
	opts := options.Update().SetUpsert(true)
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": data.ID}, bson.M{"$set": data}, opts)
	if err != nil {
		return err
	}

	return nil
}

func (s productUnitDao) Create(ctx context.Context, data model.ProductUnit) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s productUnitDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s productUnitDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s productUnitDao) Get(ctx context.Context, filter bson.M) (model.ProductUnit, error) {
	var data model.ProductUnit
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.ProductUnit{}, err
	}
	return data, nil
}

// List 查询
func (s productUnitDao) List(ctx context.Context, filter bson.M) ([]model.ProductUnit, error) {
	var list []model.ProductUnit

	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func NewProductUnitDao(collect string) DaoInt {
	return productUnitDao{
		db: global.MDB.Collection(collect),
	}
}

package auditDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Audit) error
	Update(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.Audit, error)
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Audit, int64, error)
}

type auditDao struct {
	db *mongo.Collection
}

func (s auditDao) Create(ctx context.Context, data model.Audit) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

func (s auditDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s auditDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

func (s auditDao) Get(ctx context.Context, filter bson.M) (model.Audit, error) {
	var data model.Audit
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Audit{}, err
	}
	return data, nil
}

// List 查询
func (s auditDao) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Audit, int64, error) {
	var list []model.Audit
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

func NewAuditDao(collect string) DaoInt {
	return auditDao{
		db: global.MDB.Collection(collect),
	}
}

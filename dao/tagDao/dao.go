package tagDao

import (
	"base/global"
	"base/model"
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

type DaoInt interface {
	Create(ctx context.Context, data model.Tag) error
	Delete(ctx context.Context, ids []primitive.ObjectID) error
	Update(ctx context.Context, filter, update bson.M) error
	List(ctx context.Context, filter bson.M) ([]model.Tag, error)
}

type tagDao struct {
	db *mongo.Collection
}

func (s tagDao) Create(ctx context.Context, data model.Tag) error {
	_, err := s.db.InsertOne(ctx, data)
	return err
}

func (s tagDao) Delete(ctx context.Context, ids []primitive.ObjectID) error {
	if len(ids) < 1 {
		return nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}
	_, err := s.db.DeleteMany(ctx, filter)
	return err
}

func (s tagDao) Update(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s tagDao) List(ctx context.Context, filter bson.M) ([]model.Tag, error) {
	var list []model.Tag
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}
	return list, err
}

func NewTagDao(collect string) DaoInt {
	return tagDao{
		db: global.MDB.Collection(collect),
	}
}

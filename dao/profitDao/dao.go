package profitDao

import (
	"base/global"
	"base/model"
	"context"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 利润DAO接口
type DaoInt interface {
	Create(ctx context.Context, data model.Profit) error
	Update(ctx context.Context, filter, update bson.M) error
	Delete(ctx context.Context, filter bson.M) error
	Get(ctx context.Context, filter bson.M) (model.Profit, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Profit, int64, error)
	List(ctx context.Context, filter bson.M) ([]model.Profit, error)
}

type profitDao struct {
	db *mongo.Collection
}

// Create 创建
func (s profitDao) Create(ctx context.Context, data model.Profit) error {
	_, err := s.db.InsertOne(ctx, data)
	if err != nil {
		return err
	}
	return nil
}

// Update 更新
func (s profitDao) Update(ctx context.Context, filter, update bson.M) error {
	res, err := s.db.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	_ = res
	return nil
}

// Delete 删除
func (s profitDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	if err != nil {
		return err
	}
	return nil
}

// Get 查询
func (s profitDao) Get(ctx context.Context, filter bson.M) (model.Profit, error) {
	var data model.Profit
	err := s.db.FindOne(ctx, filter).Decode(&data)
	if err != nil {
		return model.Profit{}, err
	}
	return data, nil
}

// ListByPage 查询
func (s profitDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.Profit, int64, error) {
	var list []model.Profit
	//skip := (page - 1) * limit
	opts := options.Find().SetSkip((page - 1) * limit).SetLimit(limit)

	count, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, 0, err
	}

	return list, count, nil
}

// List 查询
func (s profitDao) List(ctx context.Context, filter bson.M) ([]model.Profit, error) {
	var list []model.Profit
	opts := options.Find()

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(context.Background())

	err = cursor.All(ctx, &list)
	if err != nil {
		return nil, err
	}

	return list, nil
}

// NewProfitDao 创建利润DAO
func NewProfitDao(collect string) DaoInt {
	return profitDao{
		db: global.MDB.Collection(collect),
	}
}

package orderAdjustSettleDao

import (
	"base/global"
	"base/model"
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// DaoInt 订单调整结算记录
type DaoInt interface {
	Create(ctx context.Context, data model.OrderAdjustSettle) error
	Get(ctx context.Context, filter bson.M) (model.OrderAdjustSettle, error)
	GetByID(ctx context.Context, id primitive.ObjectID) (model.OrderAdjustSettle, error)
	List(ctx context.Context, filter bson.M) ([]model.OrderAdjustSettle, error)
	ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderAdjustSettle, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateByID(ctx context.Context, id primitive.ObjectID, update bson.M) error
	UpdateStatus(ctx context.Context, id primitive.ObjectID, status model.OrderAdjustSettleStatus) error
	Delete(ctx context.Context, filter bson.M) error
	SoftDelete(ctx context.Context, id primitive.ObjectID) error
	GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.OrderAdjustSettle, error)
	ListByOrderID(ctx context.Context, orderID primitive.ObjectID) ([]model.OrderAdjustSettle, error)
}

type orderAdjustSettleDao struct {
	db *mongo.Collection
}

func (s orderAdjustSettleDao) Create(ctx context.Context, data model.OrderAdjustSettle) error {
	_, err := s.db.InsertOne(ctx, data)
	return err
}

func (s orderAdjustSettleDao) Get(ctx context.Context, filter bson.M) (model.OrderAdjustSettle, error) {
	var result model.OrderAdjustSettle
	err := s.db.FindOne(ctx, filter).Decode(&result)
	return result, err
}

func (s orderAdjustSettleDao) GetByID(ctx context.Context, id primitive.ObjectID) (model.OrderAdjustSettle, error) {
	filter := bson.M{
		"_id":        id,
		"deleted_at": 0,
	}
	return s.Get(ctx, filter)
}

func (s orderAdjustSettleDao) List(ctx context.Context, filter bson.M) ([]model.OrderAdjustSettle, error) {
	var results []model.OrderAdjustSettle
	cursor, err := s.db.Find(ctx, filter)
	if err != nil {
		return results, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &results)
	return results, err
}

func (s orderAdjustSettleDao) ListByPage(ctx context.Context, filter bson.M, page, limit int64) ([]model.OrderAdjustSettle, int64, error) {
	var results []model.OrderAdjustSettle

	// 计算总数
	total, err := s.db.CountDocuments(ctx, filter)
	if err != nil {
		return results, 0, err
	}

	// 分页查询
	opts := options.Find()
	opts.SetSkip((page - 1) * limit)
	opts.SetLimit(limit)
	opts.SetSort(bson.D{{"created_at", -1}})

	cursor, err := s.db.Find(ctx, filter, opts)
	if err != nil {
		return results, total, err
	}
	defer cursor.Close(ctx)

	err = cursor.All(ctx, &results)
	return results, total, err
}

func (s orderAdjustSettleDao) Count(ctx context.Context, filter bson.M) (int64, error) {
	return s.db.CountDocuments(ctx, filter)
}

func (s orderAdjustSettleDao) UpdateOne(ctx context.Context, filter, update bson.M) error {
	_, err := s.db.UpdateOne(ctx, filter, update)
	return err
}

func (s orderAdjustSettleDao) UpdateByID(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	filter := bson.M{
		"_id":        id,
		"deleted_at": 0,
	}
	return s.UpdateOne(ctx, filter, update)
}

func (s orderAdjustSettleDao) UpdateStatus(ctx context.Context, id primitive.ObjectID, status model.OrderAdjustSettleStatus) error {
	update := bson.M{
		"$set": bson.M{
			"status": status,
		},
	}
	return s.UpdateByID(ctx, id, update)
}

func (s orderAdjustSettleDao) Delete(ctx context.Context, filter bson.M) error {
	_, err := s.db.DeleteOne(ctx, filter)
	return err
}

func (s orderAdjustSettleDao) SoftDelete(ctx context.Context, id primitive.ObjectID) error {
	update := bson.M{
		"$set": bson.M{
			"deleted_at": time.Now().UnixMilli(),
		},
	}
	return s.UpdateByID(ctx, id, update)
}

func (s orderAdjustSettleDao) GetByOrderID(ctx context.Context, orderID primitive.ObjectID) (model.OrderAdjustSettle, error) {
	filter := bson.M{
		"order_id":   orderID,
		"deleted_at": 0,
	}
	return s.Get(ctx, filter)
}

func (s orderAdjustSettleDao) ListByOrderID(ctx context.Context, orderID primitive.ObjectID) ([]model.OrderAdjustSettle, error) {
	filter := bson.M{
		"order_id":   orderID,
		"deleted_at": 0,
	}
	return s.List(ctx, filter)
}

func NewDao() DaoInt {
	return &orderAdjustSettleDao{
		db: global.MDB.Collection("order_adjust_settle"),
	}
}

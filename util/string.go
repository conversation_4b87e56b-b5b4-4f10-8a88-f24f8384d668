package util

import (
	"encoding/base64"
	"encoding/json"
	"strings"

	"go.uber.org/zap"
)

func SubString(str string, begin, length int) string {
	rs := []rune(str)
	lth := len(rs)
	if begin < 0 {
		begin = 0
	}
	if begin >= lth {
		begin = lth
	}
	end := begin + length

	if end > lth {
		end = lth
	}
	return string(rs[begin:end])
}

// DealWrap 处理换行符和空格
func DealWrap(s string) string {
	trim := strings.ReplaceAll(s, "\\n", "")

	trim = strings.TrimSpace(trim)

	return trim
}

func DecodeMNSContent(content string, data any) error {
	decodeString, err := base64.StdEncoding.DecodeString(content)
	if err != nil {
		zap.S().<PERSON><PERSON><PERSON>("decodeContent,内容:%s，err:%v", content, err)
		return err
	}
	err = json.Unmarshal(decodeString, &data)
	if err != nil {
		return err
	}
	return nil
}

func DecodeMNSInfo(messageID, content string, data any) error {
	decodeString, err := base64.StdEncoding.DecodeString(content)
	if err != nil {
		zap.S().<PERSON><PERSON><PERSON>("decodeContent,内容:%s，err:%v", content, err)
		return err
	}
	err = json.Unmarshal(decodeString, &data)
	if err != nil {
		return err
	}
	zap.S().Infof("接收,消息ID:%s\",消息内容:%s", messageID, string(decodeString))
	return nil
}

func DealMobile(mobile string) string {
	if len(mobile) != 11 {
		return ""
	}
	return mobile[:3] + "****" + mobile[7:]
}

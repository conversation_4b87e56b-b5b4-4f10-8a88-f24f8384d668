package util

import (
	"encoding/json"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
	"go.uber.org/zap"
)

type Captcha struct {
	Code string `json:"code"`
}

type MessageUtil struct {
	accessKeyId     string
	accessKeySecret string
	endPoint        string
	signName        string
	config          *openapi.Config
	client          *dysmsapi20170525.Client
	err             error
}

func NewMessage(accessKeyId, accessKeySecret, endPoint string) *MessageUtil {
	msg := &MessageUtil{
		accessKeyId:     accessKeyId,
		accessKeySecret: accessKeySecret,
		endPoint:        endPoint,
		signName:        "果蔬团",
		config: &openapi.Config{
			AccessKeyId:     tea.String(accessKeyId),
			AccessKeySecret: tea.String(accessKeySecret),
			Endpoint:        tea.String(endPoint),
		},
	}

	msg.client, msg.err = dysmsapi20170525.NewClient(msg.config)
	return msg
}

func (m *MessageUtil) Error() error {
	return m.err
}

// SendCaptcha 发送验证码
func (m *MessageUtil) SendCaptcha(mobile, code string) error {
	//return m.SendMsg(mobile, "SMS_483300556", map[string]interface{}{"code": code})
	return m.SendMsg(mobile, "SMS_475135218", map[string]interface{}{"code": code})
	//return m.SendMsg(mobile, "SMS_482750173", map[string]interface{}{"code": code})
}

// SendNotify 发送通知
func (m *MessageUtil) SendNotify(mobile, role, status string) error {
	return m.SendMsg(mobile, "SMS_475810081", map[string]interface{}{
		"phone":  mobile,
		"role":   role,
		"status": status,
	})
}

// SendWarning 发送预警
func (m *MessageUtil) SendWarning(mobile, eventType, content string) error {
	// 预警
	return m.SendMsg(mobile, "SMS_475660185", map[string]interface{}{
		"type":    eventType,
		"content": content,
	})
}

func (m *MessageUtil) SendNotifyBalance(mobile string) error {
	return m.SendMsg(mobile, "SMS_475945386", nil)
}

func (m *MessageUtil) SendNotifyOrderEnd(mobile string) error {
	return m.SendMsg(mobile, "SMS_478520422", nil)
}

// SendNotifyAuditProductOffline 商品下架审核通知
func (m *MessageUtil) SendNotifyAuditProductOffline(mobile, productTitle, status string) error {
	return m.SendMsg(mobile, "SMS_488300040", map[string]interface{}{
		"phone":   mobile,
		"product": productTitle,
		"status":  status,
	})
}

// SendNotifyAuditProductEdit 商品编辑审核通知
func (m *MessageUtil) SendNotifyAuditProductEdit(mobile, productTitle, status string) error {
	return m.SendMsg(mobile, "SMS_488425042", map[string]interface{}{
		"phone":   mobile,
		"product": productTitle,
		"status":  status,
	})
}

func (m *MessageUtil) SendMsg(mobile, code string, msg map[string]interface{}) error {
	if m.Error() != nil {
		return m.err
	}

	zap.S().Infof("发送短信：%s", mobile)
	zap.S().Infof("发送短信 code：%s", code)

	// 序列化
	msgB, err := json.Marshal(msg)
	if err != nil {
		return err
	}

	// 发送短验证码请求
	req := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  tea.String(mobile),
		SignName:      tea.String(m.signName),
		TemplateCode:  tea.String(code),
		TemplateParam: tea.String(string(msgB)),
	}

	// 获取发送时错误
	err = func() (err error) {
		defer func() {
			if _e := tea.Recover(recover()); _e != nil {
				err = _e
				zap.S().Error(_e)
			}
		}()

		res, err := m.client.SendSmsWithOptions(req, &util.RuntimeOptions{})
		if err != nil {
			return err
		}
		marshal, err := json.Marshal(res.Body)
		if err != nil {
			return err
		}
		zap.S().Infof("发送短信：%s", string(marshal))

		return nil
	}()

	return err
}

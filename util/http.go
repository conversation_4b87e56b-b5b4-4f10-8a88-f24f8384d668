package util

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
)

func DoHttp(url string, method string, headers map[string]string, body map[string]interface{}) ([]byte, error) {
	client := &http.Client{}

	marshal, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(method, url, bytes.NewReader(marshal))
	if err != nil {
		return nil, err
	}

	// header
	for key, header := range headers {
		req.Header.Set(key, header)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	res, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return res, nil
}

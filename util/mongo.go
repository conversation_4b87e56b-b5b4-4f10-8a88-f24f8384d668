package util

import (
	"base/core/xerr"
	"context"
	"fmt"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"go.uber.org/zap"
	"time"
)

func MongoInit(opts *options.ClientOptions) (*mongo.Client, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	client, err := mongo.Connect(ctx, opts)
	if err != nil {
		return nil, err
	}
	if err = client.Ping(context.TODO(), readpref.Primary()); err != nil {
		return nil, err
	}
	return client, nil
}

// ConvertToObject 转换为primitive.ObjectID
func ConvertToObject(id string) (primitive.ObjectID, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return primitive.NilObjectID, xerr.NewErr(xerr.ErrParamError, nil, "参数错误："+err.Error())
	}
	return objectID, nil
}

func ConvertToObjectWithNote(id, note string) (primitive.ObjectID, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		zap.S().Errorf("ConvertToObjectWithNote 转换错误%v", err.Error())
		return primitive.NilObjectID, xerr.NewErr(xerr.ErrParamError, nil, fmt.Sprintf("参数：%s转换objectID错误：%v", note, err.Error()))
	}
	return objectID, nil
}

func ConvertToObjectWithCtx(ctx context.Context, id string) (primitive.ObjectID, error) {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		rid := ctx.Value("rid")
		zap.S().Errorf("ConvertToObjectWithCtx 转换错误,ctx-rid:%s,参数：%s,异常：%s", rid, id, err.Error())
		return primitive.NilObjectID, xerr.NewErr(xerr.ErrParamError, nil)
	}
	return objectID, nil
}

package util

import (
	"context"
	"errors"
	clientv3 "go.etcd.io/etcd/client/v3"
	"time"
)

func ConnEtcd(conf clientv3.Config) (*clientv3.Client, error) {
	client, err := clientv3.New(conf)
	if err != nil {
		return nil, errors.New("etcd连接失败" + err.<PERSON>rror())
	}
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	_, err = client.Status(timeoutCtx, conf.Endpoints[0])
	if err != nil {
		return nil, errors.New("etcd连接失败：" + err.Error())
	}
	return client, nil
}

package util

import (
	"encoding/base64"
	"net/http"
)

func ToBase64(imgByte []byte) string {
	var baseImg string
	mimeType := http.DetectContentType(imgByte)
	switch mimeType {
	case "image/jpeg":
		//baseImg = "data:image/jpeg;base64," + base64.StdEncoding.EncodeToString(imgByte)
		baseImg = base64.StdEncoding.EncodeToString(imgByte)
	case "image/png":
		//baseImg = "data:image/png;base64," + base64.StdEncoding.EncodeToString(imgByte)
		baseImg = base64.StdEncoding.EncodeToString(imgByte)
	}

	return baseImg
}

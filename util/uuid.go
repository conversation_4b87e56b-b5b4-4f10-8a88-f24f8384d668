package util

import (
	"github.com/google/uuid"
	"math/rand"
	"strings"
	"time"
)

func NewUUID() string {
	guid := uuid.New()
	return guid.String()
}

func NewUUIDNum() string {
	guid := uuid.New()

	s := guid.String()
	all := strings.ReplaceAll(s, "-", "")
	return all
}

func RangeRandom(min, max int) (number int) {
	//创建随机种子
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	number = r.Intn(max-min) + min
	return number
}

package util

import (
	"base/core/xerr"
	"github.com/golang-module/carbon/v2"
	"time"
)

func ExpireBuyerTime() int64 {
	t := time.Now().Add(time.Hour * 24 * 30).UnixMilli()
	return t
}

func ExpirePayOrderTime(minute int64) string {
	// 支付订单1小时过期
	// 1小时
	add := time.Now().Add(time.Minute * time.Duration(minute))
	return add.Format("2006-01-02 15:04:05")
}

func ExpirePayOrderTimeAT(minute int64) int64 {
	// 支付订单1小时过期
	// 1小时
	add := time.Now().Add(time.Minute * time.Duration(minute))
	return add.UnixMilli()
}

// DayStartZeroTimestamp 天开始时间零点
func DayStartZeroTimestamp(t int64) (int64, error) {
	milli := carbon.CreateFromTimestampMilli(t)

	// 天 时间戳
	ts := milli.SetHour(0).SetMinute(0).SetSecond(0).SetMillisecond(0)
	if ts.TimestampMilli() < 1 {
		return 0, xerr.NewErr(xerr.ErrParamError, nil, "时间戳格式错误")
	}

	return ts.Carbon2Time().UnixMilli(), nil
}

// DayStartTimestamp 天开始时间
func DayStartTimestamp(t int64) (int64, error) {
	milli := carbon.CreateFromTimestampMilli(t)

	// 天 时间戳
	ts := milli.SetHour(8).SetMinute(0).SetSecond(0).SetMillisecond(0)
	if ts.TimestampMilli() < 1 {
		return 0, xerr.NewErr(xerr.ErrParamError, nil, "时间戳格式错误")
	}

	return ts.Carbon2Time().UnixMilli(), nil
}

// DayEndTimestamp 天结束时间
func DayEndTimestamp(t int64) (int64, error) {
	milli := carbon.CreateFromTimestampMilli(t)

	// 天 时间戳
	ts := milli.SetHour(23).SetMinute(59).SetSecond(59).SetMillisecond(999)
	if ts.TimestampMilli() < 1 {
		return 0, xerr.NewErr(xerr.ErrParamError, nil, "时间戳格式错误")
	}

	return ts.Carbon2Time().UnixMilli(), nil
}

func StartOfDayTimestamp(t int64) (int64, error) {
	milli := carbon.CreateFromTimestampMilli(t)
	timestampMilli := milli.StartOfDay().TimestampMilli()
	return timestampMilli, nil
}

func StartOfHourTimestamp(t int64) (int64, error) {
	milli := carbon.CreateFromTimestampMilli(t)
	timestampMilli := milli.StartOfHour().TimestampMilli()
	return timestampMilli, nil
}

func DayScopeTimestamp(t int64) (int64, int64, error) {
	if t == 0 {
		return 0, 0, xerr.NewErr(xerr.ErrParamError, nil, "时间戳错误")
	}
	now := time.UnixMilli(t)
	location, _ := time.LoadLocation("Local")
	begin := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, location)
	end := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999, location)
	return begin.UnixMilli(), end.UnixMilli(), nil
}

func MonthScopeTimestamp(t int64) (int64, int64, error) {
	if t == 0 {
		return 0, 0, xerr.NewErr(xerr.ErrParamError, nil, "时间戳错误")
	}
	now := time.UnixMilli(t)
	location, _ := time.LoadLocation("Local")
	begin := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, location)
	end := time.Date(now.Year(), now.Month()+1, 0, 23, 59, 59, 999, location)
	return begin.UnixMilli(), end.UnixMilli(), nil
}

// MonthStartTimestamp 获取月份的初始化时间戳（月初0点）
func MonthStartTimestamp(t int64) (int64, error) {
	if t == 0 {
		return 0, xerr.NewErr(xerr.ErrParamError, nil, "时间戳错误")
	}
	now := time.UnixMilli(t)
	location, _ := time.LoadLocation("Local")
	begin := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, location)
	return begin.UnixMilli(), nil
}

//
//func DealTimestamp(t int64) (int64, error) {
//	milli := carbon.CreateFromTimestampMilli(t)
//
//	// 天 时间戳
//	ts := milli.SetHour(0).SetMinute(0).SetSecond(0).SetMillisecond(0)
//	if ts.TimestampMilli() < 1 {
//		return 0, xerr.NewErr(xerr.ErrParamError, nil, "时间戳格式错误")
//	}
//	return ts.TimestampMilli(), nil
//}
//
//
//
//
//func DealTimestamp(t int64) (int64, error) {
//	milli := carbon.CreateFromTimestampMilli(t)
//
//	// 天 时间戳
//	ts := milli.SetHour(0).SetMinute(0).SetSecond(0).SetMillisecond(0)
//	if ts.TimestampMilli() < 1 {
//		return 0, xerr.NewErr(xerr.ErrParamError, nil, "时间戳格式错误")
//	}
//	return ts.TimestampMilli(), nil
//}
//

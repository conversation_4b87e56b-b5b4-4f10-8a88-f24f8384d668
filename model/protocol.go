package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Protocol 协议
type Protocol struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	Type      string             `json:"type" bson:"type"` // buyer 采购商 supplier 供应商
	Content   string             `json:"content" bson:"content"`
	CreatedAt int64              `json:"created_at" bson:"created_at"`
	UpdatedAt int64              `json:"updated_at" bson:"updated_at"`
	DeletedAt int64              `json:"deleted_at" bson:"deleted_at"`
}

package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// DeliveryNote 配送清单
type DeliveryNote struct {
	ID          primitive.ObjectID `json:"id" bson:"_id"`
	BuyerID     primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`         // 采购商ID
	FilePath    string             `json:"file_path" bson:"file_path"`       // 文件路径
	Remark      string             `json:"remark" bson:"remark"`             // 备注
	BeginAt     int64              `json:"begin_at" bson:"begin_at"`         // 订单区间
	EndAt       int64              `json:"end_at" bson:"end_at"`             // 订单区间
	DayAt       int64              `json:"day_at" bson:"day_at"`             // 配送单日期
	DeliverType DeliverType        `json:"deliver_type" bson:"deliver_type"` // 配送方式
	CreatedAt   int64              `json:"created_at" bson:"created_at"`     // 创建时间
	UpdatedAt   int64              `json:"updated_at" bson:"updated_at"`     // 更新时间
	DeletedAt   int64              `json:"deleted_at" bson:"deleted_at"`     // 删除时间
}

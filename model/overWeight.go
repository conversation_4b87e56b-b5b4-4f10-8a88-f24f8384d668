package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type OverWeight struct {
	BuyerID        primitive.ObjectID `json:"buyer_id"`
	BuyerName      string             `json:"buyer_name"`
	OrderID        primitive.ObjectID `json:"order_id"`
	SupplierID     primitive.ObjectID `json:"supplier_id"`
	SupplierName   string             `json:"supplier_name"`
	ProductID      primitive.ObjectID `json:"product_id"`
	ProductTitle   string             `json:"product_title"`
	Num            int                `json:"num"`
	SortNum        int                `json:"sort_num"`
	RoughWeight    int                `json:"rough_weight"`
	DueWeight      int                `json:"due_weight"`
	SortWeight     int                `json:"sort_weight"`
	OverWeight     int                `json:"over_weight"`
	StockUpDayTime int64              `json:"stock_up_day_time"`
	CreatedAt      int64              `bson:"created_at" json:"created_at"`
}

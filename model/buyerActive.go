package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// BuyerActive 采购商活跃
type BuyerActive struct {
	ID        primitive.ObjectID `bson:"_id" json:"id"`
	BuyerID   primitive.ObjectID `bson:"buyer_id" json:"buyer_id"` // 采购商ID
	IsPass    bool               `json:"is_pass" bson:"is_pass"`   // 是否通过
	IsAudit   bool               `json:"is_audit" bson:"is_audit"` // 是否审核
	CreatedAt int64              `bson:"created_at" json:"created_at"`
	UpdatedAt int64              `bson:"updated_at" json:"updated_at"`
}

package model

//
//// 企业会员
//
//// Entity2 主体信息
//type Entity2 struct {
//	ID                           primitive.ObjectID `json:"id" bson:"_id"`
//	ObjectType                   ObjectType         `json:"object_type" bson:"object_type"`                                         // 对象类型
//	ObjectID                     primitive.ObjectID `json:"object_id" bson:"object_id"`                                             // 对象ID
//	EntityType                   EntityType         `json:"entity_type" bson:"entity_type"`                                         // 类型
//	EntityName                   string             `json:"entity_name" bson:"entity_name"`                                         // 主体名称
//	CreditCode                   string             `json:"credit_code" bson:"credit_code"`                                         // 统一社会信用代码
//	BusinessLicenseValidTo       string             `json:"business_license_valid_to" bson:"business_license_valid_to"`             // 营业执照有效期    ---不需要
//	BusinessLicenseImg           FileInfo           `json:"business_license_img" bson:"business_license_img"`                       // 营业执照图
//	BusinessLicenseElectronicImg FileInfo           `json:"business_license_electronic_img" bson:"business_license_electronic_img"` // 营业执照电子版图  ---不需要
//	BusinessLicense              BusinessLicense    `json:"business_license" bson:"business_license"`
//	Legal                        Legal              `json:"legal" bson:"legal"`
//	CreatedAt                    int64              `bson:"created_at" json:"created_at"`
//	UpdatedAt                    int64              `bson:"updated_at" json:"updated_at"`
//	DeletedAt                    int64              `bson:"deleted_at" json:"deleted_at"`
//}
//
//// Legal 法人
//type Legal2 struct {
//	LegalName      string   `json:"legal_name" bson:"legal_name"`               // 法人姓名
//	IdentityType   int      `json:"identity_type" bson:"identity_type"`         // 法人证件类型  1 身份证
//	LegalIds       string   `json:"legal_ids" bson:"legal_ids"`                 // 法人证件号码
//	LegalPhone     string   `json:"legal_phone" bson:"legal_phone"`             // 法人手机号
//	IDCard         IDCard   `json:"id_card" bson:"id_card"`                     // 身份证信息
//	IdCardFrontImg FileInfo `bson:"id_card_front_img" json:"id_card_front_img"` // 身份证-正面
//	IdCardBackImg  FileInfo `bson:"id_card_back_img" json:"id_card_back_img"`   // 身份证-背面
//	//IdCardWithHandImg FileInfo `bson:"id_card_with_hand_img" json:"id_card_with_hand_img"` // 身份证-手持
//}
//
//// BusinessLicense 营业执照
//type BusinessLicense2 struct {
//	RegistrationNumber  string `json:"registration_number" bson:"registration_number" validate:"required"`    // 注册码，社会统一信用码
//	Name                string `json:"name"  bson:"name" validate:"required"`                                 // 企业名称
//	Type                string `json:"type" bson:"type" validate:"required"`                                  // 企业类型
//	Address             string `json:"address" bson:"address" validate:"required"`                            // 地址
//	LegalRepresentative string `json:"legal_representative" bson:"legal_representative"  validate:"required"` // 法人
//	RegisteredCapital   string `json:"registered_capital" bson:"registered_capital"  validate:"-"`            // 注册资金
//	FoundDate           string `json:"found_date" bson:"found_date"  validate:"-"`                            // 注册时间
//	BusinessTerm        string `json:"business_term" bson:"business_term" validate:"-"`                       // 有效期
//	BusinessScope       string `json:"business_scope" bson:"business_scope"  validate:"-"`                    // 经营范围
//}
//
//// IDCard 身份证
//type IDCard2 struct {
//	Name            string `json:"name" bson:"name" validate:"required"`                        // 姓名
//	Sex             string `json:"sex"  bson:"sex" validate:"required"`                         // 性别
//	Ethnicity       string `json:"ethnicity" bson:"ethnicity" validate:"required"`              // 民族
//	Birth           string `json:"birth" bson:"birth" validate:"required"`                      // 生日
//	Address         string `json:"address" bson:"address" validate:"required"`                  // 地址
//	CertificateType string `json:"certificate_type" bson:"certificate_type"  validate:"eq=身份证"` // 证件类型-默认：身份证
//	IdCardNumber    string `json:"id_card_number" bson:"id_card_number"  validate:"required"`   // 身份证号码
//	ValidFrom       string `json:"valid_from" bson:"valid_from"  validate:"required"`           // 有效期开始
//	ValidTo         string `json:"valid_to" bson:"valid_to" validate:"required"`                // 有效期截止
//	Issue           string `json:"issue" bson:"issue"  validate:"-"`                            // 签发机构
//}

package model

// Dict 字典值
var Dict = map[string]interface{}{
	"AccountStatusType":  AccountStatusTypeMsg,
	"AuditStatusType":    AuditStatusTypeMsg,
	"BuyerType":          BuyerTypeMsg,
	"ServiceAbilityType": ServiceAbilityTypeMsg,
	"ObjectType":         ObjectTypeMsg,
	"MainBusinessType":   MainBusinessTypeMsg,
	"OrderStatusType":    OrderStatusTypeMsg,
	//"OrderProductStatusType": OrderProductStatusTypeMsg,
	// 优惠券范围
	"CouponScopeType": CouponScopeTypeMsg,
}

type AccountStatusType int

const (
	AccountStatusTypeNormal AccountStatusType = 1 + iota
	AccountStatusTypeForbid
	AccountStatusTypeNotApply
	AccountStatusTypeDormant
)

var AccountStatusTypeMsg = map[AccountStatusType]string{
	AccountStatusTypeNormal:   "正常",
	AccountStatusTypeForbid:   "不可用",
	AccountStatusTypeNotApply: "未申请",
	AccountStatusTypeDormant:  "已休眠",
}

type AuditStatusType int

const (
	AuditStatusTypeDoing   AuditStatusType = 1
	AuditStatusTypePass    AuditStatusType = 2
	AuditStatusTypeNotPass AuditStatusType = 3
	AuditStatusTypeNone    AuditStatusType = 4
)

var AuditStatusTypeMsg = map[AuditStatusType]string{
	AuditStatusTypeDoing:   "审核中",
	AuditStatusTypePass:    "审核通过",
	AuditStatusTypeNotPass: "审核未通过",
	AuditStatusTypeNone:    "不处理",
}

type BuyerType int

const (
	ShopTypeFruit BuyerType = 1 + iota
	ShopTypeMarket
	ShopTypeOther
)

var BuyerTypeMsg = map[BuyerType]string{
	ShopTypeFruit:  "水果店",
	ShopTypeMarket: "商超",
	ShopTypeOther:  "其他",
}

type ServiceAbilityType int

const (
	ServiceAbilityTypeStorage = 1 + iota
	ServiceAbilityTypeDeliver
	ServiceAbilityTypeLogistic
	ServiceAbilityTypeInstantDeliver
)

var ServiceAbilityTypeMsg = map[ServiceAbilityType]string{
	ServiceAbilityTypeStorage:        "自提",
	ServiceAbilityTypeDeliver:        "配送",
	ServiceAbilityTypeLogistic:       "物流",
	ServiceAbilityTypeInstantDeliver: "即时配送",
}

type CompanyType int

const (
	CompanyTypeCo CompanyType = 1 + iota
	CompanyTypePerson
)

var CompanyTypeMsg = map[CompanyType]string{
	CompanyTypeCo:     "公司",
	CompanyTypePerson: "个体工商户",
}

type MainBusinessType int

const (
	MainBusinessTypeFruit MainBusinessType = 1 + iota
	MainBusinessTypeFood
	MainBusinessTypeMaterial
	MainBusinessTypeVegetable
)

var MainBusinessTypeMsg = map[MainBusinessType]string{
	MainBusinessTypeFruit:     "生鲜水果",
	MainBusinessTypeFood:      "食品",
	MainBusinessTypeMaterial:  "包材",
	MainBusinessTypeVegetable: "蔬菜",
}

type ObjectType int

const (
	ObjectTypeBuyer        ObjectType = 1
	ObjectTypeSupplier     ObjectType = 2
	ObjectTypeServicePoint ObjectType = 3
	ObjectTypeWarehouse    ObjectType = 4
	ObjectTypePlatform     ObjectType = 5
	ObjectTypeDeliverMan   ObjectType = 6
	ObjectTypeStation      ObjectType = 7

	ObjectTypeSecondPoint = 8
)

var ObjectTypeMsg = map[ObjectType]string{
	ObjectTypeBuyer:        "采购商",
	ObjectTypeSupplier:     "供应商",
	ObjectTypeServicePoint: "中心仓",
	ObjectTypeWarehouse:    "集中仓",
	ObjectTypePlatform:     "平台",
	ObjectTypeDeliverMan:   "配送员",
	ObjectTypeStation:      "城市仓",
	ObjectTypeSecondPoint:  "城市仓",
}

type PayStatusType int

const (
	PayStatusTypeToPay         PayStatusType = 1
	PayStatusTypeFail          PayStatusType = 3
	PayStatusTypePaid          PayStatusType = 4
	PayStatusTypePaidButRefund PayStatusType = 5
	PayStatusTypeClose         PayStatusType = 6
	PayStatusTypePending       PayStatusType = 99
)

var OrderPayStatusTypeMsg = map[PayStatusType]string{
	PayStatusTypeToPay:         "待付款",   // 未支付
	PayStatusTypeFail:          "支付失败",  // 交易失败	3	整型	交易过程中出现错误
	PayStatusTypePaid:          "支付成功",  // 交易成功
	PayStatusTypePaidButRefund: "支付成功",  // 交易成功-发生退款
	PayStatusTypeClose:         "支付已关闭", // 关闭
	PayStatusTypePending:       "支付中",   // 进行中
}

type OrderStatusType int

const (
	OrderStatusTypeClosed    OrderStatusType = 1
	OrderStatusTypeCancel    OrderStatusType = 2
	OrderStatusTypeToStockUp OrderStatusType = 3
	OrderStatusTypeToQuality OrderStatusType = 4
	OrderStatusTypeToSort    OrderStatusType = 5
	OrderStatusTypeToShip    OrderStatusType = 6
	OrderStatusTypeToArrive  OrderStatusType = 7
	OrderStatusTypeToReceive OrderStatusType = 8
	OrderStatusTypeFinish    OrderStatusType = 9
	OrderStatusTypeRefundAll OrderStatusType = 101
)

//OrderStatusTypeAfterSale
//OrderStatusTypeToComment
//OrderStatusTypeCommented

var OrderStatusTypeMsg = map[OrderStatusType]string{
	//OrderStatusTypeClosed: "超时支付关闭", // 超时支付关闭
	OrderStatusTypeClosed:    "已关闭", // 未支付
	OrderStatusTypeCancel:    "已取消", // 已经支付，全额退款
	OrderStatusTypeToStockUp: "待备货",
	OrderStatusTypeToQuality: "待品控",
	OrderStatusTypeToSort:    "待分拣",
	OrderStatusTypeToShip:    "待发货",
	OrderStatusTypeToArrive:  "待到货", // 到服务点之前
	OrderStatusTypeToReceive: "待收货",
	OrderStatusTypeFinish:    "已完成",
	OrderStatusTypeRefundAll: "已全退", // 发货环节
}

//
//// OrderProductStatusType 订单产品状态
//type OrderProductStatusType int
//
//const (
//	OrderProductStatusTypeToStockUp OrderProductStatusType = 1 + iota
//	OrderProductStatusTypeToQuality
//	OrderProductStatusTypeToSort
//	OrderProductStatusTypeToCar
//)
//
//var OrderProductStatusTypeMsg = map[OrderProductStatusType]string{
//	OrderProductStatusTypeToStockUp: "待备货",
//	OrderProductStatusTypeToQuality: "待品控",
//	OrderProductStatusType:          "待分拣",
//	OrderProductStatusType:          "待装车",
//}

func BackRecordKey(status OrderStatusType) string {
	var key string
	switch status {
	case OrderStatusTypeToQuality:
		key = "stock_up_time"
	case OrderStatusTypeToSort:
		key = "quality_time"
	case OrderStatusTypeToShip:
		key = "sort_time"
	case OrderStatusTypeToArrive:
		key = "ship_time"
	case OrderStatusTypeToReceive:
		key = "arrive_time"
	//case OrderStatusTypeToComment:
	//	key = "receive_time"
	//case OrderStatusTypeCommented:
	//	key = "comment_time"
	case OrderStatusTypeFinish:
		key = "receive_time"
	}
	return key
}

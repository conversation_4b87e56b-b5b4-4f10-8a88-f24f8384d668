package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// oss

type ConfigStruct struct {
	Expiration string     `json:"expiration"`
	Conditions [][]string `json:"conditions"`
}

type PolicyToken struct {
	AccessKeyId    string `json:"access_key_id"`
	Host           string `json:"host"`
	Expire         int64  `json:"expire"`
	Signature      string `json:"signature"`
	Policy         string `json:"policy"`
	Directory      string `json:"dir"`
	FileNamePrefix string `json:"file_name_prefix"`
}

// Announce 公告
type Announce struct {
	ServicePointID primitive.ObjectID `json:"service_point_id"`
	Content        []string           `json:"content"`
}

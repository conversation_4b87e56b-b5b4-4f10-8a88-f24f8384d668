package model

import (
	"encoding/json"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AuditObjectType 审核类型
type AuditObjectType int

const (
	AuditObjectTypeProduct AuditObjectType = 1 + iota
)

var AuditObjectTypeMsg = map[AuditObjectType]string{
	AuditObjectTypeProduct: "商品审核",
}

// Audit 审核
type Audit struct {
	ID              primitive.ObjectID `bson:"_id" json:"id"`
	ObjectID        primitive.ObjectID `bson:"object_id" json:"object_id"`                 // 对象ID
	AuditUserID     primitive.ObjectID `bson:"audit_user_id" json:"audit_user_id"`         // 审核人
	AuditObjectType AuditObjectType    `bson:"audit_object_type" json:"audit_object_type"` // 审核类型
	AuditContent    json.RawMessage    `bson:"audit_content" json:"audit_content"`         // 待审核内容
	AuditStatus     AuditStatusType    `bson:"audit_status" json:"audit_status"`           // 审核状态
	FailReason      string             `bson:"fail_reason" json:"fail_reason"`             // 失败原因
	CreatedAt       int64              `bson:"created_at" json:"created_at"`
	UpdatedAt       int64              `bson:"updated_at" json:"updated_at"`
}

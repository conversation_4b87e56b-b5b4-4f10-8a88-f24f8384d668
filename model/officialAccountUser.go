package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// OfficialAccountUser 公众号用户
type OfficialAccountUser struct {
	ID            primitive.ObjectID `json:"id" bson:"_id"`
	OpenID        string             `json:"open_id" bson:"open_id"`
	UnionID       string             `json:"union_id" bson:"union_id"`
	Subscribe     int                `json:"subscribe" bson:"subscribe"`           // 用户是否订阅该公众号标识，值为0时，代表此用户没有关注该公众号，拉取不到其余信息
	SubscribeTime int64              `json:"subscribe_time" bson:"subscribe_time"` // 用户关注时间，为时间戳。如果用户曾多次关注，则取最后关注时间
	Remark        string             `json:"remark" bson:"remark"`                 // 公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注
	CreatedAt     int64              `json:"created_at" bson:"created_at"`
}

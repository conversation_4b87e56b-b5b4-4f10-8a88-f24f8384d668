package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// OrderFinalSettle 订单最终结算记录
type OrderFinalSettle struct {
	ID                         primitive.ObjectID   `json:"id" bson:"_id"`
	OrderID                    primitive.ObjectID   `json:"order_id" bson:"order_id"`                                                   // 关联订单ID
	SupplierID                 primitive.ObjectID   `json:"supplier_id" bson:"supplier_id"`                                             // 供应商ID
	SupplierName               string               `json:"supplier_name" bson:"supplier_name"`                                         // 供应商名称
	TotalProductAmount         int                  `json:"total_product_amount" bson:"total_product_amount"`                           // 总商品金额
	TotalProductBuyPriceAmount int                  `json:"total_product_buy_price_amount" bson:"total_product_buy_price_amount"`       // 总商品采购价金额
	TotalAdjustSettleAmount    int                  `json:"total_adjust_settle_amount_profit" bson:"total_adjust_settle_amount_profit"` // 总调价结算金额
	TotalQualityRefundAmount   int                  `json:"total_quality_refund_amount" bson:"total_quality_refund_amount"`             // 品控退款金额
	TotalAfterSaleRefundAmount int                  `json:"total_after_sale_refund_amount" bson:"total_after_sale_refund_amount"`       // 售后退款金额
	TotalDebtAmount            int                  `json:"total_debt_amount" bson:"total_debt_amount"`                                 // 补差金额
	TotalBuyPriceProfit        int                  `json:"total_buy_price_profit" bson:"total_buy_price_profit"`                       // 总采购价利润金额
	TotalFinalProfit           int                  `json:"total_final_profit" bson:"total_final_profit"`                               // 总最终利润金额
	ProductList                []ProductFinalSettle `json:"product_list" bson:"product_list"`                                           // 商品最终结算明细
	OrderCreatedAt             int64                `json:"order_created_at" bson:"order_created_at"`                                   // 订单创建时间
	CreatedAt                  int64                `json:"created_at" bson:"created_at"`
	UpdatedAt                  int64                `json:"updated_at" bson:"updated_at"`
}

// ProductFinalSettle 商品最终结算明细
type ProductFinalSettle struct {
	ProductID                  primitive.ObjectID `json:"product_id" bson:"product_id"`                                         // 商品ID
	ProductTitle               string             `json:"product_title" bson:"product_title"`                                   // 商品标题
	ProductCoverImg            string             `json:"product_cover_img" bson:"product_cover_img"`                           // 商品封面
	SkuIDCode                  string             `json:"sku_id_code" bson:"sku_id_code"`                                       // SKU编码
	SkuName                    string             `json:"sku_name" bson:"sku_name"`                                             // SKU名称
	TotalProductAmount         int                `json:"total_product_amount" bson:"total_product_amount"`                     // 商品金额
	TotalQualityRefundAmount   int                `json:"total_quality_refund_amount" bson:"total_quality_refund_amount"`       // 品控退款
	TotalAfterSaleRefundAmount int                `json:"total_after_sale_refund_amount" bson:"total_after_sale_refund_amount"` // 售后退款金额
	TotalDebtAmount            int                `json:"total_debt_amount" bson:"total_debt_amount"`                           // 补差金额
	TotalBuyPriceAmount        int                `json:"total_buy_price_amount" bson:"total_buy_price_amount"`                 // 采购价金额
	BuyPriceProfit             int                `json:"buy_price_profit" bson:"buy_price_profit"`                             // 采购价利润
	FinalProfit                int                `json:"final_profit" bson:"final_profit"`                                     // 最终利润
}

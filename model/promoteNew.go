package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// PromoteNew 推广新品
type PromoteNew struct {
	ID             primitive.ObjectID `bson:"_id" json:"id"`
	ServicePointID primitive.ObjectID `bson:"service_point_id" json:"service_point_id"`
	Title          string             `json:"title" bson:"title"`
	FileType       string             `json:"file_type" bson:"file_type"`
	ImgFileList    []FileInfo         `json:"img_file_list" bson:"img_file_list"` // 图片
	Video          FileInfo           `json:"video" bson:"video"`                 // 视频
	LinkProductID  primitive.ObjectID `json:"link_product_id" bson:"link_product_id"`
	CreatedAt      int64              `bson:"created_at" json:"created_at"`
}

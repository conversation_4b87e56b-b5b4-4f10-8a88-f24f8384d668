package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	HistoryTypeShortCut int = 1
	HistoryTypeTopic    int = 2
	HistoryTypePart     int = 3
)

// ProductHistory 商品记录表
type ProductHistory struct {
	ID          primitive.ObjectID `bson:"_id" json:"id"`
	HistoryType int                `bson:"history_type" json:"history_type"` // 记录类型  1 快捷栏 2主题 3.专区
	ObjectID    primitive.ObjectID `bson:"object_id" json:"object_id"`       // 相关对象ID
	ProductID   primitive.ObjectID `bson:"product_id" json:"product_id"`
	CreatedAt   int64              `bson:"created_at" json:"created_at"`
}

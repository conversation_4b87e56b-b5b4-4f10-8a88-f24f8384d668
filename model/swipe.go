package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// SwipeType 轮播图类型
type SwipeType int

const (
	SwipeTypeDisplay SwipeType = 1 // 纯展示
	//SwipeTypeProduct SwipeType = 2 // 商品说明和列表
	SwipeTypeNotice SwipeType = 2 // 公告页/h5
)

// Swipe 轮播图
type Swipe struct {
	ID        primitive.ObjectID `bson:"_id" json:"id"`
	Sort      int                `bson:"sort" json:"sort"`       // 排序
	Visible   bool               `bson:"visible" json:"visible"` // 是否展示
	Type      SwipeType          `bson:"type" json:"type"`       // 跳转类型，1. 纯展示 2.h5页面
	Url       string             `json:"url" bson:"url"`         // 页面url
	Img       FileInfo           `bson:"img" json:"img"`         // 轮播图展示图片地址
	CreatedAt int64              `bson:"created_at" json:"created_at"`
	UpdatedAt int64              `bson:"updated_at" json:"updated_at"`
}

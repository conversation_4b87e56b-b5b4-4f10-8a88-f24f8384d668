package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DepositSet 保证金设置
type DepositSet struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	ObjectType ObjectType         `json:"object_type" bson:"object_type"` // 对象类型
	Amount     int                `json:"amount" bson:"amount"`           // 金额-分
	CreatedAt  int64              `bson:"created_at" json:"created_at"`
	UpdatedAt  int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt  int64              `bson:"deleted_at" json:"deleted_at"`
}

// DepositAccount 保证金账户
type DepositAccount struct {
	ID           primitive.ObjectID `json:"id" bson:"_id"`
	ObjectType   ObjectType         `json:"object_type" bson:"object_type"`       // 对象类型
	ObjectID     primitive.ObjectID `json:"object_id" bson:"object_id"`           // 对象ID
	DueAmount    int                `json:"due_amount" bson:"due_amount"`         // 应有金额-分
	NowAmount    int                `json:"now_amount" bson:"now_amount"`         // 当前金额-分
	HasSet       bool               `json:"has_set" bson:"has_set"`               // 已经缴纳
	IsSetOffline bool               `json:"is_set_offline" bson:"is_set_offline"` // 是否线下缴纳
	CreatedAt    int64              `bson:"created_at" json:"created_at"`
	UpdatedAt    int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt    int64              `bson:"deleted_at" json:"deleted_at"`
}

// DepositRecord 保证金明细记录
type DepositRecord struct {
	ID               primitive.ObjectID `json:"id" bson:"_id"`
	DepositAccountID primitive.ObjectID `json:"deposit_account_id" bson:"deposit_account_id"` // 账户ID
	BizTransferNo    string             `json:"biz_transfer_no" bson:"biz_transfer_no"`       // 商户系统转账订单号，商户系统唯一
	TransferNo       string             `json:"transfer_no" bson:"transfer_no"`               // 云商通转账订单号
	ExtendInfo       string             `json:"extend_info" bson:"extend_info"`               // 扩展信息
	ResAmount        int                `json:"res_amount" bson:"res_amount"`                 // 结果金额
	Amount           int                `json:"amount" bson:"amount"`                         // 金额
	IsAdd            bool               `json:"is_add" bson:"is_add"`                         // 变化类型  1：增加  2：减少
	Note             string             `json:"note" bson:"note"`
	CreatedAt        int64              `bson:"created_at" json:"created_at"`
}

// DepositApplyOrder 保证金充值订单
type DepositApplyOrder struct {
	ID               primitive.ObjectID `json:"id" bson:"_id"`
	DepositAccountID primitive.ObjectID `json:"deposit_account_id" bson:"deposit_account_id"` // 保证金账户ID
	BizUserId        string             `json:"biz_user_id" bson:"biz_user_id"`               // 仅交易验证方式为“0”时返回 平台，返回#yunBizUserId_B2C#
	Amount           int                `json:"amount" bson:"amount"`
	BizOrderNo       string             `json:"biz_order_no" bson:"biz_order_no"` // 商户订单号（支付订单）
	PayStatus        PayStatusType      `json:"pay_status" bson:"pay_status"`     // 支付状态
	PayResult        PayResult          `json:"pay_result" bson:"pay_result"`     // 微信支付结果
	CreatedAt        int64              `bson:"created_at" json:"created_at"`
	UpdatedAt        int64              `bson:"updated_at" json:"updated_at"`
}

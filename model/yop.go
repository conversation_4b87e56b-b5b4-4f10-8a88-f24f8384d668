package model

// UploadRes 子商户入网资质文件上传
type UploadRes struct {
	MerQualUrl string `json:"merQualUrl"` // 商户资质存储url
	ReturnCode string `json:"returnCode"` //  响应编码
	ReturnMsg  string `json:"returnMsg"`  //  响应信息
}

type QueryRes struct {
	ReturnCode          string `json:"returnCode"`          //  响应编码
	ReturnMsg           string `json:"returnMsg"`           //  响应信息
	RequestNo           string `json:"requestNo"`           // 入网请求号
	ApplicationNo       string `json:"applicationNo"`       // 申请单编号
	MerchantNo          string `json:"merchantNo"`          // 商户编号
	ApplicationStatus   string `json:"applicationStatus"`   // 申请状态
	AuditOpinion        string `json:"auditOpinion"`        // 审核意见
	ProgressDescription string `json:"progressDescription"` // 入网进度说明
	ElecSignUrl         string `json:"elecSignUrl"`         // 电子签约地址
}

// RegisterRes 入网结果
type RegisterRes struct {
	ReturnCode        string `json:"returnCode"`        //  响应编码
	ReturnMsg         string `json:"returnMsg"`         //  响应信息
	RequestNo         string `json:"requestNo"`         // 入网请求号
	ApplicationNo     string `json:"applicationNo"`     // 申请单编号
	ApplicationStatus string `json:"applicationStatus"` // 商户编号
	MerchantNo        string `json:"merchantNo"`        // 商户编号
}

// WechatAuthApplyRes 微信实名认证申请
type WechatAuthApplyRes struct {
	ReturnCode  string `json:"returnCode"`  //  响应编码
	ReturnMsg   string `json:"returnMsg"`   //  响应信息
	RequestNo   string `json:"requestNo"`   // 入网请求号
	ApplymentID string `json:"applymentId"` // 申请单编号
}

// WechatAuthQueryRes 微信实名认证申请-查询
type WechatAuthQueryRes struct {
	ReturnCode     string `json:"returnCode"`     //  响应编码
	ReturnMsg      string `json:"returnMsg"`      //  响应信息
	RequestNo      string `json:"requestNo"`      // 入网请求号
	ApplymentID    string `json:"applymentId"`    // 申请单编号
	ApplymentState string `json:"applymentState"` // 申请单状态
	QrcodeUrl      string `json:"qrcodeUrl"`      // 小程序码图片url
	RejectParam    string `json:"rejectParam"`    // 驳回参数
	RejectReason   string `json:"rejectReason"`   // 驳回原因
}

// WechatAuthCancelRes 微信实名认证申请-取消
type WechatAuthCancelRes struct {
	ReturnCode string `json:"returnCode"` //  响应编码
	ReturnMsg  string `json:"returnMsg"`  //  响应信息
	RequestNo  string `json:"requestNo"`  // 入网请求号
}

// MerAuthQueryRes 商户授权-查询
type MerAuthQueryRes struct {
	ReturnCode       string `json:"returnCode"` //  响应编码
	ReturnMsg        string `json:"returnMsg"`  //  响应信息
	IdentityAuthDtos []struct {
		ReportMerchantNo string `json:"reportMerchantNo"`
		FeeType          string `json:"feeType"`
		AuthorizeState   string `json:"authorizeState"`
		ReturnCode       string `json:"returnCode"`
		ReturnMsg        string `json:"returnMsg"`
		BackUpFlag       string `json:"backUpFlag"` // 备份标签
		PromotionType    string `json:"promotionType"`
		ChannelNo        string `json:"channelNo"`
		Open             string `json:"open"` // 开关状态
	} `json:"identityAuthDtos"` // 入网请求号
}

type TradeOrderRes struct {
	Code             string  `json:"Code"`             //  响应编码
	Message          string  `json:"message"`          //  响应信息
	OrderId          string  `json:"orderId"`          //  响应信息
	UniqueOrderNo    string  `json:"uniqueOrderNo"`    //  响应信息
	ParentMerchantNo string  `json:"parentMerchantNo"` //  响应信息
	MerchantNo       string  `json:"merchantNo"`       //  响应信息
	Token            string  `json:"token"`            //  响应信息
	OrderAmount      float64 `json:"orderAmount"`      //  响应信息
}

type TradeOrderQueryRes struct {
	Code              string  `json:"Code"`
	Message           string  `json:"message"`
	ParentMerchantNo  string  `json:"parentMerchantNo"`
	MerchantNo        string  `json:"merchantNo"`
	OrderId           string  `json:"orderId"`
	UniqueOrderNo     string  `json:"uniqueOrderNo"`
	Status            string  `json:"status"`
	OrderAmount       float64 `json:"orderAmount"`
	PayAmount         float64 `json:"payAmount"`
	PaySuccessDate    string  `json:"paySuccessDate"`
	PayWay            string  `json:"payWay"`
	UnSplitAmount     float64 `json:"unSplitAmount"`
	TotalRefundAmount float64 `json:"totalRefundAmount"`
	TotalDivideAmount float64 `json:"totalDivideAmount"`
}

//type TradeOrderCombineRes struct {
//	Code             string `json:"Code"`             //  响应编码
//	Message          string `json:"message"`          //  响应信息
//	ParentMerchantNo string `json:"parentMerchantNo"` //  响应信息
//	OrderId          string `json:"orderId"`          //  响应信息
//	Token            string `json:"token"`            //  响应信息
//	SubOrderInfoList string `json:"subOrderInfoList"`
//}

type YeeTradeSubOrder struct {
	MerchantNo          string  `json:"merchantNo"`
	OrderId             string  `json:"orderId"`
	UniqueOrderNo       string  `json:"uniqueOrderNo"`
	ChannelOrderId      string  `json:"channelOrderId"` // 该笔订单在微信、支付宝或银行侧系统生成的单号
	OrderAmount         float64 `json:"orderAmount"`
	PayAmount           float64 `json:"payAmount"`
	RealPayAmount       float64 `json:"realPayAmount"`
	UnSplitAmount       float64 `json:"unSplitAmount"`
	GoodsName           string  `json:"goodsName"`
	Memo                string  `json:"memo"`
	Status              string  `json:"status"`
	PayWay              string  `json:"payWay"`
	PaySuccessDate      string  `json:"paySuccessDate"`
	FundProcessType     string  `json:"fundProcessType"`
	FundControlCsStatus string  `json:"fundControlCsStatus"` // 订单管控状态 INIT:处理中 FROZEN:已冻结 UN_FROZEN:已解冻
}

type YeeTradeOrderPayNotify struct {
	OrderId                string `json:"orderId"`
	UniqueOrderNo          string `json:"uniqueOrderNo"`
	OrderAmount            string `json:"orderAmount"`
	PayAmount              string `json:"payAmount"`
	ParentMerchantNo       string `json:"parentMerchantNo"`
	MerchantNo             string `json:"merchantNo"`
	Status                 string `json:"status"`
	ChannelOrderId         string `json:"channelOrderId"` // 该笔订单在微信、支付宝或银行侧系统生成的单号
	PaySuccessDate         string `json:"paySuccessDate"`
	Channel                string `json:"channel"`
	PayWay                 string `json:"payWay"`
	PayerInfo              string `json:"payerInfo"`
	PayerInfoFormat        YeePayerInfo
	SubOrderInfoList       string `json:"subOrderInfoList"`
	SubOrderInfoListFormat []SubOrderInfoList
	RealPayAmount          string `json:"realPayAmount"`
	FailCode               string `json:"failCode"`
	FailReason             string `json:"failReason"`
	ChannelTrxId           string `json:"channelTrxId"` // 微信/支付宝订单号
	BankOrderId            string `json:"bankOrderId"`  // 银行订单号
}

type YeeTradeOrderPaySingleNotify struct {
	OrderId          string `json:"orderId"`
	UniqueOrderNo    string `json:"uniqueOrderNo"`
	OrderAmount      string `json:"orderAmount"`
	PayAmount        string `json:"payAmount"`
	ParentMerchantNo string `json:"parentMerchantNo"`
	MerchantNo       string `json:"merchantNo"`
	Status           string `json:"status"`
	ChannelOrderId   string `json:"channelOrderId"` // 该笔订单在微信、支付宝或银行侧系统生成的单号
	PaySuccessDate   string `json:"paySuccessDate"`
	Channel          string `json:"channel"`
	PayWay           string `json:"payWay"`
	PayerInfo        string `json:"payerInfo"`
	PayerInfoFormat  YeePayerInfo
	RealPayAmount    string `json:"realPayAmount"`
	FailCode         string `json:"failCode"`
	FailReason       string `json:"failReason"`
	ChannelTrxId     string `json:"channelTrxId"` // 微信/支付宝订单号
	BankOrderId      string `json:"bankOrderId"`  // 银行订单号
}

type YeePayerInfo struct {
	UserID          string `json:"userID"`          // 微信支付返回openID
	YpAccountBookNo string `json:"ypAccountBookNo"` // 记帐簿编号
}

type SubOrderInfoList struct {
	MerchantNo     string  `json:"merchantNo"`
	UniqueOrderNo  string  `json:"uniqueOrderNo"`
	OrderId        string  `json:"orderId"`
	OrderAmount    float64 `json:"orderAmount" bson:"-"`
	OrderAmountInt int     `json:"order_amount_int" bson:"order_amount_int"`
}

type YeeRefundRes struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Status  string `json:"status"`
}

type YeeTradeRefundNotify struct {
	OrderId           string `json:"orderId"`
	UniqueOrderNo     string `json:"uniqueOrderNo"`
	RefundRequestId   string `json:"refundRequestId"`
	RefundSuccessDate string `json:"refundSuccessDate"`
	ErrorMessage      string `json:"errorMessage"`
	PaymentMethod     string `json:"paymentMethod"`
	RefundAmount      string `json:"refundAmount"`
	Status            string `json:"status"`
}

type YeeWithdrawNotify struct {
	FinishTime              string `json:"finishTime"`
	ReverseTime             string `json:"reverseTime"`
	OrderNo                 string `json:"orderNo"`
	AccountName             string `json:"accountName"`
	Fee                     string `json:"fee"`
	BankName                string `json:"bankName"`
	DebitAmount             string `json:"debitAmount"`
	RequestNo               string `json:"requestNo"`
	ReceiveAmount           string `json:"receiveAmount"`
	OrderTime               string `json:"orderTime"`
	OrderAmount             string `json:"orderAmount"`
	FeeUndertakerMerchantNo string `json:"feeUndertakerMerchantNo"`
	AccountNo               string `json:"accountNo"`
	FailReason              string `json:"failReason"`
	ReceiveType             string `json:"receiveType"`
	MerchantNo              string `json:"merchantNo"`
	Status                  string `json:"status"`
	IsReversed              string `json:"isReversed"`
}

type YeeAccountOpenRes struct {
	ReturnCode            string `json:"returnCode"`
	ReturnMsg             string `json:"returnMsg"`
	MerchantNo            string `json:"merchantNo"`
	MerchantAccountBookNo string `json:"merchantAccountBookNo"`
	YpAccountBookNo       string `json:"ypAccountBookNo"`
	Status                string `json:"status"`
	CreateTime            string `json:"createTime"`
}

type YeeAccountBookPayRes struct {
	Code          string `json:"code"`
	Message       string `json:"message"`
	UniqueOrderNo string `json:"uniqueOrderNo"`
	OrderId       string `json:"orderId"`
	OrderStatus   string `json:"orderStatus"`
}

type YeeAccountQueryRes struct {
	ReturnCode              string  `json:"returnCode"`
	ReturnMsg               string  `json:"returnMsg"`
	MerchantNo              string  `json:"merchantNo"`
	MerchantAccountBookNo   string  `json:"merchantAccountBookNo"`
	MerchantAccountBookName string  `json:"merchantAccountBookName"`
	YpAccountBookNo         string  `json:"ypAccountBookNo"`
	Balance                 float64 `json:"balance"`
	BalanceInt              int     `json:"balance_int"`
	FreezeBalance           float64 `json:"freezeBalance"`
	FreezeBalanceInt        int     `json:"freeze_balance_int"`
}
type YeeAccountUpdateRes struct {
	ReturnCode              string `json:"returnCode"`
	ReturnMsg               string `json:"returnMsg"`
	MerchantNo              string `json:"merchantNo"`
	MerchantAccountBookNo   string `json:"merchantAccountBookNo"`
	MerchantAccountBookName string `json:"merchantAccountBookName"`
	YpAccountBookNo         string `json:"ypAccountBookNo"`
	Status                  string `json:"status"`
	CreateTime              string `json:"createTime"`
}

type YeeAccountRechargeQueryRes struct {
	ReturnCode string `json:"returnCode"`
	ReturnMsg  string `json:"returnMsg"`
	TotalCount int    `json:"totalCount"`
	List       []struct {
		CreateTime       string  `json:"createTime"`
		RequestNo        string  `json:"requestNo"`
		OrderNo          string  `json:"orderNo"`
		Status           string  `json:"status"`
		MerchantNo       string  `json:"merchantNo"`
		OrderAmount      float64 `json:"orderAmount"`
		CreditAmount     float64 `json:"creditAmount"`
		Fee              float64 `json:"fee"`
		PayType          string  `json:"payType"`
		BankCode         string  `json:"bankCode"`
		BankName         string  `json:"bankName"`
		PayerAccountNo   string  `json:"payerAccountNo"`
		PayerAccountName string  `json:"payerAccountName"`
		Remark           string  `json:"remark"`
		FinishTime       string  `json:"finishTime"`
		Reason           string  `json:"reason"`
		YpAccountBookNo  string  `json:"ypAccountBookNo"`
	} `json:"list"`
}

type YeeBalanceQueryRes struct {
	ReturnCode        string  `json:"returnCode"`
	ReturnMsg         string  `json:"returnMsg"`
	MerchantNo        string  `json:"merchantNo"`
	AccountNo         string  `json:"accountNo"`
	AccountCreateTime string  `json:"accountCreateTime"`
	Balance           float64 `json:"balance"`
	BalanceInt        int     `json:"balance_int"`
	AccountStatus     string  `json:"accountStatus"`
}

type YeeBalanceAccountQueryRes struct {
	ReturnCode             string        `json:"returnCode"`
	ReturnMsg              string        `json:"returnMsg"`
	MerchantNo             string        `json:"merchantNo"`
	InitiateMerchantNo     string        `json:"initiateMerchantNo"` // 发起方商户编号
	TotalAccountBalance    float64       `json:"totalAccountBalance"`
	TotalAccountBalanceInt int           `json:"totalAccountBalanceInt"`
	AccountInfoList        []AccountInfo `json:"accountInfoList"`
}

type AccountInfo struct {
	AccountType   string  `json:"accountType"`
	Balance       float64 `json:"balance"`
	BalanceInt    int     `json:"balanceInt"`
	AccountStatus string  `json:"accountStatus"`
}

type AccountWithDrawCardQueryRes struct {
	ReturnCode          string `json:"returnCode"`
	ReturnMsg           string `json:"returnMsg"`
	MerchantNo          string `json:"merchantNo"`
	BankCardAccountList []struct {
		BankCardType   string `json:"bankCardType"`   // 银行卡类型
		AccountName    string `json:"accountName"`    // 银行卡类型
		BankCode       string `json:"bankCode"`       // 银行卡类型
		AccountNo      string `json:"accountNo"`      // 银行卡类型
		BindCardId     string `json:"bindCardId"`     // 银行卡类型
		BranchBankCode string `json:"branchBankCode"` // 银行卡类型
	} `json:"bankCardAccountList"`
}

type AccountWithDrawCardBindRes struct {
	ReturnCode string `json:"returnCode"`
	ReturnMsg  string `json:"returnMsg"`
	BindId     int    `json:"bindId"`
}

type AccountWithDrawRes struct {
	ReturnCode string `json:"returnCode"`
	ReturnMsg  string `json:"returnMsg"`
	Status     string `json:"status"`
	OrderNo    string `json:"orderNo"`
}

type YeeDivideDetailRes struct {
	DivideDetailNo   string  `json:"divideDetailNo"`
	LedgerNo         string  `json:"ledgerNo"`
	Amount           float64 `json:"amount"`
	DivideDetailDesc string  `json:"divideDetailDesc"`
}

type YeeDivideCompleteRes struct {
	Code              string  `json:"code"`
	Message           string  `json:"message"`
	DivideStatus      string  `json:"divideStatus"`
	Amount            float64 `json:"amount"`
	CreateDate        string  `json:"createDate"`
	DivideSuccessDate string  `json:"divideSuccessDate"`
}

type TransferRes struct {
	ReturnCode     string `json:"returnCode"`
	ReturnMsg      string `json:"returnMsg"`
	TransferStatus string `json:"transferStatus"` // REQUEST_RECEIVE 请求已接收 SUCCESS 转账成功 FAIL 失败 WAIT_AUDIT 已受理,待复核
	OrderNo        string `json:"orderNo"`        // 易宝订单号
	RequestNo      string `json:"requestNo"`
	OrderAmount    string `json:"orderAmount"`
	CreateTime     string `json:"createTime"`
	FinishTime     string `json:"finishTime"`
	Fee            string `json:"fee"`            // 手续费
	DebitAmount    string `json:"debitAmount"`    // 扣账金额
	ReceiveAmount  string `json:"receiveAmount"`  // 入账金额
	FromMerchantNo string `json:"fromMerchantNo"` // 转出方商户编号
	ToMerchantNo   string `json:"toMerchantNo"`   // 转入方商户编号
	FeeMerchantNo  string `json:"feeMerchantNo"`  // 手续费承担
	CapitalInfo    string `json:"capitalInfo"`    // 资方信息
}

// YeePromotionSubsidyApplyRes 营销补贴申请
type YeePromotionSubsidyApplyRes struct {
	Code           string `json:"code"`
	Message        string `json:"message"`
	SubsidyOrderNo string `json:"subsidyOrderNo"`
	Status         string `json:"status"`
	SubsidyAmount  string `json:"subsidyAmount"`
	FailReason     string `json:"failReason"`
}

// PromotionSubsidyRes 查询
type PromotionSubsidyRes struct {
	Code              string `json:"code"`
	Message           string `json:"message"`
	MerchantNo        string `json:"merchantNo"`
	OrderID           string `json:"orderId"`
	UniqueOrderNo     string `json:"uniqueOrderNo"`
	SubsidyOrderNo    string `json:"subsidyOrderNo"` // 易宝补贴订单号
	Status            string `json:"status"`
	SubsidyAmount     string `json:"subsidyAmount"`
	FailReason        string `json:"failReason"`
	SubsidyBackAmount string `json:"subsidyBackAmount"`
}

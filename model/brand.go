package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// Brand 品牌
type Brand struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	Name      string             `json:"name" bson:"name"`             // 名称
	Desc      string             `json:"desc" bson:"desc"`             // 描述
	AvatarImg FileInfo           `json:"avatar_img" bson:"avatar_img"` // 头像
	CreatedAt int64              `json:"created_at" bson:"created_at"`
}

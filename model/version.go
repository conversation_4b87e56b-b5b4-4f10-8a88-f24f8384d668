package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Version 版本
type Version struct {
	ID          primitive.ObjectID `bson:"_id" json:"id"`
	Version     string             `bson:"version" json:"version"`           // 版本
	ForceUpdate bool               `bson:"force_update" json:"force_update"` // 强制更新
	CreatedAt   int64              `bson:"created_at" json:"created_at"`
	DeletedAt   int64              `bson:"deleted_at" json:"deleted_at"`
}

package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type RefundType int // 退款类型

const (
	RefundTypeAfterSale RefundType = 1 // 售后退款
	RefundTypeQuality   RefundType = 2 // 品控退款
)

type RefundReasonType int // 退款原因类型

const (
	RefundReasonTypeWeight   RefundReasonType = 1 // 商品重量不足
	RefundReasonTypeQuality  RefundReasonType = 2 // 商品质量问题
	RefundReasonTypeMismatch RefundReasonType = 3 // 商品描述不符
	RefundReasonTypeNum      RefundReasonType = 4 // 商品缺货
	RefundReasonTypeSKU      RefundReasonType = 5 // 规格不符
	RefundReasonTypeOther    RefundReasonType = 6 // 其他
)

type AuditorType string

const (
	AuditorTypeSupplier AuditorType = "supplier"
	AuditorTypePlatform AuditorType = "platform"
)

type ConfirmType string

const (
	ConfirmTypeAgree    ConfirmType = "agree"
	ConfirmTypeDisagree ConfirmType = "disagree"
)

// OrderRefund 退款订单
type OrderRefund struct {
	ID                            primitive.ObjectID `json:"id" bson:"_id"`
	BuyerID                       primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	UserID                        primitive.ObjectID `json:"user_id" bson:"user_id"`
	BuyerName                     string             `json:"buyer_name" bson:"buyer_name"`
	SupplierID                    primitive.ObjectID `json:"supplier_id" bson:"supplier_id"`     // 供应商ID
	SupplierName                  string             `json:"supplier_name" bson:"supplier_name"` // 供应商名称
	OrderID                       primitive.ObjectID `json:"order_id" bson:"order_id"`           // 订单ID
	OrderIDNum                    string             `json:"order_id_num" bson:"order_id_num"`   // 订单ID
	OrderType                     OrderType          `json:"order_type" bson:"order_type"`
	UserType                      UserType           `json:"user_type" bson:"user_type"`
	PayMethod                     PayMethodType      `json:"pay_method" bson:"pay_method"`                                                   // 支付方式
	ParentOrderID                 primitive.ObjectID `json:"parent_order_id" bson:"parent_order_id"`                                         // 父单
	ServicePointID                primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`                                       // 父单
	OriPayBizUserID               string             `json:"ori_pay_biz_user_id" bson:"ori_pay_biz_user_id"`                                 // 原付款方
	OriReceiverBizUserID          string             `json:"ori_receiver_biz_user_id" bson:"ori_receiver_biz_user_id"`                       // 原收款方-供应商
	BizOrderNo                    string             `json:"biz_order_no" bson:"biz_order_no"`                                               // 支付订单号（云商通
	OriBizOrderNo                 string             `json:"ori_biz_order_no" bson:"ori_biz_order_no"`                                       // 需要退款的原交易订单号
	OriOrderNo                    string             `json:"ori_order_no" bson:"ori_order_no"`                                               // 原云商通订单号
	OriTransactionID              string             `json:"ori_transaction_id" bson:"ori_transaction_id"`                                   // jsapi
	OriTotalAmount                int                `json:"ori_total_amount" bson:"ori_total_amount"`                                       // jsapi
	RefundType                    RefundType         `json:"refund_type" bson:"refund_type"`                                                 // 退款类型   2 // 发货退款
	IsShipRefundAll               bool               `json:"is_ship_refund_all" bson:"is_ship_refund_all"`                                   //  发货退款 是否退全部 2 // 发货退款
	RefundBackType                RefundBackType     `json:"refund_back_type" bson:"refund_back_type"`                                       // 退款返回类型  mini 小程序  coupon 代金券
	RefundReasonType              RefundReasonType   `json:"refund_reason_type" bson:"refund_reason_type"`                                   // 退款原因类型
	Reason                        string             `json:"reason" bson:"reason"`                                                           // 原因
	ProductID                     primitive.ObjectID `json:"product_id" bson:"product_id"`                                                   // 商品ID
	SkuIDCode                     string             `json:"sku_id_code" bson:"sku_id_code"`                                                 // 商品编码
	SkuName                       string             `json:"sku_name" bson:"sku_name"`                                                       // 商品名称
	ProductImageID                primitive.ObjectID `json:"product_image_id" bson:"product_image_id"`                                       // 商品镜像ID
	ProductTitle                  string             `json:"product_title" bson:"product_title"`                                             // 商品标题
	ProductCover                  FileInfo           `json:"product_cover" bson:"product_cover"`                                             // 商品封面
	IsCheckWeight                 bool               `json:"is_check_weight" bson:"is_check_weight"`                                         // 分拣检查重量  是/否
	RoughWeight                   int                `json:"rough_weight" bson:"rough_weight"`                                               // 毛重
	OutWeight                     int                `bson:"out_weight" json:"out_weight"`                                                   // 皮重
	NetWeight                     int                `bson:"net_weight" json:"net_weight"`                                                   // 净重
	ImageList                     []FileInfo         `json:"image_list" bson:"image_list"`                                                   // 图片列表
	ImageListOne                  []FileInfo         `json:"image_list_one" bson:"image_list_one"`                                           // 图片列表
	ImageListTwo                  []FileInfo         `json:"image_list_two" bson:"image_list_two"`                                           // 图片列表
	ImageListThree                []FileInfo         `json:"image_list_three" bson:"image_list_three"`                                       // 图片列表
	Video                         FileInfo           `json:"video" bson:"video"`                                                             // 视频
	Price                         int                `json:"price" bson:"price"`                                                             // 单价
	SettleUnitPrice               int                `json:"settle_unit_price" bson:"settle_unit_price"`                                     // 单价
	Num                           int                `json:"num" bson:"num"`                                                                 // 退款数量
	ProductRoughWeightUnitPriceKG int                `json:"product_rough_weight_unit_price_kg" bson:"product_rough_weight_unit_price_kg"`   // 商品毛重单价/kg
	UserPayRoughWeightUnitPriceKG int                `json:"user_pay_rough_weight_unit_price_kg" bson:"user_pay_rough_weight_unit_price_kg"` // 用户支付（不包含运费）毛重单价/kg
	CouponRoughWeightUnitPriceKG  int                `json:"coupon_rough_weight_unit_price_kg" bson:"coupon_rough_weight_unit_price_kg"`     // 优惠券毛重单价/kg
	SortWeight                    int                `json:"sort_weight" bson:"sort_weight"`                                                 // 分拣重量
	SortNum                       int                `json:"sort_num" bson:"sort_num"`                                                       // 分拣数量
	RefundWeight                  int                `json:"refund_weight" bson:"refund_weight"`                                             // 退款重量g
	Amount                        int                `json:"amount" bson:"amount"`                                                           // 退款金额
	AuditAmount                   int                `json:"audit_amount" bson:"audit_amount"`                                               // 审核金额
	IsRefundTransport             bool               `json:"is_refund_transport" bson:"is_refund_transport"`                                 // 是否退运费
	UnitTransportFee              int                `json:"unit_transport_fee" bson:"unit_transport_fee"`                                   // 干线费单价
	TotalTransportFee             int                `json:"total_transport_fee" bson:"total_transport_fee"`                                 // 干线费
	UnitWarehouseLoadFee          int                `json:"unit_warehouse_load_fee" bson:"unit_warehouse_load_fee"`                         // 仓配费单价
	TotalWarehouseLoadFee         int                `json:"total_warehouse_load_fee" bson:"total_warehouse_load_fee"`                       // 仓配费总价
	DeliverFee                    int                `json:"deliver_fee" bson:"deliver_fee"`                                                 // 配送费
	TotalServiceFee               int                `json:"total_service_fee" bson:"total_service_fee"`                                     // 服务费
	AuditStatus                   AuditStatusType    `json:"audit_status" bson:"audit_status"`                                               // 审核状态
	IsWithdraw                    bool               `json:"is_withdraw" bson:"is_withdraw"`                                                 // 是否撤销
	AuditNote                     string             `json:"audit_note" bson:"audit_note"`                                                   // 审核备注
	ReasonType                    RefundReasonType   `json:"reason_type" bson:"reason_type"`                                                 // 原因-类型   1  质量 2 缺货
	ReasonImg                     FileInfo           `json:"reason_img" bson:"reason_img"`                                                   // 原因-图片
	RefundResult                  RefundResult       `json:"refund_result" bson:"refund_result"`                                             // 调用结果
	WXRefundResult                WXRefundResult     `json:"wx_refund_result" bson:"wx_refund_result"`                                       // 调用结果
	YeeRefundResult               YeeRefundResult    `json:"yee_refund_result" bson:"yee_refund_result"`                                     // 易宝微信退款
	AuditObjection                string             `json:"audit_objection" bson:"audit_objection"`                                         // 审核异议内容
	AuditorType                   AuditorType        `json:"auditor_type" bson:"auditor_type"`                                               // 审核员类型  供应商/平台 supplier/platform
	SupplierAuditAmount           int                `json:"supplier_audit_amount" bson:"supplier_audit_amount"`                             // 供应商审核金额
	SupplierAuditContent          string             `json:"supplier_audit_content" bson:"supplier_audit_content"`                           // 供应商审核内容
	SupplierAuditAt               int64              `json:"supplier_audit_at" bson:"supplier_audit_at"`                                     // 供应商审核时间
	SupplierAuditTimeout          bool               `json:"supplier_audit_timeout" bson:"supplier_audit_timeout"`                           // 供应商审核超时
	PlatformAuditAt               int64              `json:"platform_audit_at" bson:"platform_audit_at"`                                     // 平台审核时间
	IsComplete                    bool               `json:"is_complete" bson:"is_complete"`                                                 // 是否完结
	CreatedAt                     int64              `bson:"created_at" json:"created_at"`
	UpdatedAt                     int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt                     int64              `bson:"deleted_at" json:"deleted_at"`
}

type RefundBackType string

const (
	RefundBackTypeMini   RefundBackType = "mini"
	RefundBackTypeCoupon RefundBackType = "coupon"
)

type RefundResult struct {
	PayStatus              string `json:"pay_status" bson:"pay_status"`                                 //     	 支付状态	成功：success 进行中：pending 失败：fail 退款到银行卡/微信/支付宝成功、失败时会发订单结果通知商户。
	PayFailMessage         string `json:"pay_fail_message" bson:"pay_fail_message"`                     //         支付失败信息	只有payStatus为fail时有效
	OrderNo                string `json:"order_no" bson:"order_no"`                                     // 必填     云商通订单编号
	BizOrderNo             string `json:"biz_order_no" bson:"biz_order_no"`                             // 必填     商户订单号（支付订单）
	Amount                 int    `json:"amount" bson:"amount"`                                         // 必填     本次退款总金额	单位：分
	CouponAmount           int    `json:"coupon_amount" bson:"coupon_amount"`                           //   	     代金券退款金额	单位：分
	PayInterfaceOutTradeNo string `json:"pay_interface_out_trade_no" bson:"pay_interface_out_trade_no"` // 交易流水号
	PayDatetime            string `json:"pay_datetime" bson:"pay_datetime"`                             //
	FeeAmount              int    `json:"fee_amount" bson:"fee_amount"`                                 //         手续费退款金额	单位：分
	ExtendInfo             string `json:"extend_info" bson:"extend_info"`                               //         扩展信息
}

package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// IntegralAccount 积分账户
type IntegralAccount struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	BuyerID   primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	UserID    primitive.ObjectID `json:"user_id" bson:"user_id"`
	Num       int                `json:"num" bson:"num"`
	Status    int                `json:"status" bson:"status"`
	CreatedAt int64              `json:"created_at" bson:"created_at"`
	UpdatedAt int64              `json:"updated_at" bson:"updated_at"`
}

type RecordType int

const (
	RecordTypeOrder          RecordType = 11 // 下单
	RecordTypeOrderClose     RecordType = 12 // 关闭订单
	RecordTypeOrderCancel    RecordType = 13 // 取消订单
	RecordTypeOrderQuality   RecordType = 14 // 品控退款
	RecordTypeOrderAfterSale RecordType = 15 // 售后
	RecordTypeOrderDebtPay   RecordType = 16 // 补差支付
	RecordTypeComment        RecordType = 21 // 评论
	RecordTypeExchange       RecordType = 32 // 兑换
)

type IntegralRecordStatus int

const (
	IntegralRecordStatusValid IntegralRecordStatus = 1
)

// IntegralRecord 积分记录
type IntegralRecord struct {
	ID                primitive.ObjectID   `json:"id" bson:"_id"`
	IntegralAccountID primitive.ObjectID   `json:"integral_account_id" bson:"integral_account_id"` // 积分账户ID
	RecordType        RecordType           `json:"record_type" bson:"record_type"`                 // 记录类型
	ObjectID          primitive.ObjectID   `json:"object_id" bson:"object_id"`                     // 对象ID
	ChangeNum         int                  `json:"change_num" bson:"change_num"`                   // 变化数值
	ChangeType        int                  `json:"change_type" bson:"change_type"`                 // 变化类型   0 -  1+
	Note              string               `json:"note" bson:"note"`                               // 备注
	Status            IntegralRecordStatus `json:"status" bson:"status"`                           // 积分记录状态
	ReduceRecordList  []ReduceRecord       `json:"reduce_record_list" bson:"reduce_record_list"`   // 扣减记录
	CreatedAt         int64                `json:"created_at" bson:"created_at"`
	UpdatedAt         int64                `json:"updated_at" bson:"updated_at"`
}

// ReduceRecord 积分扣减
type ReduceRecord struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	ChangeNum int                `json:"change_num" bson:"change_num"`
	Note      string             `json:"note" bson:"note"` // 备注
	CreatedAt int64              `json:"created_at" bson:"created_at"`
}

type IntegralProductStatus int

const (
	IntegralProductStatusUp   IntegralProductStatus = 1 // 上架
	IntegralProductStatusDown IntegralProductStatus = 2 // 下架
)

type TargetType string

const (
	TargetTypeCoupon  TargetType = "coupon"  // 代金券
	TargetTypeProduct TargetType = "product" // 商品
)

type Target struct {
	TargetType TargetType `json:"target_type" bson:"target_type"`
	Value      string     `json:"value" bson:"value"`
	Desc       string     `json:"desc" bson:"desc"`
}

// IntegralProduct 积分商品
type IntegralProduct struct {
	ID            primitive.ObjectID    `json:"id" bson:"_id"`
	Title         string                `json:"title" bson:"title"`                   // 标题
	CostNum       int                   `json:"cost_num" bson:"cost_num"`             // 消耗积分数
	Price         int                   `json:"price" bson:"price"`                   // 原价
	DiscountPrice int                   `json:"discount_price" bson:"discount_price"` // 折扣价
	ImageDesc     []FileInfo            `json:"image_desc" bson:"image_desc"`         // 详情图
	TagList       []TagPer              `json:"tag_list" bson:"tag_list"`             // 标签
	Desc          string                `json:"desc" bson:"desc"`                     // 描述
	ImageCover    FileInfo              `json:"image_cover" bson:"image_cover"`       // 封面
	ImageDisplay  []FileInfo            `json:"image_display" bson:"image_display"`   // 轮播图
	Stock         int                   `json:"stock" bson:"stock"`                   // 库存
	ExchangeCount int                   `json:"exchange_count" bson:"exchange_count"` // 已兑换数
	Status        IntegralProductStatus `json:"status" bson:"status"`                 // 状态
	Target        Target                `json:"target" bson:"target"`                 // 目标商品
	Sort          int                   `json:"sort" bson:"sort"`                     // 排序
	CreatedAt     int64                 `json:"created_at" bson:"created_at"`
	UpdatedAt     int64                 `json:"updated_at" bson:"updated_at"`
	DeletedAt     int64                 `json:"deleted_at" bson:"deleted_at"`
}

// IntegralProductRecord 积分商品记录
type IntegralProductRecord struct {
	ID                primitive.ObjectID `json:"id" bson:"_id"`
	UserID            primitive.ObjectID `json:"user_id" bson:"user_id"`
	BuyerID           primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	IntegralProductID primitive.ObjectID `json:"integral_product_id" bson:"integral_product_id"` // 积分商品ID
	Title             string             `json:"title" bson:"title"`                             // 标题
	ImageCover        FileInfo           `json:"image_cover" bson:"image_cover"`                 // 封面
	ImageDisplay      []FileInfo         `json:"image_display" bson:"image_display"`             // 轮播图
	Status            int                `json:"status" bson:"status"`                           // 状态
	CreatedAt         int64              `json:"created_at" bson:"created_at"`
	UpdatedAt         int64              `json:"updated_at" bson:"updated_at"`
}

type IntegralOrderStatus string

const (
	IntegralOrderStatusClosed IntegralOrderStatus = "closed"
	IntegralOrderStatusCancel IntegralOrderStatus = "cancel"
	IntegralOrderStatusToShip IntegralOrderStatus = "toShip"
	IntegralOrderStatusFinish IntegralOrderStatus = "finish"
)

// IntegralOrder 积分订单
type IntegralOrder struct {
	ID                        primitive.ObjectID  `json:"id" bson:"_id"`
	UserID                    primitive.ObjectID  `json:"user_id" bson:"user_id"`               // 用户ID
	BuyerID                   primitive.ObjectID  `json:"buyer_id" bson:"buyer_id"`             // 用户ID
	ProductID                 primitive.ObjectID  `json:"product_id" bson:"product_id"`         // 积分商品ID
	ProductTitle              string              `json:"product_title" bson:"product_title"`   // 标题
	ImageCover                FileInfo            `json:"image_cover" bson:"image_cover"`       // 封面
	CostNum                   int                 `json:"cost_num" bson:"cost_num"`             // 消耗积分数
	Price                     int                 `json:"price" bson:"price"`                   // 原价
	DiscountPrice             int                 `json:"discount_price" bson:"discount_price"` // 折扣价
	Num                       int                 `json:"num" bson:"num"`                       // 数量
	BizOrderNo                string              `json:"biz_order_no" bson:"biz_order_no"`
	PayStatus                 PayStatusType       `json:"pay_status" bson:"pay_status"`
	PayResult                 PayResult           `json:"pay_result" bson:"pay_result"`
	Status                    IntegralOrderStatus `json:"status" bson:"status"`                                                 // 状态
	CancelResult              RefundResult        `json:"cancel_result" bson:"cancel_result"`                                   // 取消订单结果
	AgentPayBizOrderNo        string              `json:"agent_pay_biz_order_no" bson:"agent_pay_biz_order_no"`                 // 代付
	AgentPayReceiverBizUserID string              `json:"agent_pay_receiver_biz_user_id" bson:"agent_pay_receiver_biz_user_id"` //
	AgentPayAmount            int                 `json:"agent_pay_amount" bson:"agent_pay_amount"`                             //
	AgentPayFee               int                 `json:"agent_pay_fee" bson:"agent_pay_fee"`                                   //
	AgentPayResult            AgentPayResult      `json:"agent_pay_result" bson:"agent_pay_result"`                             //
	CreatedAt                 int64               `json:"created_at" bson:"created_at"`
	UpdatedAt                 int64               `json:"updated_at" bson:"updated_at"`
	DeletedAt                 int64               `json:"deleted_at" bson:"deleted_at"`
}

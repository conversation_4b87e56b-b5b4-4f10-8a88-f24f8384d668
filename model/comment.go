package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Comment 评论
type Comment struct {
	ID                primitive.ObjectID `bson:"_id" json:"id"`
	UserID            primitive.ObjectID `bson:"user_id" json:"user_id"`             // 用户ID
	BuyerID           primitive.ObjectID `bson:"buyer_id" json:"buyer_id"`           // 采购商ID
	BuyerName         string             `json:"buyer_name" bson:"buyer_name"`       // 采购商
	BuyerAvatar       FileInfo           `json:"buyer_avatar" bson:"buyer_avatar"`   // 采购商头像
	OrderID           primitive.ObjectID `bson:"order_id" json:"order_id"`           // 订单ID
	SupplierID        primitive.ObjectID `bson:"supplier_id" json:"supplier_id"`     // 供应商ID
	SupplierName      string             `json:"supplier_name" bson:"supplier_name"` // 供应商名称
	ProductID         primitive.ObjectID `bson:"product_id" json:"product_id"`       // 商品ID
	ProductTitle      string             `json:"product_title" bson:"product_title"` // 商品标题
	ProductCover      FileInfo           `json:"product_cover" bson:"product_cover"` // 商品封面
	DeliverType       DeliverType        `bson:"deliver_type" json:"deliver_type"`   // 配送方式
	LogisticStar      int                `bson:"logistic_star" json:"logistic_star"` // 物流评分
	DeliverStar       int                `bson:"deliver_star" json:"deliver_star"`   // 配送评分
	ProductStar       int                `bson:"product_star" json:"product_star"`   // 产品评分
	SupplierStar      int                `json:"supplier_star" bson:"supplier_star"` // 商家
	Content           string             `bson:"content" json:"content"`             // 内容
	Video             FileInfo           `json:"video" bson:"video"`
	ImgList           []FileInfo         `json:"img_list" bson:"img_list"`
	AuditStatus       AuditStatusType    `bson:"audit_status" json:"audit_status"`               // 审核状态
	SupplierAuditNote string             `bson:"supplier_audit_note" json:"supplier_audit_note"` // 供应商审核备注
	AuditAt           int64              `bson:"audit_at" json:"audit_at"`                       // 审核时间
	ReplyContent      string             `bson:"reply_content" json:"reply_content"`             // 回复内容
	ReplyAt           int64              `json:"reply_at" bson:"reply_at"`                       // 回复时间
	CreatedAt         int64              `bson:"created_at" json:"created_at"`
	UpdatedAt         int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt         int64              `bson:"deleted_at" json:"deleted_at"`
}

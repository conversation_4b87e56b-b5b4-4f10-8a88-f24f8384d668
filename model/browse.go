package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// Browse 浏览记录
type Browse struct {
	ID        primitive.ObjectID `json:"id" bson:"id"`
	BuyerID   primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`     // 采购商ID
	ProductID primitive.ObjectID `json:"product_id" bson:"product_id"` // 商品ID
	CreatedAt int64              `bson:"created_at" json:"created_at"`
	DeletedAt int64              `bson:"deleted_at" json:"deleted_at"`
}

type BrowseStats struct {
	ProductID primitive.ObjectID `bson:"product_id"`
	Total     int                `bson:"total"`
	Time      int                `bson:"time"`
	Count     int                `bson:"count"`
}

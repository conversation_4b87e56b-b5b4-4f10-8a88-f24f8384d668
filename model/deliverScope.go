package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// DeliverScope 配送范围
type DeliverScope struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	ServicePointID primitive.ObjectID `json:"service_point_id" bson:"service_point_id"` // 服务仓ID
	PointList      []PointLocation    `json:"point_list" bson:"point_list"`             // 坐标点列表
	CenterPoint    PointLocation      `json:"center_point" bson:"center_point"`         // 中心点
	CreatedAt      int64              `json:"created_at" bson:"created_at"`             // 创建时间
}

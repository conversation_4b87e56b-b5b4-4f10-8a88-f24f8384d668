package model

import (
	"base/core/xerr"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CouponType 代金券类型
type CouponType int

const (
	CouponTypeDiscount CouponType = 1 + iota // 代金券
	CouponTypeDeliver                        // 配送券
)

// ValidRuleType 有效规则类型
type ValidRuleType int

const (
	ValidRuleTypeInterval ValidRuleType = 1 + iota // 时间区间
	ValidRuleTypeWhenGet                           // 领取生效
)

func BackValidRuleType(t ValidRuleType) (ValidRuleType, error) {
	switch t {
	case ValidRuleTypeInterval:
		return ValidRuleTypeInterval, nil
	case ValidRuleTypeWhenGet:
		return ValidRuleTypeWhenGet, nil
	default:
		return 0, xerr.NewErr(xerr.ErrParamError, nil, "生效规则类型错误")
	}
}

// Coupon 代金券
type Coupon struct {
	ID                primitive.ObjectID `json:"id" bson:"_id"`
	Amount            int                `json:"amount" bson:"amount"`                           // 金额
	ConditionAmount   int                `json:"condition_amount" bson:"condition_amount"`       // 使用金额条件 0无门槛 非0 满减
	Title             string             `json:"title" bson:"title"`                             // 标题
	DescList          []string           `json:"desc_list" bson:"desc_list"`                     // 描述列表
	CouponType        CouponType         `json:"coupon_type" bson:"coupon_type"`                 // 代金券类型 1 代金券 2. 配送券
	TotalNum          int                `json:"total_num" bson:"total_num"`                     // 总数数量
	ValidRuleType     ValidRuleType      `json:"valid_rule_type" bson:"valid_rule_type"`         // 有效类型  1:时间区间   2:领取生效
	ValidDurationHour int64              `json:"valid_duration_hour" bson:"valid_duration_hour"` // 有效期间/小时 2:领取生效 时必填
	ValidGetBegin     int64              `json:"valid_get_begin" bson:"valid_get_begin"`         // 有效期-获取-开始，此期间可以领券
	ValidGetEnd       int64              `json:"valid_get_end" bson:"valid_get_end"`             // 有效期-获取-结束
	ValidUseBegin     int64              `json:"valid_use_begin" bson:"valid_use_begin"`         // 有效期-使用-开始--有效类型  1:时间区间 必填
	ValidUseEnd       int64              `json:"valid_use_end" bson:"valid_use_end"`             // 有效期-使用-结束
	MaxNumPer         int                `json:"max_num_per" bson:"max_num_per"`                 // 领取上限
	HasGetNum         int                `json:"has_get_num" bson:"has_get_num"`                 // 已领取数量
	IsOpen            bool               `bson:"is_open" json:"is_open"`                         // 是否开放
	Note              string             `json:"note" bson:"note"`                               // 备注
	CreatedAt         int64              `json:"created_at" bson:"created_at"`
	UpdatedAt         int64              `json:"updated_at" bson:"updated_at"`
	DeletedAt         int64              `json:"deleted_at" bson:"deleted_at"`
}

// UseScope 使用范围
type UseScope struct {
	ScopeType   CouponScopeProductType `json:"scope_type" bson:"scope_type"`     // 范围类型
	CategoryID  primitive.ObjectID     `json:"category_id" bson:"category_id"`   // 类型ID
	ProductList []primitive.ObjectID   `json:"product_list" bson:"product_list"` // 产品列表
}

// CouponScopeProductType 使用商品范围
type CouponScopeProductType int

const (
	CouponScopeTypeAll CouponScopeProductType = 1 + iota
	CouponScopeTypeCategoryLevel1
	CouponScopeTypeCategoryLevel2
	CouponScopeTypeProduct
)

var CouponScopeTypeMsg = map[CouponScopeProductType]string{
	CouponScopeTypeAll:            "所有",
	CouponScopeTypeCategoryLevel1: "商品一类",
	CouponScopeTypeCategoryLevel2: "商品二类",
	CouponScopeTypeProduct:        "商品",
}

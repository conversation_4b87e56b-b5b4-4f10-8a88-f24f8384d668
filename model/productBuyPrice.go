package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// ProductBuyPrice 采购价
type ProductBuyPrice struct {
	ID                  primitive.ObjectID   `json:"id" bson:"_id"`
	ServicePointID      primitive.ObjectID   `json:"service_point_id" bson:"service_point_id"`
	SupplierID          primitive.ObjectID   `json:"supplier_id" bson:"supplier_id"`
	SupplierSimpleName  string               `json:"supplier_simple_name" bson:"supplier_simple_name"`
	ProductID           primitive.ObjectID   `json:"product_id" bson:"product_id"`
	SkuIDCode           string               `json:"sku_id_code" bson:"sku_id_code"`
	SkuName             string               `json:"sku_name" bson:"sku_name"`
	CategoryIDs         []primitive.ObjectID `bson:"category_ids" json:"category_ids"` // 商品分类信息
	ProductTitle        string               `json:"product_title" bson:"product_title"`
	AveragePrice        int                  `json:"average_price" bson:"average_price"`                   // 均价
	AverageUnitPrice    int                  `json:"average_unit_price" bson:"average_unit_price"`         // 均价-单价
	RoughWeight         int                  `json:"rough_weight" bson:"rough_weight"`                     // 毛重
	TotalStandardWeight int                  `json:"total_standard_weight" bson:"total_standard_weight"`   // 总订单重量
	TotalSortWeight     int                  `json:"total_sort_weight" bson:"total_sort_weight"`           // 总分拣重量
	IsCheckWeight       bool                 `json:"is_check_weight" bson:"is_check_weight"`               // 是否计重
	TotalNum            int                  `json:"total_num" bson:"total_num"`                           // 订单总数
	BuyNum              int                  `json:"buy_num" bson:"buy_num"`                               // 购买数
	BuyAmount           int                  `json:"buy_amount" bson:"buy_amount"`                         // 采购总价
	SupplyAmount        int                  `json:"supply_amount" bson:"supply_amount"`                   // 采购总价
	AverageBuyPrice     int                  `json:"average_buy_price" bson:"average_buy_price"`           // 采购-均价
	AverageBuyUnitPrice int                  `json:"average_buy_unit_price" bson:"average_buy_unit_price"` // 采购-均价-单价
	ProfitAmount        int                  `json:"profit_amount" bson:"profit_amount"`                   // 利润
	ProfitPercent       float64              `json:"profit_percent" bson:"profit_percent"`                 // 利润率
	OrderList           []PerOrderData       `json:"order_list" bson:"order_list"`                         // 订单列表
	StockUpNo           int                  `json:"stock_up_no" bson:"stock_up_no"`                       // 备货 批次
	StockUpDayTime      int64                `json:"stock_up_day_time" bson:"stock_up_day_time"`           // 备货 日期
	RefreshStatus       int                  `json:"refresh_status" bson:"refresh_status"`                 // 刷新状态 0 1 可刷新
	CreatedAt           int64                `bson:"created_at" json:"created_at"`
	UpdatedAt           int64                `bson:"updated_at" json:"updated_at"`
}

type PerOrderData struct {
	OrderID         primitive.ObjectID `json:"order_id" bson:"order_id"` // 订单ID
	Price           int                `json:"price" bson:"price"`
	SupplyPrice     int                `json:"supply_price" bson:"supply_price"`
	UnitPrice       int                `json:"unit_price" bson:"unit_price"`               // 单价
	SupplyUnitPrice int                `json:"supply_unit_price" bson:"supply_unit_price"` // 单价
	TotalNum        int                `json:"total_num" bson:"total_num"`                 // 订单总数
}

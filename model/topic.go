package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// Topic 专题
type Topic struct {
	ID          primitive.ObjectID   `bson:"_id" json:"id"`
	Sort        int                  `bson:"sort" json:"sort"`       // 排序
	Visible     bool                 `bson:"visible" json:"visible"` // 是否展示
	Page        string               `json:"page" bson:"page"`       // 页面
	TopImg      FileInfo             `json:"top_img" bson:"top_img"` // 顶图
	ProductList []primitive.ObjectID `json:"product_list" bson:"product_list"`
	Img         FileInfo             `bson:"img" json:"img"` // 图片
	CreatedAt   int64                `bson:"created_at" json:"created_at"`
	UpdatedAt   int64                `bson:"updated_at" json:"updated_at"`
}

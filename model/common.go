package model

import "base/core/xerr"

// Contact 联系人
type Contact struct {
	Name   string `json:"name" bson:"name"  validate:"-"`     // 姓名
	Mobile string `json:"mobile" bson:"mobile"  validate:"-"` // 手机
}

// Location 定位
type Location struct {
	Longitude    float64 `json:"longitude" bson:"longitude"`         // 经度
	Latitude     float64 `json:"latitude" bson:"latitude"`           // 维度
	Name         string  `json:"name" bson:"name"`                   // 位置名称
	Address      string  `json:"address" bson:"address"`             // 详细地址
	Province     string  `json:"province" bson:"province"`           // 省份
	ProvinceCode string  `json:"province_code" bson:"province_code"` // 省份code
	City         string  `json:"city" bson:"city"`                   // 城市
	CityCode     string  `json:"city_code" bson:"city_code"`         // 城市code
	District     string  `json:"district" bson:"district"`           // 区县
	DistrictCode string  `json:"district_code" bson:"district_code"` // 区县
}

// FileInfo 图片
type FileInfo struct {
	Type       string `json:"type" bson:"type"`               // 文件类型， 图片类型或视频类型
	OriginName string `json:"origin_name" bson:"origin_name"` // 文件原名称，前端展示使用
	Name       string `json:"name" bson:"name"`               // 文件在桶中的名称
	Uri        string `json:"uri" bson:"uri"`                 // 图片地址
	Poster     string `json:"poster" bson:"poster"`           // 视频封面
}

type TagPer struct {
	Key   string `json:"key" bson:"key"`     //
	Value string `json:"value" bson:"value"` //
}

// FieldInfo 字段信息
type FieldInfo struct {
	Field string `json:"field" bson:"field"` // 展示字段
	Value string `json:"value" bson:"value"` // 值
}

// AuditNotPass 审核未通过原因
type AuditNotPass struct {
	Content   string `json:"content" bson:"content"` // 内容
	CreatedAt int64  `bson:"created_at" json:"created_at"`
}

func BackObjectType(objectType int) (ObjectType, error) {
	switch ObjectType(objectType) {
	case ObjectTypeBuyer:
		return ObjectTypeBuyer, nil
	case ObjectTypeSupplier:
		return ObjectTypeSupplier, nil
	case ObjectTypeServicePoint:
		return ObjectTypeServicePoint, nil
	case ObjectTypeWarehouse:
		return ObjectTypeWarehouse, nil
	default:
		return 0, xerr.NewErr(xerr.ErrParamError, nil, "对象类型错误")
	}
}

func BackAuditStatusType(s int) (AuditStatusType, error) {
	switch AuditStatusType(s) {
	case AuditStatusTypeDoing:
		return AuditStatusTypeDoing, nil
	case AuditStatusTypeNotPass:
		return AuditStatusTypeNotPass, nil
	case AuditStatusTypePass:
		return AuditStatusTypePass, nil
	default:
		return 0, xerr.NewErr(xerr.ErrParamError, nil, "审核类型错误")
	}
}

func BackRefundType(s int) (RefundType, error) {
	switch RefundType(s) {
	case RefundTypeAfterSale:
		return RefundTypeAfterSale, nil
	case RefundTypeQuality:
		return RefundTypeQuality, nil
	default:
		return 0, xerr.NewErr(xerr.ErrParamError, nil, "退款类型错误")
	}
}

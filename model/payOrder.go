package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type PayOrderType int

const (
	PayOrderTypeDepositApply PayOrderType = 1 // 充值
	PayOrderTypeWithdraw                  = 2 // 提现
	PayOrderTypeAgentCollect              = 3 // 托管代收
	PayOrderTypeAgentPay                  = 4 // 托管代付
	PayOrderTypeRefund                    = 5 // 退款
)

// PayOrder  支付单--调用通联
type PayOrder struct {
	ID           primitive.ObjectID `json:"id" bson:"_id"`
	PayOrderType PayOrderType       `json:"pay_order_type" bson:"pay_order_type"` // 订单类型
	BizOrderNo   string             `json:"biz_order_no" bson:"biz_order_no"`     // 订单编号
	CreatedAt    int64              `bson:"created_at" json:"created_at"`
	UpdatedAt    int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt    int64              `bson:"deleted_at" json:"deleted_at"`
}

package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// PurchaseCart 采购购物车
type PurchaseCart struct {
	ID                primitive.ObjectID `bson:"_id" json:"id"`
	SupplierID        primitive.ObjectID `bson:"supplier_id" json:"supplier_id"`                 // 当前供应商ID
	ProductID         primitive.ObjectID `bson:"product_id" json:"product_id"`                   // 所属商品ID
	ProductSupplierID primitive.ObjectID `bson:"product_supplier_id" json:"product_supplier_id"` // 供应商ID
	Count             int                `bson:"count" json:"count"`                             // 商品数量
}

type CartProduct struct {
	ProductID   string `json:"product_id"`   // 商品ID
	Price       int    `json:"price"`        // 单价
	Num         int    `json:"num"`          // 数量
	RoughWeight int    `json:"rough_weight"` // 单一毛重
}

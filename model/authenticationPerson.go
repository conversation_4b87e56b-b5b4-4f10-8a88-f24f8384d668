package model

import (
	pays "github.com/cnbattle/allinpay/service"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AuthenticationPerson 适用（个人会员）个人采购商
type AuthenticationPerson struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	UserID         primitive.ObjectID `json:"user_id" bson:"user_id"`                   // 用户ID
	ObjectType     ObjectType         `json:"object_type" bson:"object_type"`           // 对象类型
	ObjectID       primitive.ObjectID `json:"object_id" bson:"object_id"`               // 对象ID
	Mobile         string             `json:"mobile" bson:"mobile"`                     // 手机号-支付
	IsMobileVerify bool               `json:"is_mobile_verify" bson:"is_mobile_verify"` // 手机号-支付是否验证
	MemberType     pays.MemberType    `json:"member_type" bson:"member_type"`           // 会员类型
	PayUserID      string             `json:"pay_user_id" bson:"pay_user_id"`           // 云商通用户唯一标识
	PayBizUserId   string             `json:"pay_biz_user_id" bson:"pay_biz_user_id"`   // 商户系统用户标识，商户系统中唯一编号。
	CreatedAt      int64              `bson:"created_at" json:"created_at"`
	UpdatedAt      int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt      int64              `bson:"deleted_at" json:"deleted_at"`
}

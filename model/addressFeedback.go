package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// AddressFeedback 地址反馈
type AddressFeedback struct {
	ID              primitive.ObjectID `bson:"_id"  json:"id"`
	AddressID       primitive.ObjectID `bson:"address_id" json:"address_id"`
	ServicePointID  primitive.ObjectID `bson:"service_point_id" json:"service_point_id"`
	BuyerID         primitive.ObjectID `bson:"buyer_id" json:"buyer_id"`
	BuyerName       string             `json:"buyer_name" bson:"buyer_name"`
	FeedbackType    int                `json:"feedback_type" bson:"feedback_type"`       // 反馈类型  1 配送费 2 地址 3 配送方式 4 补贴 5 其他
	FeedbackContent string             `json:"feedback_content" bson:"feedback_content"` // 反馈内容
	FeedbackResult  string             `json:"feedback_result" bson:"feedback_result"`   // 反馈结果
	AuditStatus     AuditStatusType    `json:"audit_status" bson:"audit_status"`         // 作为处理状态    1 2
	CreatedAt       int64              `bson:"created_at"  json:"created_at"`
	UpdatedAt       int64              `bson:"updated_at" json:"updated_at"`
}

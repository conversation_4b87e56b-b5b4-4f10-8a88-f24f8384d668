package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type IdentityStatus string

const (
	IdentityStatusNo  IdentityStatus = "no"
	IdentityStatusYes IdentityStatus = "yes"
)

type BankBindStatus string

const (
	BankBindStatusNo  BankBindStatus = "no"
	BankBindStatusYes BankBindStatus = "yes"
)

type OpenStatus string

const (
	OpenStatusOpen   OpenStatus = "open"
	OpenStatusClosed OpenStatus = "closed"
)

// Station 站点
type Station struct {
	ID             primitive.ObjectID `bson:"_id" json:"id"`
	ServicePointID primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`               //
	UserID         primitive.ObjectID `bson:"user_id" json:"user_id"`                                 // 用户ID
	Name           string             `bson:"name" json:"name"`                                       // 名称
	DeliverType    []DeliverType      `json:"deliver_type" bson:"deliver_type"`                       // 配送方式
	ContactUser    string             `json:"contact_user"  bson:"contact_user"  validate:"required"` // 联系人
	ContactMobile  string             `json:"contact_mobile" bson:"contact_mobile"`                   // 联系人
	Location       Location           `bson:"location" json:"location"`                               // 定位地址
	Address        string             `bson:"address" json:"address"`                                 // 详细地址
	//IdentityStatus IdentityStatus     `json:"identity_status" bson:"identity_status"`
	//BankBindStatus BankBindStatus     `json:"bank_bind_status" bson:"bank_bind_status"`
	OpenStatus     OpenStatus `json:"open_status" bson:"open_status"`
	CommissionRate int        `json:"commission_rate" bson:"commission_rate"` // 佣金
	CreatedAt      int64      `bson:"created_at" json:"created_at"`
	UpdatedAt      int64      `bson:"updated_at" json:"updated_at"`
	DeletedAt      int64      `bson:"deleted_at" json:"deleted_at"`
}

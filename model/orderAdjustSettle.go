package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// OrderAdjustSettle 订单调整结算记录
type OrderAdjustSettle struct {
	ID                primitive.ObjectID      `json:"id" bson:"_id"`
	OrderID           primitive.ObjectID      `json:"order_id" bson:"order_id"`                         // 关联订单ID
	BuyerID           primitive.ObjectID      `json:"buyer_id" bson:"buyer_id"`                         // 采购商ID
	BuyerName         string                  `json:"buyer_name" bson:"buyer_name"`                     // 采购商名称
	SupplierID        primitive.ObjectID      `json:"supplier_id" bson:"supplier_id"`                   // 供应商ID
	SupplierName      string                  `json:"supplier_name" bson:"supplier_name"`               // 供应商名称
	TotalAmount       int                     `json:"total_amount" bson:"total_amount"`                 // 总调价金额
	ProductList       []ProductAdjustSettle   `json:"product_list" bson:"product_list"`                 // 商品调价明细
	Remark            string                  `json:"remark" bson:"remark"`                             // 调价备注
	CreatedByUserID   primitive.ObjectID      `json:"created_by_user_id" bson:"created_by_user_id"`     // 创建人ID
	CreatedByName     string                  `json:"created_by_name" bson:"created_by_name"`           // 创建人姓名
	ConfirmedByUserID primitive.ObjectID      `json:"confirmed_by_user_id" bson:"confirmed_by_user_id"` // 确认人ID
	ConfirmedByName   string                  `json:"confirmed_by_name" bson:"confirmed_by_name"`       // 确认人姓名
	RefundResult      YeeRefundResult         `json:"refund_result" bson:"refund_result"`               // 退款结果
	Status            OrderAdjustSettleStatus `json:"status" bson:"status"`                             // 调价状态
	ConfirmedAt       int64                   `json:"confirmed_at" bson:"confirmed_at"`                 // 确认时间
	CreatedAt         int64                   `json:"created_at" bson:"created_at"`
	UpdatedAt         int64                   `json:"updated_at" bson:"updated_at"`
}

// ProductAdjustSettle 商品调整结算明细
type ProductAdjustSettle struct {
	ProductID        primitive.ObjectID `json:"product_id" bson:"product_id"`               // 商品ID
	ProductTitle     string             `json:"product_title" bson:"product_title"`         // 商品标题
	ProductCoverImg  string             `json:"product_cover_img" bson:"product_cover_img"` // 商品封面
	SkuIDCode        string             `json:"sku_id_code" bson:"sku_id_code"`             // SKU编码
	SkuName          string             `json:"sku_name" bson:"sku_name"`                   // SKU名称
	OrderAmount      int                `json:"order_amount" bson:"order_amount"`           // 订单总价
	AdjustAmount     int                `json:"adjust_amount" bson:"adjust_amount"`         // 调价金额
	AdjustRemark     string             `json:"adjust_remark" bson:"adjust_remark"`         // 调价备注
}

// OrderAdjustSettleStatus 调整结算状态
type OrderAdjustSettleStatus string

const (
	// OrderAdjustSettleStatusDraft 草稿状态(可编辑)
	OrderAdjustSettleStatusDraft OrderAdjustSettleStatus = "draft"
	// OrderAdjustSettleStatusConfirmed 已确认(进入退款流程)
	OrderAdjustSettleStatusConfirmed OrderAdjustSettleStatus = "confirmed"
	// OrderAdjustSettleStatusRefunding 退款中
	OrderAdjustSettleStatusRefunding OrderAdjustSettleStatus = "refunding"
	// OrderAdjustSettleStatusCompleted 已完成
	OrderAdjustSettleStatusCompleted OrderAdjustSettleStatus = "completed"
	// OrderAdjustSettleStatusFailed 退款失败
	OrderAdjustSettleStatusFailed OrderAdjustSettleStatus = "failed"
	// OrderAdjustSettleStatusClosed 已关闭
	OrderAdjustSettleStatusClosed OrderAdjustSettleStatus = "closed"
)

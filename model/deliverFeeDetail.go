package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// DeliverFeeDetail 配送费明细记录
type DeliverFeeDetail struct {
	ID                 primitive.ObjectID `json:"id" bson:"_id"`
	ParentOrderID      primitive.ObjectID `json:"parent_order_id" bson:"parent_order_id"`           // 父订单ID
	BuyerID            primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`                         // 采购商ID
	DeliverType        DeliverType        `json:"deliver_type" bson:"deliver_type"`                 // 配送方式
	InstantDeliverType int                `json:"instant_deliver_type" bson:"instant_deliver_type"` // 即时配送方式
	InstantDeliverName string             `json:"instant_deliver_name" bson:"instant_deliver_name"` // 即时配送方式名称
	TotalDeliverFee    int                `json:"total_deliver_fee" bson:"total_deliver_fee"`       // 总配送费
	SubsidyDeliverFee  int                `json:"subsidy_deliver_fee" bson:"subsidy_deliver_fee"`   // 补贴配送费
	FinalDeliverFee    int                `json:"final_deliver_fee" bson:"final_deliver_fee"`       // 最终配送费
	OrderCreatedAt     int64              `json:"order_created_at" bson:"order_created_at"`         // 订单创建时间
	CreatedAt          int64              `json:"created_at" bson:"created_at"`                     // 创建时间
}

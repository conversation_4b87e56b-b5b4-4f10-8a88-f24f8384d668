package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type EventType string

const (
	EventTypeAppEnter     EventType = "AppEnter"
	EventTypeAppLeave     EventType = "AppLeave"
	EventTypeProductEnter EventType = "ProductEnter"
	EventTypeProductLeave EventType = "ProductLeave"
	EventTypeHeartBeat    EventType = "HeartBeat"
)

type Track struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	VisitType int                `json:"visit_type" bson:"visit_type"`
	BuyerID   primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	Event     EventType          `json:"event" bson:"event"`
	ProductID primitive.ObjectID `json:"product_id" bson:"product_id"`
	IP        string             `json:"ip" bson:"ip"`
	CreatedAt int64              `json:"created_at" bson:"created_at"`
	LeavedAt  int64              `json:"leaved_at" bson:"leaved_at"`
}

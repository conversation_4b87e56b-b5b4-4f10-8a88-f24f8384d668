package model

type QueueEvent string

const (
	QueueEventBalanceCreateRecord QueueEvent = "balanceCreateRecord"
)

const (
	QueueEventDivideNormalOrder   QueueEvent = "divideNormalOrder"
	QueueEventDivideDebtOrder     QueueEvent = "divideDebtOrder"
	QueueEventDivideIntegralOrder QueueEvent = "divideIntegralOrder"
	QueueEventDivideFlatCheck     QueueEvent = "DivideFlatCheck"
	//QueueEventDivideEndCheck      QueueEvent = "DivideEndCheck"
	QueueEventDivideDeliver QueueEvent = "divideDeliver"
)

const (
	QueueEventProductUp   QueueEvent = "productUp"
	QueueEventProductDown QueueEvent = "productDown"
)

type QueueMessage struct {
	MessageID   string     `json:"message_id"`
	Event       QueueEvent `json:"event"`
	Value       string     `json:"value"`
	DelaySecond int64      `json:"delay_second"`
}

type QueueValueRemoveCart struct {
	CartIDList []string `json:"cart_id_list"`
}

type QueueValueOrderIDList struct {
	OrderIDList []string `json:"order_id_list"`
}

type QueueValueBalanceRecord struct {
	BuyerID                string                 `json:"buyer_id"`
	ObjectID               string                 `json:"object_id"`
	BuyerBalanceRecordType BuyerBalanceRecordType `json:"buyer_balance_record_type"`
	Amount                 int                    `json:"amount"`
}

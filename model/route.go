package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// Route 路线-集中仓至服务点
type Route struct {
	ID               primitive.ObjectID `json:"id" bson:"_id"`
	FromWarehouseID  primitive.ObjectID `json:"from_warehouse_id" bson:"from_warehouse_id"`     // 集中仓
	RouteType        RouteType          `json:"route_type" bson:"route_type"`                   // 线路类型
	ToWarehouseID    primitive.ObjectID `json:"to_warehouse_id" bson:"to_warehouse_id"`         // 集中仓
	ToServicePointID primitive.ObjectID `json:"to_service_point_id" bson:"to_service_point_id"` // 服务点
	ActualDistance   int                `json:"actual_distance" bson:"actual_distance"`         // 实际距离/m
	FeePerKG         int                `json:"fee_per_kg" bson:"fee_per_kg"`                   // 分/kg
	Note             string             `json:"note" bson:"note"`                               // 备注
	DeliverTime      []DeliverTime      `json:"deliver_time" bson:"deliver_time"`
	CreatedAt        int64              `bson:"created_at" json:"created_at"`
	UpdatedAt        int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt        int64              `bson:"deleted_at" json:"deleted_at"`
}

// DeliverTime 配送时间
type DeliverTime struct {
	OrderBeginTime int64 `json:"order_begin_time" bson:"order_begin_time"` // 订单开始时间  hh 24小时格式
	OrderEndTime   int64 `json:"order_end_time" bson:"order_end_time"`     // 订单结束时间  hh 24小时格式
	OffsetDay      int64 `json:"offset_day" bson:"offset_day"`             // 偏移天数
	ReachTime      int64 `json:"reach_time" bson:"reach_time"`             // 到达时间  hh 24小时格式
}

// RouteType 线路类型
type RouteType int

const (
	RouteTypeWarehouse    RouteType = 1 // 集中仓-集中仓
	RouteTypeServicePoint RouteType = 2 // 集中仓-服务点
)

var RouteTypeMsg = map[RouteType]string{
	RouteTypeWarehouse:    "集中仓-集中仓",
	RouteTypeServicePoint: "集中仓-服务点",
}

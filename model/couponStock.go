package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// CouponStockType 优惠券批次类型
type CouponStockType string

const (
	// CouponStockTypeNewUser 新人券
	CouponStockTypeNewUser CouponStockType = "newUser"
	// CouponStockTypeNormal 普通券
	CouponStockTypeNormal CouponStockType = "normal"
)

// CouponStockStatus 优惠券批次状态
type CouponStockStatus string

const (
	// CouponStockStatusValid 有效
	CouponStockStatusValid CouponStockStatus = "valid"
	// CouponStockStatusInvalid 无效
	CouponStockStatusInvalid CouponStockStatus = "invalid"
	// CouponStockStatusExpired 已过期
	CouponStockStatusExpired CouponStockStatus = "expired"
)

// CouponStock 优惠券批次
type CouponStock struct {
	ID                 primitive.ObjectID `json:"id" bson:"_id"`
	CouponStockType    CouponStockType    `json:"coupon_stock_type" bson:"coupon_stock_type"`       //  类型
	Title              string             `json:"title" bson:"title"`                               //  标题
	Description        string             `json:"description" bson:"description"`                   //  描述
	AvailableBeginTime int64              `json:"available_begin_time" bson:"available_begin_time"` // 可用时间
	AvailableEndTime   int64              `json:"available_end_time" bson:"available_end_time"`     //  可用时间
	ValidDays          int                `json:"valid_days" bson:"valid_days"`                     //  生效时长天
	MaxSendNum         int                `json:"max_send_num" bson:"max_send_num"`                 //  最大发放数量
	MaxPerUserNum      int                `json:"max_per_user_num" bson:"max_per_user_num"`         //  最大领取数量
	CouponAmount       int                `json:"coupon_amount" bson:"coupon_amount"`               //  优惠券面额
	MinAmount          int                `json:"min_amount" bson:"min_amount"`                     //  门槛金额
	CouponStatus       CouponStockStatus  `json:"coupon_status" bson:"coupon_status"`               // 状态
	SendedNum          int                `json:"sended_num" bson:"sended_num"`                     //  已发放数量
	CreatedAt          int64              `json:"created_at" bson:"created_at"`
	UpdatedAt          int64              `json:"updated_at" bson:"updated_at"`
	DeletedAt          int64              `json:"deleted_at" bson:"deleted_at"` // 软删除时间
}

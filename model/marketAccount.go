package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type ChangeType int

const (
	ChangeTypeCharge  ChangeType = 1 // 充值
	ChangeTypeConsume ChangeType = 2 // 消费
	ChangeTypeRefund  ChangeType = 2 // 退款
)

// MarketingAccount 营销账户
type MarketingAccount struct {
	ID               primitive.ObjectID `bson:"_id" json:"id"`
	BuyerID          primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`                       // 采购商ID
	ChangeType       ChangeType         `json:"change_type" bson:"change_type"`                 // 变化类型
	BizOrderNo       string             `json:"biz_order_no" bson:"biz_order_no"`               // 支付订单号（云商通
	BizOrderNoResult PayResult          `json:"biz_order_no_result" bson:"biz_order_no_result"` // 微信支付结果
	Amount           int                `json:"total_amount" bson:"total_amount"`               // 总金额
	PayStatus        PayStatusType      `json:"pay_status" bson:"pay_status"`                   // 支付状态
	Expire           int64              `json:"expire" bson:"expire"`                           // 订单超时
	PayResult        PayResult          `json:"pay_result" bson:"pay_result"`                   // 代收支付结果
	CreatedAt        int64              `bson:"created_at" json:"created_at"`
	UpdatedAt        int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt        int64              `bson:"deleted_at" json:"deleted_at"`
}

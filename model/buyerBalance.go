package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// BuyerBalanceAccount 余额账户
type BuyerBalanceAccount struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	BuyerID   primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	Amount    int                `json:"amount" bson:"amount"`
	CreatedAt int64              `json:"created_at" bson:"created_at"`
	UpdatedAt int64              `json:"updated_at" bson:"updated_at"`
}

// BuyerBalanceOrder 余额订单,充值和提现
type BuyerBalanceOrder struct {
	ID                   primitive.ObjectID `json:"id" bson:"_id"`
	BuyerID              primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`
	OrderID              primitive.ObjectID `json:"order_id" bson:"order_id"`
	BizOrderNo           string             `json:"biz_order_no" bson:"biz_order_no"`
	OrderType            int                `json:"order_type" bson:"order_type"` // 订单类型  1 重置 2 提现
	Amount               int                `json:"amount" bson:"amount"`         // 金额
	PayStatus            PayStatusType      `json:"pay_status" bson:"pay_status"`
	PayResult            PayResult          `json:"pay_result" bson:"pay_result"`
	Note                 string             `json:"note" bson:"note"`                 // 备注
	WithdrawFee          int                `json:"withdraw_fee" bson:"withdraw_fee"` // 提现手续费
	WithdrawBizUserID    string             `json:"withdraw_biz_user_id" bson:"withdraw_biz_user_id"`
	WithdrawValidateType int                `json:"withdraw_validate_type" bson:"withdraw_validate_type"`   // 提现验证方式
	WithdrawBankCardNo   string             `json:"withdraw_bank_card_no" bson:"withdraw_bank_card_no"`     // 提现-银行卡号/账号
	WithdrawBankCardPro  int                `json:"withdraw_bank_card_pro" bson:"withdraw_bank_card_pro"`   // 提现-// 银行卡/账户属性 0：个人银行卡  1：企业对公账户  如果不传默认为0  平台提现，必填1
	WithdrawAccountSetNo string             `json:"withdraw_account_set_no" bson:"withdraw_account_set_no"` // 提现
	CreatedAt            int64              `json:"created_at" bson:"created_at"`
	UpdatedAt            int64              `json:"updated_at" bson:"updated_at"`
}

type BuyerBalanceRecordType int

const (
	BuyerBalanceRecordTypeOrder          BuyerBalanceRecordType = 11 // 下单支付
	BuyerBalanceRecordTypeOrderDebtPay   BuyerBalanceRecordType = 12 // 补差支付
	BuyerBalanceRecordTypeOrderCancel    BuyerBalanceRecordType = 13 // 取消订单
	BuyerBalanceRecordTypeOrderQuality   BuyerBalanceRecordType = 14 // 品控退款
	BuyerBalanceRecordTypeOrderAfterSale BuyerBalanceRecordType = 15 // 售后退款
	BuyerBalanceRecordTypeDeliverFee     BuyerBalanceRecordType = 16 // 配送费退款
	BuyerBalanceRecordTypeAdjustPlus     BuyerBalanceRecordType = 21 // 调账-加
	BuyerBalanceRecordTypeAdjustMinus    BuyerBalanceRecordType = 22 // 调账-减
)

// BuyerBalanceRecord 消费记录
type BuyerBalanceRecord struct {
	ID                     primitive.ObjectID     `json:"id" bson:"_id"`
	BuyerID                primitive.ObjectID     `json:"buyer_id" bson:"buyer_id"`
	ObjectID               primitive.ObjectID     `json:"object_id" bson:"object_id"`                                 // 对象ID
	Amount                 int                    `json:"amount" bson:"amount"`                                       // 金额
	BuyerBalanceRecordType BuyerBalanceRecordType `json:"buyer_balance_record_type" bson:"buyer_balance_record_type"` // 记录类型
	Note                   string                 `json:"note" bson:"note"`                                           // 备注
	CreatedAt              int64                  `json:"created_at" bson:"created_at"`
	UpdatedAt              int64                  `json:"updated_at" bson:"updated_at"`
}

type MNSProductID struct {
	ProductID string `json:"product_id"`
}

type MNSBuyerID struct {
	BuyerID string `json:"buyer_id"`
}

package model

// UniformMessage 统一下发消息
type UniformMessage struct {
	TemplateID string                 `json:"template_id" bson:"template_id"` // 模板ID
	OpenID     string                 `json:"open_id" bson:"open_id"`
	MiniAppID  string                 `json:"mini_app_id" bson:"mini_app_id"`
	PagePath   string                 `json:"page_path" bson:"page_path"`
	Data       map[string]interface{} `json:"data" bson:"data"`
	CreateAt   int64                  `json:"create_at" bson:"create_at"`
}

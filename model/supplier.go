package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SupplierLevel string

const (
	SupplierLevelPoint   SupplierLevel = "point"
	SupplierLevelStation SupplierLevel = "station"
)

// Supplier 供应商
type Supplier struct {
	ID                  primitive.ObjectID `bson:"_id" json:"id"`
	UserID              primitive.ObjectID `bson:"user_id" json:"user_id"`                             // 用户ID
	ShopSimpleName      string             `bson:"shop_simple_name" json:"shop_simple_name"`           // 店铺简称
	TagList             []SupplierTag      `json:"tag_list" bson:"tag_list"`                           // 商家标签
	ContactUser         string             `bson:"contact_user" json:"contact_user"`                   // 联系人
	Location            Location           `bson:"location" json:"location"`                           // 定位地址
	Address             string             `bson:"address" json:"address"`                             // 详细地址
	AvatarImg           FileInfo           `bson:"avatar_img" json:"avatar_img"`                       // 头像
	ServicePointID      primitive.ObjectID `bson:"service_point_id" json:"service_point_id"`           // 服务仓
	StationID           primitive.ObjectID `bson:"station_id" json:"station_id"`                       // 服务仓
	AccountStatus       AccountStatusType  `bson:"account_status" json:"account_status"`               // 账号状态
	SupplierServiceFee  float64            `bson:"supplier_service_fee" json:"supplier_service_fee"`   // 供应商服务费费率
	Level               SupplierLevel      `bson:"level" json:"level"`                                 // 供应商等级
	FrozenBalanceAmount int                `bson:"frozen_balance_amount" json:"frozen_balance_amount"` //  冻结金额
	IsBusinessManage    bool               `bson:"is_business_manage" json:"is_business_manage"`       // 是否经营管理
	CreatedAt           int64              `bson:"created_at" json:"created_at"`
	UpdatedAt           int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt           int64              `bson:"deleted_at" json:"deleted_at"`
}

//ShopHeadImg        FileInfo           `bson:"shop_head_img" json:"shop_head_img"`               // 门头照
//ShopImgList        []FileInfo         `bson:"shop_img_list" json:"shop_img_list"`               // 经营场所
// MainBusiness       []MainBusinessType `json:"main_business" bson:"main_business"`               // 主营行业
// ShopName           string             `bson:"shop_name" json:"shop_name"`                       // 店铺名称

type SupplierStats struct {
	SupplierID         primitive.ObjectID `bson:"_id" json:"supplier_id"`
	SaleTrueCount      int                `bson:"sale_true_count" json:"sale_true_count"`
	SaleFalseCount     int                `bson:"sale_false_count" json:"sale_false_count"`
	TotalItemCount     int                `bson:"total_item_count" json:"total_item_count"`
	TotalToUpdateCount int                `bson:"total_to_update_count" json:"total_to_update_count"`
}

type CategoryOnly struct {
	CategoryIds []primitive.ObjectID `json:"category_ids" bson:"category_ids"`
}

package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type PromoteStatus int

const (
	PromoteStatusVisible   = 1
	PromoteStatusInvisible = 2
)

// Promote 推广
type Promote struct {
	ID             primitive.ObjectID   `bson:"_id" json:"id"`
	ServicePointID primitive.ObjectID   `bson:"service_point_id" json:"service_point_id"`
	Title          string               `json:"title" bson:"title"`
	Desc           string               `json:"desc" bson:"desc"`
	Sort           int                  `bson:"sort" json:"sort"`     // 排序
	Status         PromoteStatus        `json:"status" bson:"status"` // 状态
	Cover          FileInfo             `json:"cover" bson:"cover"`   // 封面
	Video          FileInfo             `json:"video" bson:"video"`   // 视频
	ProductList    []primitive.ObjectID `json:"product_list" bson:"product_list"`
	CreatedAt      int64                `bson:"created_at" json:"created_at"`
	UpdatedAt      int64                `bson:"updated_at" json:"updated_at"`
}

package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type RetailAddress struct {
	ID        primitive.ObjectID `bson:"_id"  json:"id"`
	UserID    primitive.ObjectID `bson:"user_id" json:"user_id"`
	Address   string             `bson:"address" json:"address"`   // 具体地址
	Contact   Contact            `bson:"contact" json:"contact"`   // 联系人信息
	Location  Location           `bson:"location" json:"location"` // 地址经纬度
	CreatedAt int64              `bson:"created_at"  json:"created_at"`
	UpdatedAt int64              `bson:"updated_at" json:"updated_at"`
}

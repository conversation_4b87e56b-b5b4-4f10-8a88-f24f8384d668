package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type ProductCollect struct {
	ID             primitive.ObjectID `json:"id" bson:"_id"`
	ProductID      primitive.ObjectID `json:"product_id" bson:"product_id"`             // 商品ID
	BuyerID        primitive.ObjectID `json:"buyer_id" bson:"buyer_id"`                 // 采购商ID
	ServicePointID primitive.ObjectID `json:"service_point_id" bson:"service_point_id"` // 服务仓ID
	CreatedAt      int64              `bson:"created_at" json:"created_at"`
}

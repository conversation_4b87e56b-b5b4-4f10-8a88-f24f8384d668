package model

import "go.mongodb.org/mongo-driver/bson/primitive"

// 标签

// Tag 分类标签
type Tag struct {
	ID         primitive.ObjectID `json:"id" bson:"_id"`
	CategoryID primitive.ObjectID `json:"category_id" bson:"category_id"` // 二级分类ID
	Title      string             `json:"title" bson:"title"`             // 名称
	Valid      bool               `json:"valid" bson:"valid"`             // 有效
	CreatedAt  int64              `json:"created_at" bson:"created_at"`
}

// SupplierTag 供应商标签
type SupplierTag struct {
	ID    primitive.ObjectID `json:"id" bson:"_id"`
	Title string             `json:"title" bson:"title"` // 名称
	Color string             `json:"color" bson:"color"` // 颜色
	//CreatedAt int64              `json:"created_at" bson:"created_at"`
	//UpdatedAt int64              `json:"updated_at" bson:"updated_at"`
}

// ProductTag 商品标签
type ProductTag struct {
	ID        primitive.ObjectID `json:"id" bson:"_id"`
	Title     string             `json:"title" bson:"title"`       // 名称
	Img       FileInfo           `json:"img" bson:"img"`           // 图片
	TagType   ProductTagType     `json:"tag_type" bson:"tag_type"` // 标签类型
	Color     string             `json:"color" bson:"color"`       // 颜色
	CreatedAt int64              `json:"created_at" bson:"created_at"`
	UpdatedAt int64              `json:"updated_at" bson:"updated_at"`
	DeletedAt int64              `json:"deleted_at" bson:"deleted_at"`
}
type ProductTagType int

const (
	TagTypeCoverLeftTop ProductTagType = 1 // 1 封面左上
	TagTypeUnderTitle   ProductTagType = 2 // 2 产品标题下
)

package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DeliverAssign 配送指派
type DeliverAssign struct {
	ID              primitive.ObjectID   `bson:"_id"  json:"id"`
	UserID          primitive.ObjectID   `json:"user_id" bson:"user_id"`
	DeliveryManID   primitive.ObjectID   `json:"delivery_man_id" bson:"delivery_man_id"`
	DeliveryManName string               `json:"delivery_man_name" bson:"delivery_man_name"`
	BuyerID         primitive.ObjectID   `json:"buyer_id" bson:"buyer_id"`
	DeliverType     DeliverType          `json:"deliver_type" bson:"deliver_type"`
	OrderIDList     []primitive.ObjectID `json:"order_id_list" bson:"order_id_list"`
	BuyerName       string               `json:"buyer_name" bson:"buyer_name"`
	ServicePointID  primitive.ObjectID   `bson:"service_point_id" json:"service_point_id"`
	Address         OrderAddress         `json:"address" bson:"address"`
	SortWeight      int                  `json:"sort_weight" bson:"sort_weight"`
	SortNum         int                  `json:"sort_num" bson:"sort_num"`
	Timestamp       int64                `json:"timestamp" bson:"timestamp"`
	CreatedAt       int64                `bson:"created_at"  json:"created_at"`
	UpdatedAt       int64                `bson:"updated_at"  json:"updated_at"`
}

type DeliverAssignInfo struct {
	BuyerID   primitive.ObjectID `json:"buyer_id"`
	BuyerName string             `json:"buyer_name"`
	OrderList []Order            `json:"order_list"`
}

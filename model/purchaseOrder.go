package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type PurchaseStatus int

const (
	PurchaseStatusClosed     PurchaseStatus = 1
	PurchaseStatusCancel     PurchaseStatus = 2
	PurchaseStatusToConfirm  PurchaseStatus = 31
	PurchaseStatusPurchasing PurchaseStatus = 41
	PurchaseStatusShipped    PurchaseStatus = 51
	PurchaseStatusFinish     PurchaseStatus = 61
)

var PurchaseStatusMsg = map[PurchaseStatus]string{
	PurchaseStatusClosed:     "已关闭",
	PurchaseStatusCancel:     "已取消",
	PurchaseStatusToConfirm:  "待确认",
	PurchaseStatusPurchasing: "采购中",
	PurchaseStatusShipped:    "已发货",
	PurchaseStatusFinish:     "已完成",
}

// PurchaseOrder 采购订单
type PurchaseOrder struct {
	ID                         primitive.ObjectID     `json:"id" bson:"_id"`
	IDNum                      string                 `json:"id_num" bson:"id_num"`                                               // 订单编号
	PurchaseParentOrderID      primitive.ObjectID     `json:"purchase_parent_order_id" bson:"purchase_parent_order_id"`           // 父单ID
	PayMethod                  PayMethodType          `json:"pay_method" bson:"pay_method"`                                       // 支付方式
	UserID                     primitive.ObjectID     `json:"user_id" bson:"user_id"`                                             // 用户ID
	PurchaseSupplierID         primitive.ObjectID     `json:"purchase_supplier_id" bson:"purchase_supplier_id"`                   // 进货供应商
	PurchaseSupplierName       string                 `json:"purchase_supplier_name" bson:"purchase_supplier_name"`               // 进货供应商
	SupplierID                 primitive.ObjectID     `json:"supplier_id" bson:"supplier_id"`                                     // 供应商ID
	SupplierName               string                 `json:"supplier_name" bson:"supplier_name"`                                 // 供应商名称
	PayBizUserID               string                 `json:"pay_biz_user_id" bson:"pay_biz_user_id"`                             // 付款人-采购
	ReceiverBizUserID          string                 `json:"receiver_biz_user_id" bson:"receiver_biz_user_id"`                   // 收款人-供应商
	TotalPaidAmount            int                    `json:"total_paid_amount" bson:"total_paid_amount"`                         // 实付金额
	TotalProductAmount         int                    `json:"total_product_amount" bson:"total_product_amount"`                   // 商品总金额
	TotalPurchaseProductAmount int                    `json:"total_purchase_product_amount" bson:"total_purchase_product_amount"` // 总采购金额
	ProductList                []PurchaseProductOrder `json:"product_list" bson:"product_list"`                                   // 产品列表
	ServicePointID             primitive.ObjectID     `json:"service_point_id" bson:"service_point_id"`                           // 服务点ID
	PayStatus                  PayStatusType          `json:"pay_status" bson:"pay_status"`                                       // 支付状态
	OrderStatus                PurchaseStatus         `json:"order_status" bson:"order_status"`                                   // 采购状态
	Expire                     int64                  `json:"expire" bson:"expire"`                                               // 订单超时
	CreatedAt                  int64                  `bson:"created_at" json:"created_at"`
	UpdatedAt                  int64                  `bson:"updated_at" json:"updated_at"`
	DeletedAt                  int64                  `bson:"deleted_at" json:"deleted_at"`
}

// PurchaseProductOrder 商品订单
type PurchaseProductOrder struct {
	ProductID                     primitive.ObjectID   `json:"product_id" bson:"product_id"`               // 商品ID
	CategoryIDs                   []primitive.ObjectID `bson:"category_ids" json:"category_ids"`           // 商品分类信息
	ProductTitle                  string               `json:"product_title" bson:"product_title"`         // 商品标题
	ProductCoverImg               FileInfo             `json:"product_cover_img" bson:"product_cover_img"` // 封面
	Price                         int                  `json:"price" bson:"price"`                         // 单价
	IsCheckWeight                 bool                 `json:"is_check_weight" bson:"is_check_weight"`     // 分拣检查重量  是/否
	HasParam                      bool                 `json:"has_param" bson:"has_param"`                 // 规格参数 有/无
	ProductParamType              ProductParamType     `json:"product_param_type" bson:"product_param_type"`
	StandardAttr                  StandardAttr         `json:"standard_attr" bson:"standard_attr"`                                             // 标品参数
	NonStandardAttr               NonStandardAttr      `json:"non_standard_attr" bson:"non_standard_attr"`                                     // 非标品参数
	RoughWeight                   int                  `json:"rough_weight" bson:"rough_weight"`                                               // 毛重
	OutWeight                     int                  `bson:"out_weight" json:"out_weight"`                                                   // 皮重
	NetWeight                     int                  `bson:"net_weight" json:"net_weight"`                                                   // 净重
	ProductRoughWeightUnitPriceKG int                  `json:"product_rough_weight_unit_price_kg" bson:"product_rough_weight_unit_price_kg"`   // 商品毛重单价/kg
	UserPayRoughWeightUnitPriceKG int                  `json:"user_pay_rough_weight_unit_price_kg" bson:"user_pay_rough_weight_unit_price_kg"` // 用户支付（不包含运费）毛重单价/kg
	CouponRoughWeightUnitPriceKG  int                  `json:"coupon_rough_weight_unit_price_kg" bson:"coupon_rough_weight_unit_price_kg"`     // 优惠券毛重单价/kg
	Num                           int                  `json:"num" bson:"num"`                                                                 // 数量
	TotalWeight                   int                  `json:"total_weight" bson:"total_weight"`                                               // 总重量
	DueWeight                     int                  `json:"due_weight" bson:"due_weight"`                                                   // 实际数量对应的应有重量
	SortNum                       int                  `json:"sort_num" bson:"sort_num"`                                                       // 分拣数量
	SortWeight                    int                  `json:"sort_weight" bson:"sort_weight"`                                                 // 分拣重量
	PurchaseProductAmount         int                  `json:"purchase_product_amount" bson:"purchase_product_amount"`                         // 采购金额
	SortStatus                    int                  `json:"sort_status" bson:"sort_status"`                                                 // 2 已分拣
	TotalAmount                   int                  `json:"total_amount" bson:"total_amount"`                                               // 总价
	ProductAmount                 int                  `json:"product_amount" bson:"product_amount"`                                           // 商品总价
	PaidAmount                    int                  `json:"paid_amount" bson:"paid_amount"`                                                 // 实付
}

// PurchaseParentOrder  支付父单
type PurchaseParentOrder struct {
	ID                     primitive.ObjectID `json:"id" bson:"_id"`
	UserID                 primitive.ObjectID `json:"user_id" bson:"user_id"`                                       // 用户ID
	PurchaseSupplierID     primitive.ObjectID `json:"purchase_supplier_id" bson:"purchase_supplier_id"`             // 供应商ID
	ServicePointID         primitive.ObjectID `json:"service_point_id" bson:"service_point_id"`                     // 服务仓ID
	PayMethod              PayMethodType      `json:"pay_method" bson:"pay_method"`                                 // 支付方式
	BizOrderNo             string             `json:"biz_order_no" bson:"biz_order_no"`                             // 支付订单号（云商通
	BizOrderNoResult       PayResult          `json:"biz_order_no_result" bson:"biz_order_no_result"`               // 微信支付结果
	BizOrderCouponNo       string             `json:"biz_order_coupon_no" bson:"biz_order_coupon_no"`               // 代金券（云商通，未使用代金券时为空
	BizOrderCouponNoResult PayResult          `json:"biz_order_coupon_no_result" bson:"biz_order_coupon_no_result"` // 代金券支付结果
	TotalProductAmount     int                `json:"total_product_amount" bson:"total_product_amount"`             // 商品总金额
	TotalPaidAmount        int                `json:"total_paid_amount" bson:"total_paid_amount"`                   // 实付金额
	PayStatus              PayStatusType      `json:"pay_status" bson:"pay_status"`                                 // 支付状态
	Expire                 int64              `json:"expire" bson:"expire"`                                         // 订单超时
	PayResult              PayResult          `json:"pay_result" bson:"pay_result"`                                 // 代收支付结果
	CreatedAt              int64              `bson:"created_at" json:"created_at"`
	UpdatedAt              int64              `bson:"updated_at" json:"updated_at"`
	DeletedAt              int64              `bson:"deleted_at" json:"deleted_at"`
}

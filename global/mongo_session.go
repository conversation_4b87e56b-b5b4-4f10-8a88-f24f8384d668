package global

import (
	"go.mongodb.org/mongo-driver/mongo"
)

func SessionMain() {
	//ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	//defer cancel()
	//
	//client, err := mongo.Connect(ctx, options.Client().ApplyURI("<mongodb-uri>"))
	//if err != nil {
	//	// handle error
	//}
	//defer func() {
	//	if err = client.Disconnect(ctx); err != nil {
	//		// handle error
	//	}
	//}()
	//
	//err = client.Ping(ctx, readpref.Primary())
	//if err != nil {
	//	// handle error
	//}
	//
	//session, err := client.StartSession()
	//if err != nil {
	//	// handle error
	//}
	//defer session.EndSession(ctx)
	//
	//txnOpts := options.Transaction().SetMaxCommitTime(10 * time.Second)
	//
	//err = mongo.WithSession(ctx, session, func(sessCtx mongo.SessionContext) error {
	//	err := operation1(sessCtx)
	//	if err != nil {
	//		return err
	//	}
	//	err = operation2(sessCtx)
	//	if err != nil {
	//		return err
	//	}
	//	return nil
	//})
	//if err != nil {
	//	// handle error
	//}
	//
	//err = session.CommitTransaction(ctx)
	//if err != nil {
	//	// handle error
	//}
}

func operation1(sessCtx mongo.SessionContext) error {
	// perform operation 1
	return nil
}

func operation2(sessCtx mongo.SessionContext) error {
	// perform operation 2
	return nil
}

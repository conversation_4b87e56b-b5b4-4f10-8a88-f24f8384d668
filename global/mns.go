package global

import (
	"base/core/config"
	"github.com/gin-gonic/gin"
)

type AppConf struct {
	Url             string `json:"url"`
	AccessKeyId     string `json:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret"`
}

var MNSConf = AppConf{
	Url:             "http://1136167804098145.mns.cn-chengdu.aliyuncs.com",
	AccessKeyId:     "LTAI5tCErKyjsd4v9DoU3XA5",
	AccessKeySecret: "******************************",
}

var QueueOrder string

func InitMNS(c *config.Config) {
	if c.Mode == gin.ReleaseMode {
		QueueOrder = "order"
	} else {
		QueueOrder = "localOrder"
	}
}

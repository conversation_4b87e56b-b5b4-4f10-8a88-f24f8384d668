package global

import (
	"base/core/config"
	"fmt"
	"github.com/casbin/casbin/v2"
	mongodbadapter "github.com/casbin/mongodb-adapter/v3"
	mongooptions "go.mongodb.org/mongo-driver/mongo/options"
	"log"
)

var Enforcer *casbin.Enforcer

func InitCasbin() {
	path := "core/files/casbin/model.conf"
	conf := config.Conf.Mongo

	mongoClientOption := mongooptions.Client().ApplyURI(fmt.Sprintf(conf.Url))
	a, err := mongodbadapter.NewAdapterWithCollectionName(mongoClientOption, conf.Database, "casbin")
	// Or you can use NewAdapterWithCollectionName for custom collection name.
	if err != nil {
		log.Fatalln(err)
	}

	e, err := casbin.NewEnforcer(path, a)
	if err != nil {
		log.Fatalln(err)
	}

	// Load the policy from DB.
	err = e.LoadPolicy()
	if err != nil {
		log.Fatalln("加载policy失败：", err)
	}

	Enforcer = e
}

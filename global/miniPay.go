package global

import (
	"context"
	"crypto/rsa"
	"github.com/gin-gonic/gin"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/services/refunddomestic"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"log"
)

// // 跳转协议路径
// var SignAcctUrl = "https://test.allinpay.com/yungateway/member/signContract.html"
// var SignContractPageUrl = "https://fintech.allinpay.com/yungateway/member/signContract.html"

const NotifyUrlPayNormalOrder = "/api/pay/back/notify/pay/normal/order"
const NotifyUrlPayDebtOrder = "/api/pay/back/notify/pay/debt/order"

const NotifyUrlRefundNormalOrderCancel = "/api/pay/back/notify/refund/order/cancel"
const NotifyUrlRefundNormalOrderRefund = "/api/pay/back/notify/refund/order/refund"

//
//// BackUrl 支付异步通知
//var BackUrl = "/api/merchant/back/notify"
//var BackUrlSignAcctProtocol = "/api/merchant/back/notify/sign/acct"
//var BackUrlSignAcctProtocolPersonal = "/api/merchant/back/notify/sign/acct/personal"
//var BackUrlWithdraw = "/api/merchant/back/notify/withdraw"
//var BackUrlWithdrawBuyer = "/api/merchant/back/notify/withdraw/buyer"
//var BackUrlDeposit = "/api/merchant/back/notify/deposit"
//var BackUrlDepositBuyerBalance = "/api/merchant/back/notify/deposit/buyer/balance"
//var BackUrlDepositMarketing = "/api/merchant/back/notify/deposit/marketing"
//var BackUrlAgentCollect = "/api/merchant/back/notify/agent/collect"              // 托管代收
//var BackUrlAgentCollectDebt = "/api/merchant/back/notify/agent/collect/debt"     // 托管代收
//var BackUrlAgentCollectCoupon = "/api/merchant/back/notify/agent/collect/coupon" // 托管代收
//var BackUrlSignalAgentPay = "/api/merchant/back/notify/agent/merchant/signal"         // 托管单笔代付
//var BackUrlCancel = "/api/merchant/back/notify/cancel"                           // 取消订单
//var BackUrlRefund = "/api/merchant/back/notify/refund"                           // 退款
//var BackUrlRefundManual = "/api/merchant/back/notify/refund/manual"              // 手动取消整个订单 退款
//var BackUrlRefundDeliver = "/api/merchant/back/notify/refund/deliver"            //  退款  配送费
//
//var BackUrlLargeTransfer = "/api/merchant/back/notify/large/transfer" // 大额转账，暂不部署
////https://www.guoshut.com/api/pay/back/notify/large/transfer

var payClient *core.Client
var PayJSAPI jsapi.JsapiApiService
var PayRefund refunddomestic.RefundsApiService

var (
	MchID                      string = "1694598262"                               // 商户号
	MchCertificateSerialNumber string = "17BA5E0190456C6E68DB85FBE11BAE950467A419" // 商户证书序列号
	MchAPIv3Key                string = "6R2KqXqmhC1pKflvWZ0GoR3pFGpCchDv"         // 商户APIv3密钥
	MchPrivateKey              *rsa.PrivateKey
)

func initPay() {
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	var err error
	MchPrivateKey, err = utils.LoadPrivateKeyWithPath("./core/files/merchant/apiclient_key.pem")
	if err != nil {
		log.Fatal("load merchant private key error")
	}

	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(MchID, MchCertificateSerialNumber, MchPrivateKey, MchAPIv3Key),
	}

	payClient, err = core.NewClient(ctx, opts...)
	if err != nil {
		log.Fatalf("new wechat merchant client err:%s", err)
	}

	PayJSAPI = jsapi.JsapiApiService{Client: payClient}
	PayRefund = refunddomestic.RefundsApiService{Client: payClient}

	if gin.Mode() == gin.DebugMode {
		BackHost = backHostDev
	}

}

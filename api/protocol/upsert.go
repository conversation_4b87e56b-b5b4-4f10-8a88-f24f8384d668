package protocol

import (
	"base/core/xhttp"
	"base/service/protocolService"
	"github.com/gin-gonic/gin"
)

func Update(ctx *gin.Context) {
	var req = struct {
		Type    string `json:"type"`
		Content string `json:"content"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	err = protocolService.NewProtocolService().Update(ctx, req.Type, req.Content)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func Get(ctx *gin.Context) {
	var req = struct {
		Type string `json:"type"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	data, err := protocolService.NewProtocolService().Get(ctx, req.Type)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

package orderServicePoint

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderPointService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"sort"
)

// ListSelfGet  自提列表
func ListSelfGet(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		Timestamp      int64  `json:"timestamp" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.ServicePointID, "ListToArrive service_point_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderPointService.NewOrderPointService().ListSelfGet(ctx, id, begin, end)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	m := make(map[primitive.ObjectID][]model.Order)
	for _, o := range list {
		m[o.BuyerID] = append(m[o.BuyerID], o)
	}
	var resList []toAllotRes
	for i, orders := range m {
		mBuyerAddr := make(map[primitive.ObjectID][]model.Order)
		for _, order := range orders {
			mBuyerAddr[order.Address.AddressID] = append(mBuyerAddr[order.Address.AddressID], order)
		}

		for _, orderList := range mBuyerAddr {

			var sWeight int
			var sNum int

			var existNotReceive bool
			for _, order := range orderList {
				for _, p := range order.ProductList {
					if !p.IsShipRefundAll {
						sWeight += p.SortWeight
						sNum += p.SortNum
					}
				}
				if !existNotReceive && order.OrderStatus != model.OrderStatusTypeFinish {
					existNotReceive = true
				}
			}

			var bName string
			if bName == "" {
				bName = orderList[0].BuyerName
				if len(mBuyerAddr) > 1 {
					bName += "【多地址】"
				}
			}

			item := toAllotRes{
				BuyerId:   i,
				BuyerName: bName,
				Address:   orderList[0].Address,
				//Distance:   backDistance(point, l.Longitude, l.Latitude),
				SortWeight:  sWeight,
				SortNum:     sNum,
				Received:    !existNotReceive,
				DeliverType: orderList[0].DeliverType,
			}
			resList = append(resList, item)
		}
	}

	sort.Sort(SingleList(resList))

	xhttp.RespSuccess(ctx, resList)
}

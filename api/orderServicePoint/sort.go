package orderServicePoint

type SingleList []toAllotRes

func (array SingleList) Len() int {
	return len(array)
}

func (array SingleList) Less(i, j int) bool {
	// false--->true
	if array[i].Received && !array[j].Received {
		return false
	}

	if !array[i].Received && array[j].Received {
		return true
	}

	return array[i].BuyerName > array[j].BuyerName
}

func (array SingleList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

// LogisticsList 物流
type LogisticsList []logisticsRes

func (array LogisticsList) Len() int {
	return len(array)
}

func (array LogisticsList) Less(i, j int) bool {
	if array[i].LogisticsAll && !array[j].LogisticsAll {
		return false
	}

	if !array[i].LogisticsAll && array[j].LogisticsAll {
		return true
	}

	return array[i].BuyerName > array[j].BuyerName
}

func (array LogisticsList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

// DeliverList 配送
type DeliverList []toAllotRes

func (array DeliverList) Len() int {
	return len(array)
}

func (array DeliverList) Less(i, j int) bool {
	if array[i].SortNum > array[j].SortNum {
		return false
	}

	if array[i].SortNum < array[j].SortNum {
		return true
	}

	return array[i].BuyerName > array[j].BuyerName
}

func (array DeliverList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

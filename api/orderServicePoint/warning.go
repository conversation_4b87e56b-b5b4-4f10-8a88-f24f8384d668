package orderServicePoint

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// WarningReceive 告警-收货
func WarningReceive(ctx *gin.Context) {
	var req = struct {
		ServicePointID string            `json:"service_point_id"`
		Timestamp      int64             `json:"timestamp" validate:"required"`
		DeliverType    model.DeliverType `json:"deliver_type"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	end, err := util.DayEndTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"order_status": bson.M{
			"$eq": model.OrderStatusTypeToReceive,
		},
		"deliver_type":     req.DeliverType,
		"service_point_id": pointID,
	}

	filter["order_status_record.ship_time"] = bson.M{
		"$ne":  0,
		"$lte": end,
	}

	if req.DeliverType == model.DeliverTypeLogistics {
		filter["logistics_time"] = bson.M{
			"$eq": 0,
		}
	}

	list, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	m := make(map[primitive.ObjectID]int)
	for _, order := range list {
		m[order.BuyerID] = 0
	}

	xhttp.RespSuccess(ctx, len(m))
}

// WarningDeliverCenter 告警-交付为完成
func WarningDeliverCenter(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	now := time.Now()
	_ = now

	end, err := util.DayEndTimestamp(now.UnixMilli())
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin := now.Add(-time.Hour * 24 * 5).UnixMilli()

	filter := bson.M{
		"order_status": bson.M{
			"$eq": model.OrderStatusTypeToReceive,
		},
		//"deliver_type":     req.DeliverType,
		"service_point_id": pointID,
		//"created_at": bson.M{
		//	"$gte": begin,
		//	"$lte": end,
		//},
	}

	filter["order_status_record.ship_time"] = bson.M{
		"$ne":  begin,
		"$lte": end,
	}

	//if req.DeliverType == model.DeliverTypeLogistics {
	//	filter["logistics_time"] = bson.M{
	//		"$eq": 0,
	//	}
	//}

	list, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	m := make(map[primitive.ObjectID]int)

	for _, order := range list {
		if order.DeliverType == model.DeliverTypeDoor {

			continue
		}

		if order.DeliverType == model.DeliverTypeSelfPickUp {

			continue
		}

		if order.DeliverType == model.DeliverTypeLogistics {
			if order.LogisticsTime == 0 {
				m[order.BuyerID] = 0
			}
			continue
		}

		if order.DeliverType == model.DeliverTypeInstantDeliver {

			continue
		}
	}

	xhttp.RespSuccess(ctx, len(m))
}

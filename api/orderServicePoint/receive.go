package orderServicePoint

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderPointService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// Receive 确认收货
func Receive(ctx *gin.Context) {
	var req = struct {
		OrderIDList     []string         `json:"order_id_list" validate:"-"`
		DeliveryImgList []model.FileInfo `json:"delivery_img_list" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if len(req.OrderIDList) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "订单缺失"))
		return
	}
	if len(req.DeliveryImgList) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "确认订单图片缺失"))
		return
	}
	if len(req.DeliveryImgList) > 0 {
		for _, info := range req.DeliveryImgList {
			if info.Name == "" {
				xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "确认订单图片缺失"))
				return
			}
		}
	}

	var ids []primitive.ObjectID
	for _, s := range req.OrderIDList {
		id, err := util.ConvertToObjectWithNote(s, "Receive 订单ID"+s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	now := time.Now().UnixMilli()

	for _, id := range ids {
		err = orderPointService.NewOrderPointService().DoReceive(ctx, id, req.DeliveryImgList, now)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}
	xhttp.RespSuccess(ctx, nil)
}

// ReceiveSingle 确认收货-单一订单
func ReceiveSingle(ctx *gin.Context) {
	var req = struct {
		OrderID         string           `json:"order_id"`
		DeliveryImgList []model.FileInfo `json:"delivery_img_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if len(req.OrderID) != 24 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "订单ID错误"))
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	now := time.Now().UnixMilli()

	err = orderPointService.NewOrderPointService().DoReceive(ctx, id, req.DeliveryImgList, now)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

package orderServicePoint

import (
	"base/core/xhttp"
	"base/model"
	deliverAssigneService "base/service/deliverAssignService"
	"base/service/deliveryManService"
	"base/service/orderPointService"
	"base/service/servicePointService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"sort"
)

// ListToAllot  待分配
func ListToAllot(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		Timestamp      int64  `json:"timestamp" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ServicePointID, "ListToArrive service_point_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	point, err := servicePointService.NewServicePointService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderPointService.NewOrderPointService().ListToAllot(ctx, point.ID, begin, end)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(list) < 1 {
		xhttp.RespSuccess(ctx, nil)
		return
	}

	m := make(map[primitive.ObjectID][]model.Order)
	for _, o := range list {
		m[o.BuyerID] = append(m[o.BuyerID], o)
	}

	var resList []toAllotRes
	for i, orders := range m {
		l := orders[0].Address.Location
		var sWeight int
		var sNum int

		for _, order := range orders {
			for _, p := range order.ProductList {
				if !p.IsShipRefundAll {
					sWeight += p.SortWeight
					sNum += p.SortNum
				}
			}
		}

		item := toAllotRes{
			BuyerId:          i,
			BuyerName:        orders[0].BuyerName,
			DeliveryUserName: orders[0].DeliveryUserName,
			Address:          orders[0].Address,
			Distance:         backDistance(point, l.Longitude, l.Latitude),
			SortWeight:       sWeight,
			SortNum:          sNum,
		}

		resList = append(resList, item)
	}

	xhttp.RespSuccess(ctx, resList)
}

type toAllotRes struct {
	BuyerId            primitive.ObjectID `json:"buyer_id"`
	BuyerName          string             `json:"buyer_name"`
	DeliveryUserName   string             `json:"delivery_user_name"`
	Address            model.OrderAddress `json:"address"`
	Distance           int                `json:"distance"`
	SortWeight         int                `json:"sort_weight"`
	SortNum            int                `json:"sort_num"`
	Received           bool               `json:"received"`
	DeliverAssign      bool               `json:"deliver_assign"` // 配送指派
	DeliverAssignID    primitive.ObjectID `json:"deliver_assign_id"`
	DeliverType        model.DeliverType  `json:"deliver_type"`
	InstantDeliverName string             `json:"instant_deliver_name"`
}

func ListByBuyerID(ctx *gin.Context) {
	var req = struct {
		Timestamp      int64  `json:"timestamp" validate:"-"`
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		BuyerID        string `json:"buyer_id" validate:"len=24"`
		AddressID      string `json:"address_id" validate:"len=24"`
		DeliveryUserId string `json:"delivery_user_id" validate:"-"`
		ReceiveHas     bool   `json:"receive_has" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	addressID, err := util.ConvertToObjectWithCtx(ctx, req.AddressID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var dID primitive.ObjectID
	if len(req.DeliveryUserId) == 24 {
		dID, err = util.ConvertToObjectWithCtx(ctx, req.DeliveryUserId)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderPointService.NewOrderPointService().ListByBuyerAndPoint(ctx, buyerID, addressID, pointID, dID, begin, end, req.ReceiveHas)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(list) < 1 {
		xhttp.RespSuccess(ctx, nil)
		return
	}

	xhttp.RespSuccess(ctx, list)
}

func ListByBuyerIDSelfGet(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		BuyerID        string `json:"buyer_id" validate:"len=24"`
		Timestamp      int64  `json:"timestamp" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithNote(req.ServicePointID, "ListByBuyerID service_point_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyerID, err := util.ConvertToObjectWithNote(req.BuyerID, "ListByBuyerID buyer_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderPointService.NewOrderPointService().ListByBuyerAndPointSelfGet(ctx, buyerID, pointID, begin, end)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(list) < 1 {
		xhttp.RespSuccess(ctx, nil)
		return
	}

	xhttp.RespSuccess(ctx, list)
}

func Allot(ctx *gin.Context) {
	var req = struct {
		Timestamp      int64    `json:"timestamp" validate:"-"`
		ServicePointID string   `json:"service_point_id" validate:"len=24"`
		BuyerIDList    []string `json:"buyer_id_list" validate:"min=1"`
		DeliveryUserId string   `json:"delivery_user_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithNote(req.ServicePointID, "Allot service_point_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var buyerList []primitive.ObjectID
	for _, s := range req.BuyerIDList {
		id, err := util.ConvertToObjectWithNote(s, "Allot buyer_id")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		buyerList = append(buyerList, id)
	}

	deliveryUserId, err := util.ConvertToObjectWithNote(req.DeliveryUserId, "Allot DeliveryUserId")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	deliveryMan, err := deliveryManService.NewDeliveryManService().GetByUserID(ctx, deliveryUserId)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderPointService.NewOrderPointService().DoAllot(ctx, buyerList, pointID, deliveryMan, begin, end)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func AllotList(ctx *gin.Context) {
	var req = struct {
		Timestamp      int64  `json:"timestamp" validate:"-"`
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		DeliveryUserId string `json:"delivery_user_id" validate:"-"`
		ReceiveHas     bool   `json:"receive_has" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithNote(req.ServicePointID, "ListByBuyerID service_point_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var dID primitive.ObjectID
	if len(req.DeliveryUserId) == 24 {
		dID, err = util.ConvertToObjectWithNote(req.DeliveryUserId, "AllotList delivery_user_id")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

	}

	point, err := servicePointService.NewServicePointService().Get(ctx, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ordersList, err := orderPointService.NewOrderPointService().AllotList(ctx, pointID, dID, begin, end, req.ReceiveHas)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(ordersList) < 1 {
		xhttp.RespSuccess(ctx, nil)
		return
	}

	m := make(map[primitive.ObjectID][]model.Order)
	for _, o := range ordersList {
		m[o.BuyerID] = append(m[o.BuyerID], o)
	}

	var buyerIDs []primitive.ObjectID
	for objectID, _ := range m {
		buyerIDs = append(buyerIDs, objectID)
	}

	timestamp, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	assigns, err := deliverAssigneService.NewDeliverAssignService().ListByBuyerIDs(ctx, buyerIDs, timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var resList []toAllotRes
	for i, orders := range m {
		mBuyerAddr := make(map[primitive.ObjectID][]model.Order)
		for _, order := range orders {
			mBuyerAddr[order.Address.AddressID] = append(mBuyerAddr[order.Address.AddressID], order)
		}

		for _, orderList := range mBuyerAddr {
			l := orderList[0].Address.Location
			var sWeight int
			var sNum int

			for _, order := range orderList {
				for _, p := range order.ProductList {
					if !p.IsShipRefundAll {
						sWeight += p.SortWeight
						sNum += p.SortNum
					}
				}
			}

			dt := orders[0].DeliverType

			var deliverAssign bool
			var deliverAssignID primitive.ObjectID
			for _, assign := range assigns {
				if assign.BuyerID == i {
					deliverAssign = true
					deliverAssignID = assign.ID
					break
				}
			}

			var bName string
			if bName == "" {
				bName = orderList[0].BuyerName
				if len(mBuyerAddr) > 1 {
					bName += "【多地址】"
				}
			}

			item := toAllotRes{
				BuyerId:          i,
				BuyerName:        bName,
				DeliveryUserName: orders[0].DeliveryUserName,
				Address:          orderList[0].Address,
				Distance:         backDistance(point, l.Longitude, l.Latitude),
				SortWeight:       sWeight,
				SortNum:          sNum,
				DeliverType:      dt,
				Received:         false,
				DeliverAssign:    deliverAssign,
				DeliverAssignID:  deliverAssignID,
			}
			resList = append(resList, item)
		}
	}

	sort.Sort(DeliverList(resList))

	xhttp.RespSuccess(ctx, resList)
}

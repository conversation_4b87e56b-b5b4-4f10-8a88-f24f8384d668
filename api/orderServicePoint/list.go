package orderServicePoint

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderPointService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// ListToArrive  待到货
func ListToArrive(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.ServicePointID, "ListToArrive service_point_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := orderPointService.NewOrderPointService().ListToArrive(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

func backDistance(point model.ServicePoint, lon, lat float64) int {
	if lon == 0 || lat == 0 {
		return 0
	}
	return int(util.LatitudeLongitudeDistance(point.Location.Longitude, point.Location.Latitude, lon, lat))
}

func ListOrderForPoint(ctx *gin.Context) {
	var req = struct {
		Begin         int64  `json:"begin"`
		End           int64  `json:"end"`
		SecondPointID string `json:"second_point_id"`
		Page          int64  `json:"page" validate:"min=1"`
		Limit         int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.SecondPointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, count, err := orderPointService.NewOrderPointService().ListOrders(ctx, id, req.Page, req.Limit, req.Begin, req.End)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, data, count)
}

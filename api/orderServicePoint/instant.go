package orderServicePoint

import (
	"base/core/xhttp"
	"base/model"
	deliverAssigneService "base/service/deliverAssignService"
	"base/service/orderPointService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"sort"
)

// ListInstant  即时配送列表
func ListInstant(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		Timestamp      int64  `json:"timestamp" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 即时配送订单
	list, err := orderPointService.NewOrderPointService().ListInstant(ctx, id, begin, end)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	timestamp, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	m := make(map[primitive.ObjectID][]model.Order)
	for _, o := range list {
		m[o.BuyerID] = append(m[o.BuyerID], o)
	}

	var buyerIDs []primitive.ObjectID
	for objectID, _ := range m {
		buyerIDs = append(buyerIDs, objectID)
	}

	assigns, err := deliverAssigneService.NewDeliverAssignService().ListByBuyerIDs(ctx, buyerIDs, timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var resList []toAllotRes
	for i, orders := range m {
		mBuyerAddr := make(map[primitive.ObjectID][]model.Order)
		for _, order := range orders {
			mBuyerAddr[order.Address.AddressID] = append(mBuyerAddr[order.Address.AddressID], order)
		}

		for _, orderList := range mBuyerAddr {

			var sWeight int
			var sNum int

			var existNotReceive bool
			for _, order := range orderList {
				for _, p := range order.ProductList {
					if !p.IsShipRefundAll {
						sWeight += p.SortWeight
						sNum += p.SortNum
					}
				}
				if !existNotReceive && order.OrderStatus != model.OrderStatusTypeFinish {
					existNotReceive = true
				}
			}

			var deliverAssign bool
			var deliverAssignID primitive.ObjectID
			for _, assign := range assigns {
				if assign.BuyerID == i {
					deliverAssign = true
					deliverAssignID = assign.ID
					break
				}
			}

			var bName string
			bName = orderList[0].BuyerName
			if len(mBuyerAddr) > 1 {
				bName += "【多地址】"
			}

			item := toAllotRes{
				BuyerId:          i,
				BuyerName:        bName,
				Address:          orderList[0].Address,
				DeliveryUserName: orderList[0].DeliveryUserName,
				//Distance:   backDistance(point, l.Longitude, l.Latitude),
				SortWeight:         sWeight,
				SortNum:            sNum,
				Received:           !existNotReceive,
				DeliverType:        orderList[0].DeliverType,
				InstantDeliverName: orderList[0].InstantDeliverName,
				DeliverAssign:      deliverAssign,
				DeliverAssignID:    deliverAssignID,
			}
			resList = append(resList, item)
		}
	}

	sort.Sort(SingleList(resList))

	xhttp.RespSuccess(ctx, resList)
}

func ListByBuyerIDInstant(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		BuyerID        string `json:"buyer_id" validate:"len=24"`
		Timestamp      int64  `json:"timestamp" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderPointService.NewOrderPointService().ListByBuyerAndPointInstant(ctx, buyerID, pointID, begin, end)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(list) < 1 {
		xhttp.RespSuccess(ctx, nil)
		return
	}

	xhttp.RespSuccess(ctx, list)
}

package orderServicePoint

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderPointService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// StatsConfirm 统计-确认订单
func StatsConfirm(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		StationID      string `json:"station_id"`
		Timestamp      int64  `json:"timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var stationID primitive.ObjectID

	env := xhttp.GetEnv(ctx)

	if env == model.ObjectTypeStation {
		stationID, err = util.ConvertToObjectWithCtx(ctx, req.StationID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	orders, err := orderPointService.NewOrderPointService().ListOrderToStockUp(ctx, servicePointID, stationID, req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var w int
	mSingle := make(map[primitive.ObjectID]int)
	mBuyer := make(map[primitive.ObjectID]int)
	mSupplier := make(map[primitive.ObjectID]int)
	var pNum int
	for _, order := range orders {
		for _, p := range order.ProductList {
			pNum += p.Num
			mSingle[p.ProductID]++
			w += p.RoughWeight * p.Num
		}
		mSupplier[order.SupplierID]++
		mBuyer[order.BuyerID] = 0
	}

	item := ToStockUpList{
		//ServicePointID:   id,
		//ServicePointName: i[0].ServicePointName,
		TotalOrder:    len(orders),
		TotalSupplier: len(mSupplier),
		TotalBuyer:    len(mBuyer),
		TotalProduct:  pNum,
		TotalSingle:   len(mSingle),
		TotalWeight:   w,
	}

	xhttp.RespSuccess(ctx, item)
}

type ToStockUpList struct {
	//ServicePointID   primitive.ObjectID `json:"service_point_id"`
	//ServicePointName string             `json:"service_point_name"`
	TotalOrder    int `json:"total_order"`
	TotalWeight   int `json:"total_weight"`
	TotalBuyer    int `json:"total_buyer"`
	TotalSupplier int `json:"total_supplier"`
	TotalProduct  int `json:"total_product"`
	TotalSingle   int `json:"total_single"`
}

package invoice

import (
	"base/core/xhttp"
	"base/service/invoiceService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// Get 发票信息
func Get(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := invoiceService.NewInvoiceService().GetByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

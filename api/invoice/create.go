package invoice

import (
	"github.com/gin-gonic/gin"
	"sync"
)

var lock sync.Mutex

func Create(ctx *gin.Context) {
	//lock.Lock()
	//defer lock.Unlock()
	//
	//var req = struct {
	//	InvoiceTitleID string `json:"invoice_title_id" bson:"invoice_title_id"` // 发票抬头ID
	//	ApplyNote      string `json:"apply_note" bson:"apply_note"`             // 申请备注
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//userID, err := xhttp.UserID(ctx)
	//if err != nil {
	//	return
	//}
	//buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, userID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//invoiceTitleID, err := util.ConvertToObjectWithCtx(ctx, req.InvoiceTitleID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//// 判断开票
	//count, err := invoiceService.NewInvoiceService().Count(ctx, bson.M{
	//	"buyer_id": buyer.ID,
	//	"status": bson.M{
	//		"$in": []model.InvoiceStatusType{model.InvoiceStatusTypeAuditing, model.InvoiceStatusTypeInvoicing},
	//	},
	//})
	//if count > 0 {
	//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "存在发票正在开具中"))
	//	return
	//}
	//
	//invoiceTitle, err := invoiceTitleService.NewInvoiceTitleService().GetByID(ctx, invoiceTitleID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//err = invoiceService.NewInvoiceService().Create(ctx, buyer.ID, req.ApplyNote, invoiceTitle)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//xhttp.RespSuccess(ctx, nil)
}

func CreateCheck(ctx *gin.Context) {
	//userID, err := xhttp.UserID(ctx)
	//if err != nil {
	//	return
	//}
	//buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, userID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//// 判断开票
	//count, err := invoiceService.NewInvoiceService().Count(ctx, bson.M{
	//	"buyer_id": buyer.ID,
	//	"status": bson.M{
	//		"$in": []model.InvoiceStatusType{model.InvoiceStatusTypeAuditing, model.InvoiceStatusTypeInvoicing},
	//	},
	//})
	//
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//f, err := orderDebtService.NewOrderDebtService().ExistByBuyer(ctx, buyer.ID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//xhttp.RespSuccess(ctx, map[string]interface{}{
	//	"not_issue_invoice_count": count,
	//	"exist_debt":              f,
	//})
}

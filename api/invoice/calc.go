package invoice

import (
	"base/api/orderStats"
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Calc(ctx *gin.Context) {
	var req = struct {
		OrderIDList []string `json:"order_id_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var orderIDs []primitive.ObjectID
	for _, i := range req.OrderIDList {
		id, err := util.ConvertToObjectWithNote(i, "")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		orderIDs = append(orderIDs, id)
	}

	filter := bson.M{}
	filter["_id"] = bson.M{"$in": orderIDs}
	if len(req.OrderIDList) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "订单列表不能为空"))
		return
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	debts := make([]model.OrderDebt, 0)
	// 查询补差
	if len(orderIDs) > 0 {
		debts, err = orderDebtService.NewOrderDebtService().List(ctx, bson.M{"order_id": bson.M{"$in": orderIDs}})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
			//"refund_type": model.RefundTypeAfterSale,
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	//var parentOrderIDs []primitive.ObjectID
	//for _, order := range orders {
	//	parentOrderIDs = append(parentOrderIDs, order.ParentOrderID)
	//}

	//parentOrders, err := parentOrderService.NewParentOrderService().List(ctx, bson.M{"_id": bson.M{"$in": parentOrderIDs}})
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	beginAt, endAt, orderLists, res := CalcFunc(orders, refunds, debts)
	_ = beginAt
	_ = endAt
	_ = orderLists

	xhttp.RespSuccess(ctx, res)
}

type CalcRes struct {
	TotalAmount              int   `json:"total_amount"`
	TotalPaidAmount          int   `json:"total_paid_amount"`
	TotalDeliverAmount       int   `json:"total_deliver_amount"`
	TotalFinalAmount         int   `json:"total_final_amount"`
	TotalDebtAmount          int   `json:"total_debt_amount"`
	TotalServiceFeeAmount    int   `json:"total_service_fee_amount"`
	TotalAfterSalePassAmount int   `json:"total_after_sale_pass_amount"`
	TotalShipRefundAmount    int   `json:"total_ship_refund_amount"`
	OrderTimeBegin           int64 `json:"order_time_begin"`
	OrderTimeEnd             int64 `json:"order_time_end"`
	ExistAfterSaleAuditing   bool  `json:"exist_after_sale_auditing"`
	ExistDebtNotPaid         bool  `json:"exist_debt_not_paid"`
}

func CalcFunc(orders []model.Order, refunds []model.OrderRefund, debts []model.OrderDebt) (int64, int64, []orderStats.OrderList, CalcRes) {
	var orderTimeBegin int64
	var orderTimeEnd int64

	var totalPaidAmount int
	var totalDebtAmount int
	var totalAfterSalePassAmount int
	var totalShipRefundAmount int
	var totalServiceFeeAmount int

	var existAfterSaleAuditing bool
	var existDebtNotPaid bool

	for _, order := range orders {
		totalPaidAmount += order.PaidAmount

		if orderTimeBegin == 0 {
			orderTimeBegin = order.CreatedAt
		}
		if orderTimeEnd == 0 {
			orderTimeEnd = order.CreatedAt
		}

		if orderTimeBegin > order.CreatedAt {
			orderTimeBegin = order.CreatedAt
		}
		if orderTimeEnd < order.CreatedAt {
			orderTimeEnd = order.CreatedAt
		}

		totalServiceFeeAmount += order.TotalServiceFee
	}

	mDebt := make(map[primitive.ObjectID]model.OrderDebt)

	for _, debt := range debts {

		for _, settle := range debt.SettleProductList {
			if settle.SettleResultType == model.SettleResultTypeRefund {
				totalShipRefundAmount += settle.DiffProductAmount
			}
		}

		for _, settle := range debt.SettleProductList {
			if settle.SettleResultType == model.SettleResultTypeDebt {
				totalDebtAmount += settle.DiffProductAmount
			}
		}

		mDebt[debt.OrderID] = debt
		if debt.PayStatus != model.PayStatusTypePaid {
			existDebtNotPaid = true
		}
	}

	mRefund := make(map[primitive.ObjectID][]model.OrderRefund)

	for _, re := range refunds {
		if re.RefundType == model.RefundTypeAfterSale && re.AuditStatus == model.AuditStatusTypePass {
			totalAfterSalePassAmount += re.AuditAmount
		}
		mRefund[re.OrderID] = append(mRefund[re.OrderID], re)

		if re.RefundType == model.RefundTypeAfterSale && re.AuditStatus == model.AuditStatusTypeDoing {
			existAfterSaleAuditing = true
		}
	}

	resList := make([]orderStats.OrderList, 0)
	for _, order := range orders {
		item := orderStats.OrderList{
			Order:      order,
			Debt:       mDebt[order.ID],
			RefundList: mRefund[order.ID],
		}
		resList = append(resList, item)
	}

	//totalFinalAmount := totalAmount - totalShipRefundAmount - totalAfterSalePassAmount + totalDebtPaidAmount + totalDebtNotPaidAmount
	totalFinalAmount := totalPaidAmount - totalShipRefundAmount + totalDebtAmount

	res := CalcRes{
		TotalAmount:              totalPaidAmount,
		TotalPaidAmount:          totalPaidAmount,
		TotalDebtAmount:          totalDebtAmount,
		TotalAfterSalePassAmount: totalAfterSalePassAmount,
		TotalShipRefundAmount:    totalShipRefundAmount,
		TotalServiceFeeAmount:    totalServiceFeeAmount,
		OrderTimeBegin:           orderTimeBegin,
		OrderTimeEnd:             orderTimeEnd,
		ExistAfterSaleAuditing:   existAfterSaleAuditing,
		ExistDebtNotPaid:         existDebtNotPaid,
		TotalFinalAmount:         totalFinalAmount,
	}
	return orderTimeBegin, orderTimeEnd, resList, res

}

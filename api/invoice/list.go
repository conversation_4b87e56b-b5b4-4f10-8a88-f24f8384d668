package invoice

import (
	"base/core/xhttp"
	"base/service/aesService"
	"base/service/invoiceService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// List  发票列表
func List(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page"`
		Limit   int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"deleted_at": 0,
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter["buyer_id"] = buyerID

	list, count, err := invoiceService.NewInvoiceService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	for i, invoice := range list {
		if len(list[i].BankAccount) > 0 {
			deBank, _ := aesService.NewAesService().De(invoice.BankAccount)
			list[i].BankAccount = deBank
		}

		if len(list[i].PhoneNumber) > 0 {
			dePhone, _ := aesService.NewAesService().De(invoice.PhoneNumber)
			list[i].PhoneNumber = dePhone
		}
	}

	xhttp.RespSuccessList(ctx, list, count)
}

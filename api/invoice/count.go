package invoice

import (
	"base/core/xhttp"
	"base/service/invoiceService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func CountStatus(ctx *gin.Context) {
	filter := bson.M{
		"status": bson.M{
			"$in": []int{2, 3},
		},
	}
	data, err := invoiceService.NewInvoiceService().Count(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

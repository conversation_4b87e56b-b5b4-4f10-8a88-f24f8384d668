package user

import (
	"base/core/xhttp"
	"base/service/userService"
	"github.com/gin-gonic/gin"
)

// ListRegexMobile 查询
func ListRegexMobile(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile" validate:"-"`
		Page   int64  `json:"page" validate:"min=1"`
		Limit  int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	users, i, err := userService.NewUserService().ListByRegexMobile(ctx, req.Mobile, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, users, i)
}

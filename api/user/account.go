package user

import (
	"github.com/gin-gonic/gin"
)

func Login(ctx *gin.Context) {
	//var req = struct {
	//	MobileCode string `json:"mobile_code" validate:"required"` // 手机号code
	//	LoginCode  string `json:"login_code" validate:"required"`  // openID code
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//user, err := userService.NewUserService().Login(ctx, req.LoginCode, req.MobileCode)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//env, err := xhttp.GetEnv(ctx)
	//if err != nil {
	//	return
	//}
	//_ = env
	//token, refreshToken, expireAt, err := jwtService.NewJwtService().MakeToken(user.ID.Hex())
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//// 检查会员存在性
	//buyerService.NewBuyerService().CheckBuyerInit(ctx, user.ID)
	//
	//r := types.LoginRes{
	//	UserID:       user.ID.Hex(),
	//	User:         user,
	//	AccessToken:  token,
	//	RefreshToken: refreshToken,
	//	Expires:      expireAt,
	//}
	//
	//xhttp.RespSuccess(ctx, r)

}

// LoginByMobile 手机号登录
func LoginByMobile(ctx *gin.Context) {
	//var req = struct {
	//	Mobile    string `json:"mobile"`
	//	Captcha   string `json:"captcha"`
	//	LoginCode string `json:"login_code"` // openID code
	//	WithAuth  bool   `json:"with_auth"`
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//getEnv, err := xhttp.GetEnv(ctx)
	//if err != nil {
	//	return
	//}
	//
	//if !util.CheckMobile(req.Mobile) {
	//	err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//if !util.CheckCaptcha(req.Captcha) {
	//	err = xerr.NewErr(xerr.ErrParamError, nil, "验证码格式错误")
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//var loginCode string
	//if getEnv == model.ObjectTypeBuyer {
	//	loginCode = req.LoginCode
	//}
	//
	//user, err := userService.NewUserService().LoginByMobile(ctx, req.Mobile, req.Captcha, loginCode)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//token, refreshToken, expireAt, err := jwtService.NewJwtService().MakeToken(user.ID.Hex())
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//// 检查会员存在性
	//buyerService.NewBuyerService().CheckBuyerInit(ctx, user.ID)
	//
	//r := types.LoginRes{
	//	UserID:       user.ID.Hex(),
	//	User:         user,
	//	AccessToken:  token,
	//	RefreshToken: refreshToken,
	//	Expires:      expireAt,
	//}
	//
	//if getEnv == model.ObjectTypePlatform || req.WithAuth {
	//	admin, err := adminService.NewAdminService().GetByUser(ctx, user.ID)
	//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//	r.RoleList = admin.RoleList
	//	r.AuthList = admin.AuthList
	//	if getEnv == model.ObjectTypePlatform && admin.ID == primitive.NilObjectID {
	//		//	 非管理员
	//		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
	//		return
	//	}
	//}
	//
	//xhttp.RespSuccess(ctx, r)
}

func LoginTest(ctx *gin.Context) {
	//var req = struct {
	//	UserID string `json:"user_id"  validate:"required"`
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//objectID, err := util.ConvertToObject(req.UserID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//user, err := userService.NewUserService().Get(ctx, objectID)
	//if errors.Is(err, mongo.ErrNoDocuments) {
	//	xhttp.RespNoExist(ctx)
	//	return
	//}
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//token, refreshToken, expireAt, err := jwtService.NewJwtService().MakeToken(req.UserID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//xhttp.RespSuccess(ctx, types.LoginRes{
	//	UserID:       user.ID.Hex(),
	//	User:         user,
	//	AccessToken:  token,
	//	RefreshToken: refreshToken,
	//	Expires:      expireAt,
	//})
}

package user

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/adminService"
	"base/service/jwtService"
	"base/service/userService"
	"base/types"
	"base/util"
	"errors"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
)

// ResetPWD 重置密码
func ResetPWD(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id"`
		PWD    string `json:"pwd"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if req.PWD == "" {
		err = xerr.NewErr(xerr.ErrParamError, nil, "请输入密码")
		xhttp.RespErr(ctx, err)
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = userService.NewUserService().ResetPWD(ctx, id, "", req.PWD)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// CreateUser 创建用户
func CreateUser(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile"`
		PWD    string `json:"pwd"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if !util.CheckMobile(req.Mobile) {
		err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	if req.PWD == "" {
		err = xerr.NewErr(xerr.ErrParamError, nil, "请输入密码")
		xhttp.RespErr(ctx, err)
		return
	}

	err = userService.NewUserService().CreateUser(ctx, req.Mobile, req.PWD)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// LoginByPWD 通过密码登录
func LoginByPWD(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile"`
		PWD    string `json:"pwd"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	if !util.CheckMobile(req.Mobile) {
		err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	user, err := userService.NewUserService().LoginByPWD(ctx, req.Mobile, req.PWD)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	token, refreshToken, expireAt, err := jwtService.NewJwtService().MakeToken(user.ID.Hex())
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r := types.LoginUserRes{
		UserID:       user.ID,
		User:         user,
		AccessToken:  token,
		Expires:      expireAt,
		RefreshToken: refreshToken,
	}

	admin, err := adminService.NewAdminService().GetByUser(ctx, user.ID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, err)
		return
	}
	r.RoleInfo = admin.RoleInfo

	xhttp.RespSuccess(ctx, r)
}

package brand

import (
	"base/core/xhttp"
	"base/service/brandService"
	"base/service/productService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	err = brandService.NewBrandService().Delete(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	products, err := productService.NewProductService().List(ctx, bson.M{
		"link_brand_id": id,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	for _, product := range products {
		productService.NewProductService().UpdateCus(ctx, product.ID, bson.M{
			"link_brand_status": 1,
			"link_brand_id":     primitive.NilObjectID,
			"link_brand_name":   "",
		})

	}
	xhttp.RespSuccess(ctx, nil)
}

package brand

import (
	"base/core/xhttp"
	"base/model"
	"base/service/brandService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	var req = struct {
		Name      string         `json:"name"`
		Desc      string         `json:"desc"`
		AvatarImg model.FileInfo `json:"avatar_img"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = brandService.NewBrandService().Create(ctx, req.Name, req.Desc, req.AvatarImg)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)

}

func Update(ctx *gin.Context) {
	var req = struct {
		ID        string         `json:"id"`
		Name      string         `json:"name"`
		Desc      string         `json:"desc"`
		AvatarImg model.FileInfo `json:"avatar_img"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = brandService.NewBrandService().Update(ctx, id, req.Name, req.Desc, req.AvatarImg)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package buyerManager

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/buyerManagerService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// GetByBuyer 客户经理
func GetByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	u, err := buyerManagerService.NewBuyerManagerService().GetByBuyer(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, u)
}

// GetByUser 客户经理
func GetByUser(ctx *gin.Context) {

	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "不存在"))
}

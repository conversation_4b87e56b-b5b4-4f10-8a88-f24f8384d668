package buyerManager

import (
	"base/core/xhttp"
	"base/service/buyerManagerService"
	"base/service/buyerService"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	var req = struct {
		Mobile   string `json:"mobile"`
		UserName string `json:"user_name"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	getByMobile, err := buyerService.NewBuyerService().GetByMobile(ctx, req.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerManagerService.NewBuyerManagerService().Create(ctx, getByMobile.ID, req.UserName)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

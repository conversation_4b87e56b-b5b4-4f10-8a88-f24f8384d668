package buyerManager

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/buyerManagerService"
	"base/service/buyerService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		ManagerBuyerID string `json:"manager_buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ManagerBuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"buyer_manager_buyer_id": id,
	}
	count, err := buyerService.NewBuyerService().Count(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	if count > 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请先解绑"))
		return
	}

	err = buyerManagerService.NewBuyerManagerService().Delete(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package buyerManager

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerManagerService"
	"base/service/userService"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func List(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page"`
		Limit int64 `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, i, err := buyerManagerService.NewBuyerManagerService().List(ctx, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ids := make([]primitive.ObjectID, 0, len(list))
	for _, user := range list {
		ids = append(ids, user.UserID)
	}

	users, err := userService.NewUserService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resList := make([]res, 0, len(list))
	for _, user := range list {
		var mobile string
		for _, m := range users {
			if m.ID == user.UserID {
				mobile = m.Mobile
			}
		}
		item := res{
			BuyerManager: user,
			Mobile:       mobile,
		}
		resList = append(resList, item)
	}

	xhttp.RespSuccessList(ctx, resList, i)
}

type res struct {
	model.BuyerManager
	Mobile string `json:"mobile"`
}

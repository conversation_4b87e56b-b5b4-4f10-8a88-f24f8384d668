package buyerManager

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/fileservice"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/ossService"
	"base/util"
	"bytes"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"log"
	"strconv"
	"time"
)

func GenStats(ctx *gin.Context) {
	var req = struct {
		ManagerBuyerID string `json:"manager_buyer_id"`
		MonthStamp     int64  `json:"month_stamp"`
		QueryType      string `json:"query_type"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if req.QueryType != "all" && req.QueryType != "manager" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数错误:query_type"))
		return
	}

	begin, end, err := util.MonthScopeTimestamp(req.MonthStamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orderFilter := bson.M{
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
			//"$lte": begin + 72000000,
		},
		"order_status": bson.M{
			"$nin": bson.A{model.OrderStatusTypeClosed, model.OrderStatusTypeCancel},
		},
	}

	mBuyer := make(map[primitive.ObjectID]model.Buyer)

	if req.QueryType == "manager" {
		id, err := util.ConvertToObjectWithCtx(ctx, req.ManagerBuyerID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		filter := bson.M{
			"manager_buyer_id": id,
		}

		buyers, err := buyerService.NewBuyerService().ListByCus(ctx, filter)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		var buyerIDs []primitive.ObjectID

		for _, buyer := range buyers {
			buyerIDs = append(buyerIDs, buyer.ID)
			mBuyer[buyer.ID] = buyer
		}

		if len(buyerIDs) < 1 {
			xhttp.RespSuccess(ctx, nil)
			return
		}

		orderFilter["buyer_id"] = bson.M{
			"$in": buyerIDs,
		}
	}

	orders, err := orderService.NewOrderService().List(ctx, orderFilter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = orders

	mOrderProductAmount := make(map[primitive.ObjectID]int)

	var orderIDs []primitive.ObjectID
	for _, order := range orders {
		orderIDs = append(orderIDs, order.ID)
		mOrderProductAmount[order.BuyerID] += order.ProductTotalAmount

		if req.QueryType == "all" {
			mBuyer[order.BuyerID] = model.Buyer{
				ID:        order.BuyerID,
				BuyerName: order.BuyerName,
			}
		}
	}

	// 结算
	debts, err := orderDebtService.NewOrderDebtService().List(ctx, bson.M{
		"order_id": bson.M{
			"$in": orderIDs,
		},
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mDebtAmount := make(map[primitive.ObjectID]int)
	mQualityRefundAmount := make(map[primitive.ObjectID]int)
	for _, debt := range debts {
		for _, settle := range debt.SettleProductList {
			if settle.SettleResultType == model.SettleResultTypeDebt {
				mDebtAmount[debt.BuyerID] += settle.DiffProductAmount
			}
			if settle.SettleResultType == model.SettleResultTypeRefund {
				mQualityRefundAmount[debt.BuyerID] += settle.DiffProductAmount
			}
		}
	}

	// 结算
	refunds, err := orderRefundService.NewOrderRefundService().List(ctx, bson.M{
		"order_id": bson.M{
			"$in": orderIDs,
		},
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mAfterSaleRefundAmount := make(map[primitive.ObjectID]int)
	for _, re := range refunds {
		if re.AuditStatus == model.AuditStatusTypePass {
			mAfterSaleRefundAmount[re.BuyerID] += re.AuditAmount
		}
	}

	buffer, err := toExcel(mOrderProductAmount, mBuyer, mDebtAmount, mQualityRefundAmount, mAfterSaleRefundAmount, begin, end)

	now := time.Now()

	dir := "filecenter"
	pathSuffix := now.Format("20060102T150405")

	objectName := dir + "/productstats/" + pathSuffix + ".xlsx"

	err = ossService.NewOssService().UploadDeliverNote(objectName, buffer)
	if err != nil {
		zap.S().Errorf("上传oss失败：%v", err.Error())
		xhttp.RespErr(ctx, err)
	}

	beginTime := time.UnixMilli(begin)
	endTime := time.UnixMilli(end)
	fileName := fmt.Sprintf("用户销售统计_%d月%d日_%d月%d日.xlsx",
		beginTime.Month(), beginTime.Day(),
		endTime.Month(), endTime.Day())

	fileservice.NewFileService().SaveFileInfo(ctx, fileName, objectName)

	xhttp.RespSuccess(ctx, nil)

}

func GenStatsAll(ctx *gin.Context) {
	var req = struct {
		MonthStamp int64 `json:"month_stamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	begin, end, err := util.MonthScopeTimestamp(req.MonthStamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//var begin int64 = 1743436800000
	//var end int64 = 1744732799000

	//var buyerIDs []primitive.ObjectID
	//mBuyer := make(map[primitive.ObjectID]model.Buyer)
	//
	//for _, buyer := range buyers {
	//	buyerIDs = append(buyerIDs, buyer.ID)
	//	mBuyer[buyer.ID] = buyer
	//}
	//
	//if len(buyerIDs) < 1 {
	//	return
	//}

	orders, err := orderService.NewOrderService().List(ctx, bson.M{
		//"buyer_id": bson.M{
		//	"$in": buyerIDs,
		//},
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
			//"$lte": begin + 72000000,
		},
		"order_status": bson.M{
			"$nin": bson.A{model.OrderStatusTypeClosed, model.OrderStatusTypeCancel},
		},
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = orders

	mBuyer := make(map[primitive.ObjectID]model.Buyer)

	mOrderProductAmount := make(map[primitive.ObjectID]int)

	var orderIDs []primitive.ObjectID
	for _, order := range orders {
		orderIDs = append(orderIDs, order.ID)
		mOrderProductAmount[order.BuyerID] += order.ProductTotalAmount

		mBuyer[order.BuyerID] = model.Buyer{
			ID:        order.BuyerID,
			BuyerName: order.BuyerName,
		}
	}

	// 结算
	debts, err := orderDebtService.NewOrderDebtService().List(ctx, bson.M{
		"order_id": bson.M{
			"$in": orderIDs,
		},
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mDebtAmount := make(map[primitive.ObjectID]int)
	mQualityRefundAmount := make(map[primitive.ObjectID]int)
	for _, debt := range debts {
		for _, settle := range debt.SettleProductList {
			if settle.SettleResultType == model.SettleResultTypeDebt {
				mDebtAmount[debt.BuyerID] += settle.DiffProductAmount
			}
			if settle.SettleResultType == model.SettleResultTypeRefund {
				mQualityRefundAmount[debt.BuyerID] += settle.DiffProductAmount
			}
		}
	}

	// 结算
	refunds, err := orderRefundService.NewOrderRefundService().List(ctx, bson.M{
		"order_id": bson.M{
			"$in": orderIDs,
		},
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mAfterSaleRefundAmount := make(map[primitive.ObjectID]int)
	for _, re := range refunds {
		if re.AuditStatus == model.AuditStatusTypePass {
			mAfterSaleRefundAmount[re.BuyerID] += re.AuditAmount
		}
	}

	toExcel(mOrderProductAmount, mBuyer, mDebtAmount, mQualityRefundAmount, mAfterSaleRefundAmount, begin, end)
}

func toExcel(mOrder map[primitive.ObjectID]int, mBuyer map[primitive.ObjectID]model.Buyer, mDebtAmount, mQualityRefundAmount, mAfterSaleRefundAmount map[primitive.ObjectID]int, begin, end int64) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer f.Close()
	sheetName := "Sheet1"
	// 创建一个工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	setSheet(f, sheetName)

	i := 1

	//title(f, sheetName, i)

	//buyer(f, sheetName, list[0].Order.BuyerName, i+1)

	orderTime(f, sheetName, i+1, begin, end)
	//createTime(f, sheetName, i+2)

	category(f, sheetName, i+2)

	var totalProductAmount int
	var totalDebtAmount int
	var totalQualityRefundAmount int
	var totalAfterSaleRefundAmount int

	i = 5

	for buyerID, productAmount := range mOrder {
		buyer := mBuyer[buyerID]
		debtAmount := mDebtAmount[buyerID]
		qualityRefundAmount := mQualityRefundAmount[buyerID]
		afterSaleRefundAmount := mAfterSaleRefundAmount[buyerID]

		err = f.SetCellValue(sheetName, "B"+strconv.Itoa(i), buyer.BuyerName)

		err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i), dealMoney(productAmount))

		err = f.SetCellValue(sheetName, "D"+strconv.Itoa(i), dealMoney(qualityRefundAmount))
		err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i), dealMoney(debtAmount))
		err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), dealMoney(afterSaleRefundAmount))

		totalProductAmount += productAmount
		totalDebtAmount += debtAmount
		totalQualityRefundAmount += qualityRefundAmount
		totalAfterSaleRefundAmount += afterSaleRefundAmount
		i++
	}

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	//content2 := strconv.Itoa(i)
	//err = f.SetCellStyle(sheetName, "A8", "O"+content2, style)
	err = f.SetRowStyle(sheetName, 5, i, style)

	//setProductStyle(f, sheetName, 5, i)

	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i), dealMoney(totalProductAmount))
	err = f.SetCellValue(sheetName, "D"+strconv.Itoa(i), dealMoney(totalQualityRefundAmount))
	err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i), dealMoney(totalDebtAmount))
	err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), dealMoney(totalAfterSaleRefundAmount))

	// 汇总表
	i += 11

	f.SetActiveSheet(index)

	toBuffer, err := f.WriteToBuffer()
	if err != nil {
		log.Println(err)
		return nil, err
	}

	return toBuffer, nil
}

//bufferFile, err := toExcel(orderLists, parentOrders, calcRes)
//if err != nil {
//xhttp.RespErr(ctx, err)
//return
//}
//
//_ = beginAt
//_ = endAt
//_ = bufferFile
//now := time.Now()
//
//dir := "deliverNote"
//pathSuffix := now.Format("20060102T150405")
//
//fileName := pathSuffix + ".xlsx"
//objectName := dir + "/" + fileName
//
//err = ossService.NewOssService().UploadDeliverNote(objectName, bufferFile)
//if err != nil {
//zap.S().Errorf("上传oss失败：%v", err.Error())
//xhttp.RespErr(ctx, err)
//return
//}
//
//_ = buyerID
//_ = zeroTimestamp

func float64Ptr(f float64) *float64 { return &f }
func boolPtr(f bool) *bool          { return &f }

func setSheet(f *excelize.File, sheetName string) {
	opts := excelize.PageLayoutMarginsOptions{
		Bottom: float64Ptr(0.22),
		Footer: float64Ptr(0.2),
		Header: float64Ptr(0.2),
		Left:   float64Ptr(0.23),
		Right:  float64Ptr(0.23),
		Top:    float64Ptr(0.22),
	}
	err := f.SetPageMargins(sheetName, &opts)
	if err != nil {
		zap.S().Info(err)
	}
	err = f.SetAppProps(&excelize.AppProperties{
		Application:       "Microsoft Excel",
		ScaleCrop:         true,
		DocSecurity:       3,
		Company:           "Company Name",
		LinksUpToDate:     true,
		HyperlinksChanged: true,
		AppVersion:        "16.0000",
	})
	_ = err

	err = f.SetSheetProps(sheetName, &excelize.SheetPropsOptions{
		FitToPage: boolPtr(true), // 开启自适应页面打印，默认值为 false
	})
	_ = err

	if err != nil {
		log.Println(err)
	}
}

func orderTime(f *excelize.File, sheetName string, index int, min, max int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "H"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("订单区间：[ %s , %s ]", minT, MaxT))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func createTime(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	ts := time.Now().Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "C"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "生成时间："+ts)

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func category(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	row2 := strconv.Itoa(index + 1)
	var err error

	// 索引
	err = f.MergeCell(sheetName, "A"+row, "A"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "A"+row, "")

	// 供应商
	err = f.MergeCell(sheetName, "B"+row, "B"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "B"+row, "会员名称")

	err = f.MergeCell(sheetName, "C"+row, "C"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "C"+row, "下单金额")

	err = f.MergeCell(sheetName, "D"+row, "D"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "D"+row, "品控退款")

	err = f.MergeCell(sheetName, "E"+row, "E"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "E"+row, "补差")

	err = f.MergeCell(sheetName, "F"+row, "F"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "F"+row, "售后")

	// 金额小计
	//err = f.MergeCell(sheetName, "G"+row, "G"+row2)
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}
	//err = f.SetCellValue(sheetName, "G"+row, "金额小计")

	//mergeList := []string{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N"}
	//for _, s := range mergeList {
	//	err := f.MergeCell(sheetName, s+row, s+row2)
	//	_ = err
	//}

	err = f.SetColWidth(sheetName, "A", "A", 4)
	err = f.SetColWidth(sheetName, "B", "B", 32)
	err = f.SetColWidth(sheetName, "C", "C", 14)
	err = f.SetColWidth(sheetName, "D", "D", 14)
	//err = f.SetColWidth(sheetName, "G", "G", 14)

	//err = f.SetSheetRow(sheetName, "A"+row, &[]interface{}{"", "供应商", "商品", "计价\n方式", "订单\n数量", "发货\n数量", "商品\n金额", "订单\n重量", "发货\n重量", "仓配\n费", "配送\n费", "支付\n金额", "发货\n退款", "补差\n金额", "小计"})
	//_ = err
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   14,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+row, "F"+row2, style)

	err = f.SetRowHeight(sheetName, index, 20)
	err = f.SetRowHeight(sheetName, index+1, 26)
}

// 商品名称
func setProductStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "left", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "C"+cell1, "C"+cell2, style)
}

func dealMoney(amount int) float64 {
	f, exact := decimal.NewFromInt(int64(amount)).Div(decimal.NewFromInt(100)).Round(2).Float64()

	_ = exact

	return f
}

func dealWeight(w int) float64 {
	f, exact := decimal.NewFromInt(int64(w)).Div(decimal.NewFromInt(1000)).Round(1).Float64()

	_ = exact

	return f
}

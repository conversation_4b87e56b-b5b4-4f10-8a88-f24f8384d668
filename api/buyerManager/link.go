package buyerManager

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/buyerManagerService"
	"base/service/buyerService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func CreateLink(ctx *gin.Context) {
	var req = struct {
		BuyerID        string `json:"buyer_id"`
		ManagerBuyerID string `json:"manager_buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	managerBuyerID, err := util.ConvertToObjectWithCtx(ctx, req.ManagerBuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	getByID, err := buyerService.NewBuyerService().GetByID(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if getByID.ManagerBuyerID != primitive.NilObjectID {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "该客户已被绑定"))
		return
	}

	buyerManager, err := buyerManagerService.NewBuyerManagerService().GetByBuyer(ctx, managerBuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	update := bson.M{
		"manager_buyer_id":  managerBuyerID,
		"manager_user_name": buyerManager.UserName,
	}

	err = buyerService.NewBuyerService().UpdateOne(ctx, buyerID, update)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func DeleteLink(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	update := bson.M{
		"manager_id":        primitive.NilObjectID,
		"manager_buyer_id":  primitive.NilObjectID,
		"manager_user_name": "",
	}

	err = buyerService.NewBuyerService().UpdateOne(ctx, buyerID, update)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func ListLinkByManager(ctx *gin.Context) {
	var req = struct {
		ManagerBuyerID string `json:"manager_buyer_id"`
		Page           int64  `json:"page"`
		Limit          int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	managerBuyerID, err := util.ConvertToObjectWithCtx(ctx, req.ManagerBuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"manager_buyer_id": managerBuyerID,
	}

	list, i, err := buyerService.NewBuyerService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}

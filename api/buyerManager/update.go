package buyerManager

import (
	"base/core/xhttp"
	"base/service/buyerManagerService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Update(ctx *gin.Context) {
	var req = struct {
		ID       string `json:"id"`
		UserName string `json:"user_name"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerManagerService.NewBuyerManagerService().Update(ctx, id, req.UserName)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package deliverAssign

import (
	"base/core/xhttp"
	deliverAssigneService "base/service/deliverAssignService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = deliverAssigneService.NewDeliverAssignService().Delete(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

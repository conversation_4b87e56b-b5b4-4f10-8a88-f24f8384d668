package deliverAssign

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	deliverAssigneService "base/service/deliverAssignService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ListBuyer(ctx *gin.Context) {
	var req = struct {
		UserID    string `json:"user_id"`
		Timestamp int64  `json:"timestamp"` // 时间
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	userID, err := util.ConvertToObjectWithCtx(ctx, req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	timestamp, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	listByUser, err := deliverAssigneService.NewDeliverAssignService().ListByUser(ctx, userID, timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//var orderIDs []primitive.ObjectID
	//for _, assign := range listByUser {
	//	orderIDs = append(orderIDs, assign.OrderIDList...)
	//}

	list := make([]Res, 0)
	for _, assign := range listByUser {
		orders, err := orderService.NewOrderService().ListByOrderIDs(ctx, assign.OrderIDList)
		if err != nil {

			return
		}
		receive := true
		for _, order := range orders {
			if order.OrderStatus != 9 {
				receive = false
				break
			}
		}

		item := Res{
			Receive:       receive,
			DeliverAssign: assign,
		}
		list = append(list, item)
	}

	xhttp.RespSuccess(ctx, list)
}

type Res struct {
	Receive bool `json:"receive"`
	model.DeliverAssign
}

func ListBuyerOrder(ctx *gin.Context) {
	var req = struct {
		//BuyerID   string `json:"buyer_id"`
		//Timestamp int64  `json:"timestamp"` // 时间
		OrderIDList []string `json:"order_id_list"`
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	//begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	var ids []primitive.ObjectID
	for _, s := range req.OrderIDList {
		id, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数错误，请刷新"))
			return
		}
		ids = append(ids, id)

	}

	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return

	}

	xhttp.RespSuccess(ctx, orders)
}

package deliverAssign

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/deliverAssignService"
	"base/service/deliveryManService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"sync"
)

var lock sync.Mutex

func Create(ctx *gin.Context) {
	lock.Lock()
	defer lock.Unlock()

	var req = struct {
		DeliveryManID  string            `json:"delivery_man_id"`
		ServicePointID string            `json:"service_point_id"`
		BuyerIDList    []string          `json:"buyer_id_list"`
		DeliverType    model.DeliverType `json:"deliver_type"`
		Timestamp      int64             `json:"timestamp"` // 时间
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if len(req.BuyerIDList) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数错误，会员列表为空，请刷新"))
		return
	}

	var buyerIDs []primitive.ObjectID
	for _, s := range req.BuyerIDList {
		id, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数错误，请刷新"))
			return
		}
		for _, d := range buyerIDs {
			if d == id {
				xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数错误，存在重复值，请刷新"))
				return
			}
		}
		buyerIDs = append(buyerIDs, id)

	}

	if req.DeliverType == 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数错误，配送方式错误"))
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	timestamp, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	deliveryManID, err := util.ConvertToObjectWithCtx(ctx, req.DeliveryManID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	deliveryMan, err := deliveryManService.NewDeliveryManService().Get(ctx, deliveryManID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = deliverAssigneService.NewDeliverAssignService().CreateAssign(ctx, pointID, deliveryMan, buyerIDs, begin, end, timestamp, req.DeliverType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

//
//func Create(ctx *gin.Context) {
//	var req = struct {
//		UserID    string `json:"user_id"`
//		Key       string `json:"key"`
//		Timestamp int64  `json:"timestamp"` // 时间
//	}{}
//
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	userID, err := util.ConvertToObjectWithCtx(ctx, req.UserID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	_ = userID
//
//	timestamp, err := util.DayStartTimestamp(req.Timestamp)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	err = deliverAssigneService.NewDeliverAssignService().Create(ctx, userID, req.Key, timestamp)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	xhttp.RespSuccess(ctx, nil)
//}

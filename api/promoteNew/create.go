package promoteNew

import (
	"base/core/xhttp"
	"base/model"
	"base/service/ossService"
	"base/service/promoteNewService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	var req = struct {
		ServicePointID string           `json:"service_point_id"`
		Title          string           `json:"title"`
		FileType       string           `json:"file_type"`
		ImgFileList    []model.FileInfo `json:"img_file_list"` // 封面
		Video          model.FileInfo   `json:"video"`         // 视频
		LinkProductID  string           `json:"link_product_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if req.Video.Name != "" {
		snap, err := ossService.NewOssService().VideoSnap("promote", req.Video.Name)
		if err != nil {
			return
		}

		req.Video.Poster = snap
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.LinkProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = promoteNewService.NewPromoteNewService().Create(ctx, pointID, req.Title, req.FileType, req.ImgFileList, req.Video, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

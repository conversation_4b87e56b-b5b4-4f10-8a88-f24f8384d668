package promoteNew

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/service/promoteNewService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func List(ctx *gin.Context) {
	var req = struct {
		Timestamp int64 `json:"timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := xhttp.GetPointID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := promoteNewService.NewPromoteNewService().List(ctx, begin, end, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var productIDs []primitive.ObjectID

	for _, promoteNew := range list {
		productIDs = append(productIDs, promoteNew.LinkProductID)
	}

	products, err := productService.NewProductService().ListByIDs(ctx, productIDs)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resList := make([]promoteNewRes, 0, len(list))
	for _, promoteNew := range list {

		item := promoteNewRes{
			PromoteNew: promoteNew,
		}

		for _, product := range products {
			if product.ID == promoteNew.LinkProductID {
				item.LinkProductTitle = product.Title
				item.LinkProductCover = product.CoverImg
				item.LinkProductPrice = product.StartPrice
				item.RoughWeight = 0
				item.IsCheckWeight = product.IsCheckWeight
			}
		}
		resList = append(resList, item)

	}

	f, _, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}

	if !f {
		for j, _ := range resList {
			resList[j].LinkProductPrice = 0
		}
	}

	xhttp.RespSuccess(ctx, resList)
}

type promoteNewRes struct {
	model.PromoteNew
	LinkProductTitle string         `json:"link_product_title"`
	LinkProductCover model.FileInfo `json:"link_product_cover"`
	LinkProductPrice int            `json:"link_product_price"`
	RoughWeight      int            `json:"rough_weight"`
	IsCheckWeight    bool           `json:"is_check_weight"`
}

func ListByPoint(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		Timestamp      int64  `json:"timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.DayScopeTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := promoteNewService.NewPromoteNewService().List(ctx, begin, end, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var productIDs []primitive.ObjectID

	for _, promoteNew := range list {
		productIDs = append(productIDs, promoteNew.LinkProductID)
	}

	products, err := productService.NewProductService().ListByIDs(ctx, productIDs)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resList := make([]promoteNewRes, 0, len(list))
	for _, promoteNew := range list {

		item := promoteNewRes{
			PromoteNew: promoteNew,
		}

		for _, product := range products {
			if product.ID == promoteNew.LinkProductID {
				item.LinkProductTitle = product.Title
				item.LinkProductCover = product.CoverImg
				item.LinkProductPrice = product.StartPrice
				item.RoughWeight = 0
				item.IsCheckWeight = product.IsCheckWeight
			}
		}
		resList = append(resList, item)

	}

	xhttp.RespSuccess(ctx, resList)
}

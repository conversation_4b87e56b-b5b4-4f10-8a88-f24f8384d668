package authentication

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/authenticationService"
	"base/service/messageService"
	"base/util"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/gin-gonic/gin"
)

func SendPayBindMobileByStation(ctx *gin.Context) {
	var req = struct {
		StationID string `json:"station_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	stationID, err := util.ConvertToObjectWithCtx(ctx, req.StationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	authentication, err := authenticationService.NewAuthenticationService().GetByStation(ctx, stationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := messageService.NewMessageService().SendAllInPay(authentication.PayBizUserId, pays.VerificationCodeTypeBind, authentication.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func BindPayMobileByStation(ctx *gin.Context) {
	var req = struct {
		StationID string `json:"station_id"`
		Captcha   string `json:"captcha"` // 支付绑定验证码
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	stationID, err := util.ConvertToObjectWithCtx(ctx, req.StationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(req.Captcha) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入验证码"))
		return
	}

	//get, err := stationService.NewStationService().Get(ctx, stationID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	authentication, err := authenticationService.NewAuthenticationService().GetByStation(ctx, stationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = authenticationService.NewAuthenticationService().BindMobile(ctx, authentication.ID, authentication.Mobile, req.Captcha)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

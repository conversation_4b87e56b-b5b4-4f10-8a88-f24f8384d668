package authentication

import (
	"base/core/xhttp"
	"base/model"
	"base/service/authenticationService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Get(ctx *gin.Context) {
	var req = struct {
		ObjectType int    `json:"object_type" validate:"required"`
		ObjectID   string `json:"object_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	objectType, err := model.BackObjectType(req.ObjectType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	id, err := util.ConvertToObject(req.ObjectID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := authenticationService.NewAuthenticationService().GetByObject(ctx, id, objectType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func GetByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := authenticationService.NewAuthenticationService().GetByBuyer(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func GetMember(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := authenticationService.NewAuthenticationService().GetMember(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func GetMemberByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	auth, err := authenticationService.NewAuthenticationService().GetByBuyer(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	res, err := authenticationService.NewAuthenticationService().GetMemberIndividual(ctx, auth.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res.IdentityCardNo = dealIdentityNo(res.IdentityCardNo)

	xhttp.RespSuccess(ctx, res)
}

func GetByPoint(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := authenticationService.NewAuthenticationService().GetByServicePoint(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func GetByStation(ctx *gin.Context) {
	var req = struct {
		StationID string `json:"station_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.StationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := authenticationService.NewAuthenticationService().GetByStation(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func dealIdentityNo(s string) string {
	var str string
	if len(s) == 18 {
		str = s[:3] + "******" + s[12:]
	}
	return str
}

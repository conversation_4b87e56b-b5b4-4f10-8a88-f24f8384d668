package authentication

import (
	"base/core/xhttp"
	"base/service/authenticationService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// UnbindBank 解绑银行卡
func UnbindBank(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = authenticationService.NewAuthenticationService().UnbindBank(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// UnbindIndividualBank 解绑个人银行卡
func UnbindIndividualBank(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = authenticationService.NewAuthenticationService().UnbindIndividualBank(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// ApplyBindBank 申请绑定银行卡
func ApplyBindBank(ctx *gin.Context) {
	var req = struct {
		ID     string `json:"id"`
		CardNo string `json:"card_no"`
		Phone  string `json:"phone"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	authentication, err := authenticationService.NewAuthenticationService().GetByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = authenticationService.NewAuthenticationService().ApplyBindBank(ctx, authentication.ID, req.CardNo, req.Phone)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

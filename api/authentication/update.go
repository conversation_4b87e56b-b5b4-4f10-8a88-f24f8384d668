package authentication

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/authenticationService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Update(ctx *gin.Context) {
	var req types.AuthenticationUpdateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = authenticationService.NewAuthenticationService().Update(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// BindPayMobile 绑定支付手机号
func BindPayMobile(ctx *gin.Context) {
	//var req types.BindPayMobileReq
	//userID, err := xhttp.ParseUser(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//env, err := xhttp.GetEnv(ctx)
	//if err != nil {
	//	return
	//}
	//
	//authentication, err := authenticationService.NewAuthenticationService().GetByUserAndEnv(ctx, userID, env)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//if authentication.Mobile != req.Mobile {
	//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "支付手机号与认证手机号不一致"))
	//	return
	//}
	//err = authenticationService.NewAuthenticationService().BindMobile(ctx, authentication.ID, req.Mobile, req.Captcha)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//xhttp.RespSuccess(ctx, nil)
}

// BindPayMobileByAuth 绑定支付手机号
func BindPayMobileByAuth(ctx *gin.Context) {
	var req = struct {
		AuthID  string `json:"auth_id"`
		Mobile  string `json:"mobile"`  // 支付绑定手机号
		Captcha string `json:"captcha"` // 支付绑定验证码
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	authID, err := util.ConvertToObjectWithCtx(ctx, req.AuthID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(req.Captcha) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入验证码"))
		return
	}

	authentication, err := authenticationService.NewAuthenticationService().GetByID(ctx, authID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//if authentication.Mobile != req.Mobile {
	//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "支付手机号与认证手机号不一致"))
	//	return
	//}
	err = authenticationService.NewAuthenticationService().BindMobile(ctx, authentication.ID, req.Mobile, req.Captcha)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// UnbindPayMobile 解绑支付手机号
func UnbindPayMobile(ctx *gin.Context) {
	var req = struct {
		AuthID  string `json:"auth_id"`
		Mobile  string `json:"mobile"`  // 支付绑定手机号
		Captcha string `json:"captcha"` // 支付绑定验证码
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	authID, err := util.ConvertToObjectWithCtx(ctx, req.AuthID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	authentication, err := authenticationService.NewAuthenticationService().GetByID(ctx, authID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	if authentication.Mobile == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "手机号不存在，无需解绑"))
		return
	}
	if authentication.Mobile != req.Mobile {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "手机号与认证手机号不一致"))
		return
	}
	err = authenticationService.NewAuthenticationService().UnbindMobile(ctx, authentication.ID, req.Mobile, req.Captcha)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

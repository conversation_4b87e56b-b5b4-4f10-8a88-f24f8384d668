package authentication

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/authenticationService"
	"base/util"
	"github.com/gin-gonic/gin"
	"strings"
)

// SetCompany 设置企业信息
func SetCompany(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"` // 认证信息ID
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = authenticationService.NewAuthenticationService().SetCompany(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// BindCompanyPersonalBank 绑定公司个人银行卡
func BindCompanyPersonalBank(ctx *gin.Context) {
	var req = struct {
		ID     string `json:"id" validate:"len=24"` // 认证信息ID
		CardNo string `json:"card_no"`              // 银行卡号
		Mobile string `json:"mobile"`               // 银行卡号
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mobile := strings.TrimSpace(req.Mobile)
	cardNo := strings.TrimSpace(req.CardNo)

	if cardNo == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "银行卡号缺失"))
		return
	}

	if !util.CheckMobile(mobile) {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "手机号不正确"))
		return
	}

	err = authenticationService.NewAuthenticationService().BindCompanyPersonalBank(ctx, id, cardNo, mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

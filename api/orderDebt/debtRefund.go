package orderDebt

import (
	"base/core/xhttp"
	"base/service/orderDebtService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// DebtRefund 补差退款
func DebtRefund(ctx *gin.Context) {
	var req = struct {
		DebtID string `json:"debt_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.DebtID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	err = orderDebtService.NewOrderDebtService().DoDebtPaidRefundYee(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package orderDebt

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderDebtService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"sync"
)

var payLock sync.Mutex

// ToDebtPay 去支付
func ToDebtPay(ctx *gin.Context) {
	payLock.Lock()
	defer payLock.Unlock()

	var req = struct {
		OrderID   string              `json:"order_id"`
		OpenID    string              `json:"open_id"`
		PayMethod model.PayMethodType `json:"pay_method"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if req.PayMethod != model.PayMethodTypeYeeWechat && req.PayMethod != model.PayMethodTypeYeeBalance {
		err = xerr.NewErr(xerr.ErrParamError, nil, "支付方式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	orderID, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.OpenID == "" {
		err = xerr.NewErr(xerr.ErrLoginExpire, nil, "登录信息过期，请重新登录")
		xhttp.RespErr(ctx, err)
		return
	}

	debt, err := orderDebtService.NewOrderDebtService().GetByOrderID(ctx, orderID)
	if err != nil {
		zap.S().Errorf("ToDebtPay查询错误%v", err)
		xhttp.RespErr(ctx, err)
		return
	}

	if req.PayMethod == model.PayMethodTypeYeeWechat {
		ip := xhttp.IP(ctx)
		pay, err := orderDebtService.NewOrderDebtService().YeeAggTutelagePrePay(ctx, debt.ID, req.OpenID, ip)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		xhttp.RespSuccess(ctx, pay)
		return
	}

	if req.PayMethod == model.PayMethodTypeYeeBalance {
		err = orderDebtService.NewOrderDebtService().YeeAccountBookPay(ctx, debt.ID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		xhttp.RespSuccess(ctx, nil)
		return
	}
}

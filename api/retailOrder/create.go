package retailOrder

//
//// Create 创建订单
//func Create(ctx *gin.Context) {
//	global.OrderLock.Lock()
//	defer global.OrderLock.Unlock()
//
//	var req types.RetailOrderCreateReq
//	userID, err := xhttp.ParseUser(ctx, &req)
//	if err != nil {
//		return
//	}
//	_ = userID
//
//	// 查询地址
//
//	address, err := retailAddrService.NewRetailAddrService().GetByUserID(ctx, userID)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	if errors.Is(err, mongo.ErrNoDocuments) {
//		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请填写地址"))
//		return
//	}
//
//	if address.ID == primitive.NilObjectID {
//		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请填写地址"))
//		return
//	}
//
//	buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, userID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	pointID, _ := util.ConvertToObjectWithCtx(ctx, "647d77ef1db1e622b23c3339")
//
//	parentOrder, err := orderService.NewOrderService().CreateRetailOrder(ctx, req, buyer, pointID, address)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	xhttp.RespSuccess(ctx, parentOrder)
//}

package retailOrder

import (
	"github.com/gin-gonic/gin"
)

func CreateAddress(ctx *gin.Context) {
	//req := &types.RetailAddrCreate{}
	//err := xhttp.Parse(ctx, req)
	//if err != nil {
	//	return
	//}
	//
	//userID, err := xhttp.UserID(ctx)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//err = retailAddrService.NewRetailAddrService().Create(ctx, req, userID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//xhttp.RespSuccess(ctx, nil)
}

func UpdateAddress(ctx *gin.Context) {
	//req := &types.RetailAddrUpdate{}
	//err := xhttp.Parse(ctx, req)
	//if err != nil {
	//	return
	//}
	//
	//userID, err := xhttp.UserID(ctx)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//err = retailAddrService.NewRetailAddrService().Update(ctx, req, userID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//xhttp.RespSuccess(ctx, nil)
}

func GetAddress(ctx *gin.Context) {
	//var req = struct {
	//	UserID string `json:"user_id"`
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//userID, err := xhttp.UserID(ctx)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//address, err := retailAddrService.NewRetailAddrService().GetByUserID(ctx, userID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//if address.ID == primitive.NilObjectID {
	//	xhttp.RespNoExist(ctx)
	//	return
	//}
	//xhttp.RespSuccess(ctx, address)
}

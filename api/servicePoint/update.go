package servicePoint

import (
	"base/core/xhttp"
	"base/model"
	"base/service/servicePointService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// UpdateHeadImg 更新门头照
func UpdateHeadImg(ctx *gin.Context) {
	var req = struct {
		ServicePointID string         `json:"service_point_id" validate:"len=24"`
		ShopHeadImg    model.FileInfo `json:"shop_head_img"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = servicePointService.NewServicePointService().UpdateHeadImg(ctx, id, req.ShopHeadImg)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func UpdateScope(ctx *gin.Context) {
	var req = struct {
		ServicePointID string                `json:"service_point_id"`
		Scope          []model.PointLocation `json:"scope"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = servicePointService.NewServicePointService().UpdateScope(ctx, id, req.Scope)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func UpdateCenterLocation(ctx *gin.Context) {
	var req = struct {
		ServicePointID string  `json:"service_point_id"`
		Longitude      float64 `json:"longitude"` // 经度
		Latitude       float64 `json:"latitude"`  // 维度
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = servicePointService.NewServicePointService().UpdateCenterLocation(ctx, id, req.Longitude, req.Latitude)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

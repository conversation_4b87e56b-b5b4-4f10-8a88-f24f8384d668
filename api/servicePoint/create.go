package servicePoint

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/servicePointService"
	"base/service/userService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
)

func CreateSecond(ctx *gin.Context) {
	var req types.ServicePointSecondCreateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	for _, v := range req.DeliverType {
		if _, ok := model.DeliverTypeMsg[v]; !ok {
			xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "配送方式参数错误"))
			return
		}
	}

	user, err := userService.NewUserService().GetByMobile(ctx, req.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, "647d77ef1db1e622b23c3339")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = servicePointService.NewServicePointService().CreateSecond(ctx, user.ID, id, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
	return
}

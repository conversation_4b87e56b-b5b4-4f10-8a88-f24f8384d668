package buyerBalance

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/buyerBalanceOrderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"time"
)

// Withdraw 提现
func Withdraw(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Amount  int    `json:"amount"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.Amount <= 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "提现金额错误"))
		return
	}

	now := time.Now()

	hour := now.Hour()

	if hour < 9 || hour > 19 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请在工作时间进行提现"))
		return
	}

	err = buyerBalanceOrderService.NewBuyerBalanceOrderService().Withdraw(ctx, buyerID, req.Amount)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func ListWithdraw(ctx *gin.Context) {
	var req = struct {
		PayStatus model.PayStatusType `json:"pay_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	data, err := buyerBalanceOrderService.NewBuyerBalanceOrderService().ListWithdraw(ctx, req.PayStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

func AuditWithdraw(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerBalanceOrderService.NewBuyerBalanceOrderService().AuditWithdraw(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

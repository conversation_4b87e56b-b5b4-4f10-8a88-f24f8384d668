package buyerBalance

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/buyerBalanceOrderService"
	"base/service/buyerService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Deposit(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Amount  int    `json:"amount"`
		OpenID  string `json:"open_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	if req.Amount < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "金额参数错误，请联系客服"))
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := buyerBalanceOrderService.NewBuyerBalanceOrderService().CreateAndPay(ctx, buyerID, req.Amount, req.OpenID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

func ListDepositByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"buyer_id":   buyerID,
		"pay_status": model.PayStatusTypePaid,
	}
	data, err := buyerBalanceOrderService.NewBuyerBalanceOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

func ListDepositWeb(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page"`
		Limit int64 `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"pay_status": model.PayStatusTypePaid,
	}
	list, count, err := buyerBalanceOrderService.NewBuyerBalanceOrderService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resList := make([]depositRes, 0, len(list))

	var ids []primitive.ObjectID
	for _, d := range list {
		ids = append(ids, d.BuyerID)
	}

	if len(ids) < 1 {
		xhttp.RespSuccessList(ctx, resList, count)
		return
	}

	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	})

	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	for _, d := range list {
		var buyerName string
		for _, buyer := range buyers {
			if d.BuyerID == buyer.ID {
				buyerName = buyer.BuyerName
			}
		}

		resList = append(resList, depositRes{
			BuyerBalanceOrder: d,
			BuyerName:         buyerName,
		})
	}

	xhttp.RespSuccessList(ctx, resList, count)
}

type depositRes struct {
	model.BuyerBalanceOrder
	BuyerName string `json:"buyer_name"`
}

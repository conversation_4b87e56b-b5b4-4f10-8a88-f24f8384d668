package buyerBalance

import (
	"base/core/xhttp"
	"base/service/buyerBalanceAccountService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func GetAccount(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := buyerBalanceAccountService.NewBuyerBalanceAccountService().Get(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

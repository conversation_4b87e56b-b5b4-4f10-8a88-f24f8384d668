package category

import (
	"base/core/xhttp"
	"base/service/categoryService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListFirst 第一级所有
func ListFirst(ctx *gin.Context) {
	var req = struct {
		VisibleType int `uri:"visible_type" validate:"oneof=1 2 3"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	list, err := categoryService.NewCategoryService().ListFirst(ctx, req.VisibleType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

// ListNext 下一级
func ListNext(ctx *gin.Context) {
	var req = struct {
		ParentID    string `uri:"parent_id"  validate:"-"`
		VisibleType int    `uri:"visible_type" validate:"oneof=1 2 3"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	env := xhttp.GetEnv(ctx)

	list, err := categoryService.NewCategoryService().List(ctx, req.ParentID, env, req.VisibleType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)
}

func GetDetail(ctx *gin.Context) {
	var req = struct {
		ThirdCategoryID string `json:"third_category_id"  validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ThirdCategoryID, "third_category_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := categoryService.NewCategoryService().GetDetail(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

func ListSecondByIDs(ctx *gin.Context) {
	var req = struct {
		CategoryIDList []string `json:"category_id_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, s := range req.CategoryIDList {
		id, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	list, err := categoryService.NewCategoryService().ListSecondByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

package adminUser

import (
	"base/core/xhttp"
	"base/model"
	"base/service/adminService"
	"base/service/userService"

	"github.com/gin-gonic/gin"
)

// Create 创建管理员
func Create(ctx *gin.Context) {
	var req = struct {
		Mobile   string           `json:"mobile"`
		Note     string           `json:"note"`
		RoleInfo []model.RoleInfo `json:"role_info"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	user, err := userService.NewUserService().GetByMobile(ctx, req.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = adminService.NewAdminService().Create(ctx, user.ID, req.Note, req.RoleInfo)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

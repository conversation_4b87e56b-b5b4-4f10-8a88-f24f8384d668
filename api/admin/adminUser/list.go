package adminUser

import (
	"base/core/xhttp"
	"base/model"
	"base/service/adminService"
	"base/service/userService"
	"base/types"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// List 查询
func List(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, err := adminService.NewAdminService().List(ctx, bson.M{})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var ids []primitive.ObjectID
	for _, admin := range list {
		ids = append(ids, admin.UserID)
	}

	users, err := userService.NewUserService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var res []types.AdminRes
	for _, admin := range list {
		for _, user := range users {
			if admin.UserID == user.ID {
				res = append(res, types.AdminRes{
					Admin:  admin,
					Mobile: user.Mobile,
				})
			}
		}
	}

	xhttp.RespSuccess(ctx, res)
}

// ListAuth 角色与权限列表
func ListAuth(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	res := []any{
		model.SuperRoleInfo,
		model.NormalRoleInfo,
		model.YhtRoleInfo,
	}

	xhttp.RespSuccess(ctx, res)
}

package adminUser

import (
	"base/core/xhttp"
	"base/service/adminService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Delete 删除管理员
func Delete(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	userID, err := util.ConvertToObjectWithCtx(ctx, req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = adminService.NewAdminService().Delete(ctx, userID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

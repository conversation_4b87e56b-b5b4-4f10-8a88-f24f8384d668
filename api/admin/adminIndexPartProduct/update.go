package adminIndexPartProduct

import (
	"base/core/xhttp"
	"base/service/indexPartProductService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Update(ctx *gin.Context) {
	var req = struct {
		IndexPartID   string   `json:"index_part_id" validate:"len=24"`
		Operate       string   `json:"operate" validate:"oneof=add remove"`
		ProductIDList []string `json:"product_id_list" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.IndexPartID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var pIDs []primitive.ObjectID
	for _, s := range req.ProductIDList {
		i, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		pIDs = append(pIDs, i)
	}

	err = indexPartProductService.NewIndexPartProductService().Update(ctx, id, pIDs, req.Operate)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateSort(ctx *gin.Context) {
	var req = struct {
		IndexPartID   string   `json:"index_part_id" validate:"len=24"`
		ProductIDList []string `json:"product_id_list" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.IndexPartID, "adminIndexPartProduct IndexPartID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var pIDs []primitive.ObjectID
	for _, s := range req.ProductIDList {
		i, err := util.ConvertToObjectWithNote(s, "adminIndexPartProduct ProductIDList")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		pIDs = append(pIDs, i)
	}

	err = indexPartProductService.NewIndexPartProductService().UpdateSort(ctx, id, pIDs)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

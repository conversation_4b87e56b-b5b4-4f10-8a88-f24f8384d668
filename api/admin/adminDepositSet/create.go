package adminDeposiSet

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/depositSetService"
	"github.com/gin-gonic/gin"
)

// Create 创建
func Create(ctx *gin.Context) {
	var req = struct {
		ObjectType int `json:"object_type" validate:"required"`
		Amount     int `json:"amount" validate:"min=0"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if _, ok := model.ObjectTypeMsg[model.ObjectType(req.ObjectType)]; !ok {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "对象类型错误"))
		return
	}

	err = depositSetService.NewDepositSetService().Create(ctx, model.ObjectType(req.ObjectType), req.Amount)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

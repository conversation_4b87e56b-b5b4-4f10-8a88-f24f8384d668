package adminOrder

import (
	"base/model"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type Calc<PERSON><PERSON> struct {
	TotalAmount              int   `json:"total_amount"`
	TotalPaidAmount          int   `json:"total_paid_amount"`
	TotalDeliverAmount       int   `json:"total_deliver_amount"`
	TotalFinalAmount         int   `json:"total_final_amount"`
	TotalDebtPaidAmount      int   `json:"total_debt_paid_amount"`
	TotalDebtNotPaidAmount   int   `json:"total_debt_not_paid_amount"`
	TotalAfterSalePassAmount int   `json:"total_after_sale_pass_amount"`
	TotalShipRefundAmount    int   `json:"total_ship_refund_amount"`
	OrderTimeBegin           int64 `json:"order_time_begin"`
	OrderTimeEnd             int64 `json:"order_time_end"`
	ExistAfterSaleAuditing   bool  `json:"exist_after_sale_auditing"`
	ExistDebtNotPaid         bool  `json:"exist_debt_not_paid"`
}

func CalcFunc(orders []model.Order, refunds []model.OrderRefund, debts []model.OrderDebt) ([]OrderList, CalcRes) {
	var orderTimeBegin int64
	var orderTimeEnd int64

	var totalPaidAmount int
	var totalDebtPaidAmount int
	var totalDebtNotPaidAmount int
	var totalAfterSalePassAmount int
	var totalShipRefundAmount int

	var existAfterSaleAuditing bool
	var existDebtNotPaid bool

	for _, order := range orders {
		totalPaidAmount += order.PaidAmount

		if orderTimeBegin == 0 {
			orderTimeBegin = order.CreatedAt
		}
		if orderTimeEnd == 0 {
			orderTimeEnd = order.CreatedAt
		}

		if orderTimeBegin > order.CreatedAt {
			orderTimeBegin = order.CreatedAt
		}
		if orderTimeEnd < order.CreatedAt {
			orderTimeEnd = order.CreatedAt
		}
	}

	mDebt := make(map[primitive.ObjectID]model.OrderDebt)

	for _, debt := range debts {
		if debt.PayStatus == model.PayStatusTypePaid {
			//totalDebtPaidAmount += debt.TotalProductAmount + debt.TotalTransportFee + debt.TotalWarehouseLoadFee
			totalDebtPaidAmount += debt.TotalProductAmount
		} else {
			//totalDebtNotPaidAmount += debt.TotalProductAmount + debt.TotalTransportFee + debt.TotalWarehouseLoadFee
			totalDebtNotPaidAmount += debt.TotalProductAmount
		}
		mDebt[debt.OrderID] = debt
		if debt.PayStatus != model.PayStatusTypePaid {
			existDebtNotPaid = true
		}
	}

	mRefund := make(map[primitive.ObjectID][]model.OrderRefund)

	for _, re := range refunds {
		if re.RefundType == model.RefundTypeQuality {
			totalShipRefundAmount += re.AuditAmount + re.TotalWarehouseLoadFee + re.TotalServiceFee
		}
		if re.RefundType == model.RefundTypeAfterSale && re.AuditStatus == model.AuditStatusTypePass {
			totalAfterSalePassAmount += re.AuditAmount
		}
		mRefund[re.OrderID] = append(mRefund[re.OrderID], re)

		if re.RefundType == model.RefundTypeAfterSale && re.AuditStatus == model.AuditStatusTypeDoing {
			existAfterSaleAuditing = true
		}
	}

	resList := make([]OrderList, 0)
	for _, order := range orders {
		item := OrderList{
			Order:      order,
			Debt:       mDebt[order.ID],
			RefundList: mRefund[order.ID],
		}
		resList = append(resList, item)
	}

	totalFinalAmount := totalPaidAmount - totalShipRefundAmount + totalDebtPaidAmount + totalDebtNotPaidAmount

	res := CalcRes{
		TotalAmount:              totalPaidAmount,
		TotalPaidAmount:          totalPaidAmount,
		TotalDebtPaidAmount:      totalDebtPaidAmount,
		TotalDebtNotPaidAmount:   totalDebtNotPaidAmount,
		TotalAfterSalePassAmount: totalAfterSalePassAmount,
		TotalShipRefundAmount:    totalShipRefundAmount,
		OrderTimeBegin:           orderTimeBegin,
		OrderTimeEnd:             orderTimeEnd,
		ExistAfterSaleAuditing:   existAfterSaleAuditing,
		ExistDebtNotPaid:         existDebtNotPaid,
		TotalFinalAmount:         totalFinalAmount,
	}
	return resList, res

}

type OrderList struct {
	Order      model.Order         `json:"order"`
	Debt       model.OrderDebt     `json:"debt"`
	RefundList []model.OrderRefund `json:"refund_list"`
}

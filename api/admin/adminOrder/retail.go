package adminOrder

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/global"
	"base/model"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/orderWarehouseService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ListRetail(ctx *gin.Context) {
	var req = struct {
		PayStatus   int   `json:"pay_status"`
		OrderStatus int   `json:"order_status"`
		TimeBegin   int64 `json:"time_begin"`
		TimeEnd     int64 `json:"time_end"`
		Page        int64 `json:"page" validate:"min=1"`
		Limit       int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	filter := bson.M{}
	//filter["pay_status"] = bson.M{
	//	"$or": bson.A{model.PayStatusTypePaid, model.PayStatusTypePaidButRefund},
	//}
	pointID, err := util.ConvertToObjectWithCtx(ctx, "647d77ef1db1e622b23c3339")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter["service_point_id"] = pointID
	filter["order_type"] = model.OrderTypeRetail

	if req.OrderStatus != 0 {
		filter["order_status"] = req.OrderStatus
	}
	if req.PayStatus != 0 {
		filter["pay_status"] = req.PayStatus
	}
	if req.TimeBegin != 0 {
		filter["created_at"] = bson.M{
			"$gte": req.TimeBegin,
			"$lte": req.TimeEnd,
		}
	}

	orders, count, err := orderService.NewOrderService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, orders, count)
}

func ShipRetail(ctx *gin.Context) {
	global.OrderLock.Lock()
	defer global.OrderLock.Unlock()

	var req = struct {
		OrderID            string           `json:"order_id"`
		LogisticsImageList []model.FileInfo `json:"logistics_image_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(req.LogisticsImageList) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "快递单图片缺失"))
		return
	}

	for _, info := range req.LogisticsImageList {
		if info.Name == "" {
			xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "快递单图片缺失"))
			return
		}
	}

	err = orderWarehouseService.NewOrderWarehouseService().ShipRetail(ctx, id, req.LogisticsImageList)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// ConfirmRetail 确认订单
func ConfirmRetail(ctx *gin.Context) {
	global.OrderLock.Lock()
	defer global.OrderLock.Unlock()

	var req = struct {
		OrderIDList []string `json:"order_id_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if len(req.OrderIDList) < 1 {
		err = xerr.NewErr(xerr.ErrParamError, nil, "请选择订单")
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, i := range req.OrderIDList {
		id, err := util.ConvertToObjectWithCtx(ctx, i)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		var f bool
		for _, objectID := range ids {
			if id == objectID {
				f = true
			}
		}
		if !f {
			ids = append(ids, id)
		}
	}

	err = orderWarehouseService.NewOrderWarehouseService().ConfirmRetail(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)

}

func ListRetailRefundOrder(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		AuditStatus    int    `json:"audit_status"`
		WithdrawStatus int    `json:"withdraw_status"` // 撤销状态   0 所有 1 未撤销 2 已撤销
		Page           int64  `json:"page" validate:"min=1"`
		Limit          int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	statusType, err := model.BackAuditStatusType(req.AuditStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, i, err := orderRefundService.NewOrderRefundService().ListRetailByPage(ctx, pointID, statusType, req.Page, req.Limit, req.WithdrawStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}

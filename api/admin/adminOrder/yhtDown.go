package adminOrder

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/parentOrderService"
	"base/util"
	"bytes"
	"fmt"
	"log"
	"sort"
	"strconv"
	"time"
	"unicode/utf8"

	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

func DownExcel(ctx *gin.Context) {
	var req = struct {
		Month int64 `json:"month"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{}
	filter["order_type"] = model.OrderTypeWholeSale
	filter["user_type"] = "YHT"

	filter["order_status"] = model.OrderStatusTypeFinish

	filter["pay_status"] = model.PayStatusTypePaid

	begin, end, err := util.MonthScopeTimestamp(req.Month)
	filter["created_at"] = bson.M{
		"$gte": begin,
		"$lte": end,
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(orders) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "无已完成的订单"))
		return
	}

	var parentOrderIDs []primitive.ObjectID
	var orderIDs []primitive.ObjectID
	for _, order := range orders {
		parentOrderIDs = append(parentOrderIDs, order.ParentOrderID)
		orderIDs = append(orderIDs, order.ID)
	}

	parentOrders, err := parentOrderService.NewParentOrderService().List(ctx, bson.M{"_id": bson.M{"$in": parentOrderIDs}})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	debts := make([]model.OrderDebt, 0)
	mDebt := make(map[primitive.ObjectID]model.OrderDebt)
	for _, debt := range debts {
		mDebt[debt.OrderID] = debt
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	orderLists, calcRes := CalcFunc(orders, refunds, debts)

	bufferFile, err := toExcel(orderLists, parentOrders, calcRes, begin, end)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	ctx.Writer.Header().Set("Content-Type", "application/vnd.ms-excel")
	ctx.Writer.Header().Set("response-type", "blob")
	_, err = ctx.Writer.Write(bufferFile.Bytes())
	if err != nil {

	}

}

type parentOrderSort []model.ParentOrder

func (array parentOrderSort) Len() int {
	return len(array)
}

func (array parentOrderSort) Less(i, j int) bool {
	return array[i].CreatedAt < array[j].CreatedAt //从小到大， 若为大于号，则从大到小
}

func (array parentOrderSort) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

func toExcel(list []OrderList, parentOrders []model.ParentOrder, res CalcRes, exportBegin, exportEnd int64) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	sheetName := "Sheet1"
	// 创建一个工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	setSheet(f, sheetName)

	i := 1

	title(f, sheetName, i)

	orderTime(f, sheetName, i+1, res.OrderTimeBegin, res.OrderTimeEnd, exportBegin, exportEnd)
	createTime(f, sheetName, i+2)

	category(f, sheetName, i+4)

	i = 7

	mParent := make(map[primitive.ObjectID][]OrderList)
	for _, orderList := range list {
		mParent[orderList.Order.ParentOrderID] = append(mParent[orderList.Order.ParentOrderID], orderList)
	}

	sort.Sort(parentOrderSort(parentOrders))

	var totalProductAmount int
	var pAllFinalAmount int
	var totalSortNum int

	var setColorList []string

	for pI, pOrder := range parentOrders {
		if allList, ok := mParent[pOrder.ID]; ok {
			deliverMergeBegin := i
			var paidAmountPerOrder int
			for k, v := range allList {
				productNum := len(v.Order.ProductList)
				if productNum > 1 {
					err = f.MergeCell(sheetName, "C"+strconv.Itoa(i), "C"+strconv.Itoa(i+productNum-1))
				}

				_ = k
				err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i+productNum-1), v.Order.BuyerName)

				if productNum > 1 {
					err = f.MergeCell(sheetName, "D"+strconv.Itoa(i), "D"+strconv.Itoa(i+productNum-1))
				}
				err = f.SetCellValue(sheetName, "D"+strconv.Itoa(i+productNum-1), v.Order.Address.Address)

				if productNum > 1 {
					err = f.MergeCell(sheetName, "E"+strconv.Itoa(i), "E"+strconv.Itoa(i+productNum-1))
				}
				err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i+productNum-1), v.Order.SupplierName)

				for j, p := range v.Order.ProductList {
					productTitle := fmt.Sprintf("%s[%s]", p.ProductTitle, p.SkuName)
					err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i+j), productTitle)
					// 单价
					err = f.SetCellValue(sheetName, "H"+strconv.Itoa(i+j), dealMoney(p.Price))
					err = f.SetCellValue(sheetName, "I"+strconv.Itoa(i+j), p.Num)
					err = f.SetCellValue(sheetName, "L"+strconv.Itoa(i+j), p.SortNum)

					totalSortNum += p.SortNum

					runeLen := utf8.RuneCountInString(productTitle)
					if runeLen < 30 {
						runeLen = 30
					}

					saleWay := "按件"
					if p.IsCheckWeight {
						saleWay = "称重"
					}
					err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i+j), saleWay)

					err = f.SetRowHeight(sheetName, i+j, float64(runeLen))
					err = f.SetCellValue(sheetName, "J"+strconv.Itoa(i+j), dealMoney(p.ProductAmount))
					err = f.SetCellValue(sheetName, "K"+strconv.Itoa(i+j), dealWeight(p.RoughWeight*p.Num))

					var afterSaleRefund int
					var auditStatus model.AuditStatusType
					for _, r1 := range v.RefundList {
						if r1.ProductID == p.ProductID && r1.SkuIDCode == p.SkuIDCode {
							if r1.RefundType == model.RefundTypeAfterSale && (r1.AuditStatus == model.AuditStatusTypeDoing || r1.AuditStatus == model.AuditStatusTypePass) {
								//	 售后退款
								afterSaleRefund += r1.AuditAmount
								auditStatus = r1.AuditStatus
							}
						}
					}

					if afterSaleRefund != 0 {
						var afterSaleNote string
						if auditStatus == model.AuditStatusTypeDoing {
							afterSaleNote = "[审核中]"
							setColorList = append(setColorList, "N"+strconv.Itoa(i+j))
						}
						err = f.SetCellValue(sheetName, "N"+strconv.Itoa(i+j), fmt.Sprintf("%.2f%s", dealMoney(afterSaleRefund), afterSaleNote))
					}

					var qualityRefund int
					for _, d1 := range v.Debt.SettleProductList {
						if d1.ProductID == p.ProductID && d1.SkuIDCode == p.SkuIDCode && d1.SettleResultType == model.SettleResultTypeRefund {
							qualityRefund += d1.DiffProductAmount
							continue
						}
					}
					if qualityRefund != 0 {
						err = f.SetCellValue(sheetName, "M"+strconv.Itoa(i+j), dealMoney(qualityRefund))
					}

					var debtPaidAmount int
					var debtNotPaidAmount int
					for _, d1 := range v.Debt.SettleProductList {
						if d1.ProductID == p.ProductID && d1.SkuIDCode == p.SkuIDCode && d1.SettleResultType == model.SettleResultTypeDebt {
							if v.Debt.PayStatus != 4 {
								debtNotPaidAmount += d1.DiffProductAmount
							}

							if v.Debt.PayStatus == 4 {
								debtPaidAmount += d1.DiffProductAmount
							}

							continue
						}
					}

					// 金额小计 商品金额+仓配费-品控退款+补差金额
					pFinalAmount := p.ProductAmount - qualityRefund
					if afterSaleRefund != 0 && auditStatus == model.AuditStatusTypePass {
						pFinalAmount -= afterSaleRefund
					}

					pAllFinalAmount += pFinalAmount

					err = f.SetCellValue(sheetName, "O"+strconv.Itoa(i+j), dealMoney(pFinalAmount))

				}
				i += productNum

				//totalServiceFee += v.Order.TotalServiceFee
				//totalLoadFee += v.Order.TotalWarehouseLoadFee
				totalProductAmount += v.Order.ProductTotalAmount

				paidAmountPerOrder += v.Order.PaidAmount
			}

			mergeNum := strconv.Itoa(deliverMergeBegin)
			err = f.MergeCell(sheetName, "A"+strconv.Itoa(deliverMergeBegin), "A"+strconv.Itoa(i-1))
			err = f.SetCellValue(sheetName, "A"+mergeNum, pI+1)

			//totalDeliverFee += pOrder.DeliverFeeRes.FinalDeliverFee
			//totalDeliverFeeAll += pOrder.DeliverFeeRes.TotalDeliverFee
			//if pOrder.DeliverFeeRes.SubsidyDeliverFee > 0 {
			//	totalDeliverFeeSubsidy += pOrder.DeliverFeeRes.SubsidyDeliverFee
			//}

			// 下单时间
			err = f.MergeCell(sheetName, "B"+strconv.Itoa(deliverMergeBegin), "B"+strconv.Itoa(i-1))
			format := time.UnixMilli(pOrder.CreatedAt).Format("2006-01-02 15:04:05")
			err = f.SetCellValue(sheetName, "B"+strconv.Itoa(deliverMergeBegin), format)

		}

	}

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	content2 := strconv.Itoa(i)
	err = f.SetCellStyle(sheetName, "A7", "O"+content2, style)
	//err = f.SetRowStyle(sheetName, 6, i, style)

	setProductStyle(f, sheetName, 7, i)
	setColorStyle(f, sheetName, setColorList)

	// 小计
	resForCol(f, sheetName, i, res, totalProductAmount, pAllFinalAmount, totalSortNum)

	i++
	serviceFeeRow(f, sheetName, i, totalSortNum)

	//totalPaidServiceFee := totalServiceFee - totalRefundServiceFee
	//setOther(f, sheetName, i+1, totalDeliverFee, totalDeliverFeeAll, totalDeliverFeeSubsidy, totalServiceFee, totalPaidServiceFee, pAllFinalAmount)

	i += 7

	//note(f, sheetName, i)

	f.SetActiveSheet(index)

	toBuffer, err := f.WriteToBuffer()
	if err != nil {
		log.Println(err)
		return nil, err
	}

	//f.SaveAs("./api/admin/adminOrder/test.xlsx")

	return toBuffer, nil
}

func setColorStyle(f *excelize.File, sheetName string, cellList []string) {
	var err error
	_ = err
	for _, s := range cellList {
		style, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{
				Bold:   true,
				Family: "宋体",
				Size:   10,
				Color:  "#ff0000",
			},
			Border: []excelize.Border{
				{Type: "left", Color: "000000", Style: 1},
				{Type: "top", Color: "000000", Style: 1},
				{Type: "bottom", Color: "000000", Style: 1},
				{Type: "right", Color: "000000", Style: 1},
			},
			Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
		})
		_ = f.SetCellStyle(sheetName, s, s, style)
	}
}

// 其他
func setOther(f *excelize.File, sheetName string, index, totalDeliverFee, totalDeliverFeeAll, totalDeliverFeeSubsidy, totalServiceFee, totalPaidServiceFee, pAllFinalAmount int) {
	originIndex := index
	row := strconv.Itoa(index)
	var err error

	err = f.SetCellValue(sheetName, "B"+row, "其他")

	colNum := 3
	colValueNum := 17
	// 配送费
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), convertToCol(colNum+1)+strconv.Itoa(index+1))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), convertToCol(colNum+1)+strconv.Itoa(index+2))
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "配送费")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), "配送费补贴")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), "配送费实付")
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(totalDeliverFeeAll))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+1), -1*dealMoney(totalDeliverFeeSubsidy))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+2), dealMoney(totalDeliverFee))

	// 服务费
	index += 2
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), convertToCol(colNum+1)+strconv.Itoa(index+1))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), convertToCol(colNum+1)+strconv.Itoa(index+2))
	//err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "服务费")
	//err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), "服务费平台补贴")
	//err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), "服务费实付")
	//err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(totalServiceFee))
	//err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+1), dealMoney(totalPaidServiceFee))
	//err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+2), 0)

	// 优惠券
	//index += 1
	//err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	//err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "优惠券使用")
	//err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), 0)

	//index += 1
	err = f.MergeCell(sheetName, "B"+strconv.Itoa(originIndex), "B"+strconv.Itoa(index))
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	//err = f.MergeCell(sheetName, "B"+strconv.Itoa(index+1), "B"+strconv.Itoa(index))
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}

	index += 1
	err = f.SetCellValue(sheetName, "B"+strconv.Itoa(index), "合计")
	finalAmount := pAllFinalAmount + totalDeliverFee
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(finalAmount))
	//
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+strconv.Itoa(originIndex), convertToCol(colValueNum)+strconv.Itoa(index), style)

	style2, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+strconv.Itoa(index), convertToCol(colValueNum)+strconv.Itoa(index), style2)
	//zap.S().Info("originIndex::", originIndex)
	//err = f.SetRowHeight(sheetName, originIndex, 20)
	//err = f.SetRowHeight(sheetName, originIndex+1, 20)
	//err = f.SetRowHeight(sheetName, originIndex+2, 20)
	//err = f.SetRowHeight(sheetName, originIndex+3, 20)
	//err = f.SetRowHeight(sheetName, originIndex+4, 20)
	//err = f.SetRowHeight(sheetName, originIndex+5, 20)
	//err = f.SetRowHeight(sheetName, originIndex+6, 20)
	//err = f.SetRowHeight(sheetName, colValueNum+7, 20)
	//err = f.SetRowHeight(sheetName, colValueNum+8, 26)
}

// 商品名称
func setProductStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "left", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "D"+cell1, "D"+cell2, style)
}

// 小计
func resForCol(f *excelize.File, sheetName string, index int, res CalcRes, totalProductAmount, pAllFinalAmount, totalSortNum int) {
	row := strconv.Itoa(index)
	//row2 := strconv.Itoa(index + 1)

	var err error
	_ = err

	err = f.SetRowHeight(sheetName, index, 26)
	err = f.SetCellValue(sheetName, "B"+row, "小计")
	err = f.SetCellValue(sheetName, "J"+row, dealMoney(totalProductAmount))
	err = f.SetCellValue(sheetName, "L"+row, strconv.Itoa(totalSortNum)+"件")
	err = f.SetCellValue(sheetName, "M"+row, dealMoney(res.TotalShipRefundAmount))
	err = f.SetCellValue(sheetName, "N"+row, dealMoney(res.TotalAfterSalePassAmount))
	//err = f.SetCellValue(sheetName, "P"+row, dealMoney(res.TotalDebtPaidAmount+res.TotalDebtNotPaidAmount))
	err = f.SetCellValue(sheetName, "O"+row, dealMoney(pAllFinalAmount))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+row, "O"+row, style)

	err = f.SetRowHeight(sheetName, index, 20)

}

func serviceFeeRow(f *excelize.File, sheetName string, index int, totalSortNum int) {
	row := strconv.Itoa(index)
	row2 := strconv.Itoa(index + 1)

	var err error
	_ = err

	amount := totalSortNum * 5

	err = f.SetRowHeight(sheetName, index, 26)
	err = f.SetCellValue(sheetName, "L"+row, "服务费5元每件")
	err = f.SetCellValue(sheetName, "L"+row2, "总计："+strconv.Itoa(amount))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "L"+row, "L"+row2, style)

	err = f.SetRowHeight(sheetName, index, 20)

}

func float64Ptr(f float64) *float64 { return &f }
func boolPtr(f bool) *bool          { return &f }

func setSheet(f *excelize.File, sheetName string) {
	opts := excelize.PageLayoutMarginsOptions{
		Bottom: float64Ptr(0.22),
		Footer: float64Ptr(0.2),
		Header: float64Ptr(0.2),
		Left:   float64Ptr(0.14),
		Right:  float64Ptr(0.14),
		Top:    float64Ptr(0.22),
	}
	err := f.SetPageMargins(sheetName, &opts)
	if err != nil {
		zap.S().Info(err)
	}
	err = f.SetAppProps(&excelize.AppProperties{
		Application:       "Microsoft Excel",
		ScaleCrop:         true,
		DocSecurity:       3,
		Company:           "Company Name",
		LinksUpToDate:     true,
		HyperlinksChanged: true,
		AppVersion:        "16.0000",
	})
	_ = err

	err = f.SetSheetProps(sheetName, &excelize.SheetPropsOptions{
		FitToPage: boolPtr(true), // 开启自适应页面打印，默认值为 false
	})
	_ = err

	if err != nil {
		log.Println(err)
	}
}

func createTime(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	ts := time.Now().Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "E"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "生成时间："+ts)

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func note(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	err := f.MergeCell(sheetName, "B"+row, "C"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "备注说明：")

	err = f.MergeCell(sheetName, "C"+strconv.Itoa(index+1), "M"+strconv.Itoa(index+1))
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+1), "补差未支付时，补差金额不计入金额小计；")

	err = f.MergeCell(sheetName, "C"+strconv.Itoa(index+2), "M"+strconv.Itoa(index+2))
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+2), "售后存在审核中时，售后金额不计入金额小计；")

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "C"+strconv.Itoa(index+2), style)

}

func orderTime(f *excelize.File, sheetName string, index int, min, max, exportBegin, exportEnd int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "Q"+row)
	if err != nil {

	}

	exportBeginFormat := time.UnixMilli(exportBegin).Format("2006/01/02 15:04:05")
	exportEndFormat := time.UnixMilli(exportEnd).Format("2006/01/02 15:04:05")

	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("订单区间：[ %s , %s ], 导出区间：[ %s , %s ]", minT, MaxT, exportBeginFormat, exportEndFormat))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

// 导出区间
func exportTime(f *excelize.File, sheetName string, index int, min, max int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "H"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("导出区间：[ %s , %s ]", minT, MaxT))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func title(f *excelize.File, sheetName string, index int) {
	_ = index
	err := f.MergeCell(sheetName, "A1", "O1")
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	err = f.SetRowHeight(sheetName, 1, 30)

	err = f.SetCellValue(sheetName, "A1", "账单")
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	titleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   24,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})
	if err != nil {

	}
	err = f.SetCellStyle(sheetName, "A1", "A1", titleStyle)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

}

func category(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	row2 := strconv.Itoa(index + 1)
	var err error

	// 索引
	err = f.MergeCell(sheetName, "A"+row, "A"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "A"+row, "")
	err = f.SetColWidth(sheetName, "A", "A", 4)

	colNum := 2
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 14)
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "下单时间")

	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "会员")
	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 20)

	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "地址")
	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 20)

	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "供应商")
	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 20)

	// 商品
	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 32)
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "商品")

	// 计价方式
	colNum += 1
	err = f.MergeCell(sheetName, convertToCol(colNum)+row, convertToCol(colNum)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "计价\n方式")

	// 订单信息
	colNum += 1
	colBegin := colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin+3)+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "订单信息")
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row2, "单价\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+1)+row2, "订单数量\n（件）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+2)+row2, "商品金额\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+3)+row2, "订单重量\n（kg）")
	//err = f.SetCellValue(sheetName, convertToCol(colBegin+4)+row2, "仓配费\n（元）")

	err = f.SetColWidth(sheetName, convertToCol(colBegin+2), convertToCol(colBegin+2), 14)
	err = f.SetColWidth(sheetName, convertToCol(colBegin+3), convertToCol(colBegin+3), 14)
	//err = f.SetCellValue(sheetName, "I"+row2, "配送费\n（元）")

	// 发货信息
	colNum += 4
	colBegin = colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin)+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "发货信息")
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row2, "发货数量\n（件）")
	//err = f.SetCellValue(sheetName, convertToCol(colBegin+1)+row2, "发货重量\n（kg）")
	//err = f.SetCellValue(sheetName, convertToCol(colBegin+2)+row2, "重量误差\n（kg）")
	err = f.SetColWidth(sheetName, convertToCol(colBegin), convertToCol(colBegin), 18)

	// 退款补差
	colNum += 1
	colBegin = colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin+1)+row)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "退款")
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row2, "缺货退款\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colBegin+1)+row2, "售后金额\n（元）")
	//err = f.SetCellValue(sheetName, convertToCol(colBegin+2)+row2, "补差金额\n（元）")

	// 金额小计
	colNum += 2
	colBegin = colNum
	err = f.MergeCell(sheetName, convertToCol(colBegin)+row, convertToCol(colBegin)+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetColWidth(sheetName, convertToCol(colBegin), convertToCol(colBegin), 14)
	err = f.SetCellValue(sheetName, convertToCol(colBegin)+row, "金额小计\n（元）")

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   14,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+row, convertToCol(colBegin)+row2, style)

	style2, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "F"+row2, "O"+row2, style2)

	err = f.SetRowHeight(sheetName, index, 20)
	err = f.SetRowHeight(sheetName, index+1, 26)
}

func dealMoney(amount int) float64 {
	f, exact := decimal.NewFromInt(int64(amount)).Div(decimal.NewFromInt(100)).Round(2).Float64()

	_ = exact

	return f
}

func dealWeight(w int) float64 {
	f, exact := decimal.NewFromInt(int64(w)).Div(decimal.NewFromInt(1000)).Round(1).Float64()

	_ = exact

	return f
}

func convertToCol(columnNumber int) string {
	var res []byte
	for columnNumber > 0 {
		a := columnNumber % 26
		if a == 0 {
			a = 26
		}
		res = append(res, 'A'+byte(a-1))
		columnNumber = (columnNumber - a) / 26
	}
	// 上面输出的res是反着的，前后交换
	for i, n := 0, len(res); i < n/2; i++ {
		res[i], res[n-1-i] = res[n-1-i], res[i]
	}
	return string(res)
}

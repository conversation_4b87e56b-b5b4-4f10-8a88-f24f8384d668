package adminOrder

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func List(ctx *gin.Context) {
	var req = struct {
		SearchContent string `json:"search_content"`
		PayStatus     int    `json:"pay_status"`
		OrderStatus   int    `json:"order_status"`
		OrderTime     int    `json:"order_time"`
		TimeBegin     int64  `json:"time_begin"`
		TimeEnd       int64  `json:"time_end"`
		Page          int64  `json:"page" validate:"min=1"`
		Limit         int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	filter := bson.M{}
	//filter["pay_status"] = bson.M{
	//	"$or": bson.A{model.PayStatusTypePaid, model.PayStatusTypePaidButRefund},
	//}
	//pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//filter["service_point_id"] = pointID
	filter["order_type"] = model.OrderTypeWholeSale

	if req.OrderStatus != 0 {
		filter["order_status"] = req.OrderStatus
	}
	if req.PayStatus != 0 {
		filter["pay_status"] = req.PayStatus
	}
	if req.TimeBegin != 0 && req.OrderTime == 0 {
		filter["created_at"] = bson.M{
			"$gte": req.TimeBegin,
			"$lte": req.TimeEnd,
		}
	}

	if req.OrderTime != 0 {
		filter["order_status_record.ship_time"] = bson.M{
			"$gte": req.TimeBegin,
			"$lte": req.TimeEnd,
		}
	}

	if req.SearchContent != "" {
		filter["$or"] = bson.A{
			bson.M{
				"address.contact.name": bson.M{
					"$regex": req.SearchContent,
				},
			},
			bson.M{
				"address.contact.mobile": bson.M{
					"$regex": req.SearchContent,
				},
			},
			bson.M{
				"buyer_name": bson.M{
					"$regex": req.SearchContent,
				},
			},
		}
	}

	orders, count, err := orderService.NewOrderService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, orders, count)
}

func ListByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID        string `json:"buyer_id"`
		MonthTimestamp int64  `json:"month_timestamp"`
		Page           int64  `json:"page" validate:"min=1"`
		Limit          int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.MonthScopeTimestamp(req.MonthTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"buyer_id": buyerID,
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	orders, i, err := orderService.NewOrderService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, orders, i)

}

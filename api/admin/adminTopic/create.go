package adminTopic

import (
	"base/core/xhttp"
	"base/service/topicService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	req := types.TopicCreateReq{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	res, err := topicService.NewTopicService().Create(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res.Hex())
}

func Delete(ctx *gin.Context) {
	var req = struct {
		ID string `uri:"id"  validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = topicService.NewTopicService().Del(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

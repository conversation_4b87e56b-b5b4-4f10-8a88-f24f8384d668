package adminTopic

import (
	"base/core/xhttp"
	"base/service/topicService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Update(ctx *gin.Context) {
	req := types.TopicUpdateReq{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = topicService.NewTopicService().Update(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)

}

func UpdateProduct(ctx *gin.Context) {
	var req = struct {
		ID          string   `json:"id" validate:"required"`
		ProductList []string `json:"product_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productList := make([]primitive.ObjectID, len(req.ProductList))
	for i, productId := range req.ProductList {
		productList[i], err = primitive.ObjectIDFromHex(productId)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	err = topicService.NewTopicService().UpdateProduct(ctx, id, productList)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)

}

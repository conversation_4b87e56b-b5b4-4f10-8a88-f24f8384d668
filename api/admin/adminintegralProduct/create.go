package adminIntegralProduct

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/integralProductService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// Create 新建积分商品
func Create(ctx *gin.Context) {
	var req = struct {
		Title         string                      `json:"title"`          // 商品名称
		CostNum       int                         `json:"cost_num"`       // 消耗积分数
		Price         int                         `json:"price"`          // 原价
		DiscountPrice int                         `json:"discount_price"` // 折扣价
		ImageCover    model.FileInfo              `json:"image_cover"`    // 商品封面图片
		ImageDisplay  []model.FileInfo            `json:"image_display"`  // 轮播展示图
		ImageDesc     []model.FileInfo            `json:"image_desc"`     // 详情图
		Stock         int                         `json:"stock"`          // 库存
		TagList       []model.TagPer              `json:"tag_list"`       // 标签
		Desc          string                      `json:"desc"`           // 描述
		Status        model.IntegralProductStatus `json:"status"`         // 上下架  1 上架 2 下架
		Target        model.Target                `json:"target"`         // 积分商品
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	now := time.Now().UnixMilli()

	if req.CostNum <= 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "兑换消耗积分需大于0"))
		return
	}

	err = check(req.Price, req.DiscountPrice)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data := model.IntegralProduct{
		ID:            primitive.NewObjectID(),
		Title:         req.Title,
		Desc:          req.Desc,
		CostNum:       req.CostNum,
		Price:         req.Price,
		DiscountPrice: req.DiscountPrice,
		ImageCover:    req.ImageCover,
		ImageDisplay:  req.ImageDisplay,
		ImageDesc:     req.ImageDesc,
		TagList:       req.TagList,
		Stock:         req.Stock,
		Status:        req.Status,
		Target:        req.Target,
		CreatedAt:     now,
		UpdatedAt:     now,
		DeletedAt:     0,
	}

	err = integralProductService.NewIntegralProductService().Create(ctx, data)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

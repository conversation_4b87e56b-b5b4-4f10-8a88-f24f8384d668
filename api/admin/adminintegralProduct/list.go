package adminIntegralProduct

import (
	"base/core/xhttp"
	"base/service/integralProductService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func List(ctx *gin.Context) {
	var req = struct {
		Status int   `json:"status"`
		Page   int64 `json:"page" validate:"min=1"`
		Limit  int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	filter := bson.M{
		"deleted_at": 0,
	}
	if req.Status != 0 {
		filter["status"] = req.Status
	}
	list, count, err := integralProductService.NewIntegralProductService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}

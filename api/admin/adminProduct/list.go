package adminProduct

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func ListAudit(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		Status         int    `json:"status" validate:"required"`
		Page           int64  `json:"page" validate:"min=1"`
		Limit          int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	statusType, err := model.BackAuditStatusType(req.Status)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"audit_status": statusType,
		"deleted_at":   0,
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter["service_point_id"] = pointID

	list, count, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}

//
//func Get(ctx *gin.Context) {
//	var req = struct {
//		ProductApplyID string `json:"product_apply_id" validate:"len=24"`
//	}{}
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//	objectID, err := util.ConvertToObject(req.ProductApplyID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	data, err := productApplyService.NewProductApplyService().Get(ctx, objectID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	xhttp.RespSuccess(ctx, data)
//}

func ListExternalSale(ctx *gin.Context) {
	var req = struct {
		//Status int   `json:"status" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"is_external_sale": true,
		"deleted_at":       0,
	}
	list, err := productService.NewProductService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)
}

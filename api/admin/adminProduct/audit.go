package adminProduct

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/service/productTagService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Audit 商品审核
func Audit(ctx *gin.Context) {
	var req = struct {
		AuditID       string           `json:"audit_id"`
		SkuPriceList  []types.SkuPrice `json:"sku_price_list"`
		FailReason    string           `json:"fail_reason" validate:"-"`
		Status        int              `json:"status" validate:"required"`
		CoverTagID    string           `json:"cover_tag_id"`     // 封面标签ID
		WordTagIDList []string         `json:"word_tag_id_list"` // 文字标签ID列表
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if _, ok := model.AuditStatusTypeMsg[model.AuditStatusType(req.Status)]; !ok {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "审核状态类型错误"))
		return
	}

	auditID, err := util.ConvertToObjectWithCtx(ctx, req.AuditID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	tagList := make([]model.ProductTag, 0)

	// 处理封面标签
	if req.CoverTagID != "" {
		coverTagID, err := util.ConvertToObjectWithCtx(ctx, req.CoverTagID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		coverTag, err := productTagService.NewProductTagService().Get(ctx, coverTagID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		tagList = append(tagList, coverTag)
	}

	// 处理文字标签列表
	for _, id := range req.WordTagIDList {
		wordTagID, err := util.ConvertToObjectWithCtx(ctx, id)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		wordTag, err := productTagService.NewProductTagService().Get(ctx, wordTagID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		tagList = append(tagList, wordTag)
	}

	err = productService.NewProductService().Audit(ctx, auditID, model.AuditStatusType(req.Status), req.FailReason, req.SkuPriceList, tagList)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

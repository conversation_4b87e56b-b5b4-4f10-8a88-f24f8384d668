package adminProduct

import (
	"base/core/xhttp"
	"base/service/productService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"strconv"
)

func SearchForAdmin(ctx *gin.Context) {
	var req = struct {
		SupplierID     string `json:"supplier_id" validate:"-"`
		ServicePointID string `json:"service_point_id" validate:"-"`
		CategoryID     string `json:"category_id" validate:"-"`
		CategoryLevel  int    `json:"category_level"`
		ProductTitle   string `json:"product_title" validate:"-"`
		SaleType       string `json:"sale_type" validate:"-"` //   all   up   down
		Page           int64  `json:"page"`
		Limit          int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"deleted_at": 0,
	}

	if req.SaleType != "all" {
		if req.SaleType == "up" {
			filter["sale"] = true
		}
		if req.SaleType == "down" {
			filter["sale"] = false
		}
	}

	if len(req.ProductTitle) != 0 {
		filter["title"] = bson.M{
			"$regex": req.ProductTitle,
		}
	}

	if len(req.SupplierID) == 24 {
		id, err := util.ConvertToObject(req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["supplier_id"] = id
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter["service_point_id"] = pointID

	if len(req.CategoryID) == 24 {
		cid, err := util.ConvertToObject(req.CategoryID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		levelStr := strconv.Itoa(req.CategoryLevel - 1)
		key := "category_ids." + levelStr

		filter[key] = cid

	} else {
		stationID, _ := primitive.ObjectIDFromHex("66ea8e010bf5034b411ea72f")

		excludeIDList := []primitive.ObjectID{stationID}

		filter["category_ids.1"] = bson.M{
			"$nin": excludeIDList,
		}
	}

	products, count, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	xhttp.RespSuccessList(ctx, list, count)

}

package adminProduct

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/productService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// UpdateCommission 更新服务费率
func UpdateCommission(ctx *gin.Context) {
	var req = struct {
		ProductID         string `json:"product_id"`
		CommissionPercent int    `json:"commission_percent"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//productId, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	if req.CommissionPercent < 0 || req.CommissionPercent > 5 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "服务费费率范围为0~5"))
		return
	}

	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "该功能已移除"))
	return

	//err = productService.NewProductService().UpdateCommission(ctx, productId, req.CommissionPercent)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//xhttp.RespSuccess(ctx, nil)
}

// UpdateCommissionBatch 批量-更新服务费率
func UpdateCommissionBatch(ctx *gin.Context) {
	var req = struct {
		ProductIDList     []string `json:"product_id_list"`
		CommissionPercent int      `json:"commission_percent"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, s := range req.ProductIDList {
		productId, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, productId)
	}

	if req.CommissionPercent < 0 || req.CommissionPercent > 5 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "服务费费率范围为0~5"))
		return
	}

	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "该功能已移除"))
	return

	//err = productService.NewProductService().UpdateCommissionBatch(ctx, ids, req.CommissionPercent)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//xhttp.RespSuccess(ctx, nil)
}

func UpdateDesc(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id" validate:"required"`
		Desc      string `json:"desc"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productId, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().UpdateDesc(ctx, productId, req.Desc)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateExternalSale(ctx *gin.Context) {
	var req = struct {
		ProductIDList  []string `json:"product_id_list"`
		IsExternalSale bool     `json:"is_external_sale"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var ids []primitive.ObjectID

	for _, s := range req.ProductIDList {
		id, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	err = productService.NewProductService().UpdateExternalSale(ctx, ids, req.IsExternalSale)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

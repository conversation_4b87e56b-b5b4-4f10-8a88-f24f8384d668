package adminServicePoint

import (
	"base/core/xhttp"
	"base/service/servicePointCommissionService"
	"base/util"
	"github.com/gin-gonic/gin"
	"time"
)

// UpdateCommission 更新服务点佣金比例
func UpdateCommission(ctx *gin.Context) {
	var req = struct {
		ServicePointID   string `json:"service_point_id" validate:"len=24"`
		WarehousePercent int    `json:"warehouse_percent" validate:"min=0"`
		PointPercent     int    `json:"point_percent" validate:"min=0"`
		PlatformPercent  int    `json:"platform_percent" validate:"min=0"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	point, err := servicePointCommissionService.NewPartnerCommissionService().GetByServicePoint(id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	point.UpdatedAt = time.Now().UnixMilli()
	point.WarehousePercent = req.WarehousePercent
	point.PointPercent = req.PointPercent
	point.PlatformPercent = req.PlatformPercent

	err = servicePointCommissionService.NewPartnerCommissionService().Upsert(ctx, point)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

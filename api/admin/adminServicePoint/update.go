package adminServicePoint

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/servicePointService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// UpdateOpen 开启关闭
func UpdateOpen(ctx *gin.Context) {
	//var req = struct {
	//	ServicePointID string `json:"service_point_id" validate:"len=24"`
	//	IsOpen         bool   `json:"is_open" validate:"-"`
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//id, err := util.ConvertToObject(req.ServicePointID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//point, err := servicePointService.NewServicePointService().Get(id)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//if req.IsOpen {
	//	//	 检查路线
	//	route, err := routeService.NewTransportFeeService().GetServicePoint(ctx, point.ID)
	//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//	if route.ID == primitive.NilObjectID {
	//		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "开启失败，需先设置集中仓至该服务点的路线"))
	//		return
	//	}
	//}
	//
	//xhttp.RespSuccess(ctx, nil)
}

// UpdateSupplierFee 供应商费率
func UpdateSupplierFee(ctx *gin.Context) {
	var req = struct {
		ServicePointID     string  `json:"service_point_id"`
		SupplierServiceFee float64 `json:"supplier_service_fee"`
		DeliverServiceFee  float64 `json:"deliver_service_fee"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.SupplierServiceFee < 0 || req.DeliverServiceFee < 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "费率不能为负数"))
		return
	}

	if req.SupplierServiceFee > 5 || req.DeliverServiceFee > 5 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "费率不合理，请联系服务人员"))
		return
	}

	err = servicePointService.NewServicePointService().UpdateServiceFee(ctx, id, req.SupplierServiceFee, req.DeliverServiceFee)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package adminPayOcr

import (
	"base/core/xhttp"
	"base/model"
	"base/service/payOcrService"
	"base/util"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/gin-gonic/gin"
)

// AuditOcr 提交支付企业ocr审核，设置企业信息之后
func AuditOcr(ctx *gin.Context) {
	var req = struct {
		ObjectID   string `json:"object_id" validate:"len=24"`
		ObjectType int    `json:"object_type" validate:"required"`
		PicType    int    `json:"pic_type" validate:"oneof=1 8 9"` // 1-营业执照（必传） 8-身份证正面（人像面）（必传） 9-身份证反面（国徽面）（必传）
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ObjectID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	objectType, err := model.BackObjectType(req.ObjectType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = payOcrService.NewPayOcrService().ToPayOcr(ctx, id, objectType, pays.PicType(req.PicType))
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

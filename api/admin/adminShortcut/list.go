package adminShortcut

import (
	"base/core/xhttp"
	"base/service/shortcutService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		Visible        bool   `json:"visible"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := shortcutService.NewShortcutService().ListAllWithPoint(ctx, req.Visible, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)
}

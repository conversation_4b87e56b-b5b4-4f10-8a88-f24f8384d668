package adminSupplier

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/supplierService"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
)

func Audit(ctx *gin.Context) {
	var req = struct {
		ObjectID        string `json:"object_id" validate:"len=24"`
		ServicePointID  string `json:"service_point_id"`
		AuditStatus     int    `json:"audit_status" validate:"required"`
		AuditFailReason string `json:"audit_fail_reason" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if _, ok := model.AuditStatusTypeMsg[model.AuditStatusType(req.AuditStatus)]; !ok {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "审核状态类型错误"))
		return
	}

	id, err := util.ConvertToObject(req.ObjectID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	info, err := supplierService.NewSupplierService().Get(ctx, id)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespNoExist(ctx)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = supplierService.NewSupplierService().Audit(ctx, info, req.ServicePointID, model.AuditStatusType(req.AuditStatus), req.AuditFailReason)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

package adminSwipe

import (
	"base/core/xhttp"
	"base/service/swipeService"
	"base/types"
	"github.com/gin-gonic/gin"
)

func Update(ctx *gin.Context) {
	req := types.SwipeUpdate{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = swipeService.NewSwipeService().Update(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)

}

func UpdateSort(ctx *gin.Context) {
	req := types.SwipeUpdateSort{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = swipeService.NewSwipeService().UpdateSort(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)

}

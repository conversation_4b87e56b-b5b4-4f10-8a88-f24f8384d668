package adminSwipe

import (
	"base/core/xhttp"
	"base/service/swipeService"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) {
	var req = struct {
		Visible bool `json:"visible"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	list, err := swipeService.NewSwipeService().ListALl(ctx, req.Visible)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

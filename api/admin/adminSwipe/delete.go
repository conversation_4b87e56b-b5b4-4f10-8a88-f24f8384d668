package adminSwipe

import (
	"base/core/xhttp"
	"base/service/swipeService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		ID string `uri:"id"  validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = swipeService.NewSwipeService().Del(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

package adminRoute

import (
	"base/core/xhttp"
	"base/model"
	"base/service/routeService"
	"base/types"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

func Create(ctx *gin.Context) {
	var req types.RouteCreateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var data model.Route
	data.ID = primitive.NewObjectID()
	from, err := util.ConvertToObject(req.FromWarehouseID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	now := time.Now().UnixMilli()
	data.FromWarehouseID = from
	data.ActualDistance = req.ActualDistance
	data.FeePerKG = req.FeePerKG
	data.Note = req.Note
	data.DeliverTime = req.DeliverTime
	data.CreatedAt = now
	data.RouteType = req.RouteType

	if req.RouteType == model.RouteTypeWarehouse {
		to, err := util.ConvertToObject(req.ToWarehouseID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		data.ToWarehouseID = to

		r, err := routeService.NewTransportFeeService().GetToWarehouse(ctx, to)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			xhttp.RespErr(ctx, err)
			return
		}
		if r.ID != primitive.NilObjectID {
			data.ID = r.ID
			data.CreatedAt = r.CreatedAt
			data.UpdatedAt = now
		}
	}

	if req.RouteType == model.RouteTypeServicePoint {
		to, err := util.ConvertToObject(req.ToServicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		data.ToServicePointID = to

		r, err := routeService.NewTransportFeeService().GetServicePoint(ctx, to)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			xhttp.RespErr(ctx, err)
			return
		}
		if r.ID != primitive.NilObjectID {
			data.ID = r.ID
			data.CreatedAt = r.CreatedAt
			data.UpdatedAt = now
		}
	}

	err = routeService.NewTransportFeeService().Upsert(ctx, data)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

package adminPromote

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/ossService"
	"base/service/promoteService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"strings"
	"time"
)

func UpdateData(ctx *gin.Context) {
	var req = struct {
		ID          string         `json:"id"`
		Title       string         `json:"title"`
		Desc        string         `json:"desc"`
		Cover       model.FileInfo `json:"cover"` // 封面
		Video       model.FileInfo `json:"video"` // 视频
		ProductList []string       `json:"product_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if strings.TrimSpace(req.Title) == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入标题"))
		return
	}
	if strings.TrimSpace(req.Desc) == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入描述"))
		return
	}
	if req.Cover.Name == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请上传封面"))
		return
	}
	if req.Video.Name == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请上传视频"))
		return
	}

	productList := make([]primitive.ObjectID, 0)
	for _, s := range req.ProductList {
		id, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		productList = append(productList, id)
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	//
	//data, err := promoteService.NewPromoteService().Get(ctx, id)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	if req.Video.Name != "" {
		snap, err := ossService.NewOssService().VideoSnap("promote", req.Video.Name)
		if err != nil {
			return
		}

		req.Video.Poster = snap
	}

	now := time.Now().UnixMilli()

	filter := bson.M{"_id": id}
	update := bson.M{
		"title":        req.Title,
		"desc":         req.Desc,
		"cover":        req.Cover,
		"video":        req.Video,
		"product_list": productList,
		"updated_at":   now,
	}

	err = promoteService.NewPromoteService().Update(ctx, filter, update)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateStatus(ctx *gin.Context) {
	var req = struct {
		ID     string              `json:"id"`
		Status model.PromoteStatus `json:"status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = promoteService.NewPromoteService().UpdateStatus(ctx, id, req.Status)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

package adminBuyer

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func UpdateInvoiceStatus(ctx *gin.Context) {
	var req = struct {
		BuyerID           string `json:"buyer_id"`
		InvoiceAuthStatus int    `json:"invoice_auth_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerService.NewBuyerService().UpdateInvoiceStatus(ctx, buyerID, req.InvoiceAuthStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateAccountStatus(ctx *gin.Context) {
	var req = struct {
		BuyerID       string                  `json:"buyer_id"`
		AccountStatus model.AccountStatusType `json:"account_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerService.NewBuyerService().UpdateAccountStatus(ctx, buyerID, req.AccountStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func OpenCustomerManage(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = buyerID

	//TODO

	// 检查并初始化会员码
	//err = buyerService.NewBuyerService().UpdateAccountStatus(ctx, buyerID, req.AccountStatus)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	xhttp.RespSuccess(ctx, nil)
}

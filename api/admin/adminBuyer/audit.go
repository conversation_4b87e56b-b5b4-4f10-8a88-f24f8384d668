package adminBuyer

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/types"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
)

func Audit(ctx *gin.Context) {
	var req types.BuyerAuditReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if _, ok := model.AuditStatusTypeMsg[model.AuditStatusType(req.AuditStatus)]; !ok {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "审核状态类型错误"))
		return
	}

	buyerID, err := util.ConvertToObject(req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().Get(buyerID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "会员信息不存在"))
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if !buyer.IsAssignServicePoint {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "会员尚未分配服务仓"))
		return
	}

	//if buyer.AuditStatus == model.AuditStatusTypePass {
	//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "已审核通过，请勿重复审核"))
	//	return
	//}

	//servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointId)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//point, err := servicePointService.NewServicePointService().Get(servicePointID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	point := model.ServicePoint{}
	err = buyerService.NewBuyerService().Audit(ctx, buyer, point, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

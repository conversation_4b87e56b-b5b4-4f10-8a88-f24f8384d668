package adminAddress

import (
	"base/core/xhttp"
	"base/service/userAddrService"
	"base/types"
	"github.com/gin-gonic/gin"
)

func Update(ctx *gin.Context) {
	var req types.UpdateAddrAdminReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointId)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//point, err := servicePointService.NewServicePointService().Get(servicePointID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	err = userAddrService.NewUserAddrService().UpdateAudit(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateLocation(ctx *gin.Context) {
	var req types.UpdateAddrLocationReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = userAddrService.NewUserAddrService().UpdateLocation(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

//
//func UpdateLogisticsUnitFee(ctx *gin.Context) {
//	var req = struct {
//		ID         string `json:"id"`
//		UnitAmount int    `json:"unit_amount"`
//	}{}
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//
//	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	err = userAddrService.NewUserAddrService().UpdateLogisticsUnitFee(ctx, id, req.UnitAmount)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	xhttp.RespSuccess(ctx, nil)
//}

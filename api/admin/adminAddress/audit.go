package adminAddress

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/userAddrService"
	"base/types"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func Audit(ctx *gin.Context) {
	var req types.UserAddrAuditReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if _, ok := model.AuditStatusTypeMsg[model.AuditStatusType(req.AuditStatus)]; !ok {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "审核状态类型错误"))
		return
	}

	addressID, err := util.ConvertToObject(req.AddressID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	address, err := userAddrService.NewUserAddrService().Get(ctx, addressID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "地址信息不存在"))
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if !address.IsAssignServicePoint {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "地址尚未分配服务仓"))
		return
	}

	if address.AuditStatus == model.AuditStatusTypePass {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "已审核通过，请勿重复审核"))
		return
	}

	//servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointId)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//point, err := servicePointService.NewServicePointService().Get(servicePointID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	point := model.ServicePoint{}
	err = userAddrService.NewUserAddrService().Audit(ctx, address, point, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func Count(ctx *gin.Context) {
	i, err := userAddrService.NewUserAddrService().Count(ctx, bson.M{
		"audit_status": model.AuditStatusTypeDoing,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, i)
}

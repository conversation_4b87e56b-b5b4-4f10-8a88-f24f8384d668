package adminAddress

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/servicePointService"
	"base/service/stationService"
	"base/service/userAddrService"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
)

// AssignPoint 分配服务仓
func AssignPoint(ctx *gin.Context) {
	var req = struct {
		ID             string `json:"id"`
		ServicePointID string `json:"service_point_id"`
		StationID      string `json:"station_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	address, err := userAddrService.NewUserAddrService().Get(ctx, id)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "地址信息不存在"))
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if address.IsAssignServicePoint == true {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "该会员信息已经分配服务仓"))
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	point, err := servicePointService.NewServicePointService().Get(ctx, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	stationID, err := util.ConvertToObjectWithCtx(ctx, req.StationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	station, err := stationService.NewStationService().Get(ctx, stationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = userAddrService.NewUserAddrService().AssignPoint(ctx, address, point, station)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

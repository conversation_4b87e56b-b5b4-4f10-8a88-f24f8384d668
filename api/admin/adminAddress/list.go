package adminAddress

import (
	"base/core/xhttp"
	"base/global"
	"base/model"
	"base/service/buyerService"
	"base/service/orderService"
	"base/service/userAddrService"
	"base/util"
	"context"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"strings"
)

func latestOrder(ctx context.Context) {

	rdb := global.RDBDefault
	for i := 1; i < 12; i++ {
		filter := bson.M{
			"created_at": bson.M{
				"$gte": 1705233455000,
			},
			"deliver_type": model.DeliverTypeDoor,
		}
		orders, count, err := orderService.NewOrderService().ListByPage(ctx, filter, int64(i), 100)
		if err != nil {
			return
		}
		_ = count
		for _, order := range orders {
			id := order.Address.AddressID
			rdb.SAdd(ctx, "addr", id.Hex())
		}
		zap.S().Infof("总计：%d", i)
	}
}

func backIDs() []primitive.ObjectID {
	rdb := global.RDBDefault

	var ids []primitive.ObjectID
	strings := rdb.SMembers(context.Background(), "addr").Val()
	for _, s := range strings {
		id, _ := util.ConvertToObjectWithCtx(context.Background(), s)
		ids = append(ids, id)
	}
	return ids
}

// List 查询
func List(ctx *gin.Context) {
	var req = struct {
		//ServicePointID string `json:"service_point_id"`
		//AuditStatus   int    `json:"audit_status"`
		QueryType     string `json:"query_type"` // all/name/mobile
		ContactName   string `json:"contact_name"`
		ContactMobile string `json:"contact_mobile"`
		//ServiceFee     int    `json:"service_fee"`
		Page  int64 `json:"page"`
		Limit int64 `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//if _, ok := model.AuditStatusTypeMsg[model.AuditStatusType(req.AuditStatus)]; !ok {
	//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "审核状态类型错误"))
	//}

	filter := bson.M{
		//"_id": bson.M{
		//	"$in": latestIDs,
		//},
		//"audit_status": req.AuditStatus,
		//"location.address": bson.M{
		//	"$regex": "昆明",
		//},
		//"updated_at": bson.M{
		//	"$lte": 1710420059000, // 2024-03-14 20:40:59
		//},
		//"$gte": 1710429659000, // 2024-03-14 23:20:59
	}

	//if len(req.ServicePointID) == 24 {
	//	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	//	if err != nil {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//	filter["service_point_id"] = pointID
	//}

	//if req.QueryType == "all" {
	//	filter["service_fee"] = req.ServiceFee
	//}

	if req.QueryType == "name" {
		trimSpace := strings.TrimSpace(req.ContactName)
		if len(trimSpace) > 0 {
			filter["contact.name"] = bson.M{
				"$regex": trimSpace,
			}
		}
	}

	if req.QueryType == "mobile" {
		trimSpace := strings.TrimSpace(req.ContactMobile)
		if len(trimSpace) > 0 {
			filter["contact.mobile"] = bson.M{
				"$regex": trimSpace,
			}
		}
	}

	addresses, i, err := userAddrService.NewUserAddrService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]addrRes, 0, len(addresses))

	var ids []primitive.ObjectID

	for _, address := range addresses {
		ids = append(ids, address.BuyerID)
	}

	if len(ids) < 1 {
		xhttp.RespSuccessList(ctx, nil, 0)
		return
	}

	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	for _, address := range addresses {
		var bName string
		for _, buyer := range buyers {
			if address.BuyerID == buyer.ID {
				bName = buyer.BuyerName
				break
			}
		}

		list = append(list, addrRes{
			Address:   address,
			BuyerName: bName,
		})
	}

	xhttp.RespSuccessList(ctx, list, i)

}

type addrRes struct {
	model.Address
	BuyerName string `json:"buyer_name"`
}

package adminNormalUser

import (
	"base/core/xhttp"
	"base/service/userService"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile" validate:"phone"`
		Note   string `json:"note" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	user, err := userService.NewUserService().AdminCreateUser(ctx, req.Mobile, req.Note)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, user)
}

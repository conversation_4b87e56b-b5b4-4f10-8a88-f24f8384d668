package adminCategory

import (
	"base/core/xhttp"
	"base/service/categoryService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListFirstAll 第一级所有
func ListFirstAll(ctx *gin.Context) {
	list, err := categoryService.NewCategoryService().ListFirstAll(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

// ListNextAll 下一级
func ListNextAll(ctx *gin.Context) {
	var req = struct {
		ParentID string `json:"parent_id"  validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.ParentID, "ListNextAll ParentID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := categoryService.NewCategoryService().ListNextAll(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

func GetDetail(ctx *gin.Context) {
	var req = struct {
		ThirdCategoryID string `json:"third_category_id"  validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ThirdCategoryID, "third_category_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := categoryService.NewCategoryService().GetDetail(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

// ListSecondSpecial 二级专区
func ListSecondSpecial(ctx *gin.Context) {
	var req = struct {
		ParentID string `json:"parent_id"  validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.ParentID, "ListNextAll ParentID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := categoryService.NewCategoryService().ListSecondSpecial(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

func ListSecondByIDs(ctx *gin.Context) {
	var req = struct {
		CategoryIDList []string `json:"category_id_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, s := range req.CategoryIDList {
		id, err := util.ConvertToObjectWithCtx(ctx, s)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	list, err := categoryService.NewCategoryService().ListSecondByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

package adminCategory

import (
	"base/core/xhttp"
	"base/service/categoryService"
	"base/types"
	"github.com/gin-gonic/gin"
)

// Add 分类添加
func Add(ctx *gin.Context) {
	req := &types.CategoryCreate{}
	err := xhttp.Parse(ctx, req)
	if err != nil {
		return
	}

	res, err := categoryService.NewCategoryService().Add(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res.Hex())
}

// UpdateProduct 特殊分类-更新关联商品
func UpdateProduct(ctx *gin.Context) {
	req := &types.CategoryProduct{}
	err := xhttp.Parse(ctx, req)
	if err != nil {
		return
	}

	err = categoryService.NewCategoryService().AddProducts(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, "添加商品成功")
}

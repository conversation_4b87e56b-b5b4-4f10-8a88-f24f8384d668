package adminCategory

import (
	"base/core/xhttp"
	"base/service/categoryService"
	"base/types"
	"github.com/gin-gonic/gin"
)

// Update 分类信息修改
func Update(ctx *gin.Context) {
	req := &types.CategoryUpdate{}
	err := xhttp.Parse(ctx, req)
	if err != nil {
		return
	}

	err = categoryService.NewCategoryService().Update(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, "修改成功")
}

// UpdateSort 分类排序
func UpdateSort(ctx *gin.Context) {
	req := &types.CategoryUpdateSort{}
	err := xhttp.Parse(ctx, req)
	if err != nil {
		return
	}

	err = categoryService.NewCategoryService().UpdateSort(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, "修改成功")
}

package adminProductCommission

import (
	"base/core/xhttp"
	"base/service/productCommissionService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func GetByProduct(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productCommission, err := productCommissionService.NewProductCommissionService().GetByProduct(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, productCommission)
}

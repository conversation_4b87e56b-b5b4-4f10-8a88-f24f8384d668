package adminProductCommission

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productCommissionService"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"time"
)

func Update(ctx *gin.Context) {
	var req = struct {
		ProductID         string `json:"product_id" validate:"len=24"`
		CommissionPercent int    `json:"commission_percent" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	now := time.Now().UnixMilli()
	// 佣金
	info := model.ProductCommission{
		ID:        primitive.NewObjectID(),
		ProductID: id,
		Percent:   req.CommissionPercent,
		CreatedAt: now,
	}

	byProduct, err := productCommissionService.NewProductCommissionService().GetByProduct(ctx, id)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, err)
		return
	}
	if byProduct.ID != primitive.NilObjectID {
		info.ID = byProduct.ID
		info.CreatedAt = byProduct.CreatedAt
		info.UpdatedAt = now
	}

	err = productCommissionService.NewProductCommissionService().Upsert(ctx, info)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

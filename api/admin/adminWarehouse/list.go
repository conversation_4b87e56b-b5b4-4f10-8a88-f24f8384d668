package adminWarehouse

import (
	"base/core/xhttp"
	"base/service/warehouseService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func List(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page" validate:"min=1"`
		Limit int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"deleted_at": 0,
	}
	list, i, err := warehouseService.NewWarehouseServiceService().List(filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}

package adminWarehouse

import (
	"base/core/xhttp"
	"base/model"
	"base/service/warehouseService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Update(ctx *gin.Context) {
	var req = struct {
		ID       string         `json:"id" validate:"len=24"`
		Name     string         `json:"name" validate:"required"`
		Addr     string         `json:"addr" validate:"required"`
		Note     string         `json:"note" validate:"required"`
		Location model.Location `json:"location"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = warehouseService.NewWarehouseServiceService().Update(ctx, id, req.Name, req.Addr, req.Note, req.Location)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

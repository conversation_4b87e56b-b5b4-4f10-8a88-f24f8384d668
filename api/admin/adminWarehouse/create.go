package adminWarehouse

import (
	"base/core/xhttp"
	"base/service/warehouseService"
	"base/types"
	"github.com/gin-gonic/gin"
)

// Create 添加
func Create(ctx *gin.Context) {
	var req types.CreateWarehouseReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := warehouseService.NewWarehouseServiceService().Create(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, id)
	return
}

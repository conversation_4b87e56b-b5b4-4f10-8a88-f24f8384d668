package adminInvoice

import (
	"base/core/xhttp"
	"base/model"
	"base/service/invoiceService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Audit(ctx *gin.Context) {
	var req = struct {
		ID       string                  `json:"id"`
		Status   model.InvoiceStatusType `json:"status"`
		FailNote string                  `json:"fail_note"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = invoiceService.NewInvoiceService().Audit(ctx, id, req.Status, req.FailNote)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

//3469发票通知	短信通知	尊敬的会员，您提交的发票申请，${status}，请前往小程序查看。

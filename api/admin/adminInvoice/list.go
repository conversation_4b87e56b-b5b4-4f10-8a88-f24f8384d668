package adminInvoice

import (
	"base/core/xhttp"
	"base/model"
	"base/service/aesService"
	"base/service/buyerService"
	"base/service/invoiceService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// List 查询
func List(ctx *gin.Context) {
	var req = struct {
		Status model.InvoiceStatusType `json:"status"`
		Page   int64                   `json:"page" validate:"min=1"`
		Limit  int64                   `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{}

	if req.Status > 0 {
		filter["status"] = req.Status
	}

	list, count, err := invoiceService.NewInvoiceService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resList := make([]invoiceRes, 0, len(list))

	var ids []primitive.ObjectID
	for _, invoice := range list {
		ids = append(ids, invoice.BuyerID)
	}

	if len(ids) < 1 {
		xhttp.RespSuccessList(ctx, resList, count)
		return
	}

	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	})

	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	for _, invoice := range list {
		var buyerName string
		for _, buyer := range buyers {
			if invoice.BuyerID == buyer.ID {
				buyerName = buyer.BuyerName
			}
		}

		if len(invoice.BankAccount) > 0 {
			deBank, _ := aesService.NewAesService().De(invoice.BankAccount)
			invoice.BankAccount = deBank
		}

		if len(invoice.PhoneNumber) > 0 {
			dePhone, _ := aesService.NewAesService().De(invoice.PhoneNumber)
			invoice.PhoneNumber = dePhone
		}

		resList = append(resList, invoiceRes{
			Invoice:   invoice,
			BuyerName: buyerName,
		})
	}

	xhttp.RespSuccessList(ctx, resList, count)
}

type invoiceRes struct {
	model.Invoice
	BuyerName string `json:"buyer_name"`
}

package notify

import (
	"base/core/xhttp"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"sync"
)

var transferLock *sync.Mutex

// YeeTransfer 转账
func YeeTransfer(ctx *gin.Context) {
	transferLock.Lock()
	defer transferLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("转账：%s", call)

	xhttp.NotifyYeeRefundSuccess(ctx)
}

package notify

import (
	"base/core/xhttp"
	"base/global"
	"base/service/withdrawService"
	"encoding/json"
	"github.com/cnbattle/allinpay"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func PayNotifySignAcct(ctx *gin.Context) {
	ctx.Set("rid", "notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.memberService.signAcctProtocol":
		//	提现协议签约
		var res allinpay.SignAcctProtocolResult
		parseRes(notify.BizContent, &res)
		withdrawService.NewWithdrawService().NotifySignAcctProtocol(ctx, res)
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("提现协议-回调未对接：", string(bytes))
	}

}

func PayNotifySignAcctPersonal(ctx *gin.Context) {
	ctx.Set("rid", "notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.memberService.signAcctProtocol":
		//	提现协议签约
		var res allinpay.SignAcctProtocolResult
		parseRes(notify.BizContent, &res)
		withdrawService.NewWithdrawService().NotifySignAcctProtocolPersonal(ctx, res)
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("提现协议-回调未对接：", string(bytes))
	}

}

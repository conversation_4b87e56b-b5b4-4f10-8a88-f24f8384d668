package notify

import (
	"base/core/xhttp"
	"base/global"
	"base/model"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/wechatpay-apiv3/wechatpay-go/core/auth/verifiers"
	"github.com/wechatpay-apiv3/wechatpay-go/core/downloader"
	"github.com/wechatpay-apiv3/wechatpay-go/core/notify"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments"
	"go.uber.org/zap"
)

// NormalOrderPay 普通订单支付
func NormalOrderPay(ctx *gin.Context) {
	err := downloader.MgrInstance().RegisterDownloaderWithPrivateKey(ctx, global.MchPrivateKey, global.MchCertificateSerialNumber, global.MchID, global.MchAPIv3Key)
	if err != nil {
		zap.S().Errorf("注册下载器异常：%s", err.Error())
		return
	}

	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(global.MchID)

	handler, _ := notify.NewRSANotifyHandler(
		global.MchAPIv3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor),
	)

	content := new(payments.Transaction)
	notifyReq, err := handler.ParseNotifyRequest(ctx, ctx.Request, content)
	if err != nil {
		zap.S().Errorf("解析notify异常：%s", err.Error())
		return
	}
	_ = notifyReq

	marshal, _ := json.Marshal(content)
	zap.S().Infof("回调：%s", string(marshal))

	err = orderService.NewOrderService().NotifyPay(ctx, content)
	if err != nil {
		zap.S().Errorf("订单支付回调失败，交易id：%s，异常：%s", *content.TransactionId, err.Error())
		xhttp.NotifyFail(ctx)
	}

	xhttp.NotifySuccess(ctx)
}

// DebtOrderPay 补差支付
func DebtOrderPay(ctx *gin.Context) {
	err := downloader.MgrInstance().RegisterDownloaderWithPrivateKey(ctx, global.MchPrivateKey, global.MchCertificateSerialNumber, global.MchID, global.MchAPIv3Key)
	if err != nil {
		zap.S().Errorf("注册下载器异常：%s", err.Error())
		return
	}

	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(global.MchID)

	handler, _ := notify.NewRSANotifyHandler(
		global.MchAPIv3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor),
	)

	content := new(payments.Transaction)
	notifyReq, err := handler.ParseNotifyRequest(ctx, ctx.Request, content)
	if err != nil {
		zap.S().Errorf("解析notify异常：%s", err.Error())
		return
	}
	_ = notifyReq

	marshal, _ := json.Marshal(content)
	zap.S().Infof("回调：%s", string(marshal))

	err = orderDebtService.NewOrderDebtService().NotifyPay(ctx, content)
	if err != nil {
		zap.S().Errorf("订单支付回调失败，交易id：%s，异常：%s", *content.TransactionId, err.Error())
		xhttp.NotifyFail(ctx)
	}

	xhttp.NotifySuccess(ctx)
}

// NormalOrderCancel 订单取消
func NormalOrderCancel(ctx *gin.Context) {
	err := downloader.MgrInstance().RegisterDownloaderWithPrivateKey(ctx, global.MchPrivateKey, global.MchCertificateSerialNumber, global.MchID, global.MchAPIv3Key)
	if err != nil {
		zap.S().Errorf("注册下载器异常：%s", err.Error())
		return
	}

	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(global.MchID)

	handler, _ := notify.NewRSANotifyHandler(
		global.MchAPIv3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor),
	)

	content := new(model.RefundNotify)
	notifyReq, err := handler.ParseNotifyRequest(ctx, ctx.Request, &content)
	if err != nil {
		fmt.Println(err)
		return
	}
	_ = notifyReq

	marshal, _ := json.Marshal(content)
	zap.S().Infof("回调：%s", string(marshal))

	err = orderRefundService.NewOrderRefundService().NotifyCancel(ctx, content)
	if err != nil {
		zap.S().Errorf("订单取消回调失败，交易id：%s，异常：%s", content.TransactionId, err.Error())
		xhttp.NotifyFail(ctx)
	}

	xhttp.NotifySuccess(ctx)
}

// PayNotifyRefundJSAPI 退款
func PayNotifyRefundJSAPI(ctx *gin.Context) {
	err := downloader.MgrInstance().RegisterDownloaderWithPrivateKey(ctx, global.MchPrivateKey, global.MchCertificateSerialNumber, global.MchID, global.MchAPIv3Key)
	if err != nil {
		zap.S().Errorf("注册下载器异常：%s", err.Error())
		return
	}

	certificateVisitor := downloader.MgrInstance().GetCertificateVisitor(global.MchID)

	handler, _ := notify.NewRSANotifyHandler(
		global.MchAPIv3Key, verifiers.NewSHA256WithRSAVerifier(certificateVisitor),
	)

	content := new(model.RefundNotify)
	notifyReq, err := handler.ParseNotifyRequest(ctx, ctx.Request, &content)
	if err != nil {
		fmt.Println(err)
		return
	}
	_ = notifyReq

	marshal, _ := json.Marshal(content)
	zap.S().Infof("回调：%s", string(marshal))

	err = orderRefundService.NewOrderRefundService().NotifyRefund(ctx, content)
	if err != nil {
		zap.S().Errorf("订单取消回调失败，交易id：%s，异常：%s", content.TransactionId, err.Error())
		xhttp.NotifyFail(ctx)
	}

	xhttp.NotifySuccess(ctx)

	//err := orderRefundService.NewOrderRefundService().NotifyRefundStatus(ctx, res)
}

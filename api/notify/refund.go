package notify

import (
	"base/core/xhttp"
	"base/global"
	"base/service/orderRefundService"
	"encoding/json"
	"github.com/cnbattle/allinpay"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// PayNotifyRefund 退款
func PayNotifyRefund(ctx *gin.Context) {
	ctx.Set("rid", "refund notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)
		//zap.S().Errorf("手动回调%s", res.BizOrderNo)
		//xhttp.NotifySuccess(ctx)
		//return
		err := orderRefundService.NewOrderRefundService().NotifyRefundStatus(ctx, res)
		if err != nil {
			l.<PERSON><PERSON>("退款-回调更新失败%v", err)
			xhttp.NotifyFail(ctx)
			return
		}
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("退款-回调未对接：", string(bytes))
	}
}

// PayNotifyRefundManual 手动退款
func PayNotifyRefundManual(ctx *gin.Context) {
	ctx.Set("rid", "refund notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)

		marshal, _ := json.Marshal(res)
		l.Infof("手动退款整单回调：%v", string(marshal))
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("退款-回调未对接：", string(bytes))
	}
}

// PayNotifyRefundDeliver 退款配送费
func PayNotifyRefundDeliver(ctx *gin.Context) {
	ctx.Set("rid", "refund notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.orderService.pay":
		// 托管代收---订单成功
		var res allinpay.NotifyPay
		parseRes(notify.BizContent, &res)

		marshal, _ := json.Marshal(res)
		l.Infof("退款配送费回调，忽略更新：%v", string(marshal))
		xhttp.NotifySuccess(ctx)
		return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("退款-回调未对接：", string(bytes))
	}
}

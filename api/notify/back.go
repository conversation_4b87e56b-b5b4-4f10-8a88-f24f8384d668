package notify

import (
	"base/core/xhttp"
	"base/global"
	"base/model"
	"base/service/authenticationService"
	"context"
	"encoding/json"
	"github.com/cnbattle/allinpay"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"net/url"
	"strings"
)

func PayNotify(ctx *gin.Context) {
	ctx.Set("rid", "notify:"+ctx.GetString("rid"))
	l := global.PayLogger.Sugar()
	notify := deal(ctx, l)
	switch notify.NotifyType {
	case "allinpay.yunst.memberService.verifyResult":
		//	企业信息审核结果通知
		var res allinpay.VerifyResult
		parseRes(notify.BizContent, &res)
		authenticationService.NewAuthenticationService().NotifySetCompany(ctx, res)
		xhttp.NotifySuccess(ctx)

		return
	case "allinpay.yunst.memberService.ocrComparisonResult":
		//	影印件核对结果异步通知
		var res allinpay.OcrComparisonResult
		parseRes(notify.BizContent, &res)
		authenticationService.NewAuthenticationService().NotifyOcrComparisonResult(ctx, res)
		xhttp.NotifySuccess(ctx)

		return
	case "allinpay.yunst.memberService.updateResult":
		//	企业会员信息修改结果通知

		//case "allinpay.yunst.orderService.depositApply":
		//	//	充值
		//	var res allinpay.DepositApplyOrder
		//	parseRes(notify.BizContent, &res)
		//	depositAccountService.NewDepositAccountService().NotifyApply(ctx, res)
		//	xhttp.RespSuccess(ctx, nil)
		//case "allinpay.yunst.orderService.withdrawApply":
		//	//	提现
		//	xhttp.RespSuccess(ctx, nil)
		//case "allinpay.yunst.orderService.agentCollectApply":
		//	 托管代收申请（标准版）
		//var res allinpay.AgentCollectApply
		//parseRes(notify.BizContent, &res)
		//xhttp.RespSuccess(ctx, nil)
		return
	case "allinpay.yunst.orderService.pay":
		// 订单结果通知
		//var res allinpay.NotifyPay
		//parseRes(notify.BizContent, &res)
		//payOrder, _ := payOrderService.NewPayOrderService().GetByBizOrderNo(ctx, res.BizOrderNo)
		//dealNotifyOrder(ctx, res, payOrder)
		//xhttp.NotifySuccess(ctx)
		//
		//return
	default:
		bytes, _ := json.Marshal(notify)
		zap.S().Error("回调未对接：", string(bytes))
	}
}

func dealNotifyOrder(ctx context.Context, res allinpay.NotifyPay, payOrder model.PayOrder) {
	if payOrder.ID == primitive.NilObjectID {
		zap.S().Error("订单回调处理，存在支付单为空", res, payOrder)
		return
	}
	switch payOrder.PayOrderType {
	//case model.PayOrderTypeDepositApply:
	// 充值申请-订单成功
	//depositAccountService.NewDepositAccountService().NotifyApply(ctx, res)
	//case model.PayOrderTypeWithdraw:
	// 提现申请-订单成功、订单失败
	//withdrawApplyOrderService.NewWithdrawApplyOrderService().NotifyPayStatus(ctx, res)
	//case model.PayOrderTypeAgentPay:
	//	托管代付-订单成功
	case model.PayOrderTypeRefund:
		// 退款申请-退款到银行账户/微信/支付宝成功
		//orderRefundService.NewOrderRefundService().NotifyPayStatus(ctx, res)

	}
	bytes, _ := json.Marshal(res)
	zap.S().Error("订单回调未对接：", string(bytes))
}

// CommonNotify 异步通知公共请求参数
type CommonNotify struct {
	AppID      string `json:"appId"`      // 通商云分配给开发者的应用ID,长度<=32
	NotifyTime string `json:"notifyTime"` // 通知时间 通知的发送时间。格式为yyyy-MM-dd HH:mm:ss
	NotifyType string `json:"notifyType"` // 通知的类型 示例值： allinpay.yunst.memberService.verifyResult
	NotifyId   string `json:"notifyId"`   // 编码格式
	SignType   string `json:"signType"`   // 通知校验ID
	Charset    string `json:"charset"`    // 商户生成签名字符串所使用的签名算法类型，目前支持SHA256WithRSA    SHA256WithRSA
	Sign       string `json:"sign"`       // 请求参数的签名串 <=344
	Version    string `json:"version"`    // 调用的接口版本    1.0
	BizContent string `json:"bizContent"` // 请求参数的集合，最大长度不限，除公共参数外所有请求参数都必须放在这个参数中传递，具体参照各产品快速接入文档
}

func parseRes(s string, data any) {
	err := json.Unmarshal([]byte(s), data)
	if err != nil {
		zap.S().Error(err)
	}
}

func deal(ctx *gin.Context, l *zap.SugaredLogger) CommonNotify {
	ln := ctx.Request.ContentLength
	// 新建一个字节切片，长度与请求报文的内容长度相同
	body := make([]byte, ln)
	// 读取 r 的请求主体，并将具体内容读入 body 中
	n, err := ctx.Request.Body.Read(body)
	if err != nil {
		l.Error(err)
		return CommonNotify{}
	}
	_ = n
	parse, err := url.Parse(string(body))
	if err != nil {
		l.Error(err)
		return CommonNotify{}
	}
	str := parse.Path
	l.Infof("req_id:%s,notify:%s", ctx.GetString("rid"), str)
	// 移除分隔符
	trim := strings.ReplaceAll(str, ";", "@")

	values, err := url.ParseQuery(trim)
	if err != nil {
		zap.S().Error(err)
		return CommonNotify{}
	}
	var data CommonNotify
	if v, ok := values["appId"]; ok {
		data.AppID = v[0]
	}
	if v, ok := values["notifyTime"]; ok {
		data.NotifyTime = v[0]
	}
	if v, ok := values["notifyType"]; ok {
		data.NotifyType = v[0]
	}
	if v, ok := values["notifyId"]; ok {
		data.NotifyId = v[0]
	}
	if v, ok := values["signType"]; ok {
		data.SignType = v[0]
	}
	if v, ok := values["charset"]; ok {
		data.Charset = v[0]
	}
	if v, ok := values["sign"]; ok {
		data.Sign = v[0]
	}
	if v, ok := values["version"]; ok {
		data.Version = v[0]
	}
	if v, ok := values["bizContent"]; ok {
		data.BizContent = v[0]
	}
	return data
}

package notify

import (
	"base/core/xhttp"
	"base/model"
	"base/service/messageService"
	"base/service/orderService"
	"base/util"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"sync"
)

var normalOrderLock sync.Mutex

// YeeTradeOrder 交易下单
func YeeTradeOrder(ctx *gin.Context) {
	normalOrderLock.Lock()
	defer normalOrderLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("交易下单：%s", call)

	var r model.YeeTradeOrderPayNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.<PERSON>rror())
		return
	}

	var payer model.YeePayerInfo

	err = json.Unmarshal([]byte(r.PayerInfo), &payer)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	r.PayerInfoFormat = payer

	// 子单信息
	var subOrders []model.SubOrderInfoList

	err = json.Unmarshal([]byte(r.SubOrderInfoList), &subOrders)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	for i, order := range subOrders {
		yuanInt := util.DealMoneyFloatToFenInt(order.OrderAmount)
		subOrders[i].OrderAmountInt = yuanInt
	}

	r.SubOrderInfoListFormat = subOrders

	err = orderService.NewOrderService().YeeNotifyTradeOrderPay(ctx, r)
	if err != nil {
		zap.S().Errorf("更新回调异常：%s", err.Error())
		PayWarning("订单支付", r.OrderId)
		return
	}
	xhttp.NotifyYeeSuccess(ctx)

}

// YeeAggTutelagePrePay 聚合托管
func YeeAggTutelagePrePay(ctx *gin.Context) {
	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("聚合托管：%s", call)

	//var r model.YeeTradeOrderPayNotify
	//
	//err = json.Unmarshal([]byte(call), &r)
	//if err != nil {
	//	zap.S().Errorf("解析回调失败：%s", err.Error())
	//	return
	//}

}

// YeeAccountBookPay 记账簿
func YeeAccountBookPay(ctx *gin.Context) {
	normalOrderLock.Lock()
	defer normalOrderLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("记账簿：%s", call)

	var r model.YeeTradeOrderPayNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	var payer model.YeePayerInfo

	err = json.Unmarshal([]byte(r.PayerInfo), &payer)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	r.PayerInfoFormat = payer

	err = orderService.NewOrderService().YeeNotifyAccountBookPay(ctx, r)
	if err != nil {
		PayWarning("记账簿支付", r.OrderId)
		zap.S().Errorf("更新回调异常：%s", err.Error())
		return
	}
	xhttp.NotifyYeeSuccess(ctx)
}

func YeeAccountBookDeposit(ctx *gin.Context) {
	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().Errorf("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("记账簿-充值：%s", call)
	PayWarning("记账簿-充值", "银行汇款")

	xhttp.NotifyYeeSuccess(ctx)
}

func PayWarning(eventType, con string) {
	messageService.NewMessageService().SendWarning("***********", eventType, con)
}

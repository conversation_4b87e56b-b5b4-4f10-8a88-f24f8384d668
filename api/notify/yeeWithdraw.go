package notify

import (
	"base/core/xhttp"
	"base/model"
	"base/service/withdrawApplyOrderService"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"sync"
)

var withdrawLock sync.Mutex

// YeeWithdraw 提现
func YeeWithdraw(ctx *gin.Context) {
	withdrawLock.Lock()
	defer withdrawLock.Unlock()

	call, err := ParseYeeCall(ctx)
	if err != nil {
		zap.S().<PERSON><PERSON><PERSON>("%s", err.Error())
		return
	}
	_ = call

	zap.S().Infof("提现：%s", call)

	var r model.YeeWithdrawNotify

	err = json.Unmarshal([]byte(call), &r)
	if err != nil {
		zap.S().Errorf("解析回调失败：%s", err.Error())
		return
	}

	err = withdrawApplyOrderService.NewWithdrawApplyOrderService().YeeNotifyPayStatus(ctx, r)
	if err != nil {
		zap.S().<PERSON><PERSON><PERSON>("更新回调异常：%s", err.Error())
		return
	}

	xhttp.NotifyYeeRefundSuccess(ctx)
}

package orderSupplier

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func ListBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
		Page       int64  `json:"page" validate:"min=1"`
		Limit      int64  `json:"limit" validate:"min=1"`
		TimeBegin  int64  `json:"time_begin"`
		TimeEnd    int64  `json:"time_end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	env := xhttp.GetEnv(ctx)
	if env != model.ObjectTypeSupplier {
		xhttp.RespEnvError(ctx)
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orders, i, err := orderService.NewOrderService().ListBySupplier(ctx, id, req.Page, req.Limit, req.TimeBegin, req.TimeEnd)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, orders, i)

}

// ListAfterSaleBySupplier 售后列表
func ListAfterSaleBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID  string                `json:"supplier_id"`
		AuditStatus model.AuditStatusType `json:"audit_status"`
		TimeBegin   int64                 `json:"time_begin"`
		TimeEnd     int64                 `json:"time_end"`
		Page        int64                 `json:"page"`
		Limit       int64                 `json:"limit"`
	}{}
	//RefundType  model.RefundType      `json:"refund_type"`

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 售后
	refundType := model.RefundTypeAfterSale

	filter := bson.M{
		"supplier_id":  supplierID,
		"audit_status": req.AuditStatus,
		"refund_type":  refundType,
		"is_withdraw":  false,
	}

	if req.TimeBegin != 0 {
		filter["created_at"] = bson.M{
			"$gte": req.TimeBegin,
			"$lte": req.TimeEnd,
		}
	}

	list, count, err := orderRefundService.NewOrderRefundService().ListByPageCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}

package fruitClass

import (
	"base/core/xhttp"
	"base/service/fruitClassService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// Create 添加等级
func Create(ctx *gin.Context) {
	var req = struct {
		CategoryID string `json:"category_id" validate:"len=24"`
		Name       string `json:"name" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.CategoryID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	err = fruitClassService.NewFruitClassService().Create(ctx, id, req.Name)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package fruitClass

import (
	"base/core/xhttp"
	"base/service/fruitClassService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func List(ctx *gin.Context) {
	var req = struct {
		CategoryID string `json:"category_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.CategoryID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	i, err := fruitClassService.NewFruitClassService().List(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, i)
}

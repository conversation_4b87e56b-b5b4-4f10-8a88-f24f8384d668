package fruitClass

import (
	"base/core/xhttp"
	"base/service/fruitClassService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// Update 添加等级
func Update(ctx *gin.Context) {
	var req = struct {
		ID   string `json:"id" validate:"len=24"`
		Name string `json:"name" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	err = fruitClassService.NewFruitClassService().UpdateName(ctx, id, req.Name)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

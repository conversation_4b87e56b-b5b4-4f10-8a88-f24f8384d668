package invoiceTitle

import (
	"base/core/xhttp"
	"base/service/aesService"
	"base/service/invoiceTitleService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func List(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	// 查询发票信息
	filter := bson.M{
		"deleted_at": 0,
	}

	if len(req.BuyerID) == 24 {
		id, err := util.ConvertToObjectWithNote(req.BuyerID, "")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["buyer_id"] = id
	}

	list, count, err := invoiceTitleService.NewInvoiceTitleService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	for i, invoice := range list {
		if invoice.BankAccount != "" {
			de, err := aesService.NewAesService().De(invoice.BankAccount)
			if err != nil {
				xhttp.RespErr(ctx, err)
				return
			}
			list[i].BankAccount = de
		}
		if invoice.PhoneNumber != "" {
			de, err := aesService.NewAesService().De(invoice.PhoneNumber)
			if err != nil {
				xhttp.RespErr(ctx, err)
				return
			}
			list[i].PhoneNumber = de
		}
	}

	xhttp.RespSuccessList(ctx, list, count)
}

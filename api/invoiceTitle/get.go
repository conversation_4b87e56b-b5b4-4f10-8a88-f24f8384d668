package invoiceTitle

import (
	"base/core/xhttp"
	"base/service/aesService"
	"base/service/invoiceTitleService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Get(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	// 查询发票信息
	invoice, err := invoiceTitleService.NewInvoiceTitleService().GetByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if invoice.BankAccount != "" {
		de, err := aesService.NewAesService().De(invoice.BankAccount)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		invoice.BankAccount = de
	}
	if invoice.PhoneNumber != "" {
		de, err := aesService.NewAesService().De(invoice.PhoneNumber)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		invoice.PhoneNumber = de
	}

	xhttp.RespSuccess(ctx, invoice)
}

package invoiceTitle

import (
	"base/core/xerr"
	"base/model"
	"base/types"
	"github.com/gin-gonic/gin"
	"strings"
)

func Upsert(ctx *gin.Context) {
	//var req types.InvoiceTitleUpsert
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//err = check(req)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//userID, err := xhttp.UserID(ctx)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, userID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//err = invoiceTitleService.NewInvoiceTitleService().Upsert(ctx, buyer, req)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//xhttp.RespSuccess(ctx, nil)
}

func check(req types.InvoiceTitleUpsert) error {
	if req.InvoiceTitleType != model.InvoiceTitleTypeCompany && req.InvoiceTitleType != model.InvoiceTitleTypePersonal {
		return xerr.NewErr(xerr.ErrParamError, nil, "发票类型错误")
	}
	if strings.TrimSpace(req.InvoiceTitle) == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "发票抬头不能为空")
	}
	if strings.TrimSpace(req.TaxNumber) == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "纳税人识别号不能为空")
	}
	if strings.TrimSpace(req.PhoneNumber) == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "联系号码不能为空")
	}
	if strings.TrimSpace(req.BankName) == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "银行名称不能为空")
	}
	if strings.TrimSpace(req.BankAccount) == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "银行账户不能为空")
	}

	return nil
}

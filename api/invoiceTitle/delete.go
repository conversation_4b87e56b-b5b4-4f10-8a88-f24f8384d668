package invoiceTitle

import (
	"base/core/xhttp"
	"base/service/invoiceTitleService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	err = invoiceTitleService.NewInvoiceTitleService().Delete(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

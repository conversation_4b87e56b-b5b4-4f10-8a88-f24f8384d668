package division

import (
	"github.com/gin-gonic/gin"
)

// ListProvince 省份列表
func ListProvince(c *gin.Context) {
	//divisions, err := divisionService.NewDivisionService().ListProvince()
	//if err != nil {
	//	xhttp.RespErr(c, err)
	//	return
	//}
	//xhttp.RespSuccessList(c, divisions, int64(len(divisions)))
}

// ListNextByCode 根据code查询下一级列表
func ListNextByCode(c *gin.Context) {
	//var req = struct {
	//	Code int `uri:"code"`
	//}{}
	//err := xhttp.Parse(c, &req)
	//if err != nil {
	//	return
	//}
	//divisions, err := divisionService.NewDivisionService().ListNextByCode(req.Code)
	//if err != nil {
	//	xhttp.RespErr(c, err)
	//	return
	//}
	//xhttp.RespSuccessList(c, divisions, int64(len(divisions)))
}

// ListDetail 根据code查询详细
func ListDetail(c *gin.Context) {
	//var req = struct {
	//	Code int `uri:"code"`
	//}{}
	//err := xhttp.Parse(c, &req)
	//if err != nil {
	//	return
	//}
	//divisions, err := divisionService.NewDivisionService().GetDetail(req.Code)
	//if err != nil {
	//	xhttp.RespErr(c, err)
	//	return
	//}
	//xhttp.RespSuccessList(c, divisions, int64(len(divisions)))
}

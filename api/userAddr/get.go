package userAddr

import (
	"base/core/xhttp"
	"base/service/userAddrService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Get(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	res, err := userAddrService.NewUserAddrService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, res)
}

func GetDefault(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	res, err := userAddrService.NewUserAddrService().GetDefaultByUserID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, res)
}

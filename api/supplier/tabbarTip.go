package supplier

import (
	"base/core/xhttp"
	"base/model"
	"base/service/commentService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// GetTabBarTip 供应商tabBar tip
func GetTabBarTip(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	supplierID, err := util.ConvertToObject(req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"supplier_id":  supplierID,
		"audit_status": model.AuditStatusTypeDoing,
		"deleted_at":   0,
	}

	count, err := commentService.NewCommentService().Count(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var mine bool
	if count > 0 {
		mine = true
	}
	r := tip{
		Mine: mine,
	}

	xhttp.RespSuccess(ctx, r)
}

type tip struct {
	Mine bool `json:"mine"`
}

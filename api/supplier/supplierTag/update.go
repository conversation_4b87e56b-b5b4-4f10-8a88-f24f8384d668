package supplierTag

import (
	"base/core/xhttp"
	"base/service/supplierTagService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func UpdateTitle(ctx *gin.Context) {
	var req = struct {
		ID    string `json:"id" validate:"len=24"`
		Title string `json:"title" validate:"required"`
		Color string `json:"color" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = supplierTagService.NewSupplierTagService().UpdateTitle(ctx, id, req.Title, req.Color)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

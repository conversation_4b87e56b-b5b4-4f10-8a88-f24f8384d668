package supplier

import (
	"base/core/xhttp"
	"base/model"
	"base/service/commentService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// GetStats 个人中心统计
func GetStats(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"supplier_id":  supplierID,
		"audit_status": model.AuditStatusTypeDoing,
		"deleted_at":   0,
	}

	count, err := commentService.NewCommentService().Count(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = count

	r := Stats{
		AfterSaleRate: 0,
	}

	xhttp.RespSuccess(ctx, r)
}

type Stats struct {
	AfterSaleRate float64 `json:"after_sale_rate"`
}

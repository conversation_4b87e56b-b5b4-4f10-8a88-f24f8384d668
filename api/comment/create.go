package comment

import (
	"base/core/xhttp"
	"base/service/commentService"
	"base/types"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	//var req types.CommentCreate
	//if err := xhttp.Parse(ctx, &req); err != nil {
	//	return
	//}
	//
	//userId, err := xhttp.UserID(ctx)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, userId)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//err = commentService.NewCommentService().Create(ctx, req, buyer)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//xhttp.RespSuccess(ctx, nil)
}

func CreateCus(ctx *gin.Context) {
	var req types.CommentCreateCus
	if err := xhttp.Parse(ctx, &req); err != nil {
		return
	}

	err := commentService.NewCommentService().CreateCus(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

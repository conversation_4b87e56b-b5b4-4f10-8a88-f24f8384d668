package comment

import (
	"base/core/xhttp"
	"base/service/commentService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// Reply 供应商回复
func Reply(ctx *gin.Context) {
	var req = struct {
		ID      string `json:"id"`
		Content string `json:"content"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = commentService.NewCommentService().Reply(ctx, id, req.Content)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package comment

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/commentService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"time"
)

// ListCenter 评价中心
func ListCenter(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id" validate:"len=24"`
		Status  int    `json:"status"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if req.Status != 1 && req.Status != 2 {
		err = xerr.NewErr(xerr.ErrParamError, nil, "评价列表查询类型参数错误")
		xhttp.RespErr(ctx, err)
		return
	}

	buyerID, err := util.ConvertToObject(req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"buyer_id":         buyerID,
		"order_status":     model.OrderStatusTypeFinish,
		"order_refund_all": false,
		"deleted_at":       0,
		//"created_at": bson.M{
		//	"$gte": time.Now().AddDate(0, 0, -4).UnixMilli(),
		//},
	}

	if req.Status == 1 {
		filter["has_comment"] = false
		filter["created_at"] = bson.M{
			"$gte": time.Now().AddDate(0, 0, -7).UnixMilli(),
		}
	} else {
		filter["has_comment"] = true
	}

	list, i, err := orderService.NewOrderService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}

func ListByProduct(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id" validate:"len=24"`
		Page      int64  `json:"page" validate:"min=1"`
		Limit     int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, i, err := commentService.NewCommentService().ListByProduct(ctx, id, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}

func ListBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"len=24"`
		Status     int    `json:"status"`
		Page       int64  `json:"page" validate:"min=1"`
		Limit      int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	statusType, err := model.BackAuditStatusType(req.Status)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, i, err := commentService.NewCommentService().ListBySupplier(ctx, id, statusType, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}

func ListByWeb(ctx *gin.Context) {
	var req = struct {
		Status int   `json:"status"`
		Page   int64 `json:"page" validate:"min=1"`
		Limit  int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	statusType, err := model.BackAuditStatusType(req.Status)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, i, err := commentService.NewCommentService().ListByWeb(ctx, statusType, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}

func ListByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id" validate:"len=24"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, i, err := commentService.NewCommentService().ListByBuyer(ctx, id, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, i)
}

package comment

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/commentService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Audit(ctx *gin.Context) {
	var req = struct {
		CommentID   string `json:"comment_id"`
		AuditStatus int    `json:"audit_status" validate:"required"`
		AuditNote   string `json:"audit_note"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	if len(req.CommentID) != 24 {
		err = xerr.NewErr(xerr.ErrParamError, nil, "参数错误")
		xhttp.RespErr(ctx, err)
		return
	}

	statusType, err := model.BackAuditStatusType(req.AuditStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	commentID, err := util.ConvertToObject(req.CommentID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = commentService.NewCommentService().Audit(ctx, commentID, statusType, req.AuditNote)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

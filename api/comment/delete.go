package comment

import (
	"base/core/xhttp"
	"base/service/commentService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"  validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = commentService.NewCommentService().Delete(ctx, []primitive.ObjectID{id})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func DeleteSelf(ctx *gin.Context) {
	//var req = struct {
	//	ID string `json:"id"  validate:"required"`
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//userId, err := xhttp.UserID(ctx)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, userId)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//id, err := util.ConvertToObject(req.ID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//err = commentService.NewCommentService().DeleteByBuyer(ctx, id, buyer)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//xhttp.RespSuccess(ctx, nil)
}

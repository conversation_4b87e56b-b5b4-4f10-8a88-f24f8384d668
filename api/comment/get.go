package comment

import (
	"base/core/xhttp"
	"base/service/commentService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Get(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"  validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := commentService.NewCommentService().GetByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, data)
}

func GetByOrderProduct(ctx *gin.Context) {
	var req = struct {
		OrderID   string `json:"order_id"  validate:"required"`
		ProductID string `json:"product_id"  validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	orderID, err := util.ConvertToObject(req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	productID, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := commentService.NewCommentService().GetByOrderProduct(ctx, orderID, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, data)
}

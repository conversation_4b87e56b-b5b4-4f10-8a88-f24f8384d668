package captcha

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/authenticationService"
	"base/service/messageService"
	"base/util"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/gin-gonic/gin"
)

// Send 发送验证码
func Send(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if !util.CheckMobile(req.Mobile) {
		err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := messageService.NewMessageService().Send(req.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

// SendPayBindMobile  发送通联支付-绑定手机-验证码
func SendPayBindMobile(ctx *gin.Context) {
	//var req = struct {
	//	Mobile string `json:"mobile"`
	//}{}
	//userID, err := xhttp.ParseUser(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//env, err := xhttp.GetEnv(ctx)
	//if err != nil {
	//	return
	//}
	//
	//if !util.CheckMobile(req.Mobile) {
	//	err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//authentication, err := authenticationService.NewAuthenticationService().GetByUserAndEnv(ctx, userID, env)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//if authentication.Mobile != req.Mobile {
	//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "支付手机号与认证手机号不一致"))
	//	return
	//}
	//
	//res, err := messageService.NewMessageService().SendAllInPay(authentication.PayBizUserId, pays.VerificationCodeTypeBind, authentication.Mobile)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//xhttp.RespSuccess(ctx, res)
}

// SendPayBindMobileByAuth  发送通联支付-绑定手机-验证码
func SendPayBindMobileByAuth(ctx *gin.Context) {
	var req = struct {
		AuthID string `json:"auth_id"`
		Mobile string `json:"mobile"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if !util.CheckMobile(req.Mobile) {
		err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
		xhttp.RespErr(ctx, err)
		return
	}
	authID, err := util.ConvertToObjectWithCtx(ctx, req.AuthID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	authentication, err := authenticationService.NewAuthenticationService().GetByID(ctx, authID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//if authentication.Mobile != req.Mobile {
	//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "支付手机号与认证手机号不一致"))
	//	return
	//}

	res, err := messageService.NewMessageService().SendAllInPay(authentication.PayBizUserId, pays.VerificationCodeTypeBind, req.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

// SendPayUnbindMobile  发送通联支付-解绑手机-验证码
func SendPayUnbindMobile(ctx *gin.Context) {
	var req = struct {
		AuthID string `json:"auth_id"`
		Mobile string `json:"mobile"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if !util.CheckMobile(req.Mobile) {
		err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	authID, err := util.ConvertToObjectWithCtx(ctx, req.AuthID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	authentication, err := authenticationService.NewAuthenticationService().GetByID(ctx, authID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if authentication.Mobile == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "手机号不存在，无需解绑"))
		return
	}

	res, err := messageService.NewMessageService().SendAllInPay(authentication.PayBizUserId, pays.VerificationCodeTypeUnbind, authentication.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

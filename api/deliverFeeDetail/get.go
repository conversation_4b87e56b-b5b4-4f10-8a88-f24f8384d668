package deliverFeeDetail

import (
	"base/core/xhttp"
	"base/service/deliverFeeDetailService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Get 获取单个配送费明细记录
func Get(ctx *gin.Context) {
	var req struct {
		ID string `json:"id"`
	}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	detail, err := deliverFeeDetailService.NewDeliverFeeDetailService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, detail)
}

// Stats 统计配送费明细
func Stats(ctx *gin.Context) {
	var req struct {
		MonthTimestamp int64 `json:"month_timestamp"`
	}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	monthStart, monthEnd, err := util.MonthScopeTimestamp(req.MonthTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	stats, err := deliverFeeDetailService.NewDeliverFeeDetailService().List(ctx, monthStart, monthEnd)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}


	var r types.DeliverFeeDetailStatsResp

	for _, detail := range stats {
		r.TotalDeliverFee += detail.TotalDeliverFee
		r.SubsidyDeliverFee += detail.SubsidyDeliverFee
		r.FinalDeliverFee += detail.FinalDeliverFee
	}

	xhttp.RespSuccess(ctx, r)
}




package deliverFeeDetail

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/deliverFeeDetailService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListMonthly 按月查询配送费明细
func ListMonthly(ctx *gin.Context) {
	var req struct {
		MonthTimestamp int64 `json:"month_timestamp"`
	}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	monthStart, monthEnd, err := util.MonthScopeTimestamp(req.MonthTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	stats, err := deliverFeeDetailService.NewDeliverFeeDetailService().List(ctx, monthStart, monthEnd)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var buyerIDs []primitive.ObjectID
	existIDMap := make(map[primitive.ObjectID]bool)

	for _, detail := range stats {
		if _, ok := existIDMap[detail.BuyerID]; !ok {
			existIDMap[detail.BuyerID] = true
			buyerIDs = append(buyerIDs, detail.BuyerID)
		}
	}

	buyers, err := buyerService.NewBuyerService().ListByIDList(ctx, buyerIDs)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyerMap := make(map[primitive.ObjectID]model.Buyer)
	for _, buyer := range buyers {
		buyerMap[buyer.ID] = buyer
	}

	var r []types.DeliverFeeDetailResp

	for _, detail := range stats {
		buyer, ok := buyerMap[detail.BuyerID]
		if !ok {
		}

		r = append(r, types.DeliverFeeDetailResp{
			DeliverFeeDetail: detail,
			BuyerName:        buyer.BuyerName,
		})
	}

	xhttp.RespSuccess(ctx, r)
}

// ListDaily 按日查询配送费明细
func ListDaily(ctx *gin.Context) {
	var req struct {
		DayTimestamp int64 `json:"day_timestamp"`
	}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	dayStart, dayEnd, err := util.DayScopeTimestamp(req.DayTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	stats, err := deliverFeeDetailService.NewDeliverFeeDetailService().List(ctx, dayStart, dayEnd)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var buyerIDs []primitive.ObjectID
	existIDMap := make(map[primitive.ObjectID]bool)

	for _, detail := range stats {
		if _, ok := existIDMap[detail.BuyerID]; !ok {
			existIDMap[detail.BuyerID] = true
			buyerIDs = append(buyerIDs, detail.BuyerID)
		}
	}

	buyers, err := buyerService.NewBuyerService().ListByIDList(ctx, buyerIDs)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyerMap := make(map[primitive.ObjectID]model.Buyer)
	for _, buyer := range buyers {
		buyerMap[buyer.ID] = buyer
	}

	r := make([]types.DeliverFeeDetailResp, 0)

	for _, detail := range stats {
		buyer, ok := buyerMap[detail.BuyerID]
		if !ok {
		}

		r = append(r, types.DeliverFeeDetailResp{
			DeliverFeeDetail: detail,
			BuyerName:        buyer.BuyerName,
		})
	}

	res := dealResp(r)

	xhttp.RespSuccess(ctx, res)
}

func dealResp(stats []types.DeliverFeeDetailResp) []newRes {
	var r []newRes
	// 保证顺序不变，使用一个切片记录顺序
	groupMap := make(map[primitive.ObjectID]*newRes)
	var order []primitive.ObjectID
	for _, v := range stats {
		if group, ok := groupMap[v.BuyerID]; ok {
			group.List = append(group.List, v)
		} else {
			groupMap[v.BuyerID] = &newRes{
				BuyerID:   v.BuyerID,
				BuyerName: v.BuyerName,
				List:      []types.DeliverFeeDetailResp{v},
			}
			order = append(order, v.BuyerID)
		}
	}
	for _, buyerID := range order {
		r = append(r, *groupMap[buyerID])
	}

	return r
}

type newRes struct {
	BuyerID   primitive.ObjectID           `json:"buyer_id"`
	BuyerName string                       `json:"buyer_name"`
	List      []types.DeliverFeeDetailResp `json:"list"`
}

package order

import (
	"base/core/xhttp"
	"base/global"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// Cancel 取消订单--已支付
func Cancel(ctx *gin.Context) {
	global.OrderLock.Lock()
	defer global.OrderLock.Unlock()
	var req = struct {
		OrderID string `json:"order_id"` // 订单ID
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	order, err := orderService.NewOrderService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//err = orderRefundService.NewOrderRefundService().YeeRefundDeliver(ctx, order.ParentOrderID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//return

	//if order.PayMethod == model.PayMethodTypeYeeWechat {
	err = orderRefundService.NewOrderRefundService().YeeCancelOrder(ctx, order)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	//}

	//if order.PayMethod == model.PayMethodTypeYeeBalance {
	//	err = orderRefundService.NewOrderRefundService().YeeCancelOrderByAccountBook(ctx, order)
	//	if err != nil {
	//		xhttp.RespErr(ctx, err)
	//		return
	//	}
	//}

	xhttp.RespSuccess(ctx, nil)
}

package order

import (
	"base/core/xhttp"
	"base/global"
	"base/mnsSendService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// Close 关闭订单-未支付--全部关闭
func Close(ctx *gin.Context) {
	global.OrderLock.Lock()
	defer global.OrderLock.Unlock()
	var req = struct {
		ParentOrderID string `json:"parent_order_id" validate:"len=24"` // 支付单订单
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	parentOrderID, err := util.ConvertToObjectWithCtx(ctx, req.ParentOrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderService.NewOrderService().Close(ctx, parentOrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orders, err := orderService.NewOrderService().ListByParentOrderID(ctx, parentOrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	for _, order := range orders {
		for _, productOrder := range order.ProductList {
			mnsSendService.NewMNSClient().SendRemoveProductStats(productOrder.ProductID)
		}
		mnsSendService.NewMNSClient().SendRemoveBuyerStats(order.BuyerID)
	}

	xhttp.RespSuccess(ctx, nil)
}

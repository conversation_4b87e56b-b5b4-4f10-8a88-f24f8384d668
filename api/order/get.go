package order

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderService"
	"base/service/productImageService"
	"base/util"
	"errors"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
)

// Get 订单信息
func Get(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ID, "order_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	order, err := orderService.NewOrderService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var r OrderRes

	r.Order = order
	r.RecordList = deal(r.OrderStatusRecord)

	xhttp.RespSuccess(ctx, r)
}

func GetByNum(ctx *gin.Context) {
	var req = struct {
		IDNum string `json:"id_num"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	order, err := orderService.NewOrderService().GetByIDNum(ctx, req.IDNum)
	if err != nil && errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespNoExist(ctx)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var r OrderRes

	r.Order = order
	r.RecordList = deal(r.OrderStatusRecord)

	xhttp.RespSuccess(ctx, r)
}

// GetOrderProductImage 获取订单商品镜像
func GetOrderProductImage(ctx *gin.Context) {
	var req = struct {
		ProductImageID string `json:"product_image_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ProductImageID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productImage, err := productImageService.NewProductImageService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, productImage)
}

// GetOrderProductImageByOrderID 根据订单ID和产品ID获取商品镜像
func GetOrderProductImageByOrderID(ctx *gin.Context) {
	var req = struct {
		OrderID   string `json:"order_id"`
		ProductID string `json:"product_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	orderID, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productImage, err := productImageService.NewProductImageService().GetByOrderID(ctx, orderID, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, productImage)
}

// OrderRes 订单响应
type OrderRes struct {
	model.Order
	RecordList []record `json:"record_list"`
}

type record struct {
	Text      string `json:"text"`
	Timestamp int64  `json:"timestamp"`
}

func deal(data model.OrderStatusRecord) []record {
	var list []record
	list = append(list, record{
		Text:      "下单",
		Timestamp: data.CreateOrderTime,
	})
	list = append(list, record{
		Text:      "支付",
		Timestamp: data.PayTime,
	})
	list = append(list, record{
		Text:      "备货",
		Timestamp: data.StockUpTime,
	})
	list = append(list, record{
		Text:      "品控",
		Timestamp: data.QualityTime,
	})
	list = append(list, record{
		Text:      "分拣",
		Timestamp: data.SortTime,
	})
	list = append(list, record{
		Text:      "发货",
		Timestamp: data.ShipTime,
	})
	list = append(list, record{
		Text:      "运输",
		Timestamp: data.ArriveTime,
	})
	list = append(list, record{
		Text:      "收货",
		Timestamp: data.ReceiveTime,
	})
	return list
}

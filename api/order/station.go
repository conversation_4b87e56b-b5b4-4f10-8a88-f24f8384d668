package order

import (
	"base/core/xhttp"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func ListByStation(ctx *gin.Context) {
	var req = struct {
		StationID string `json:"station_id"`
		TimeBegin int64  `json:"time_begin"`
		TimeEnd   int64  `json:"time_end"`
		Page      int64  `json:"page"`
		Limit     int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.StationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orders, i, err := orderService.NewOrderService().ListByStation(ctx, id, req.Page, req.Limit, req.TimeBegin, req.TimeEnd)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, orders, i)
}

package order

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"sync"
)

var payLock sync.Mutex

// ToPay 去支付
func ToPay(ctx *gin.Context) {
	payLock.Lock()
	defer payLock.Unlock()
	var req = struct {
		ParentOrderID string              `json:"parent_order_id"`
		OpenID        string              `json:"open_id"`
		PayMethod     model.PayMethodType `json:"pay_method"` // 支付方式
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ParentOrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.PayMethod != model.PayMethodTypeYeeWechat && req.PayMethod != model.PayMethodTypeYeeBalance {
		err = xerr.NewErr(xerr.ErrParamError, nil, "支付方式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	if req.OpenID == "" {
		err = xerr.NewErr(xerr.ErrLoginExpire, nil, "登录信息过期，请重新登录")
		xhttp.RespErr(ctx, err)
		return
	}

	var payInfo interface{}

	if req.PayMethod == model.PayMethodTypeYeeWechat {
		// 微信支付-易宝
		ip := xhttp.IP(ctx)
		payInfo, err = orderService.NewOrderService().YeeAggTutelagePrePay(ctx, id, req.OpenID, ip)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	if req.PayMethod == model.PayMethodTypeYeeBalance {
		// 记账簿-支付
		err = orderService.NewOrderService().YeeAccountBookPay(ctx, id)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	xhttp.RespSuccess(ctx, payInfo)
}

package order

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/mnsSendService"
	"base/model"
	"base/service/buyerService"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/types"
	"base/util"
	"sync"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

var refundLock sync.Mutex

// Refund 售后申请
func Refund(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	var req types.OrderRefundReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if req.Video.Name == "" {
		err = xerr.NewErr(xerr.ErrParamError, nil, "请上传视频")
		xhttp.RespErr(ctx, err)
		return
	}

	for _, info := range req.ImageList {
		if info.Name == "" {
			err = xerr.NewErr(xerr.ErrParamError, nil, "请上传损坏商品图")
			xhttp.RespErr(ctx, err)
			return
		}
	}

	refund, err := orderRefundService.NewOrderRefundService().CreateRefund(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mnsSendService.NewMNSClient().SendRemoveProductStats(refund.ProductID)
	mnsSendService.NewMNSClient().SendRemoveBuyerStats(refund.BuyerID)

	mnsSendService.NewMNSClient().SendRefundChangeAuditor(refund.ID)

	xhttp.RespSuccess(ctx, refund)
}

// ReSubmitRefund 再次售后申请
func ReSubmitRefund(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	var req types.OrderRefundReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	if req.Video.Name == "" {
		err = xerr.NewErr(xerr.ErrParamError, nil, "请上传视频")
		xhttp.RespErr(ctx, err)
		return
	}

	for _, info := range req.ImageList {
		if info.Name == "" {
			err = xerr.NewErr(xerr.ErrParamError, nil, "请上传损坏商品图")
			xhttp.RespErr(ctx, err)
			return
		}
	}

	err = orderRefundService.NewOrderRefundService().ReSubmitRefund(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// GetRefund 退款单
func GetRefund(ctx *gin.Context) {
	var req = struct {
		RefundID string `json:"refund_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.RefundID, "GetRefund refund_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, userID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	refund, err := orderRefundService.NewOrderRefundService().GetByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, refund)
}

// GetQualityRefund 品控退款详情
func GetQualityRefund(ctx *gin.Context) {
	var req = struct {
		OrderID   string `json:"order_id"`
		ProductID string `json:"product_id"`
		SkuIDCode string `json:"sku_id_code"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	orderID, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//refund, err := orderRefundService.NewOrderRefundService().GetByProductIDForQuality(ctx, orderID, productID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	debt, err := orderDebtService.NewOrderDebtService().GetByOrderID(ctx, orderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var settle model.ProductSettle
	for _, quality := range debt.SettleProductList {
		if quality.ProductID == productID && quality.SkuIDCode == req.SkuIDCode {
			settle = quality
		}
	}

	if settle.ProductID == primitive.NilObjectID {
		xhttp.RespNoExist(ctx)
		return
	}

	xhttp.RespSuccess(ctx, settle)
}

// ListAfterSale 售后列表
func ListAfterSale(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page"`
		Limit   int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().Get(buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 售后
	refundType := model.RefundTypeAfterSale

	list, count, err := orderRefundService.NewOrderRefundService().ListByBuyer(ctx, buyer.ID, refundType, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, list, count)
}

func ListAfterSaleByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 售后
	refundType := model.RefundTypeAfterSale

	list, count, err := orderRefundService.NewOrderRefundService().ListByBuyer(ctx, buyerID, refundType, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, list, count)
}

// GetShipRefundByProduct 商品的售后详情
func GetShipRefundByProduct(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id" validate:"len=24"`
		OrderID   string `json:"order_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	pid, err := util.ConvertToObjectWithNote(req.ProductID, "ListRefundByProduct product_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orderID, err := util.ConvertToObjectWithNote(req.OrderID, "ListRefundByProduct order_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderRefundService.NewOrderRefundService().GetShipRefundByProductID(ctx, orderID, pid)
	if err == mongo.ErrNoDocuments {
		xhttp.RespSuccess(ctx, model.OrderRefund{})
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

func ListShipRefund(ctx *gin.Context) {
	var req = struct {
		OrderID string `json:"order_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	orderID, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderRefundService.NewOrderRefundService().ListShipRefundRefund(ctx, orderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

func GetAfterSaleRefundByProduct(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id" validate:"len=24"`
		OrderID   string `json:"order_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	pid, err := util.ConvertToObjectWithNote(req.ProductID, "ListRefundByProduct product_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orderID, err := util.ConvertToObjectWithNote(req.OrderID, "ListRefundByProduct order_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := orderRefundService.NewOrderRefundService().GetAfterSaleRefundByProductID(ctx, orderID, pid)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, data)
}

func ListAfterSaleRefund(ctx *gin.Context) {
	var req = struct {
		OrderID string `json:"order_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	orderID, err := util.ConvertToObjectWithNote(req.OrderID, "ListRefundByProduct order_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := orderRefundService.NewOrderRefundService().ListAfterSaleRefund(ctx, orderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, data)
}

func ListAllRefund(ctx *gin.Context) {
	var req = struct {
		OrderID string `json:"order_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	orderID, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := orderRefundService.NewOrderRefundService().ListByOrder(ctx, orderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, data)
}

// WithdrawRefund 撤销售后
func WithdrawRefund(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	if len(req.ID) != 24 {
		err = xerr.NewErr(xerr.ErrParamError, nil, "参数缺失")
		xhttp.RespErr(ctx, err)
		return
	}

	id, err := util.ConvertToObjectWithNote(req.ID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderRefundService.NewOrderRefundService().WithdrawRefund(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// ConfirmRefund 售后确认
func ConfirmRefund(ctx *gin.Context) {
	refundLock.Lock()
	defer refundLock.Unlock()

	var req = struct {
		RefundID       string            `json:"refund_id"`
		ConfirmType    model.ConfirmType `json:"confirm_type"`    // agree，disagree
		AuditObjection string            `json:"audit_objection"` // 审核异议
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.RefundID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderRefundService.NewOrderRefundService().ConfirmRefund(ctx, id, req.ConfirmType, req.AuditObjection)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

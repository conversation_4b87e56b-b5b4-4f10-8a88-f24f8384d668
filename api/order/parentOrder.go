package order

import (
	"base/core/xhttp"
	"base/service/parentOrderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// GetParentOrderPure 父订单信息
func GetParentOrderPure(ctx *gin.Context) {
	var req = struct {
		ParentOrderID string `json:"parent_order_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ParentOrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := parentOrderService.NewParentOrderService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

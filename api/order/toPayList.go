package order

import (
	"base/core/xhttp"
	"base/service/buyerService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func ListToPayByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().Get(buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//id, _ := primitive.ObjectIDFromHex("654dfe8504359bf9c80f6507")
	//buyer.ID = id

	orders, _, err := orderService.NewOrderService().ListToPayByBuyer(ctx, buyer.ID, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//filter := bson.M{
	//	"buyer_id":      buyer.ID,
	//	"has_agent_pay": false,
	//}

	//data, err := orderDebtService.NewOrderDebtService().List(ctx, filter)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//var list []interface{}
	//for _, i := range data {
	//	if i.PayStatus == model.PayStatusTypeToPay || i.PayStatus == model.PayStatusTypePending {
	//		list = append(list, i)
	//	}
	//}

	//for _, i := range orders {
	//	list = append(list, i)
	//}

	xhttp.RespSuccessList(ctx, orders, 0)

}

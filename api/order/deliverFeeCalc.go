package order

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/deliverFeeRuleService"
	"base/service/servicePointService"
	"base/service/userAddrService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// DeliverFeeCalc 配送费-计算
func DeliverFeeCalc(ctx *gin.Context) {
	var req = struct {
		SecondPointID      string            `json:"second_point_id"`
		AddressID          string            `json:"address_id"`
		DeliverType        model.DeliverType `json:"deliver_type"`
		InstantDeliverType int               `json:"instant_deliver_type"`
		ProductAmount      int               `json:"product_amount"`
		TotalWeight        int               `json:"total_weight"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if _, ok := model.DeliverTypeMsg[req.DeliverType]; !ok {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "配送方式错误"))
		return
	}

	if req.DeliverType == model.DeliverTypeInstantDeliver && req.InstantDeliverType == 0 {
		err = xerr.NewErr(xerr.ErrParamError, nil, "请选择即时配送方式")
		xhttp.RespErr(ctx, err)
		return
	}

	addressID, err := util.ConvertToObjectWithCtx(ctx, req.AddressID)
	if err != nil {
		err = xerr.NewErr(xerr.ErrParamError, nil, "请选择地址")
		xhttp.RespErr(ctx, err)
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.SecondPointID)
	if err != nil {
		err = xerr.NewErr(xerr.ErrParamError, nil, "请选城市仓")
		xhttp.RespErr(ctx, err)
		return
	}

	point, err := servicePointService.NewServicePointService().Get(ctx, pointID)
	if err != nil {
		err = xerr.NewErr(xerr.ErrParamError, nil, "请选城市仓")
		xhttp.RespErr(ctx, err)
		return
	}
	_ = point

	address, err := userAddrService.NewUserAddrService().Get(ctx, addressID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	feeRes, err := deliverFeeRuleService.NewDeliverFeeRuleService().CalcDistanceFee(ctx, address, req.DeliverType, req.InstantDeliverType, req.ProductAmount, req.TotalWeight, point)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, feeRes)
}

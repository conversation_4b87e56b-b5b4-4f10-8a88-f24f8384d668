package deliverScope

import (
	"base/core/xhttp"
	"base/model"
	"base/service/deliverScopeService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Upsert(ctx *gin.Context) {
	var req = struct {
		ServicePointID string                `json:"service_point_id"`
		Points         []model.PointLocation `json:"points"`
		CenterPoint    model.PointLocation   `json:"center_point"`
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = deliverScopeService.NewDeliverScopeService().Upsert(ctx, servicePointID, req.Points, req.CenterPoint)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

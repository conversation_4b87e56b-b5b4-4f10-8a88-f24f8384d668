package deliverScope

import "github.com/gin-gonic/gin"

import (
	"base/core/xhttp"
	"base/service/deliverScopeService"
	"base/util"
)

func Get(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := deliverScopeService.NewDeliverScopeService().GetByServicePointID(ctx, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

package orderStats

import (
	"base/core/xhttp"
	"base/service/orderAgentPayService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// GetSupplierCouponSubsidy 查询供应商优惠券补贴金额
func GetSupplierCouponSubsidy(ctx *gin.Context) {
	var req = struct {
		SupplierID     string `json:"supplier_id"`
		MonthTimeStamp int64  `json:"month_time_stamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	begin, end, err := util.MonthScopeTimestamp(req.MonthTimeStamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return

	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"supplier_id": id,
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	agentPays, err := orderAgentPayService.NewOrderAgentPayService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var amount int
	for _, agentPay := range agentPays {
		amount += agentPay.TotalPromotionSubsidyAmount
	}

	res := SupplierCouponSubsidy{
		Amount: amount,
	}

	xhttp.RespSuccess(ctx, res)
}

type SupplierCouponSubsidy struct {
	Amount int `json:"amount"`
}

package orderStats

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ListFinishByBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID   string `json:"buyer_id"`
		TimeBegin int64  `json:"time_begin"`
		TimeEnd   int64  `json:"time_end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	filter := bson.M{}
	filter["order_status"] = bson.M{
		"$gte": model.OrderStatusTypeToArrive,
	}
	filter["pay_status"] = model.PayStatusTypePaid
	if req.TimeBegin == 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "时间不能为0"))
		return
	}

	filter["created_at"] = bson.M{
		"$gte": req.TimeBegin,
		"$lte": req.TimeEnd,
	}

	buyerID, err := util.ConvertToObjectWithNote(req.BuyerID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter["buyer_id"] = buyerID

	list, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var orderIDs []primitive.ObjectID
	for _, order := range list {
		orderIDs = append(orderIDs, order.ID)
	}

	debts := make([]model.OrderDebt, 0)
	// 查询补差
	if len(orderIDs) > 0 {
		debts, err = orderDebtService.NewOrderDebtService().List(ctx, bson.M{"order_id": bson.M{"$in": orderIDs}})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	mDebt := make(map[primitive.ObjectID]model.OrderDebt)
	for _, debt := range debts {
		mDebt[debt.OrderID] = debt
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	mRefund := make(map[primitive.ObjectID][]model.OrderRefund)
	for _, re := range refunds {
		mRefund[re.OrderID] = append(mRefund[re.OrderID], re)
	}

	resList := make([]OrderList, 0)
	for _, order := range list {
		item := OrderList{
			Order:      order,
			Debt:       mDebt[order.ID],
			RefundList: mRefund[order.ID],
		}
		resList = append(resList, item)
	}

	xhttp.RespSuccess(ctx, resList)
}

type OrderList struct {
	Order      model.Order         `json:"order"`
	Debt       model.OrderDebt     `json:"debt"`
	RefundList []model.OrderRefund `json:"refund_list"`
}

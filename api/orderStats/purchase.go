package orderStats

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderDebtService"
	"base/service/orderService"
	"base/service/productBuyPriceService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func PurchaseStats(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
		TimeBegin  int64  `json:"time_begin"`
		TimeEnd    int64  `json:"time_end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"stock_up_day_time": bson.M{
			"$gte": req.TimeBegin,
			"$lte": req.TimeEnd,
		},
		//	只需要部分字段
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter["supplier_id"] = supplierID

	list, err := productBuyPriceService.NewProductBuyPriceService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(list) < 1 {
		xhttp.RespSuccess(ctx, PurchaseStatsRes{})
		return
	}

	var totalPurchaseAmount int
	for _, i := range list {
		totalPurchaseAmount += i.BuyAmount
	}

	// 采收金额涉及的订单
	var orderIDList []primitive.ObjectID
	for _, i := range list {
		for _, order := range i.OrderList {
			orderIDList = append(orderIDList, order.OrderID)
		}
	}

	orders, err := orderService.NewOrderService().List(ctx, bson.M{
		"_id": bson.M{"$in": orderIDList},
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 订单金额
	var totalOrderAmount int
	for _, i := range orders {
		totalOrderAmount += i.ProductTotalAmount
	}

	// 订单结算金额
	orderDebts, err := orderDebtService.NewOrderDebtService().List(ctx, bson.M{
		"order_id": bson.M{"$in": orderIDList},
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 品控退款金额和补差
	var refund int
	var debt int
	for _, d := range orderDebts {
		for _, settle := range d.SettleProductList {
			if settle.SettleResultType == model.SettleResultTypeRefund {
				refund += settle.DiffProductAmount
			}
			if settle.SettleResultType == model.SettleResultTypeDebt {
				debt += settle.DiffProductAmount
			}
		}
	}

	res := PurchaseStatsRes{
		TotalPurchaseAmount:         totalPurchaseAmount,
		TotalPurchaseAmountFmt:      formatAmount(totalPurchaseAmount),
		TotalOrderAmount:            totalOrderAmount,
		TotalOrderAmountFmt:         formatAmount(totalOrderAmount),
		TotalQualityRefundAmount:    refund,
		TotalQualityRefundAmountFmt: formatAmount(refund),
		TotalDebtAmount:             debt,
		TotalDebtAmountFmt:          formatAmount(debt),
		TotalSaleAmountFmt:          formatAmount(totalOrderAmount + debt - refund),
	}

	xhttp.RespSuccess(ctx, res)
}

type PurchaseStatsRes struct {
	TotalPurchaseAmount         int    `json:"total_purchase_amount"`
	TotalPurchaseAmountFmt      string `json:"total_purchase_amount_fmt"`
	TotalOrderAmount            int    `json:"total_order_amount"`
	TotalOrderAmountFmt         string `json:"total_order_amount_fmt"`
	TotalQualityRefundAmount    int    `json:"total_quality_refund_amount"`
	TotalQualityRefundAmountFmt string `json:"total_quality_refund_amount_fmt"`
	TotalDebtAmount             int    `json:"total_debt_amount"`
	TotalDebtAmountFmt          string `json:"total_debt_amount_fmt"`
	TotalSaleAmountFmt          string `json:"total_sale_amount_fmt"`
}

// 格式化金额
func formatAmount(amount int) string {
	return util.DealMoneyToYuanStr(amount)
}

package couponStock

import (
	"base/core/xhttp"
	"base/model"
	"base/service/couponStockService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Get 查看优惠券信息
func Get(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	data, err := couponStockService.NewService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, data)
}

type res struct {
	model.CouponUser
	Title string   `json:"title"`
	Desc  []string `json:"desc"`
}

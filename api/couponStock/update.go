package couponStock

import (
	"base/core/xhttp"
	"base/service/couponStockService"
	"base/types"

	"github.com/gin-gonic/gin"
)

// Update 更新优惠券
func Update(ctx *gin.Context) {
	var req types.CouponStockUpdateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = couponStockService.NewService().Update(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// // UpdateOpen 更新优惠券状态
// func UpdateOpen(ctx *gin.Context) {
// 	var req = struct {
// 		ID     string `json:"id"`
// 		IsOpen bool   `json:"is_open"`
// 	}{}
// 	err := xhttp.Parse(ctx, &req)
// 	if err != nil {
// 		return
// 	}
// 	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
// 	if err != nil {
// 		xhttp.RespErr(ctx, err)
// 		return
// 	}

// 	err = couponStockService.NewService().UpdateOpen(ctx, id, req.IsOpen)
// 	if err != nil {
// 		xhttp.RespErr(ctx, err)
// 		return
// 	}
// 	xhttp.RespSuccess(ctx, nil)
// }

package couponStock

import (
	"base/core/xhttp"
	"base/service/couponStockService"
	"base/types"

	"github.com/gin-gonic/gin"
)

// Create 创建优惠券
func Create(ctx *gin.Context) {
	var req types.CouponCreateReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	err = couponStockService.NewService().Create(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

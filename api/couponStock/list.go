package couponStock

import (
	"base/core/xhttp"
	"base/service/couponStockService"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// List 查看优惠券列表
func List(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page"`
		Limit int64 `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"deleted_at": 0,
	}

	list, i, err := couponStockService.NewService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, list, i)
}

package yeeMerchant

import (
	"base/core/xhttp"
	"base/service/yeeMerchantService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func WechatAuthApplyByPoint(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetYeeByPoint(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().MerchantWechatAuthApply(ctx, yeeMerchant)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func WechatAuthGetByPoint(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetYeeByPoint(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := yeeMerchantService.NewYeeMerchantService().MerchantWechatAuthQuery(ctx, yeeMerchant)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

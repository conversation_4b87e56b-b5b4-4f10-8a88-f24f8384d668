package yeeMerchant

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/yeeMerchantService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func MerchantUpload(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
		Object     string `json:"object"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.Object == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "缺失对象参数"))
		return
	}

	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetBySupplier(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.Object == "all" {
		//	 所有
		err = yeeMerchantService.NewYeeMerchantService().YeeUpload(ctx, yeeMerchant, "license")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		err = yeeMerchantService.NewYeeMerchantService().YeeUpload(ctx, yeeMerchant, "idCardFront")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		err = yeeMerchantService.NewYeeMerchantService().YeeUpload(ctx, yeeMerchant, "idCardBack")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		err = yeeMerchantService.NewYeeMerchantService().YeeUpload(ctx, yeeMerchant, "bank")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	} else {
		err = yeeMerchantService.NewYeeMerchantService().YeeUpload(ctx, yeeMerchant, req.Object)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	xhttp.RespSuccess(ctx, nil)
}

func MerchantUploadByPoint(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		Object         string `json:"object"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.Object == "" {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "缺失对象参数"))
		return
	}

	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetYeeByPoint(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.Object == "all" {
		//	 所有
		err = yeeMerchantService.NewYeeMerchantService().YeeUpload(ctx, yeeMerchant, "license")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		err = yeeMerchantService.NewYeeMerchantService().YeeUpload(ctx, yeeMerchant, "idCardFront")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		err = yeeMerchantService.NewYeeMerchantService().YeeUpload(ctx, yeeMerchant, "idCardBack")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		err = yeeMerchantService.NewYeeMerchantService().YeeUpload(ctx, yeeMerchant, "bank")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	} else {
		err = yeeMerchantService.NewYeeMerchantService().YeeUpload(ctx, yeeMerchant, req.Object)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	xhttp.RespSuccess(ctx, nil)
}

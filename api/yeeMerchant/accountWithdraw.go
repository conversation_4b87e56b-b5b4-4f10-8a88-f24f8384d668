package yeeMerchant

import (
	"base/core/xhttp"
	"base/service/yeeMerchantService"
	"base/util"
	"github.com/gin-gonic/gin"
	"sync"
)

var withdrawLock sync.Mutex

func AccountWithDrawBySupplier(ctx *gin.Context) {
	withdrawLock.Lock()
	defer withdrawLock.Unlock()

	var req = struct {
		SupplierID string `json:"supplier_id"`
		AccountNo  string `json:"account_no"`
		Amount     int    `json:"amount"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetBySupplier(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().AccountWithDraw(ctx, yeeMerchant, req.Amount, req.AccountNo)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func AccountWithDrawByPoint(ctx *gin.Context) {
	withdrawLock.Lock()
	defer withdrawLock.Unlock()

	var req = struct {
		ServicePointID string `json:"service_point_id"`
		AccountNo      string `json:"account_no"`
		Amount         int    `json:"amount"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetYeeByPoint(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().AccountWithDraw(ctx, yeeMerchant, req.Amount, req.AccountNo)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func AccountWithDrawQuery(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().AccountWithDrawQuery(ctx, "***********", "2024120217413630585029")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

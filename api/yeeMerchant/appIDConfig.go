package yeeMerchant

import (
	"base/core/xhttp"
	"base/service/yeeMerchantService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func WechatAppIDConfig(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().MerchantWechatConfigAdd(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

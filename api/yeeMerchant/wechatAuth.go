package yeeMerchant

import (
	"base/core/xhttp"
	"base/service/yeeMerchantService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func WechatAuthApply(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetBySupplier(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().MerchantWechatAuthApply(ctx, yeeMerchant)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func WechatAuthGet(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetBySupplier(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := yeeMerchantService.NewYeeMerchantService().MerchantWechatAuthQuery(ctx, yeeMerchant)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func WechatAuthQuery(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	//yeeMerchant, err := yeeMerchantService.NewYeeMerchantService().GetBySupplier(ctx, id)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	res, err := yeeMerchantService.NewYeeMerchantService().MerchantAuthQuery(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

func MerchantWechatAuthCancel(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = yeeMerchantService.NewYeeMerchantService().MerchantWechatAuthCancel(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

package index

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/indexPartProductService"
	"base/service/indexPartService"
	"base/service/productService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListPart 查询
func ListPart(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := xhttp.GetPointID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := indexPartService.NewIndexPartService().ListVisible(ctx, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

func GetPart(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := indexPartService.NewIndexPartService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

// Apply 申请加入专区
func Apply(ctx *gin.Context) {
	var req = struct {
		IndexPartID    string           `json:"index_part_id"`
		ApplyPriceList []model.PerPrice `json:"apply_price_list"`
		ProductID      string           `json:"product_id"`
		SupplierID     string           `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	indexPartID, err := util.ConvertToObjectWithCtx(ctx, req.IndexPartID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if len(req.ApplyPriceList) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "价格列表缺失"))
		return
	}

	err = indexPartProductService.NewIndexPartProductService().Apply(ctx, indexPartID, productID, supplierID, req.ApplyPriceList)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// ApplyCheck 申请加入专区-检查
func ApplyCheck(ctx *gin.Context) {
	//var req = struct {
	//	IndexPartID string `json:"index_part_id"`
	//	ProductID   string `json:"product_id"`
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//indexPartID, err := util.ConvertToObjectWithCtx(ctx, req.IndexPartID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//err = indexPartProductService.NewIndexPartProductService().Apply(ctx, indexPartID, productID, supplierID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	xhttp.RespSuccess(ctx, nil)
}

// ApplyList 申请列表
func ApplyList(ctx *gin.Context) {
	var req = struct {
		IndexPartID string                `json:"index_part_id"`
		AuditStatus model.AuditStatusType `json:"audit_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	indexPartID, err := util.ConvertToObjectWithCtx(ctx, req.IndexPartID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"index_part_id": indexPartID,
		//"supplier_id":   supplierID,
		//"audit_status":  auditStatus,
	}

	list, err := indexPartProductService.NewIndexPartProductService().ApplyList(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resList := make([]ApplyRes, 0)

	var productIDs []primitive.ObjectID
	for _, apply := range list {
		productIDs = append(productIDs, apply.ProductID)
	}

	products, err := productService.NewProductService().ListByIDs(ctx, productIDs)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	for _, apply := range list {
		item := ApplyRes{
			Apply: apply,
		}
		for _, product := range products {
			if product.ID == apply.ProductID {
				item.Product = product
			}
		}

		resList = append(resList, item)
	}

	xhttp.RespSuccess(ctx, resList)
}

// ApplyListBySupplier 申请列表
func ApplyListBySupplier(ctx *gin.Context) {
	var req = struct {
		IndexPartID string                `json:"index_part_id"`
		SupplierID  string                `json:"supplier_id"`
		AuditStatus model.AuditStatusType `json:"audit_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	indexPartID, err := util.ConvertToObjectWithCtx(ctx, req.IndexPartID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"index_part_id": indexPartID,
		"supplier_id":   supplierID,
		//"audit_status":  auditStatus,
	}

	list, err := indexPartProductService.NewIndexPartProductService().ApplyList(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	resList := make([]ApplyRes, 0)

	var productIDs []primitive.ObjectID
	for _, apply := range list {
		productIDs = append(productIDs, apply.ProductID)
	}

	products, err := productService.NewProductService().ListByIDs(ctx, productIDs)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	for _, apply := range list {
		item := ApplyRes{
			Apply: apply,
		}
		for _, product := range products {
			if product.ID == apply.ProductID {
				item.Product = product
			}
		}

		resList = append(resList, item)
	}

	xhttp.RespSuccess(ctx, resList)
}

type ApplyRes struct {
	model.Product
	Apply model.IndexPartProductApply `json:"apply"`
}

// ApplyAudit 申请审核
func ApplyAudit(ctx *gin.Context) {
	var req = struct {
		ID          string                `json:"id"`
		AuditStatus model.AuditStatusType `json:"audit_status"`
		AuditNote   string                `json:"audit_note"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = indexPartProductService.NewIndexPartProductService().ApplyAudit(ctx, id, req.AuditStatus, req.AuditNote)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// ExitPart 退出专区
func ExitPart(ctx *gin.Context) {
	var req = struct {
		IndexPartID string `json:"index_part_id"`
		ProductID   string `json:"product_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	indexPartID, err := util.ConvertToObjectWithCtx(ctx, req.IndexPartID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = indexPartProductService.NewIndexPartProductService().RemoveFromPart(ctx, indexPartID, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package index

import (
	"base/core/xhttp"
	"base/service/swipeService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// ListSwipe 轮播图
func ListSwipe(ctx *gin.Context) {
	i, err := swipeService.NewSwipeService().ListVisible(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, i)
}

// GetSwipe 轮播图
func GetSwipe(ctx *gin.Context) {
	var req = struct {
		ID string `uri:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	i, err := swipeService.NewSwipeService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, i)
}

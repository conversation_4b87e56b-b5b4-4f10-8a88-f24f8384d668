package index

import (
	"base/core/xhttp"
	"base/service/topicService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// ListTopic 主题
func ListTopic(ctx *gin.Context) {
	i, err := topicService.NewTopicService().ListVisible(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, i)
}

// GetTopic 主题
func GetTopic(ctx *gin.Context) {
	var req = struct {
		ID string `uri:"id" validate:"len=24"`
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	i, err := topicService.NewTopicService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, i)
}

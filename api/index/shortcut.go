package index

import (
	"base/core/xhttp"
	"base/service/shortcutService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// ListShortcut 快捷栏
func ListShortcut(ctx *gin.Context) {
	pointID, err := xhttp.GetPointID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	i, err := shortcutService.NewShortcutService().ListVisible(ctx, pointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, i)
}

// GetShortcut 快捷栏
func GetShortcut(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	i, err := shortcutService.NewShortcutService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, i)
}

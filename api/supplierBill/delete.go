package supplierBill

import (
	"base/core/xhttp"
	"base/service/billService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// Delete 删除账单
func Delete(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = billService.NewBillService().Delete(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

package supplierBill

import (
	"base/core/xhttp"
	"base/service/billService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// ListBySupplier 获取账单列表
func ListBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
		Page       int64  `json:"page" validate:"min=1"`
		Limit      int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	filter := bson.M{}

	SupplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter["supplier_id"] = SupplierID
	filter["deleted_at"] = 0

	bills, count, err := billService.NewBillService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccessList(ctx, bills, count)
}

//
//// ListOrderByBuyer 获取订单列表
//func ListOrderByBuyer(ctx *gin.Context) {
//	var req = struct {
//		BuyerID   string `json:"buyer_id"`
//		TimeBegin int64  `json:"time_begin"`
//		TimeEnd   int64  `json:"time_end"`
//	}{}
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//	filter := bson.M{}
//	filter["order_status"] = bson.M{
//		"$eq": model.OrderStatusTypeFinish,
//	}
//	filter["pay_status"] = model.PayStatusTypePaid
//	if req.TimeBegin == 0 {
//		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "时间不能为0"))
//		return
//	}
//
//	filter["created_at"] = bson.M{
//		"$gte": req.TimeBegin,
//		"$lte": req.TimeEnd,
//	}
//
//	buyerID, err := util.ConvertToObjectWithNote(req.BuyerID, "")
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	filter["buyer_id"] = buyerID
//
//	list, err := orderService.NewOrderService().List(ctx, filter)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//
//	//var orderIDs []primitive.ObjectID
//	//for _, order := range list {
//	//	orderIDs = append(orderIDs, order.ID)
//	//}
//	//
//	//debts := make([]model.OrderDebt, 0)
//	//// 查询补差
//	//if len(orderIDs) > 0 {
//	//	debts, err = orderDebtService.NewOrderDebtService().List(ctx, bson.M{"order_id": bson.M{"$in": orderIDs}})
//	//	if err != nil {
//	//		xhttp.RespErr(ctx, err)
//	//		return
//	//	}
//	//}
//	//
//	//mDebt := make(map[primitive.ObjectID]model.OrderDebt)
//	//for _, debt := range debts {
//	//	mDebt[debt.OrderID] = debt
//	//}
//	//
//	//refunds := make([]model.OrderRefund, 0)
//	//// 查询退款
//	//if len(orderIDs) > 0 {
//	//	refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
//	//		"order_id": bson.M{"$in": orderIDs},
//	//	})
//	//	if err != nil {
//	//		xhttp.RespErr(ctx, err)
//	//		return
//	//	}
//	//}
//	//
//	//mRefund := make(map[primitive.ObjectID][]model.OrderRefund)
//	//for _, re := range refunds {
//	//	mRefund[re.OrderID] = append(mRefund[re.OrderID], re)
//	//}
//	//
//	//resList := make([]OrderList, 0)
//	//for _, order := range list {
//	//	item := OrderList{
//	//		Order:      order,
//	//		Debt:       mDebt[order.ID],
//	//		RefundList: mRefund[order.ID],
//	//	}
//	//	resList = append(resList, item)
//	//}
//
//	xhttp.RespSuccess(ctx, list)
//}

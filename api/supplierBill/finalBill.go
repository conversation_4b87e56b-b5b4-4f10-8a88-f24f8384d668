package supplierBill

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListFinalBill 最终结算单
func ListFinalBill(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
		TimeBegin  int64  `json:"time_begin"`
		TimeEnd    int64  `json:"time_end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{}
	filter["order_status"] = bson.M{
		"$gte": model.OrderStatusTypeToStockUp,
	}
	filter["pay_status"] = model.PayStatusTypePaid
	filter["pay_method"] = model.PayMethodTypeWechat
	if req.TimeBegin == 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "时间不能为0"))
		return
	}

	filter["created_at"] = bson.M{
		"$gte": req.TimeBegin,
		"$lte": req.TimeEnd,
	}

	if len(req.SupplierID) == 24 {
		id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["supplier_id"] = id
	}

	list, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var orderIDs []primitive.ObjectID
	mSupplier := make(map[primitive.ObjectID]string)
	for _, order := range list {
		orderIDs = append(orderIDs, order.ID)
		mSupplier[order.SupplierID] = order.SupplierName
	}

	debts := make([]model.OrderDebt, 0)
	// 查询补差
	if len(orderIDs) > 0 {
		debts, err = orderDebtService.NewOrderDebtService().List(ctx, bson.M{"order_id": bson.M{"$in": orderIDs}})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	mDebt := make(map[primitive.ObjectID]model.OrderDebt)
	for _, debt := range debts {
		mDebt[debt.OrderID] = debt
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	mRefund := make(map[primitive.ObjectID][]model.OrderRefund)
	for _, re := range refunds {
		mRefund[re.OrderID] = append(mRefund[re.OrderID], re)
	}

	//resList := make([]orderStats.OrderList, 0)
	//for _, order := range list {
	//	item := orderStats.OrderList{
	//		Order:      order,
	//		Debt:       mDebt[order.ID],
	//		RefundList: mRefund[order.ID],
	//	}
	//	resList = append(resList, item)
	//}

	resList := make([]billRes, 0, len(mSupplier))

	for id, name := range mSupplier {
		var totalProductAmount int
		var totalProductRefundedAmount int
		var totalProductRefundingAmount int
		var totalProductDebtPaidAmount int
		var totalAmount int

		for _, order := range list {
			if order.SupplierID == id {
				totalProductAmount += order.ProductTotalAmount
			}
		}

		for _, refund := range refunds {
			if refund.SupplierID == id && refund.AuditStatus == model.AuditStatusTypePass {
				totalProductRefundedAmount += refund.AuditAmount
			}
			if refund.SupplierID == id && refund.AuditStatus == model.AuditStatusTypeDoing {
				totalProductRefundingAmount += refund.AuditAmount
			}
		}

		for _, debt := range debts {
			if debt.SupplierID == id && debt.PayMethod == model.PayMethodTypeWechat && debt.PayStatus == model.PayStatusTypePaid {
				totalProductDebtPaidAmount += debt.TotalProductAmount
			}
		}

		totalAmount = totalProductAmount - totalProductRefundedAmount + totalProductDebtPaidAmount

		item := billRes{
			SupplierID:                  id,
			SupplierName:                name,
			TotalProductAmount:          totalProductAmount,
			TotalProductRefundedAmount:  totalProductRefundedAmount,
			TotalProductRefundingAmount: totalProductRefundingAmount,
			TotalProductDebtPaidAmount:  totalProductDebtPaidAmount,
			TotalAmount:                 totalAmount,
		}
		resList = append(resList, item)
	}

	xhttp.RespSuccess(ctx, resList)
}

type billRes struct {
	SupplierID                  primitive.ObjectID `json:"supplier_id"`
	SupplierName                string             `json:"supplier_name"`
	TotalProductAmount          int                `json:"total_product_amount"`
	TotalProductRefundedAmount  int                `json:"total_product_refunded_amount"`
	TotalProductRefundingAmount int                `json:"total_product_refunding_amount"`
	TotalProductDebtPaidAmount  int                `json:"total_product_debt_paid_amount"`
	TotalAmount                 int                `json:"total_amount"`
}

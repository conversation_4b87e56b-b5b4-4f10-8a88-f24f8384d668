package authUser

import (
	"base/core/xhttp"
	"base/service/authUserService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func Check(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var f bool

	exist, err := authUserService.NewAuthUserService().Count(ctx, bson.M{
		"buyer_id": buyerID,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if exist > 0 {
		f = true
	}

	xhttp.RespSuccess(ctx, f)
}

package authUser

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/authUserService"
	"base/util"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Create(ctx *gin.Context) {
	var req = struct {
		BuyerID     string `json:"buyer_id"`
		BindBuyerID string `json:"bind_buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	bindBuyerID, err := util.ConvertToObjectWithCtx(ctx, req.BindBuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 已存在
	exist, err := authUserService.NewAuthUserService().Count(ctx, bson.M{
		"buyer_id":      buyerID,
		"bind_buyer_id": bindBuyerID,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	if exist > 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "委托已存在"))
		return
	}

	data := model.AuthUser{
		ID:          primitive.NewObjectID(),
		BuyerID:     buyerID,
		BindBuyerID: bindBuyerID,
		CreatedAt:   time.Now().UnixMilli(),
	}
	err = authUserService.NewAuthUserService().Create(ctx, data)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

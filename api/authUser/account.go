package authUser

import (
	"base/core/xhttp"
	"base/service/buyerService"
	"base/service/jwtService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
)

func IntiLoginToken(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyer, err := buyerService.NewBuyerService().GetByID(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	token, refreshToken, expireAt, err := jwtService.NewJwtService().MakeTokenForBuyer(buyer.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = refreshToken

	r := types.LoginRes{
		Buyer:       buyer,
		AccessToken: token,
		Expires:     expireAt,
	}

	xhttp.RespSuccess(ctx, r)

}

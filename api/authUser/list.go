package authUser

import (
	"base/core/xhttp"
	"base/model"
	"base/service/authUserService"
	"base/service/buyerService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func List(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	authUsers, err := authUserService.NewAuthUserService().List(ctx, bson.M{
		"bind_buyer_id": buyerID,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]authRes, 0, len(authUsers))
	var ids []primitive.ObjectID
	for _, u := range authUsers {
		ids = append(ids, u.BuyerID)
	}

	if len(ids) == 0 {
		xhttp.RespSuccess(ctx, list)
		return
	}

	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	for _, auth := range authUsers {
		var buyerName, mobile string
		for _, b := range buyers {
			if b.ID == auth.BuyerID {
				buyerName = b.BuyerName
				mobile = b.Mobile
			}
		}
		item := authRes{
			AuthUser:  auth,
			Mobile:    mobile,
			BuyerName: buyerName,
		}
		list = append(list, item)
	}

	xhttp.RespSuccess(ctx, list)
}

type authRes struct {
	model.AuthUser
	BuyerName string `json:"buyer_name"`
	Mobile    string `json:"mobile"`
}

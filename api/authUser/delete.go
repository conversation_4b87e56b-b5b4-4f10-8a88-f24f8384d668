package authUser

import (
	"base/core/xhttp"
	"base/service/authUserService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func DeleteOne(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = authUserService.NewAuthUserService().Delete(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

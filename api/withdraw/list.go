package withdraw

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/authenticationService"
	"base/service/withdrawApplyOrderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListApplyByObject 提现订单记录
func ListApplyByObject(ctx *gin.Context) {
	var req = struct {
		SupplierID     string `json:"supplier_id"`
		WarehouseID    string `json:"warehouse_id"`
		ServicePointID string `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	env := xhttp.GetEnv(ctx)

	var id primitive.ObjectID
	switch env {
	case model.ObjectTypeSupplier:
		id, err = util.ConvertToObjectWithCtx(ctx, req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	case model.ObjectTypeWarehouse:
		id, err = util.ConvertToObjectWithCtx(ctx, req.WarehouseID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	case model.ObjectTypeServicePoint:
		id, err = util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	default:
		break
	}

	if id == primitive.NilObjectID {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数错误"))
		return
	}

	auth, err := authenticationService.NewAuthenticationService().GetByObject(ctx, id, env)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	applyOrders, err := withdrawApplyOrderService.NewWithdrawApplyOrderService().ListByObject(ctx, auth.ObjectID, auth.ObjectType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, applyOrders)
}

func ListApplyByStation(ctx *gin.Context) {
	var req = struct {
		StationID string `json:"station_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	var id primitive.ObjectID
	id, err = util.ConvertToObjectWithCtx(ctx, req.StationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	applyOrders, err := withdrawApplyOrderService.NewWithdrawApplyOrderService().ListByObject(ctx, id, model.ObjectTypeStation)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, applyOrders)
}

package withdraw

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/authenticationService"
	"base/service/payAccountService"
	"base/service/withdrawApplyOrderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// Apply 提现申请
func Apply(ctx *gin.Context) {
	var req = struct {
		Amount         int               `json:"amount"`
		AccountType    model.AccountType `json:"account_type"` // 1 对公 0 个人
		SupplierID     string            `json:"supplier_id"`
		WarehouseID    string            `json:"warehouse_id"`
		ServicePointID string            `json:"service_point_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	env := xhttp.GetEnv(ctx)

	var id primitive.ObjectID
	switch env {
	case model.ObjectTypeSupplier:
		id, err = util.ConvertToObjectWithCtx(ctx, req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	case model.ObjectTypeWarehouse:
		id, err = util.ConvertToObjectWithCtx(ctx, req.WarehouseID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	case model.ObjectTypeServicePoint:
		id, err = util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		break
	default:
		break
	}

	if id == primitive.NilObjectID {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数错误"))
		return
	}

	err = checkWithdrawTime()
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.Amount < 101 {
		err = xerr.NewErr(xerr.ErrParamError, nil, "提现金额最小为1元")
		xhttp.RespErr(ctx, err)
		return
	}

	reserveFundBalance, err := payAccountService.NewPayAccountService().GetReserveFundBalance(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 保证预留3000元
	if reserveFundBalance.Balance < 3000*100 {
		err = xerr.NewErr(xerr.ErrParamError, nil, "资金未结算，请稍后重试")
		xhttp.RespErr(ctx, err)
		return
	}

	auth, err := authenticationService.NewAuthenticationService().GetByObject(ctx, id, env)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = withdrawApplyOrderService.NewWithdrawApplyOrderService().Create(ctx, auth, req.Amount, req.AccountType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func checkWithdrawTime() error {
	now := time.Now()
	location, _ := time.LoadLocation("Local")
	begin := time.Date(now.Year(), now.Month(), now.Day(), 8, 0, 0, 0, location)
	end := time.Date(now.Year(), now.Month(), now.Day(), 20, 00, 00, 0, location)
	if now.Before(begin) || now.After(end) {
		return xerr.NewErr(xerr.ErrParamError, nil, "请于8:00至20:00之间提现")
	}

	return nil
}

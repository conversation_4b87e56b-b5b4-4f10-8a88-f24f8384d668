package withdraw

import (
	"base/model"
	"bytes"
	"fmt"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.uber.org/zap"
	"log"
	"strconv"
	"time"
)

type parentOrderSort []model.ParentOrder

func (array parentOrderSort) Len() int {
	return len(array)
}

func (array parentOrderSort) Less(i, j int) bool {
	return array[i].CreatedAt < array[j].CreatedAt //从小到大， 若为大于号，则从大到小
}

func (array parentOrderSort) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

func toExcel(orders []model.WithdrawApplyOrder, supplier model.Supplier) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	sheetName := "Sheet1"
	// 创建一个工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	setSheet(f, sheetName)

	i := 1

	//title(f, sheetName, i)

	//buyer(f, sheetName, list[0].Order.BuyerName, i+1)

	//orderTime(f, sheetName, i+2, res.OrderTimeBegin, res.OrderTimeEnd, exportBegin, exportEnd)
	//createTime(f, sheetName, i+3)

	category(f, sheetName, i)

	i = 2

	//mParent := make(map[primitive.ObjectID][]OrderList)
	//for _, orderList := range list {
	//	mParent[orderList.Order.ParentOrderID] = append(mParent[orderList.Order.ParentOrderID], orderList)
	//}

	//sort.Sort(parentOrderSort(parentOrders))

	//var totalProductAmount int
	//var pAllFinalAmount int
	//var totalDeliverFeeAll int
	//var totalDeliverFeeSubsidy int
	//var totalDeliverFee int
	//var totalServiceFee int
	//var totalLoadFee int

	//var setColorList []string

	var totalAmount int
	var totalFee int
	var totalFinalAmount int

	for orderIndex, order := range orders {
		err = f.SetCellValue(sheetName, "A"+strconv.Itoa(i), orderIndex+1)

		err = f.SetCellValue(sheetName, "B"+strconv.Itoa(i), time.UnixMilli(order.CreatedAt).Format("2006/01/02 15:04:05"))

		status := "失败"
		if order.WithdrawPayResult.PayStatus == "success" {
			totalAmount += order.Amount
			totalFee += order.Fee
			totalFinalAmount += order.Amount - order.Fee
			status = "成功"
		}

		err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i), order.ID.Hex())
		err = f.SetCellValue(sheetName, "D"+strconv.Itoa(i), status)
		err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i), dealMoney(order.Amount))
		err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), dealMoney(order.Fee))
		err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i), dealMoney(order.Amount-order.Fee))
		err = f.SetCellValue(sheetName, "H"+strconv.Itoa(i), order.BankCardNo)

		err = f.SetCellValue(sheetName, "I"+strconv.Itoa(i), order.WithdrawPayResult.PayDateTime)
		i++
	}

	i += 1
	err = f.MergeCell(sheetName, "C"+strconv.Itoa(i), "D"+strconv.Itoa(i))
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i), "总计（提现成功）")
	err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i), dealMoney(totalAmount))
	err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), dealMoney(totalFee))
	err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i), dealMoney(totalFinalAmount))

	setContentStyle(f, sheetName, 2, i)

	//summary(f, sheetName, i, res, pAllFinalAmount, totalDeliverFee)

	f.SetActiveSheet(index)

	toBuffer, err := f.WriteToBuffer()
	if err != nil {
		log.Println(err)
		return nil, err
	}

	name := "提现记录-" + supplier.ShopSimpleName + "-" + time.Now().Format("2006-01-02 15-04-05")

	f.SaveAs(fmt.Sprintf("./api/withdraw/%s.xlsx", name))

	return toBuffer, nil
}

func setContentStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "left", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "A"+cell1, "I"+cell2, style)
}

func float64Ptr(f float64) *float64 { return &f }
func boolPtr(f bool) *bool          { return &f }

func setSheet(f *excelize.File, sheetName string) {
	opts := excelize.PageLayoutMarginsOptions{
		Bottom: float64Ptr(0.22),
		Footer: float64Ptr(0.2),
		Header: float64Ptr(0.2),
		Left:   float64Ptr(0.14),
		Right:  float64Ptr(0.14),
		Top:    float64Ptr(0.22),
	}
	err := f.SetPageMargins(sheetName, &opts)
	if err != nil {
		zap.S().Info(err)
	}
	err = f.SetAppProps(&excelize.AppProperties{
		Application:       "Microsoft Excel",
		ScaleCrop:         true,
		DocSecurity:       3,
		Company:           "Company Name",
		LinksUpToDate:     true,
		HyperlinksChanged: true,
		AppVersion:        "16.0000",
	})
	_ = err

	err = f.SetSheetProps(sheetName, &excelize.SheetPropsOptions{
		FitToPage: boolPtr(true), // 开启自适应页面打印，默认值为 false
	})
	_ = err

	if err != nil {
		log.Println(err)
	}
}

func createTime(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	ts := time.Now().Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "E"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "生成时间："+ts)

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func orderTime(f *excelize.File, sheetName string, index int, min, max, exportBegin, exportEnd int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "Q"+row)
	if err != nil {

	}

	exportBeginFormat := time.UnixMilli(exportBegin).Format("2006/01/02 15:04:05")
	exportEndFormat := time.UnixMilli(exportEnd).Format("2006/01/02 15:04:05")

	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("订单区间：[ %s , %s ], 导出区间：[ %s , %s ]", minT, MaxT, exportBeginFormat, exportEndFormat))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

// 导出区间
func exportTime(f *excelize.File, sheetName string, index int, min, max int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "H"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("导出区间：[ %s , %s ]", minT, MaxT))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func category(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	//row2 := strconv.Itoa(index + 1)
	var err error

	err = f.SetCellValue(sheetName, "A"+row, "")
	err = f.SetColWidth(sheetName, "A", "A", 4)

	colNum := 2

	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "申请时间")
	err = f.SetCellValue(sheetName, convertToCol(colNum+1)+row, "订单号")
	err = f.SetCellValue(sheetName, convertToCol(colNum+2)+row, "状态")
	err = f.SetCellValue(sheetName, convertToCol(colNum+3)+row, "提现金额")
	err = f.SetCellValue(sheetName, convertToCol(colNum+4)+row, "手续费")
	err = f.SetCellValue(sheetName, convertToCol(colNum+5)+row, "到账金额")
	err = f.SetCellValue(sheetName, convertToCol(colNum+6)+row, "银行卡")
	err = f.SetCellValue(sheetName, convertToCol(colNum+7)+row, "到账时间")

	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 24)
	err = f.SetColWidth(sheetName, convertToCol(colNum+1), convertToCol(colNum+1), 26)
	err = f.SetColWidth(sheetName, convertToCol(colNum+6), convertToCol(colNum+6), 24)
	err = f.SetColWidth(sheetName, convertToCol(colNum+7), convertToCol(colNum+7), 24)

	//err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 28)

	//err = f.SetColWidth(sheetName, convertToCol(4+i), convertToCol(4+i), 9)

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+row, convertToCol(colNum+7)+row, style)

	//style2, err := f.NewStyle(&excelize.Style{
	//	Font: &excelize.Font{
	//		Bold:   true,
	//		Family: "宋体",
	//		Size:   10,
	//	},
	//	Fill: excelize.Fill{
	//		Type:    "pattern",
	//		Color:   []string{"#e1e1e1"},
	//		Pattern: 1,
	//	},
	//	Border: []excelize.Border{
	//		{Type: "left", Color: "000000", Style: 1},
	//		{Type: "top", Color: "000000", Style: 1},
	//		{Type: "bottom", Color: "000000", Style: 1},
	//		{Type: "right", Color: "000000", Style: 1},
	//	},
	//	Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	//})
	//
	//err = f.SetCellStyle(sheetName, "F"+row2, "P"+row2, style2)

	err = f.SetRowHeight(sheetName, index, 40)
	//err = f.SetRowHeight(sheetName, index+1, 26)

	_ = err
}

func dealMoney(amount int) float64 {
	f, exact := decimal.NewFromInt(int64(amount)).Div(decimal.NewFromInt(100)).Round(2).Float64()

	_ = exact

	return f
}

func dealWeight(w int) float64 {
	f, exact := decimal.NewFromInt(int64(w)).Div(decimal.NewFromInt(1000)).Round(1).Float64()

	_ = exact

	return f
}

func convertToCol(columnNumber int) string {
	var res []byte
	for columnNumber > 0 {
		a := columnNumber % 26
		if a == 0 {
			a = 26
		}
		res = append(res, 'A'+byte(a-1))
		columnNumber = (columnNumber - a) / 26
	}
	// 上面输出的res是反着的，前后交换
	for i, n := 0, len(res); i < n/2; i++ {
		res[i], res[n-1-i] = res[n-1-i], res[i]
	}
	return string(res)
}

func title(f *excelize.File, sheetName string, index int) {
	//_ = index
	//err := f.MergeCell(sheetName, "A1", "Q1")
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}
	//
	//err = f.SetRowHeight(sheetName, 1, 30)
	//
	//err = f.SetCellValue(sheetName, "A1", "账单")
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}
	//
	//titleStyle, err := f.NewStyle(&excelize.Style{
	//	Font: &excelize.Font{
	//		Bold:   true,
	//		Family: "宋体",
	//		Size:   24,
	//	},
	//	Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	//})
	//if err != nil {
	//
	//}
	//err = f.SetCellStyle(sheetName, "A1", "A1", titleStyle)
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}

}

package withdraw

import (
	"base/core/xhttp"
	"base/model"
	"base/service/supplierService"
	"base/service/withdrawApplyOrderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Export(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplier, err := supplierService.NewSupplierService().Get(ctx, supplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//filter := bson.M{
	//	//"order_id": orderID,
	//	"created_at": bson.M{
	//		//"$gte": 1693497600000, // 0901
	//		"$lte": 1696089599000, // 0930
	//		//"$gte": 1690819200000, // 0801
	//		//"$lte": 1693497599000, // 0831
	//		"$gte": 1685548800000, // 0601
	//		//"$lte": 1690819199000, // 0731
	//	},
	//	"order_status": model.OrderStatusTypeFinish,
	//	"supplier_id":  supplierID,
	//}

	list, err := withdrawApplyOrderService.NewWithdrawApplyOrderService().ListByObject(ctx, supplierID, model.ObjectTypeSupplier)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// 导出
	toExcel(list, supplier)

}

package warehouseLoadFee

import (
	"base/core/xhttp"
	"base/service/warehouseLoadFeeService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func GetByWarehouseID(ctx *gin.Context) {
	var req = struct {
		WarehouseID string `json:"warehouse_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	warehouseID, err := util.ConvertToObjectWithNote(req.WarehouseID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	loadFee, err := warehouseLoadFeeService.NewWarehouseLoadFeeService().GetByWarehouseID(ctx, warehouseID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, loadFee)
}

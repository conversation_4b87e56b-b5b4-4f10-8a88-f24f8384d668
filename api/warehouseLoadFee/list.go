package warehouseLoadFee

import (
	"base/core/xhttp"
	"base/model"
	"base/service/warehouseLoadFeeService"
	"base/service/warehouseService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func List(ctx *gin.Context) {
	warehouses, err := warehouseService.NewWarehouseServiceService().ListCus(ctx, bson.M{
		"deleted_at": 0,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	loadFees, err := warehouseLoadFeeService.NewWarehouseLoadFeeService().List(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var list []res
	for _, i := range warehouses {
		var data model.WarehouseLoadFee
		for _, fee := range loadFees {
			if fee.WarehouseID == i.ID {
				data = fee
			}
		}

		item := res{
			WarehouseID:      i.ID,
			WarehouseName:    i.Name,
			WarehouseLoadFee: data,
		}
		list = append(list, item)
	}

	xhttp.RespSuccess(ctx, list)

}

type res struct {
	model.WarehouseLoadFee
	WarehouseID   primitive.ObjectID `json:"warehouse_id"`
	WarehouseName string             `json:"warehouse_name"`
}

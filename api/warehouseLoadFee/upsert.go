package warehouseLoadFee

import (
	"base/core/xhttp"
	"base/service/warehouseLoadFeeService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// Upsert 仓配费
func Upsert(ctx *gin.Context) {
	var req = struct {
		WarehouseID string `json:"warehouse_id" validate:"len=24"`
		FeePerKG    int    `json:"fee_per_kg" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	warehouseID, err := util.ConvertToObjectWithNote(req.WarehouseID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = warehouseLoadFeeService.NewWarehouseLoadFeeService().Upsert(ctx, warehouseID, req.FeePerKG)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

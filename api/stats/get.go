package stats

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

func GetTodayData(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		//TimeStart int64 `json:"time_start" validate:"-"`
		//TimeEnd   int64 `json:"time_end" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	now := time.Now().UnixMilli()
	start, end, err := util.DayScopeTimestamp(now)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"created_at": bson.M{
			"$gte": start,
			"$lte": end,
		},
	}

	if len(req.ServicePointID) == 24 {
		pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["service_point_id"] = pointID
	}

	list, err := orderService.NewOrderService().List(ctx, filter)

	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var amount int
	orderNum := 0
	var validAmount int
	mSingle := make(map[primitive.ObjectID]int)
	mBuyer := make(map[primitive.ObjectID]int)
	for _, v := range list {
		if v.PayStatus == model.PayStatusTypePaid && v.OrderStatus >= model.OrderStatusTypeToStockUp {
			validAmount += v.TotalAmount
			amount += v.TotalAmount
			for _, i := range v.ProductList {
				mSingle[i.ProductID]++
			}
			mBuyer[v.BuyerID] = 0
			orderNum++
		}
	}

	xhttp.RespSuccess(ctx, map[string]interface{}{
		"total_amount":       amount,
		"total_amount_valid": validAmount,
		"total_order":        orderNum,
		"total_buyer":        len(mBuyer),
		"total_single":       len(mSingle),
	})
}

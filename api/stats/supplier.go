package stats

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/service/supplierService"
	"sort"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.uber.org/zap"
)

// AllSupplierSaleMonthly 所有供应商月销售额
func AllSupplierSaleMonthly(ctx *gin.Context) {
	var req = struct {
		Begin int64 `json:"begin"`
		End   int64 `json:"end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//milli := carbon.CreateFromTimestampMilli(req.MonthStamp)
	//begin := milli.StartOfMonth().TimestampMilli()
	//end := milli.EndOfMonth().TimestampMilli()

	filter := bson.M{
		"created_at": bson.M{
			"$gte": req.Begin,
			"$lte": req.End,
		},
		"pay_status": model.PayStatusTypePaid,
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToStockUp,
		},
	}

	findOpt := options.Find()

	findOpt.SetProjection(bson.M{
		"product_total_amount": 1,
		"supplier_id":          1,
	})
	list, err := orderService.NewOrderService().ListWithOption(ctx, filter, findOpt)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var orderIDs []primitive.ObjectID
	for _, order := range list {
		orderIDs = append(orderIDs, order.ID)
	}

	// 退款
	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	mRefund := make(map[primitive.ObjectID][]model.OrderRefund)
	for _, v := range refunds {
		mRefund[v.SupplierID] = append(mRefund[v.SupplierID], v)
	}

	// 补差
	debts := make([]model.OrderDebt, 0)
	// 查询补差
	if len(orderIDs) > 0 {
		debtOpt := options.Find()
		debtOpt.SetProjection(bson.M{
			"supplier_id":                 1,
			"total_product_amount":        1,
			"refund_total_service_fee":    1,
			"refund_total_product_amount": 1,
		})
		debts, err = orderDebtService.NewOrderDebtService().ListWithOption(ctx, bson.M{"order_id": bson.M{"$in": orderIDs}}, debtOpt)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	mDebt := make(map[primitive.ObjectID][]model.OrderDebt)
	for _, v := range debts {
		mDebt[v.SupplierID] = append(mDebt[v.SupplierID], v)
	}

	mSupplier := make(map[primitive.ObjectID][]model.Order)
	for _, order := range list {
		mSupplier[order.SupplierID] = append(mSupplier[order.SupplierID], order)
	}

	var l []PerSupplier
	for id, oList := range mSupplier {
		supplier, err := supplierService.NewSupplierService().Get(ctx, id)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		var totalAmount int
		for _, order := range oList {
			pAmount := order.ProductTotalAmount

			totalAmount += pAmount
		}

		var refundAmount int
		var afterSaleRefundAmount int
		var afterSaleRefundPaidAmount int
		for _, refund := range mRefund[id] {
			auditAmount := refund.AuditAmount

			if refund.RefundType == model.RefundTypeAfterSale {
				afterSaleRefundAmount += auditAmount
				refundAmount += auditAmount

			}
			if refund.RefundType == model.RefundTypeAfterSale && refund.AuditStatus == model.AuditStatusTypePass {
				afterSaleRefundPaidAmount += auditAmount
				if refund.YeeRefundResult.Status != "SUCCESS" {
					zap.S().Warnf("统计，供应商售后尚未退款::%s", refund.ID.Hex())
				}
			}
		}

		var shipRefundAmount int
		for _, v := range mDebt[id] {
			a := v.RefundTotalProductAmount + v.RefundTotalServiceFee
			shipRefundAmount += a
			refundAmount += a
		}

		// 补差
		var debtAmount int
		var debtPaidAmount int
		for _, v := range mDebt[id] {
			pAmount := v.TotalProductAmount

			amount := pAmount

			debtAmount += amount
			if v.PayStatus == model.PayStatusTypePaid {
				debtPaidAmount += amount
			}
		}

		var finalAmount int

		finalAmount = totalAmount - shipRefundAmount - afterSaleRefundPaidAmount + debtPaidAmount

		item := PerSupplier{
			SupplierID:                id,
			SupplierName:              supplier.ShopSimpleName,
			TotalAmount:               totalAmount,
			RefundAmount:              refundAmount,
			ShipRefundAmount:          shipRefundAmount,
			AfterSaleRefundAmount:     afterSaleRefundAmount,
			AfterSaleRefundPaidAmount: afterSaleRefundPaidAmount,
			DebtAmount:                debtAmount,
			DebtPaidAmount:            debtPaidAmount,
			FinalAmount:               finalAmount,
		}
		l = append(l, item)
	}

	sort.Sort(supplierMonthSort(l))

	xhttp.RespSuccess(ctx, l)
}

//type supplierMonth struct {
//	//MonthStamp int64         `json:"month_stamp"`
//	List []PerSupplier `json:"list"`
//}

type PerSupplier struct {
	SupplierID                primitive.ObjectID `json:"supplier_id"`
	SupplierName              string             `json:"supplier_name"`
	TotalAmount               int                `json:"total_amount"`
	RefundAmount              int                `json:"refund_amount"`
	ShipRefundAmount          int                `json:"ship_refund_amount"`
	AfterSaleRefundAmount     int                `json:"after_sale_refund_amount"`
	AfterSaleRefundPaidAmount int                `json:"after_sale_refund_paid_amount"`
	DebtAmount                int                `json:"debt_amount"`
	DebtPaidAmount            int                `json:"debt_paid_amount"`
	FinalAmount               int                `json:"final_amount"`
}

package stats

import (
	"base/core/xhttp"
	"base/model"
	"base/service/categoryService"
	"base/service/orderQualityService"
	"base/service/orderStockUpService"
	"base/util"
	"bytes"
	"fmt"
	"log"
	"sort"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListSingleTemp 单个品控统计 临时备货
func ListSingleTemp(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		Timestamp      int64  `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	upList, err := orderQualityService.NewOrderQualityService().ListQualityAll(ctx, ts, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mCategory := make(map[primitive.ObjectID][]model.OrderQuality)
	for _, i := range upList {
		if len(i.CategoryIDs) > 2 {
			mCategory[i.CategoryIDs[1]] = append(mCategory[i.CategoryIDs[1]], i)
		}
	}
	var ids []primitive.ObjectID
	for cid, _ := range mCategory {
		ids = append(ids, cid)
	}

	categories, err := categoryService.NewCategoryService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	mc := make(map[primitive.ObjectID]string)
	for _, v := range categories {
		mc[v.ID] = v.Name
	}

	var resList []SingleByCategoryQuality
	for cid, ups := range mCategory {
		var l []SingleProductQuality
		for _, up := range ups {
			item := SingleProductQuality{
				QualityID:        up.ID,
				ProductID:        up.ProductID,
				SkuIDCode:        up.SkuIDCode,
				SkuName:          up.SkuName,
				StockUpNo:        up.StockUpNo,
				ProductTitle:     up.ProductTitle,
				SupplierID:       up.SupplierID,
				SupplierName:     up.SupplierName,
				HasParam:         up.HasParam,
				ProductParamType: up.ProductParamType,
				StandardAttr:     up.StandardAttr,
				NonStandardAttr:  up.NonStandardAttr,
				QualityDueNum:    up.QualityDueNum,
				QualityHasNum:    up.QualityNum,
				QualityHas:       up.QualityHas,
			}
			l = append(l, item)
		}

		//  排序 备货单列表
		sort.Sort(qualityList(l))

		m := make(map[string][]SingleProductQuality)
		for _, quality := range l {
			m[quality.SkuIDCode] = append(m[quality.SkuIDCode], quality)
		}

		var parentProductQualityList []ParentProductQuality

		for skuIDCode, qualities := range m {
			parentProductQualityList = append(parentProductQualityList, ParentProductQuality{
				ProductID:    qualities[0].ProductID,
				ProductTitle: qualities[0].ProductTitle,
				SkuIDCode:    skuIDCode,
				SkuName:      qualities[0].SkuName,
				SupplierName: qualities[0].SupplierName,
				List:         qualities,
			})

		}

		j := SingleByCategoryQuality{
			CategoryID:   cid,
			CategoryName: mc[cid],
			List:         parentProductQualityList,
		}
		resList = append(resList, j)
	}

	sort.Sort(SingleListQuality(resList))

	xhttp.RespSuccess(ctx, resList)
}

type qualityList []SingleProductQuality

func (array qualityList) Len() int {
	return len(array)
}

func (array qualityList) Less(i, j int) bool {
	if array[i].ProductTitle == array[j].ProductTitle {
		return array[i].StockUpNo < array[j].StockUpNo
	}
	if array[i].ProductTitle < array[j].ProductTitle {
		return true
	}
	return false //从小到大， 若为大于号，则从大到小
}

func (array qualityList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

// SingleByCategoryQuality 单个品控统计 临时备货
type SingleByCategoryQuality struct {
	CategoryID   primitive.ObjectID     `json:"category_id"`
	CategoryName string                 `json:"category_name"`
	List         []ParentProductQuality `json:"list"`
}

// ParentProductQuality 父级商品品控统计 临时备货
type ParentProductQuality struct {
	ProductID    primitive.ObjectID     `json:"product_id"`
	ProductTitle string                 `json:"product_title"`
	SkuIDCode    string                 `json:"sku_id_code"`
	SkuName      string                 `json:"sku_name"`
	SupplierName string                 `json:"supplier_name" bson:"supplier_name"` // 供应商名称
	List         []SingleProductQuality `json:"list"`
}

// SingleProductQuality 单个商品品控统计 临时备货
type SingleProductQuality struct {
	QualityID        primitive.ObjectID     `json:"quality_id"` // 品控单ID
	ProductID        primitive.ObjectID     `json:"product_id"`
	StockUpNo        int                    `json:"stock_up_no"` // 备货 批次
	ProductTitle     string                 `json:"product_title"`
	SkuIDCode        string                 `json:"sku_id_code"`
	SkuName          string                 `json:"sku_name"`
	SupplierID       primitive.ObjectID     `json:"supplier_id" bson:"supplier_id"`     // 供应商ID
	SupplierName     string                 `json:"supplier_name" bson:"supplier_name"` // 供应商名称
	SupplierMobile   string                 `json:"supplier_mobile" bson:"supplier_mobile"`
	HasParam         bool                   `json:"has_param" bson:"has_param"` // 规格参数 有/无
	ProductParamType model.ProductParamType `json:"product_param_type" bson:"product_param_type"`
	StandardAttr     model.StandardAttr     `json:"standard_attr" bson:"standard_attr"`         // 标品参数
	NonStandardAttr  model.NonStandardAttr  `json:"non_standard_attr" bson:"non_standard_attr"` // 非标品参数
	QualityDueNum    int                    `json:"quality_due_num"`                            // 应品控数
	QualityHasNum    int                    `json:"quality_has_num"`                            // 品控数
	QualityHas       bool                   `json:"quality_has"`                                // 是否品控
}

// SingleByCategory 单个品控统计 临时备货
type SingleByCategory struct {
	CategoryID   primitive.ObjectID `json:"category_id"`
	CategoryName string             `json:"category_name"`
	List         []SingleProduct    `json:"list"`
}

// SingleProduct 单个商品品控统计 临时备货
type SingleProduct struct {
	ProductID        primitive.ObjectID     `json:"product_id"`
	ProductTitle     string                 `json:"product_title"`
	SupplierID       primitive.ObjectID     `json:"supplier_id" bson:"supplier_id"`     // 供应商ID
	SupplierName     string                 `json:"supplier_name" bson:"supplier_name"` // 供应商名称
	SupplierMobile   string                 `json:"supplier_mobile" bson:"supplier_mobile"`
	HasParam         bool                   `json:"has_param" bson:"has_param"` // 规格参数 有/无
	ProductParamType model.ProductParamType `json:"product_param_type" bson:"product_param_type"`
	StandardAttr     model.StandardAttr     `json:"standard_attr" bson:"standard_attr"`         // 标品参数
	NonStandardAttr  model.NonStandardAttr  `json:"non_standard_attr" bson:"non_standard_attr"` // 非标品参数
	StockUpDueNum    int                    `json:"stock_up_due_num"`                           // 应备货数量
	StockUpHasNum    int                    `json:"stock_up_has_num"`                           // 已经备货
}

// ListSingleLocal 单个品控统计 临时备货
func ListSingleLocal(ctx *gin.Context) {
	var req = struct {
		WarehouseID    string `json:"warehouse_id" validate:"len=24"`
		ServicePointID string `json:"service_point_id" validate:"-"`
		Timestamp      int64  `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithNote(req.WarehouseID, "ListSingle warehouse_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var point primitive.ObjectID
	if len(req.ServicePointID) == 24 {
		point, err = util.ConvertToObject(req.ServicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	upList, err := orderStockUpService.NewOrderStockUpService().ListByWarehouseAndPoint(ctx, id, point, ts)

	mCategory := make(map[primitive.ObjectID][]model.OrderStockUp)
	for _, i := range upList {
		if len(i.CategoryIDs) > 2 {
			mCategory[i.CategoryIDs[1]] = append(mCategory[i.CategoryIDs[1]], i)
		}
	}
	var ids []primitive.ObjectID
	for cid, _ := range mCategory {
		ids = append(ids, cid)
	}

	categories, err := categoryService.NewCategoryService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	mc := make(map[primitive.ObjectID]string)
	for _, v := range categories {
		mc[v.ID] = v.Name
	}

	var resList []SingleByCategory
	for cid, ups := range mCategory {
		var l []SingleProduct
		for _, up := range ups {
			//var num int
			//for i, i := range up. {
			//
			//}

			item := SingleProduct{
				ProductID:        up.ProductID,
				ProductTitle:     up.ProductTitle,
				SupplierID:       up.SupplierID,
				SupplierName:     up.SupplierName,
				HasParam:         up.HasParam,
				ProductParamType: up.ProductParamType,
				StandardAttr:     up.StandardAttr,
				NonStandardAttr:  up.NonStandardAttr,
				StockUpDueNum:    up.StockUpDueNum,
				StockUpHasNum:    up.StockUpHasNum,
			}
			l = append(l, item)
		}

		j := SingleByCategory{
			CategoryID:   cid,
			CategoryName: mc[cid],
			List:         l,
		}
		resList = append(resList, j)
	}

	b, err := toExcel(resList)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//ctx.Set()

	ctx.Writer.Header().Set("Content-Type", "application/vnd.ms-excel")
	ctx.Writer.Header().Set("response-type", "blob")
	//ctx.Writer.Header().Set("Content-Transfer-Encoding", "binary")
	ctx.Writer.Write(b.Bytes())

	//xhttp.RespSuccess(ctx, b.Bytes())

}

func toExcel(list []SingleByCategory) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	// 创建一个工作表
	index, err := f.NewSheet("sheet1")
	if err != nil {
		fmt.Println(err)
		return nil, err
	}
	err = f.SetColWidth("sheet1", "C", "C", 60)
	if err != nil {
		log.Println(err)
	}
	ts := time.Now().Format("2006-01-02 15:04:05")
	err = f.MergeCell("sheet1", "A1", "B1")
	err = f.SetSheetRow("sheet1", "A1", &[]interface{}{nil, "下载时间:", ts})
	err = f.SetSheetRow("sheet1", "A2", &[]interface{}{"#", "二级分类", "商品", "数量", "已备", "供应商"})
	i := 3

	for _, v := range list {
		for _, p := range v.List {
			err = f.SetSheetRow("sheet1", "A"+strconv.Itoa(i), &[]interface{}{i - 2, v.CategoryName, p.ProductTitle, p.StockUpDueNum, p.StockUpHasNum, p.SupplierName})
			err = f.SetRowHeight("sheet1", i, 20)

			if err != nil {
				log.Println(err)
			}
			i++
		}
	}
	f.SetActiveSheet(index)

	toBuffer, err := f.WriteToBuffer()
	if err != nil {
		log.Println(err)
		return nil, err
	}

	return toBuffer, nil
	// 根据指定路径保存文件
	//if err := f.SaveAs("./single1.xlsx"); err != nil {
	//	fmt.Println(err)
	//}
}

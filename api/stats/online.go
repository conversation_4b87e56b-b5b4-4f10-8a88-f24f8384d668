package stats

import (
	"base/core/xhttp"
	"base/service/onlineService"
	"base/service/trackService"
	"github.com/gin-gonic/gin"
)

func OnlineStats(ctx *gin.Context) {
	var req = struct {
		Num int `json:"num"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	list, err := onlineService.NewOnlineService().List(ctx, req.Num)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)
}

func GetOnlineNum(ctx *gin.Context) {
	num := trackService.NewTrackService().GetOnlineNum()

	xhttp.RespSuccess(ctx, num)
}

package stats

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Order(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		TimeBegin      int64  `json:"time_begin"`
		TimeEnd        int64  `json:"time_end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if req.TimeBegin == 0 || req.TimeEnd == 0 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请选择查询时间区间"))
		return
	}

	if len(req.ServicePointID) != 24 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请选择服务仓"))
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"service_point_id": pointID,
		"created_at": bson.M{
			"$gte": req.TimeBegin,
			"$lte": req.TimeEnd,
		},
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToQuality,
		},
	}

	list, err := orderService.NewOrderService().List(ctx, filter)

	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//	TotalProductAmount       int `json:"total_product_amount"`
	//	TotalPaidAmount          int `json:"total_paid_amount"`
	//	TotalWarehouseLoadAmount int `json:"total_warehouse_load_amou
	//TotalDeliverAmount       int `json:"total_deliver_amount"`
	//TotalProductCount        int `json:"total_product_count"`
	//TotalProductWeight       int `json:"total_product_weight"`

	// 按天分类

	var rList []OrderStats

	mBuyer := make(map[int64]map[primitive.ObjectID]int)

	for _, order := range list {
		day, err := util.DayStartTimestamp(order.CreatedAt)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		if _, ok := mBuyer[day]; !ok {
			mBuyer[day] = map[primitive.ObjectID]int{
				order.BuyerID: 0,
			}
		} else {
			mBuyer[day][order.BuyerID] = 0
		}

		//mBuyer[day] = append(mBuyer[day], order.BuyerID)

		var productCount int
		var productWeight int
		for _, p := range order.ProductList {
			productCount += p.Num
			productWeight += p.DueWeight
		}

		var f bool
		for i, stats := range rList {
			if stats.DayTimestamp == day {
				//	存在
				f = true

				rList[i].TotalBuyerCount = len(mBuyer[day])
				rList[i].TotalPaidAmount += order.PaidAmount
				//rList[i].TotalProductAmount = 0
				//rList[i].TotalWarehouseLoadAmount = 0
				rList[i].TotalProductCount += productCount
				rList[i].TotalProductWeight += productWeight
			}
		}

		if !f {
			//	 不存在
			r := OrderStats{
				DayTimestamp:       day,
				TotalBuyerCount:    len(mBuyer[day]),
				TotalPaidAmount:    order.PaidAmount,
				TotalProductCount:  productCount,
				TotalProductWeight: productWeight,
			}
			rList = append(rList, r)
		}

	}

	xhttp.RespSuccess(ctx, rList)
}

// OrderStats 订单统计
type OrderStats struct {
	DayTimestamp    int64 `json:"day_timestamp"`
	TotalBuyerCount int   `json:"total_buyer_count"`
	//TotalProductAmount       int   `json:"total_product_amount"`
	TotalPaidAmount int `json:"total_paid_amount"`
	//TotalWarehouseLoadAmount int   `json:"total_warehouse_load_amount"`
	//TotalDeliverAmount       int   `json:"total_deliver_amount"`
	TotalProductCount  int `json:"total_product_count"`
	TotalProductWeight int `json:"total_product_weight"`
}

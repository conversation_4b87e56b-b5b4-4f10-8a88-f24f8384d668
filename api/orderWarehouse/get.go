package orderWarehouse

import (
	"base/core/xhttp"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// GetAddress 查询地址
func GetAddress(ctx *gin.Context) {
	var req = struct {
		OrderID string `json:"order_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.OrderID, "GetAddress OrderID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := orderService.NewOrderService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	a := data.Address
	var res uAddr
	res.Mobile = a.Contact.Mobile
	res.UserName = a.Contact.Name
	res.District = a.Location.District
	res.Address = a.Address
	xhttp.RespSuccess(ctx, res)
}

type uAddr struct {
	Mobile   string `json:"mobile"`
	UserName string `json:"user_name"`
	Address  string `json:"address"`
	District string `json:"district"`
}

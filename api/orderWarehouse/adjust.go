package orderWarehouse

import (
	"base/core/xhttp"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Adjust(ctx *gin.Context) {
	var req = struct {
		OrderID         string `json:"order_id"`
		ProductID       string `json:"product_id"`
		SettleUnitPrice int    `json:"settle_unit_price"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	orderID, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderService.NewOrderService().UpdateOrderSettleUnitPrice(ctx, orderID, productID, req.SettleUnitPrice)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

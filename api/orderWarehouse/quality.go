package orderWarehouse

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderQualityService"
	"base/service/orderService"
	"base/util"
	"sort"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListQualityTemp 临时-待品控
func ListQualityTemp(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		StationID      string `json:"station_id"`
		Timestamp      int64  `json:"timestamp"`
		QualityStatus  string `json:"quality_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var qList []model.OrderQuality

	if req.QualityStatus == "no" {
		qList, err = orderQualityService.NewOrderQualityService().ListNotQuality(ctx, ts, servicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	if req.QualityStatus == "yes" {
		qList, err = orderQualityService.NewOrderQualityService().ListQualityHas(ctx, ts, servicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	var ids []primitive.ObjectID
	for _, quality := range qList {
		for _, temp := range quality.OrderList {
			ids = append(ids, temp.OrderID)
		}
	}

	orders, err := orderService.NewOrderService().ListByOrderIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	sort.Sort(qualityList(qList))

	var rList []QualityInfo

	for _, quality := range qList {

		noteList := make([]Note, 0, len(orders))
		pointNoteList := make([]Note, 0, len(orders))

		for _, temp := range quality.OrderList {
			for _, order := range orders {
				if order.OrderNote != "" && temp.OrderID == order.ID {
					noteList = append(noteList, Note{
						OrderID:   order.ID,
						OrderNote: order.OrderNote,
					})
				}

				if order.ServicePointNote != "" && temp.OrderID == order.ID {
					pointNoteList = append(pointNoteList, Note{
						OrderID:          order.ID,
						ServicePointNote: order.ServicePointNote,
					})
				}
			}
		}

		rList = append(rList, QualityInfo{
			OrderQuality:         quality,
			NoteList:             noteList,
			ServicePointNoteList: pointNoteList,
		})
	}

	xhttp.RespSuccess(ctx, rList)
}

// UpdateQualityTemp 品控
func UpdateQualityTemp(ctx *gin.Context) {
	var req = struct {
		ID         string         `json:"id"`
		QualityNum int            `json:"quality_num"`
		Amount     int            `json:"amount"`
		ReasonType int            `json:"reason_type"`
		ReasonImg  model.FileInfo `json:"reason_img"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ID, "UpdateQualityTemp id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderQualityService.NewOrderQualityService().UpdateQuality(ctx, id, req.QualityNum, req.ReasonType, req.ReasonImg, req.Amount)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// SearchQuality 品控搜索
func SearchQuality(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		StationID      string `json:"station_id"`
		Timestamp      int64  `json:"timestamp" validate:"required"`
		Content        string `json:"content" validate:"-"`
		QualityStatus  int    `json:"quality_status" validate:"-"`
		QualityID      string `json:"quality_id" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var stationID primitive.ObjectID

	env := xhttp.GetEnv(ctx)

	if env == model.ObjectTypeStation {
		stationID, err = util.ConvertToObjectWithCtx(ctx, req.StationID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	qList, err := orderQualityService.NewOrderQualityService().SearchQuality(ctx, ts, servicePointID, stationID, req.Content, req.QualityID, req.QualityStatus)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	sort.Sort(qualityList(qList))

	xhttp.RespSuccess(ctx, qList)
}

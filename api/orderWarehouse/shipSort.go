package orderWarehouse

import "base/model"

// 发货页面排序

type shipList []PerBuyer

func (array shipList) Len() int {
	return len(array)
}

func (array shipList) Less(i, j int) bool {
	if array[i].DeliverType == array[j].DeliverType {
		return array[i].BuyerName < array[j].BuyerName
	}
	if array[i].DeliverType < array[j].DeliverType {
		return true
	}
	return false //从小到大， 若为大于号，则从大到小
}

func (array shipList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

// 分拣排序

type hasSortList []model.OrderQuality

func (array hasSortList) Len() int {
	return len(array)
}

func (array hasSortList) Less(i, j int) bool {
	if array[i].UpdatedAt > array[j].UpdatedAt {
		return true
	}
	return false
}

func (array hasSortList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

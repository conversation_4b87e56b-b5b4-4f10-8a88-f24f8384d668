package orderWarehouse

import (
	"base/core/xhttp"
	"base/service/orderWarehouseService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
)

// UpdateQuality 品控
func UpdateQuality(ctx *gin.Context) {
	var req types.QualityReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ID, "UpdateQuality 备货单ID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderWarehouseService.NewOrderWarehouseService().UpdateQuality(ctx, id, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

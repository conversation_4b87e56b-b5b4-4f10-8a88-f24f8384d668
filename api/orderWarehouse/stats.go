package orderWarehouse

import (
	"base/core/xhttp"
	"base/service/orderWarehouseService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// StatsQuality 品控简单统计
func StatsQuality(ctx *gin.Context) {
	var req = struct {
		WarehouseID string `json:"warehouse_id" validate:"len=24"`
		Timestamp   int64  `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	warehouseID, err := util.ConvertToObjectWithNote(req.WarehouseID, "ListQuality WarehouseID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	infos, err := orderWarehouseService.NewOrderWarehouseService().Stats(ctx, warehouseID, ts, true)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, infos)
}

package orderWarehouse

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/global"
	"base/model"
	"base/service/orderQualityService"
	"base/service/orderService"
	"base/service/orderWarehouseService"
	"base/util"
	"sort"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// ListShipTemp  发货单-临时
func ListShipTemp(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		StationID      string `json:"station_id"`
		Timestamp      int64  `json:"timestamp" validate:"required"`
		Status         int    `json:"status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var stationID primitive.ObjectID

	env := xhttp.GetEnv(ctx)

	if env == model.ObjectTypeStation {
		stationID, err = util.ConvertToObjectWithCtx(ctx, req.StationID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	filter := bson.M{
		"stock_up_day_time": ts,
		"order_status": bson.M{
			//"$gte": model.OrderStatusTypeToQuality,
			"$in": bson.A{model.OrderStatusTypeToQuality, model.OrderStatusTypeToSort, model.OrderStatusTypeToShip, model.OrderStatusTypeToArrive, model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		},
		"service_point_id": servicePointID,
		//"supplier_level":   model.SupplierLevelPoint,
	}

	if stationID != primitive.NilObjectID {
		filter["supplier_level"] = model.SupplierLevelStation
		filter["station_id"] = stationID
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	qualities, err := orderQualityService.NewOrderQualityService().ListQualityAllByPoint(ctx, ts, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	mQList := make(map[primitive.ObjectID][]model.OrderQuality)
	for _, quality := range qualities {
		mQList[quality.ProductID] = append(mQList[quality.ProductID], quality)
	}

	mBuyer := make(map[primitive.ObjectID][]model.Order)
	//mBuyerDeliver := make(map[primitive.ObjectID]model.DeliverType)
	mOrder := make(map[primitive.ObjectID]model.Order)
	for _, order := range orders {
		mBuyer[order.BuyerID] = append(mBuyer[order.BuyerID], order)
		//mBuyerDeliver[order.ID] = order.DeliverType
		mOrder[order.ID] = order

	}

	var list []PerBuyer

	for buyerID, i := range mBuyer {
		mBuyerAddr := make(map[string][]model.Order)
		for _, order := range i {
			la := order.Address.Location.Address
			mBuyerAddr[order.Address.Location.Address] = append(mBuyerAddr[la], order)
		}

		for locationAddr, orderList := range mBuyerAddr {
			_ = locationAddr
			var bName string
			var address model.OrderAddress
			var oList []Per
			var totalNum int
			var totalSortNum int
			var totalStandardWeight int
			var totalSortWeight int
			var logisticsName string

			var deliverType model.DeliverType = 4
			var deliverTypeList []model.DeliverType
			var deliverFee int
			mDeliverFee := make(map[primitive.ObjectID]int)
			var instantDeliverName string
			for _, order := range orderList {
				if bName == "" {
					bName = order.BuyerName
					if len(mBuyerAddr) > 1 {
						bName += "【多地址】"
					}
				}

				if logisticsName == "" {
					logisticsName = order.LogisticsName
				}

				if address.Address == "" {
					address = order.Address
				}
				var shipF bool
				if order.OrderStatus >= model.OrderStatusTypeToArrive {
					shipF = true
				}
				var pList []Info
				canShipF := true
				for _, p := range order.ProductList {
					var sortF bool
					var qualityF bool
					var sortNum int
					var sortWeight int

					qList := mQList[p.ProductID]

					var stockUpNo int

					var reasonType int           // 原因-类型   1  质量 2 缺货
					var reasonImg model.FileInfo // 原因-图片

					for _, quality := range qList {
						for _, temp := range quality.OrderList {
							if temp.OrderID == order.ID {
								sortNum = temp.SortNum
								sortF = temp.HasSort
								qualityF = temp.HasQuality
								//sortWeight = (temp.DueWeight / temp.DueNum) * temp.SortNum
								sortWeight = temp.SortWeight

								if !temp.HasSort {
									sortWeight = 0
								}

								//	 批次
								stockUpNo = quality.StockUpNo
								reasonType = quality.ReasonType
								reasonImg = quality.ReasonImg
							}
						}
					}

					item := Info{
						ProductID:    p.ProductID,
						SkuIDCode:    p.SkuIDCode,
						SkuName:      p.SkuName,
						ProductTitle: p.ProductTitle,
						StockUpNo:    stockUpNo,
						DueNum:       p.Num,
						SortNum:      sortNum,
						SortHas:      sortF,
						QualityHas:   qualityF,
						ReasonType:   reasonType,
						ReasonImg:    reasonImg,
					}

					totalNum += p.Num
					totalSortNum += sortNum

					totalStandardWeight += p.TotalWeight
					totalSortWeight += sortWeight

					pList = append(pList, item)
				}

				for _, info := range pList {
					if info.SortHas == false {
						if canShipF {
							canShipF = false
						}
					}
				}

				item := Per{
					OrderID:            order.ID,
					OrderIDNum:         order.IDNum,
					SupplierID:         order.SupplierID,
					SupplierLevel:      order.SupplierLevel,
					SupplierName:       order.SupplierName,
					HasShip:            shipF,
					CanShip:            canShipF,
					ProductList:        pList,
					OrderNote:          order.OrderNote,
					ServicePointNote:   order.ServicePointNote,
					DeliverType:        order.DeliverType,
					InstantDeliverType: order.InstantDeliverType,
					LogisticsName:      order.LogisticsName,
				}
				oList = append(oList, item)

				//if deliverType == 0 {
				//	deliverType = order.DeliverType
				//	deliverFee = order.DeliverFeeRes.FinalDeliverFee
				//	instantDeliverName = order.InstantDeliverName
				//}

				mDeliverFee[order.ParentOrderID] = order.DeliverFeeRes.FinalDeliverFee

				if order.DeliverType == 4 {
					instantDeliverName = order.InstantDeliverName
				}

				if order.DeliverType < deliverType {
					deliverType = order.DeliverType
				}

				var existDT bool

				for _, dt := range deliverTypeList {
					if order.DeliverType == dt {
						existDT = true
					}
				}
				if !existDT {
					deliverTypeList = append(deliverTypeList, order.DeliverType)
				}
			}

			allShip := true
			for _, per := range oList {
				if !per.HasShip {
					if allShip {
						allShip = false
					}
				}
			}

			if req.Status == 1 && allShip {
				//	to ship
				continue
			}

			if req.Status == 2 && !allShip {
				//	 has ship
				continue
			}

			for _, fee := range mDeliverFee {
				deliverFee += fee
			}

			item := PerBuyer{
				BuyerID:             buyerID,
				BuyerName:           bName,
				Address:             address,
				OrderList:           oList,
				TotalNum:            totalNum,
				TotalSortNum:        totalSortNum,
				DeliverType:         deliverType,
				DeliverTypeList:     deliverTypeList,
				DeliverFee:          deliverFee,
				InstantDeliverName:  instantDeliverName,
				TotalStandardWeight: totalStandardWeight,
				TotalSortWeight:     totalSortWeight,
				LogisticsName:       logisticsName,
			}
			list = append(list, item)
		}

	}

	sort.Sort(shipList(list))

	xhttp.RespSuccess(ctx, list)
}

type PerBuyer struct {
	BuyerID             primitive.ObjectID  `json:"buyer_id"`
	BuyerName           string              `json:"buyer_name"`
	Address             model.OrderAddress  `json:"address"`
	OrderList           []Per               `json:"order_list"`
	TotalNum            int                 `json:"total_num"`
	TotalSortNum        int                 `json:"total_sort_num"`
	DeliverType         model.DeliverType   `json:"deliver_type"`
	DeliverTypeList     []model.DeliverType `json:"deliver_type_list"`
	InstantDeliverName  string              `json:"instant_deliver_name"`
	DeliverFee          int                 `json:"deliver_fee"`
	TotalStandardWeight int                 `json:"total_standard_weight"` // 标准重量
	TotalSortWeight     int                 `json:"total_sort_weight"`     // 分拣重量
	LogisticsName       string              `json:"logistics_name"`        // 物流供公司名称
}

type Per struct {
	OrderID            primitive.ObjectID  `json:"order_id"`
	OrderIDNum         string              `json:"order_id_num"`
	SupplierID         primitive.ObjectID  `json:"supplier_id"`
	SupplierLevel      model.SupplierLevel `json:"supplier_level"`
	SupplierName       string              `json:"supplier_name"`
	HasShip            bool                `json:"has_ship"`
	CanShip            bool                `json:"can_ship"`
	ProductList        []Info              `json:"product_list"`
	OrderNote          string              `json:"order_note"`
	ServicePointNote   string              `json:"service_point_note"`
	DeliverType        model.DeliverType   `json:"deliver_type"`
	InstantDeliverType int                 `json:"instant_deliver_type"`
	LogisticsName      string              `json:"logistics_name"` // 物流供公司名称
}

type Info struct {
	ProductID                     primitive.ObjectID `json:"product_id"`
	SkuIDCode                     string             `json:"sku_id_code"`
	SkuName                       string             `json:"sku_name"`
	ProductTitle                  string             `json:"product_title"`
	StockUpNo                     int                `json:"stock_up_no"`
	DueNum                        int                `json:"due_num"`
	SortNum                       int                `json:"sort_num"`
	SortHas                       bool               `json:"sort_has"`
	QualityHas                    bool               `json:"quality_has"`
	ReasonType                    int                `json:"reason_type"` // 原因-类型   1  质量 2 缺货
	ReasonImg                     model.FileInfo     `json:"reason_img"`  // 原因-图片
	IsCheckWeight                 bool               `json:"is_check_weight"`
	Price                         int                `json:"price"`
	ProductRoughWeightUnitPriceKG int                `json:"product_rough_weight_unit_price_kg"`
	SettleUnitPrice               int                `json:"settle_unit_price"` // 结算单价
	RoughWeight                   int                `json:"rough_weight"`
	DueWeight                     int                `json:"due_weight"`
	SortWeight                    int                `json:"sort_weight"`
}

// ToShipTemp 发货
func ToShipTemp(ctx *gin.Context) {
	global.OrderLock.Lock()
	defer global.OrderLock.Unlock()

	var req = struct {
		OrderIDList []string `json:"order_id_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if len(req.OrderIDList) < 1 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "发货订单参数缺失，请刷新"))
		return
	}

	mID := make(map[primitive.ObjectID]int)
	for _, i := range req.OrderIDList {
		id, err := util.ConvertToObjectWithNote(i, "发货订单ID")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		if _, ok := mID[id]; ok {
			zap.S().Errorf("发货订单列表存在重复值，列表：%v,重复值：%s", req.OrderIDList, i)
		}
		mID[id] = 0
	}

	var ids []primitive.ObjectID
	for id, _ := range mID {
		ids = append(ids, id)
	}

	err = orderWarehouseService.NewOrderWarehouseService().ToShip(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

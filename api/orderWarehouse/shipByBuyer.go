package orderWarehouse

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderQualityService"
	"base/service/orderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ListShipByBuyer(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		BuyerID        string `json:"buyer_id"`
		Timestamp      int64  `json:"timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"stock_up_day_time": ts,
		"order_status": bson.M{
			//"$gte": model.OrderStatusTypeToQuality,
			"$in": bson.A{model.OrderStatusTypeToQuality, model.OrderStatusTypeToSort, model.OrderStatusTypeToShip, model.OrderStatusTypeToArrive, model.OrderStatusTypeToReceive, model.OrderStatusTypeFinish},
		},
		"buyer_id": buyerID,
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	qualities, err := orderQualityService.NewOrderQualityService().ListQualityAllByPoint(ctx, ts, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	mQList := make(map[primitive.ObjectID][]model.OrderQuality)
	for _, quality := range qualities {
		mQList[quality.ProductID] = append(mQList[quality.ProductID], quality)
	}

	//mBuyer := make(map[primitive.ObjectID][]model.Order)
	//mBuyerDeliver := make(map[primitive.ObjectID]model.DeliverType)
	mOrder := make(map[primitive.ObjectID]model.Order)
	for _, order := range orders {
		//mBuyer[order.BuyerID] = append(mBuyer[order.BuyerID], order)
		//mBuyerDeliver[order.ID] = order.DeliverType
		mOrder[order.ID] = order

	}

	//var list []PerBuyer

	mBuyerAddr := make(map[string][]model.Order)
	for _, order := range orders {
		la := order.Address.Location.Address
		mBuyerAddr[order.Address.Location.Address] = append(mBuyerAddr[la], order)
	}

	var res PerBuyer

	for locationAddr, orderList := range mBuyerAddr {
		_ = locationAddr
		var bName string
		var address model.OrderAddress
		var oList []Per
		var totalNum int
		var totalSortNum int
		var totalStandardWeight int
		var totalSortWeight int
		var logisticsName string

		var deliverType model.DeliverType = 4
		var deliverTypeList []model.DeliverType
		var deliverFee int
		mDeliverFee := make(map[primitive.ObjectID]int)
		var instantDeliverName string
		for _, order := range orderList {
			if bName == "" {
				bName = order.BuyerName
				if len(mBuyerAddr) > 1 {
					bName += "【多地址】"
				}
			}

			if logisticsName == "" {
				logisticsName = order.LogisticsName
			}

			if address.Address == "" {
				address = order.Address
			}
			var shipF bool
			if order.OrderStatus >= model.OrderStatusTypeToArrive {
				shipF = true
			}
			var pList []Info
			canShipF := true
			for _, p := range order.ProductList {
				var sortF bool
				var qualityF bool
				var sortNum int
				var sortWeight int

				qList := mQList[p.ProductID]

				var stockUpNo int

				var reasonType int           // 原因-类型   1  质量 2 缺货
				var reasonImg model.FileInfo // 原因-图片

				for _, quality := range qList {
					for _, temp := range quality.OrderList {
						if temp.OrderID == order.ID {
							sortNum = temp.SortNum
							sortF = temp.HasSort
							qualityF = temp.HasQuality
							sortWeight = temp.SortWeight

							if !temp.HasSort {
								sortWeight = 0
							}

							//	 批次
							stockUpNo = quality.StockUpNo
							reasonType = quality.ReasonType
							reasonImg = quality.ReasonImg
						}
					}
				}

				item := Info{
					ProductID:                     p.ProductID,
					ProductTitle:                  p.ProductTitle,
					StockUpNo:                     stockUpNo,
					DueNum:                        p.Num,
					SortNum:                       sortNum,
					SortHas:                       sortF,
					QualityHas:                    qualityF,
					ReasonType:                    reasonType,
					ReasonImg:                     reasonImg,
					IsCheckWeight:                 p.IsCheckWeight,
					Price:                         p.Price,
					ProductRoughWeightUnitPriceKG: p.ProductRoughWeightUnitPriceKG,
					SettleUnitPrice:               p.SettleUnitPrice,
					RoughWeight:                   p.RoughWeight,
					DueWeight:                     p.DueWeight,
					SortWeight:                    sortWeight,
				}

				totalNum += p.Num
				totalSortNum += sortNum

				totalStandardWeight += p.TotalWeight
				totalSortWeight += sortWeight

				pList = append(pList, item)
			}

			for _, info := range pList {
				if info.SortHas == false {
					if canShipF {
						canShipF = false
					}
				}
			}

			item := Per{
				OrderID:            order.ID,
				OrderIDNum:         order.IDNum,
				SupplierID:         order.SupplierID,
				SupplierLevel:      order.SupplierLevel,
				SupplierName:       order.SupplierName,
				HasShip:            shipF,
				CanShip:            canShipF,
				ProductList:        pList,
				OrderNote:          order.OrderNote,
				ServicePointNote:   order.ServicePointNote,
				DeliverType:        order.DeliverType,
				InstantDeliverType: order.InstantDeliverType,
				LogisticsName:      order.LogisticsName,
			}
			oList = append(oList, item)

			//if deliverType == 0 {
			//	deliverType = order.DeliverType
			//	deliverFee = order.DeliverFeeRes.FinalDeliverFee
			//	instantDeliverName = order.InstantDeliverName
			//}

			mDeliverFee[order.ParentOrderID] = order.DeliverFeeRes.FinalDeliverFee

			if order.DeliverType == 4 {
				instantDeliverName = order.InstantDeliverName
			}

			if order.DeliverType < deliverType {
				deliverType = order.DeliverType
			}

			var existDT bool

			for _, dt := range deliverTypeList {
				if order.DeliverType == dt {
					existDT = true
				}
			}
			if !existDT {
				deliverTypeList = append(deliverTypeList, order.DeliverType)
			}
		}

		allShip := true
		for _, per := range oList {
			if !per.HasShip {
				if allShip {
					allShip = false
				}
			}
		}

		//if req.Status == 1 && allShip {
		//	//	to ship
		//	continue
		//}
		//
		//if req.Status == 2 && !allShip {
		//	//	 has ship
		//	continue
		//}

		for _, fee := range mDeliverFee {
			deliverFee += fee
		}

		res = PerBuyer{
			BuyerID:             buyerID,
			BuyerName:           bName,
			Address:             address,
			OrderList:           oList,
			TotalNum:            totalNum,
			TotalSortNum:        totalSortNum,
			DeliverType:         deliverType,
			DeliverTypeList:     deliverTypeList,
			DeliverFee:          deliverFee,
			InstantDeliverName:  instantDeliverName,
			TotalStandardWeight: totalStandardWeight,
			TotalSortWeight:     totalSortWeight,
			LogisticsName:       logisticsName,
		}
		//list = append(list, item)
	}

	xhttp.RespSuccess(ctx, res)
}

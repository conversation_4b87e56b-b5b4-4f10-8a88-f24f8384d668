package orderWarehouse

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderWarehouseService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"sync"
)

var stockUpLock sync.Mutex

// ListOrderToStockUp 待备货-统计
func ListOrderToStockUp(ctx *gin.Context) {
	var req = struct {
		//WarehouseID string `json:"warehouse_id" validate:"len=24"`
		Timestamp int64 `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//warehouseID, err := util.ConvertToObjectWithNote(req.WarehouseID, "ListQuality WarehouseID")
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	warehouseID := primitive.NilObjectID

	orders, err := orderWarehouseService.NewOrderWarehouseService().ListOrderToStockUp(ctx, warehouseID, req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mPoint := make(map[primitive.ObjectID][]model.Order)
	for _, order := range orders {
		mPoint[order.ServicePointID] = append(mPoint[order.ServicePointID], order)
	}

	list := make([]ToStockUpList, 0)
	for id, i := range mPoint {
		var w int
		mSingle := make(map[primitive.ObjectID]int)
		mBuyer := make(map[primitive.ObjectID]int)
		mSupplier := make(map[primitive.ObjectID]int)
		var pNum int
		for _, order := range i {
			for _, p := range order.ProductList {
				pNum += p.Num
				mSingle[p.ProductID]++
				w += p.RoughWeight * p.Num
			}
			mSupplier[order.SupplierID]++
			mBuyer[order.BuyerID] = 0
		}
		item := ToStockUpList{
			ServicePointID:   id,
			ServicePointName: i[0].ServicePointName,
			TotalOrder:       len(i),
			TotalSupplier:    len(mSupplier),
			TotalBuyer:       len(mBuyer),
			TotalProduct:     pNum,
			TotalSingle:      len(mSingle),
			TotalWeight:      w,
		}
		list = append(list, item)
	}

	xhttp.RespSuccess(ctx, list)
}

type ToStockUpList struct {
	ServicePointID   primitive.ObjectID `json:"service_point_id"`
	ServicePointName string             `json:"service_point_name"`
	TotalOrder       int                `json:"total_order"`
	TotalWeight      int                `json:"total_weight"`
	TotalBuyer       int                `json:"total_buyer"`
	TotalSupplier    int                `json:"total_supplier"`
	TotalProduct     int                `json:"total_product"`
	TotalSingle      int                `json:"total_single"`
}

// OrderDoStockUp 备货
func OrderDoStockUp(ctx *gin.Context) {
	//
	//stockUpLock.Lock()
	//defer stockUpLock.Unlock()
	//
	//var req = struct {
	//	WarehouseID     string `json:"warehouse_id" validate:"len=24"`
	//	EndTimestamp    int64  `json:"end_timestamp" validate:"required"`
	//	TargetTimestamp int64  `json:"target_timestamp" validate:"required"`
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//warehouseID, err := util.ConvertToObjectWithNote(req.WarehouseID, "ListQuality WarehouseID")
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//err = orderWarehouseService.NewOrderWarehouseService().UpdateOrderToStockUp(ctx, warehouseID, req.EndTimestamp, req.TargetTimestamp)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//xhttp.RespSuccess(ctx, nil)
}

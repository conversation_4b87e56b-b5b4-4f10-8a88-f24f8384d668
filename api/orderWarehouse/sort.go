package orderWarehouse

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderQualityService"
	"base/service/orderService"
	"base/service/orderStockUpService"
	"base/service/orderWarehouseService"
	"base/types"
	"base/util"
	"sort"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListSortTemp 待分拣
func ListSortTemp(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		StationID      string `json:"station_id"`
		SortStatus     string `json:"sort_status"`
		Timestamp      int64  `json:"timestamp" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var qList []model.OrderQuality

	if req.SortStatus == "no" {
		qList, err = orderQualityService.NewOrderQualityService().ListNotSort(ctx, ts, servicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	if req.SortStatus == "yes" {
		qList, err = orderQualityService.NewOrderQualityService().ListSort(ctx, ts, true, servicePointID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	sort.Sort(qualityList(qList))

	xhttp.RespSuccess(ctx, qList)
}

type SortProduct struct {
	StockUpID   primitive.ObjectID `json:"stock_up_id"` // 备货单ID
	ProductID   primitive.ObjectID `json:"product_id"`
	ProductName string             `json:"product_name"`
	SortHas     bool               `json:"sort_has"`
	QualityHas  bool               `json:"quality_has"`
	//OrderList   []model.PerOrder   `json:"order_list"`
}

type PerPointSort struct {
	ServicePointID   primitive.ObjectID `json:"service_point_id"`
	ServicePointName string             `json:"service_point_name"`
	ProductList      []SortProduct      `json:"product_list"`
}

func SearchSort(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		Timestamp      int64  `json:"timestamp" validate:"required"`
		Content        string `json:"content" validate:"-"`
		SortStatus     int    `json:"sort_status" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	qList, err := orderQualityService.NewOrderQualityService().SearchSort(ctx, ts, req.Content, req.SortStatus, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	sort.Sort(sortList(qList))

	xhttp.RespSuccess(ctx, qList)
}

// QuerySort 查询
func QuerySort(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
		StockUpID      string `json:"stock_up_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	pointID, err := util.ConvertToObjectWithNote(req.ServicePointID, "QuerySort ServicePointID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	upID, err := util.ConvertToObjectWithNote(req.StockUpID, "QuerySort stock_up_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	up, err := orderStockUpService.NewOrderStockUpService().GetStockUp(ctx, upID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var orderList []model.PerOrder
	var pointName string
	var qualityNum int
	sortHas := true
	for _, order := range up.OrderList {
		if order.ServicePointID == pointID {
			orderList = append(orderList, order)
			if pointName == "" {
				pointName = order.ServicePointName
				qualityNum = order.QualityNum
			}

			// 服务点有一个未分拣，就是未分拣完成
			if order.SortHas == false {
				if sortHas {
					sortHas = false
				}
			}
		}
	}

	r := querySortRes{
		ServicePointID:   pointID,
		ServicePointName: pointName,
		ProductID:        up.ProductID,
		ProductTitle:     up.ProductTitle,
		SupplierName:     up.SupplierName,
		IsCheckWeight:    up.IsCheckWeight,
		OrderList:        orderList,
		QualityNum:       qualityNum,
		SortHas:          sortHas,
		QualityHas:       up.QualityHas,
	}

	xhttp.RespSuccess(ctx, r)
}

type querySortRes struct {
	ServicePointID   primitive.ObjectID `json:"service_point_id"`
	ServicePointName string             `json:"service_point_name"`
	ProductID        primitive.ObjectID `json:"product_id"`
	SupplierName     string             `json:"supplier_name"`
	ProductTitle     string             `json:"product_title"`
	IsCheckWeight    bool               `json:"is_check_weight"`
	QualityNum       int                `json:"quality_num"`
	QualityHas       bool               `json:"quality_has"`
	SortHas          bool               `json:"sort_has"`
	OrderList        []model.PerOrder   `json:"order_list"`
}

// ListSortOrder 分拣的订单
func ListSortOrder(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ID, "ListSortOrder id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := orderWarehouseService.NewOrderWarehouseService().ListSortOrder(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)
}

// ListSortOrderTemp 分拣的订单  执行分拣页面
func ListSortOrderTemp(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithNote(req.ID, "ListSortOrder id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := orderQualityService.NewOrderQualityService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	ids := make([]primitive.ObjectID, 0, len(data.OrderList))
	for _, temp := range data.OrderList {
		ids = append(ids, temp.OrderID)
	}

	orders, err := orderService.NewOrderService().ListByOrderIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	noteList := make([]Note, 0, len(orders))
	pointNoteList := make([]Note, 0, len(orders))
	for _, order := range orders {
		if order.OrderNote != "" {
			noteList = append(noteList, Note{
				OrderID:   order.ID,
				OrderNote: order.OrderNote,
			})
		}
		if order.ServicePointNote != "" {
			pointNoteList = append(pointNoteList, Note{
				OrderID:          order.ID,
				ServicePointNote: order.ServicePointNote,
			})
		}
	}

	r := QualityInfo{
		OrderQuality:         data,
		NoteList:             noteList,
		ServicePointNoteList: pointNoteList,
	}

	xhttp.RespSuccess(ctx, r)
}

type Note struct {
	OrderID          primitive.ObjectID `json:"order_id"`
	OrderNote        string             `json:"order_note"`
	ServicePointNote string             `json:"service_point_note"`
}

type QualityInfo struct {
	model.OrderQuality
	NoteList             []Note `json:"note_list"`
	ServicePointNoteList []Note `json:"service_point_note_list"`
}

// UpdateSort 分拣
func UpdateSort(ctx *gin.Context) {
	var req types.OrderSortReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	err = orderWarehouseService.NewOrderWarehouseService().UpdateSort(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// UpdateSortTemp 分拣
func UpdateSortTemp(ctx *gin.Context) {
	var req types.OrderSortReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//
	//multiUser, _ := multiUserService.NewMultiUserService().GetByUserAndObject(ctx, userID, model.ObjectTypeServicePoint)
	//
	//user, _ := userService.NewUserService().Get(ctx, userID)

	err = orderQualityService.NewOrderQualityService().UpdateSort(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// UpdateSortPhoto 分拣商品图
func UpdateSortPhoto(ctx *gin.Context) {
	var req = struct {
		QualityID string           `json:"quality_id"`
		OrderID   string           `json:"order_id"`
		PhotoList []model.FileInfo `json:"photo_list"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	qualityID, err := util.ConvertToObjectWithNote(req.QualityID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	orderID, err := util.ConvertToObjectWithNote(req.OrderID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = orderQualityService.NewOrderQualityService().UpdateSortPhoto(ctx, qualityID, orderID, req.PhotoList)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

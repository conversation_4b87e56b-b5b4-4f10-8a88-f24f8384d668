package buyer

import (
	"github.com/gin-gonic/gin"
	"sync"
)

var lock sync.Mutex

// Apply 采购商
func Apply(ctx *gin.Context) {
	//lock.Lock()
	//defer lock.Unlock()
	//var req types.BuyerApplyReq
	//userID, err := xhttp.ParseUser(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//byUser, err := buyerService.NewBuyerService().GetByUserID(ctx, userID)
	//if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//if byUser.ID != primitive.NilObjectID {
	//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "已存在信息"))
	//	return
	//}
	//
	//pointLongitude := 102.746418
	//pointLatitude := 25.025472
	//km, deliverFee, person, track := util.CalcDeliverFee(pointLongitude, pointLatitude, req.Location)
	//_ = km
	//
	//now := time.Now().UnixMilli()
	//instantDeliver := make([]model.InstantDeliver, 0, 2)
	//instantDeliver = append(instantDeliver, model.InstantDeliver{
	//	ID:     1,
	//	Name:   "跑腿",
	//	Amount: person,
	//})
	//instantDeliver = append(instantDeliver, model.InstantDeliver{
	//	ID:     2,
	//	Name:   "货拉拉",
	//	Amount: track,
	//})
	//
	//data := model.Buyer{
	//	ID:        primitive.NewObjectID(),
	//	UserID:    userID,
	//	BuyerName: req.BuyerName,
	//	//BuyerType:          req.BuyerType,
	//	MemberType:    pays.MemberTypeIndividual,
	//	ContactUser:   req.ContactUser,
	//	ContactMobile: req.ContactMobile,
	//	//RegionID:           rid,
	//	Location:           req.Location,
	//	Address:            req.Address,
	//	BusinessLicenseImg: req.BusinessLicenseImg,
	//	ShopHeadImg:        req.ShopHeadImg,
	//	AuditStatus:        model.AuditStatusTypeDoing,
	//	AccountStatus:      model.AccountStatusTypeNormal,
	//	LicenseStatus:      model.LicenseStatusNo,
	//	Note:               req.BuyerNote,
	//	ApplyReason:        req.ApplyReason,
	//	Entity:             req.Entity,
	//	AddressNote:        req.AddressNote,
	//	DeliverType:        req.DeliverType,
	//	DeliverFee:         deliverFee,
	//	SubsidyAmount:      50000,
	//	SubsidyPercent:     50,
	//	LogisticsNote:      "",
	//	InstantDeliver:     instantDeliver,
	//	CreatedAt:          now,
	//	UpdatedAt:          now,
	//}
	//
	//trimSpace := strings.TrimSpace(req.InviteCode)
	//if trimSpace == "0873" {
	//	pointID, _ := util.ConvertToObjectWithCtx(ctx, "665443407f5564d061a90bcd")
	//	data.ServicePointID = pointID
	//	data.IsAssignServicePoint = true
	//	data.ServicePointName = "红河城市服务仓"
	//}
	//
	//if req.Entity == 2 {
	//	//	 无营业执照
	//	data.ApplyReason = req.ApplyReason
	//}
	//
	//err = buyerService.NewBuyerService().Create(ctx, data)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//xhttp.RespSuccess(ctx, data.ID.Hex())
}

package buyer

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/buyerService"
	"base/service/jwtService"
	"base/service/messageService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
	"strings"
)

func LoginByWechat(ctx *gin.Context) {
	var req = struct {
		MobileCode string `json:"mobile_code" validate:"required"` // 手机号code
		LoginCode  string `json:"login_code" validate:"required"`  // openID code
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyer, err := buyerService.NewBuyerService().LoginByQuick(ctx, req.LoginCode, req.MobileCode)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	token, refreshToken, expireAt, err := jwtService.NewJwtService().MakeTokenForBuyer(buyer.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = refreshToken

	r := types.LoginRes{
		Buyer:       buyer,
		AccessToken: token,
		Expires:     expireAt,
	}

	xhttp.RespSuccess(ctx, r)

}

func SendLoginCaptcha(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if !util.CheckMobile(req.Mobile) {
		err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	res, err := messageService.NewMessageService().SendBuyerLogin(req.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)
}

// LoginByMobile 手机号登录
func LoginByMobile(ctx *gin.Context) {
	var req = struct {
		Mobile    string `json:"mobile"`
		Captcha   string `json:"captcha"`
		LoginCode string `json:"login_code"` // openID code
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	//if !util.CheckMobile(req.Mobile) {
	//	err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//if !util.CheckCaptcha(req.Captcha) {
	//	err = xerr.NewErr(xerr.ErrParamError, nil, "验证码格式错误")
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	mobile := strings.TrimSpace(req.Mobile)
	captcha := strings.TrimSpace(req.Captcha)

	buyer, err := buyerService.NewBuyerService().LoginByMobile(ctx, mobile, captcha, req.LoginCode)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	token, refreshToken, expireAt, err := jwtService.NewJwtService().MakeTokenForBuyer(buyer.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = refreshToken

	r := types.LoginRes{
		Buyer:       buyer,
		AccessToken: token,
		Expires:     expireAt,
	}

	xhttp.RespSuccess(ctx, r)
}

func LoginByPWD(ctx *gin.Context) {
	var req = struct {
		Mobile    string `json:"mobile"`
		PWD       string `json:"pwd"`
		LoginCode string `json:"login_code"` // openID code
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if !util.CheckMobile(req.Mobile) {
		err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	mobile := strings.TrimSpace(req.Mobile)
	pwd := strings.TrimSpace(req.PWD)

	buyer, err := buyerService.NewBuyerService().LoginByPWD(ctx, mobile, pwd, req.LoginCode)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	token, refreshToken, expireAt, err := jwtService.NewJwtService().MakeTokenForBuyer(buyer.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = refreshToken

	r := types.LoginRes{
		Buyer:       buyer,
		AccessToken: token,
		Expires:     expireAt,
	}

	xhttp.RespSuccess(ctx, r)
}

package buyer

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/userAddrService"
	"bytes"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"log"
	"strconv"
	"time"
)

func ExportBuyer(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"user_type": model.UserTypeYHT,
	}

	buyers, err := buyerService.NewBuyerService().ListByCus(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyerIDs := make([]primitive.ObjectID, 0)
	for _, buyer := range buyers {
		buyerIDs = append(buyerIDs, buyer.ID)
	}

	addresses, err := userAddrService.NewUserAddrService().ListByCus(ctx, bson.M{
		"buyer_id": bson.M{
			"$in": buyerIDs,
		},
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mAddr := make(map[primitive.ObjectID]model.Address)
	for _, address := range addresses {
		mAddr[address.BuyerID] = address
	}

	toExcel(buyers, mAddr)
}

func toExcel(buyers []model.Buyer, mAddr map[primitive.ObjectID]model.Address) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	sheetName := "Sheet1"
	// 创建一个工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	setSheet(f, sheetName)

	i := 1

	//title(f, sheetName, i)

	//buyer(f, sheetName, list[0].Order.BuyerName, i+1)

	//orderTime(f, sheetName, i+1, begin, end)
	//createTime(f, sheetName, i+2)

	category(f, sheetName, i)

	//var totalProductAmount int
	//var totalDebtAmount int
	//var totalQualityRefundAmount int
	//var totalAfterSaleRefundAmount int

	i = 3

	//for buyerID, productAmount := range mOrder {
	//	buyer := mBuyer[buyerID]
	//	debtAmount := mDebtAmount[buyerID]
	//	qualityRefundAmount := mQualityRefundAmount[buyerID]
	//	afterSaleRefundAmount := mAfterSaleRefundAmount[buyerID]
	//
	//	err = f.SetCellValue(sheetName, "B"+strconv.Itoa(i), buyer.BuyerName)
	//
	//	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i), dealMoney(productAmount))
	//
	//	err = f.SetCellValue(sheetName, "D"+strconv.Itoa(i), dealMoney(qualityRefundAmount))
	//	err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i), dealMoney(debtAmount))
	//	err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), dealMoney(afterSaleRefundAmount))
	//
	//	totalProductAmount += productAmount
	//	totalDebtAmount += debtAmount
	//	totalQualityRefundAmount += qualityRefundAmount
	//	totalAfterSaleRefundAmount += afterSaleRefundAmount
	//	i++
	//}

	for j, buyer := range buyers {

		err = f.SetCellValue(sheetName, "A"+strconv.Itoa(i), j+1)
		err = f.SetCellValue(sheetName, "B"+strconv.Itoa(i), buyer.BuyerName)

		err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i), buyer.Mobile)

		addr := mAddr[buyer.ID]

		l1 := addr.Address
		l2 := addr.Location.Address

		ss := l1 + "\n" + l2

		err = f.SetCellValue(sheetName, "D"+strconv.Itoa(i), ss)
		err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i), addr.Contact.Name)

		//err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i), dealMoney(debtAmount))
		//err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), dealMoney(afterSaleRefundAmount))

		i++
	}

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	//content2 := strconv.Itoa(i)
	//err = f.SetCellStyle(sheetName, "A8", "O"+content2, style)
	err = f.SetRowStyle(sheetName, 3, i, style)

	//setProductStyle(f, sheetName, 5, i)

	//err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i), dealMoney(totalProductAmount))
	//err = f.SetCellValue(sheetName, "D"+strconv.Itoa(i), dealMoney(totalQualityRefundAmount))
	//err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i), dealMoney(totalDebtAmount))
	//err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), dealMoney(totalAfterSaleRefundAmount))

	f.SetActiveSheet(index)

	//toBuffer, err := f.WriteToBuffer()
	//if err != nil {
	//	log.Println(err)
	//	return nil, err
	//}

	//return toBuffer, nil

	f.SaveAs("./yht会员列表.xlsx")

	defer f.Close()

	return nil, nil
}

func float64Ptr(f float64) *float64 { return &f }
func boolPtr(f bool) *bool          { return &f }

func setSheet(f *excelize.File, sheetName string) {
	opts := excelize.PageLayoutMarginsOptions{
		Bottom: float64Ptr(0.22),
		Footer: float64Ptr(0.2),
		Header: float64Ptr(0.2),
		Left:   float64Ptr(0.23),
		Right:  float64Ptr(0.23),
		Top:    float64Ptr(0.22),
	}
	err := f.SetPageMargins(sheetName, &opts)
	if err != nil {
		zap.S().Info(err)
	}
	err = f.SetAppProps(&excelize.AppProperties{
		Application:       "Microsoft Excel",
		ScaleCrop:         true,
		DocSecurity:       3,
		Company:           "Company Name",
		LinksUpToDate:     true,
		HyperlinksChanged: true,
		AppVersion:        "16.0000",
	})
	_ = err

	err = f.SetSheetProps(sheetName, &excelize.SheetPropsOptions{
		FitToPage: boolPtr(true), // 开启自适应页面打印，默认值为 false
	})
	_ = err

	if err != nil {
		log.Println(err)
	}
}

func orderTime(f *excelize.File, sheetName string, index int, min, max int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "H"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("订单区间：[ %s , %s ]", minT, MaxT))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func createTime(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	ts := time.Now().Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "C"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "生成时间："+ts)

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func category(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	row2 := strconv.Itoa(index + 1)
	var err error

	// 索引
	err = f.MergeCell(sheetName, "A"+row, "A"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "A"+row, "")

	// 供应商
	err = f.MergeCell(sheetName, "B"+row, "B"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "B"+row, "名称")

	err = f.MergeCell(sheetName, "C"+row, "C"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "C"+row, "手机号")

	err = f.MergeCell(sheetName, "D"+row, "D"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "D"+row, "地址")

	err = f.MergeCell(sheetName, "E"+row, "E"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "E"+row, "收货人")

	err = f.MergeCell(sheetName, "F"+row, "F"+row2)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	err = f.SetCellValue(sheetName, "F"+row, "编号")

	//err = f.MergeCell(sheetName, "F"+row, "F"+row2)
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}
	//err = f.SetCellValue(sheetName, "F"+row, "售后")

	// 金额小计
	//err = f.MergeCell(sheetName, "G"+row, "G"+row2)
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}
	//err = f.SetCellValue(sheetName, "G"+row, "金额小计")

	//mergeList := []string{"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N"}
	//for _, s := range mergeList {
	//	err := f.MergeCell(sheetName, s+row, s+row2)
	//	_ = err
	//}

	err = f.SetColWidth(sheetName, "A", "A", 6)
	err = f.SetColWidth(sheetName, "B", "B", 35)
	err = f.SetColWidth(sheetName, "C", "C", 16)
	err = f.SetColWidth(sheetName, "D", "D", 60)
	err = f.SetColWidth(sheetName, "E", "E", 14)
	err = f.SetColWidth(sheetName, "F", "F", 30)
	//err = f.SetColWidth(sheetName, "G", "G", 14)

	//err = f.SetSheetRow(sheetName, "A"+row, &[]interface{}{"", "供应商", "商品", "计价\n方式", "订单\n数量", "发货\n数量", "商品\n金额", "订单\n重量", "发货\n重量", "仓配\n费", "配送\n费", "支付\n金额", "发货\n退款", "补差\n金额", "小计"})
	//_ = err
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   14,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+row, "F"+row2, style)

	err = f.SetRowHeight(sheetName, index, 14)
	//err = f.SetRowHeight(sheetName, index+1, 26)
}

// 商品名称
func setProductStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "left", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "C"+cell1, "C"+cell2, style)
}

func dealMoney(amount int) float64 {
	f, exact := decimal.NewFromInt(int64(amount)).Div(decimal.NewFromInt(100)).Round(2).Float64()

	_ = exact

	return f
}

func dealWeight(w int) float64 {
	f, exact := decimal.NewFromInt(int64(w)).Div(decimal.NewFromInt(1000)).Round(1).Float64()

	_ = exact

	return f
}

package buyer

import (
	"base/core/xhttp"
	"base/service/buyerActiveService"
	"base/service/buyerService"
	"base/util"
	"context"
	"github.com/gin-gonic/gin"
)

// ActiveExpireApply 活跃时间--申请
func ActiveExpireApply(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerActiveService.NewBuyerActiveService().Create(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// ActiveExpireUpdate 活跃时间--更新
func ActiveExpireUpdate(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	err = buyerService.NewBuyerService().UpdateActiveExpire(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// ActiveExpireList 活跃时间--申请 -列表
func ActiveExpireList(ctx *gin.Context) {
	var req = struct {
		IsPass bool  `json:"is_pass" validate:"-"`
		Page   int64 `json:"page" validate:"min=1"`
		Limit  int64 `json:"limit" validate:"min=10"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, count, err := buyerActiveService.NewBuyerActiveService().List(ctx, req.IsPass, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}

// ActiveExpireAudit 活跃时间--申请-审核
func ActiveExpireAudit(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id" validate:"len=24"`
		IsPass  bool   `json:"is_pass" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	err = buyerActiveService.NewBuyerActiveService().Audit(ctx, id, req.IsPass)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func RefreshExpireBuyer(ctx context.Context) {
	//var req = struct {
	//	BuyerID string `json:"buyer_id" validate:"len=24"`
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//id, err := util.ConvertToObject(req.BuyerID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//err = buyerService.NewBuyerService().UpdateActiveExpire(ctx, id)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//xhttp.RespSuccess(ctx, nil)
}

package buyer

//
//func BankcardBindPre(ctx *gin.Context) {
//	var req = struct {
//		BuyerID string `json:"buyer_id"`
//		Mobile  string `json:"mobile"`
//		CardNo  string `json:"card_no"` // 银行卡号
//	}{}
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//
//	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	_ = buyerID
//
//	err = buyerService.NewBuyerService().BindBankPre(ctx, buyerID, req.Mobile, req.CardNo)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	xhttp.RespSuccess(ctx, nil)
//}
//
//func BankcardUnBind(ctx *gin.Context) {
//	var req = struct {
//		BuyerID string `json:"buyer_id"`
//		CardNo  string `json:"card_no"` // 银行卡号
//	}{}
//	err := xhttp.Parse(ctx, &req)
//	if err != nil {
//		return
//	}
//
//	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	_ = buyerID
//
//	err = buyerService.NewBuyerService().UnbindBank(ctx, buyerID, req.CardNo)
//	if err != nil {
//		xhttp.RespErr(ctx, err)
//		return
//	}
//	xhttp.RespSuccess(ctx, nil)
//}

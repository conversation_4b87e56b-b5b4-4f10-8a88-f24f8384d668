package buyer

import (
	"base/core/xhttp"
	"base/model"
	"base/service/authenticationService"
	"base/service/buyerService"
	"base/service/buyerStatsService"
	"base/types"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// GetByUser 查询信息
func GetByUser(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	userID, err := util.ConvertToObject(req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//userID = primitive.NewObjectID()

	buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, userID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespNoExist(ctx)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	authentication, err := authenticationService.NewAuthenticationService().GetByBuyer(ctx, buyer.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	isIdentity := false
	if authentication.IdentityNo != "" {
		isIdentity = true
	}

	xhttp.RespSuccess(ctx, types.BuyerRes{
		Buyer:          buyer,
		BuyerTypeName:  model.BuyerTypeMsg[buyer.BuyerType],
		IsMobileVerify: authentication.IsMobileVerify,
		PayMobile:      util.DealMobile(authentication.Mobile),
		IsIdentity:     isIdentity,
	})
}

// Get 查询信息
func Get(ctx *gin.Context) {
	var req = struct {
		ID string `uri:"id" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	objectID, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	buyer, err := buyerService.NewBuyerService().Get(objectID)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespNoExist(ctx)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	authentication, err := authenticationService.NewAuthenticationService().GetByBuyer(ctx, buyer.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, types.BuyerRes{
		Buyer:          buyer,
		BuyerTypeName:  model.BuyerTypeMsg[buyer.BuyerType],
		IsMobileVerify: authentication.IsMobileVerify,
		PayMobile:      util.DealMobile(authentication.Mobile),
	})
}

func GetByPost(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	buyer, err := buyerService.NewBuyerService().Get(id)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespNoExist(ctx)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, buyer)
}

func GetBuyerStats(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	stats, err := buyerStatsService.NewBuyerStatsService().Get(ctx, buyerID)
	if err != nil {
		zap.S().Errorf("查询会员统计异常:%s", err.Error())
		return
	}

	xhttp.RespSuccess(ctx, stats)
}

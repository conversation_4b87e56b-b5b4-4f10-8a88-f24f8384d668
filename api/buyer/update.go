package buyer

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
	"strings"
	"time"
)

// UpdateApply 采购商
func UpdateApply(ctx *gin.Context) {
	var req types.BuyerApplyReq
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := buyerService.NewBuyerService().GetByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//if data.AuditStatus == model.AuditStatusTypePass {
	//	xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "已审核通过，不能修改申请信息"))
	//	return
	//}

	now := time.Now().UnixMilli()

	//data.AuditStatus = model.AuditStatusTypePass
	data.AccountStatus = model.AccountStatusTypeNormal
	data.BuyerName = req.BuyerName
	data.ContactUser = req.ContactUser
	data.ContactMobile = req.ContactMobile
	data.Address = req.Address
	data.Location = req.Location
	data.BusinessLicenseImg = req.BusinessLicenseImg
	data.ShopHeadImg = req.ShopHeadImg
	data.UpdatedAt = now
	data.AuditFailReason = ""

	data.Entity = req.Entity
	data.ApplyReason = req.ApplyReason
	data.Note = req.BuyerNote

	data.AddressNote = req.AddressNote
	data.DeliverType = req.DeliverType
	data.DeliverFee = req.DeliverFee
	data.SubsidyAmount = 50000
	data.SubsidyPercent = 50

	//instantDeliver := make([]model.InstantDeliver, 0, 2)
	//instantDeliver = append(instantDeliver, model.InstantDeliver{
	//	ID:     1,
	//	Name:   "跑腿",
	//	Amount: 1500,
	//})
	//instantDeliver = append(instantDeliver, model.InstantDeliver{
	//	ID:     2,
	//	Name:   "货拉拉",
	//	Amount: 2000,
	//})
	//
	//data.InstantDeliver = instantDeliver

	//trimSpace := strings.TrimSpace(req.InviteCode)
	//if trimSpace == "0873" {
	//	pointID, _ := util.ConvertToObjectWithCtx(ctx, "665443407f5564d061a90bcd")
	//	data.ServicePointID = pointID
	//	data.IsAssignServicePoint = true
	//	data.ServicePointName = "红河城市服务仓"
	//}

	err = buyerService.NewBuyerService().UpdateAll(ctx, data)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

func UpdateBuyer(ctx *gin.Context) {
	var req = struct {
		BuyerID   string `json:"buyer_id"`
		BuyerName string `json:"buyer_name"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := buyerService.NewBuyerService().GetByID(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerService.NewBuyerService().UpdateBuyer(ctx, data.ID, req.BuyerName)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateAvatar(ctx *gin.Context) {
	var req = struct {
		BuyerID    string `json:"buyer_id"`
		AvatarFile string `json:"avatar_file"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerService.NewBuyerService().UpdateAvatarImg(ctx, id, req.AvatarFile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// UpdateLicenseStatus 更新营业执照状态
func UpdateLicenseStatus(ctx *gin.Context) {
	var req = struct {
		BuyerID    string `json:"buyer_id"`
		CreditCode string `json:"credit_code"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	creditCode := strings.TrimSpace(req.CreditCode)

	err = buyerService.NewBuyerService().UpdateLicenseStatus(ctx, buyerID, creditCode)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateServiceFee(ctx *gin.Context) {
	var req = struct {
		BuyerID        string               `json:"buyer_id"`
		ServiceFeeType model.ServiceFeeType `json:"service_fee_type"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerService.NewBuyerService().UpdateServiceFeeType(ctx, buyerID, req.ServiceFeeType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func UpdateUserType(ctx *gin.Context) {
	var req = struct {
		BuyerID  string         `json:"buyer_id"`
		UserType model.UserType `json:"user_type"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerService.NewBuyerService().UpdateUserType(ctx, buyerID, req.UserType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

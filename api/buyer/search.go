package buyer

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/buyerStatsService"
	"strings"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

// Search 查询会员
func Search(ctx *gin.Context) {
	var req = struct {
		Content string `json:"content" validate:"required"`
		Page    int64  `uri:"page" validate:"min=1"`
		Limit   int64  `uri:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"$or": bson.A{
			bson.M{
				"buyer_name": bson.M{
					"$regex": req.Content,
				},
			},
			bson.M{
				"contact_user": bson.M{
					"$regex": req.Content,
				},
			},
		},
	}

	buyers, count, err := buyerService.NewBuyerService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]buyerRes, 0, len(buyers))
	for _, buyer := range buyers {
		stats, err := buyerStatsService.NewBuyerStatsService().Get(ctx, buyer.ID)
		if err != nil {
			zap.S().Errorf("查询会员统计异常:%s", err.Error())
			return
		}
		item := buyerRes{
			Buyer:      buyer,
			BuyerStats: stats,
		}
		list = append(list, item)
	}

	xhttp.RespSuccessList(ctx, list, count)
}

// SearchByAddress 查询会员地址
func SearchByAddress(ctx *gin.Context) {
	var req = struct {
		Content string `json:"content" validate:"required"`
		Page    int64  `uri:"page" validate:"min=1"`
		Limit   int64  `uri:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"$or": bson.A{
			bson.M{
				"address": bson.M{
					"$regex": req.Content,
				},
			},
		},
	}

	buyers, count, err := buyerService.NewBuyerService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]buyerRes, 0, len(buyers))
	for _, buyer := range buyers {
		stats, err := buyerStatsService.NewBuyerStatsService().Get(ctx, buyer.ID)
		if err != nil {
			zap.S().Errorf("查询会员统计异常:%s", err.Error())
			return
		}
		item := buyerRes{
			Buyer:      buyer,
			BuyerStats: stats,
		}
		list = append(list, item)
	}

	xhttp.RespSuccessList(ctx, list, count)
}

type buyerRes struct {
	model.Buyer
	BuyerStats model.BuyerStats `json:"buyer_stats"`
}

// SearchByLoginMobile 查询会员手机号
func SearchByLoginMobile(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	mobile := strings.TrimSpace(req.Mobile)
	if len(mobile) != 11 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入正确手机号"))
		return
	}

	buyer, err := buyerService.NewBuyerService().GetByMobile(ctx, req.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	stats, err := buyerStatsService.NewBuyerStatsService().Get(ctx, buyer.ID)
	if err != nil {
		zap.S().Errorf("查询会员统计异常:%s", err.Error())
		return
	}
	r := buyerRes{
		Buyer:      buyer,
		BuyerStats: stats,
	}

	xhttp.RespSuccess(ctx, r)
}

// SearchByLoginMobileV2 查询会员手机号
func SearchByLoginMobileV2(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	mobile := strings.TrimSpace(req.Mobile)
	if len(mobile) != 11 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "请输入正确手机号"))
		return
	}

	buyer, err := buyerService.NewBuyerService().GetByMobile(ctx, req.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	stats, err := buyerStatsService.NewBuyerStatsService().Get(ctx, buyer.ID)
	if err != nil {
		zap.S().Errorf("查询会员统计异常:%s", err.Error())
		return
	}
	r := buyerRes{
		Buyer:      buyer,
		BuyerStats: stats,
	}

	xhttp.RespSuccess(ctx, r)
}

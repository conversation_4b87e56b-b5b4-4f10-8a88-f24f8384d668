package buyer

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/userAddrService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ListByStation(ctx *gin.Context) {
	var req = struct {
		StationID string `json:"station_id"`
		Page      int64  `json:"page"`
		Limit     int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.StationID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"station_id": id,
	}

	addresses, i, err := userAddrService.NewUserAddrService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, address := range addresses {
		ids = append(ids, address.BuyerID)
	}

	var buyers []model.Buyer

	if len(ids) > 0 {
		buyers, err = buyerService.NewBuyerService().ListByCus(ctx, bson.M{"_id": bson.M{"$in": ids}})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	xhttp.RespSuccessList(ctx, buyers, i)
}

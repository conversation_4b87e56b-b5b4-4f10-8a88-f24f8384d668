package buyer

import (
	"github.com/gin-gonic/gin"
)

// SetRealName 个人-实名认证
func SetRealName(ctx *gin.Context) {
	//var req = struct {
	//	BuyerID    string `json:"buyer_id"`
	//	Name       string `json:"name"`        // 姓名
	//	IdentityNo string `json:"identity_no"` // 证件号码
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//err = buyerService.NewBuyerService().SetRealName(ctx, buyerID, req.Name, req.IdentityNo)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//xhttp.RespSuccess(ctx, nil)
}

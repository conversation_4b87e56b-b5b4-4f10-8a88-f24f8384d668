package buyer

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/orderService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// LatestActive 最近活跃
func LatestActive(ctx *gin.Context) {
	now := time.Now()
	begin := now.Add(-time.Hour * 5 * 24).UnixMilli()

	filter := bson.M{
		"created_at": bson.M{
			"$gte": begin,
			"$lte": now.UnixMilli(),
		},
		"pay_status": model.PayStatusTypePaid,
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToStockUp,
		},
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	m := make(map[primitive.ObjectID]int64)
	for _, order := range orders {
		m[order.BuyerID] = order.CreatedAt
	}

	ids := make([]primitive.ObjectID, 0, len(m))
	for id, _ := range m {
		ids = append(ids, id)
	}
	if len(ids) < 1 {
		xhttp.RespSuccess(ctx, []model.Buyer{})
		return
	}
	buyers, i, err := buyerService.NewBuyerService().List(ctx, bson.M{"_id": bson.M{"$in": ids}}, 1, int64(len(ids)))

	xhttp.RespSuccessList(ctx, buyers, i)
}

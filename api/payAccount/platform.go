package payAccount

import (
	"base/core/xhttp"
	"base/service/payAccountService"
	"github.com/gin-gonic/gin"
)

// GetPlatformStandardBalanceNo 标准余额账户集
func GetPlatformStandardBalanceNo(ctx *gin.Context) {
	res, err := payAccountService.NewPayAccountService().GetPlatformStandardBalanceNo(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)

}

// GetPlatformMarketNo 标准营销账户集
func GetPlatformMarketNo(ctx *gin.Context) {
	res, err := payAccountService.NewPayAccountService().GetPlatformMarketNo(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)

}

// GetPlatformReserveLimitNo 准备金额度账户集
func GetPlatformReserveLimitNo(ctx *gin.Context) {
	res, err := payAccountService.NewPayAccountService().GetPlatformReserveLimitNo(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)

}

// GetPlatformStandardDepositNo 标准保证金账户集
func GetPlatformStandardDepositNo(ctx *gin.Context) {
	res, err := payAccountService.NewPayAccountService().GetPlatformStandardDepositNo(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)

}

// GetPlatformAccountA 用于正向交易资金的中间账户
func GetPlatformAccountA(ctx *gin.Context) {
	res, err := payAccountService.NewPayAccountService().GetPlatformAccountA(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)

}

// GetPlatformAccountB  用于逆向交易资金的中间账户
func GetPlatformAccountB(ctx *gin.Context) {
	res, err := payAccountService.NewPayAccountService().GetPlatformAccountB(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, res)

}

// GetReserveFundBalance  平台头寸查询
func GetReserveFundBalance(ctx *gin.Context) {
	res, err := payAccountService.NewPayAccountService().GetReserveFundBalance(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, res)
}

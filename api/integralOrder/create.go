package integralOrder

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/integralAccountService"
	"base/service/integralOrderService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	var req = struct {
		BuyerID   string `json:"buyer_id"`
		ProductID string `json:"product_id"`
		OpenID    string `json:"open_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	if req.OpenID == "" {
		err = xerr.NewErr(xerr.ErrLoginExpire, nil, "登录信息过期，请重新登录")
		xhttp.RespErr(ctx, err)
		return
	}

	// 判断积分够不够
	integralAccount, err := integralAccountService.NewIntegralAccountService().GetByBuyer(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	pay, err := integralOrderService.NewIntegralOrderService().CreateAndPay(ctx, buyerID, productID, req.OpenID, integralAccount)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, pay)
}

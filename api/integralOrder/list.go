package integralOrder

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/integralOrderService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ListByUser(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page"`
		Limit   int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"buyer_id":   id,
		"pay_status": model.PayStatusTypePaid,
	}

	orders, i, err := integralOrderService.NewIntegralOrderService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, orders, i)
}

func ListByWeb(ctx *gin.Context) {
	var req = struct {
		Page      int64  `json:"page"`
		Limit     int64  `json:"limit"`
		QueryType string `json:"query_type"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"pay_status": model.PayStatusTypePaid,
	}

	if req.QueryType != "" && req.QueryType != "all" {
		filter["status"] = req.QueryType
	}

	list, count, err := integralOrderService.NewIntegralOrderService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var userIDs []primitive.ObjectID

	for _, order := range list {
		userIDs = append(userIDs, order.UserID)
	}

	var buyers []model.Buyer
	if len(userIDs) > 0 {
		buyers, err = buyerService.NewBuyerService().ListByCus(ctx, bson.M{
			"user_id": bson.M{
				"$in": userIDs,
			},
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

	}

	resList := make([]orderRes, 0, len(list))
	for _, order := range list {

		var bName string
		var bID primitive.ObjectID
		for _, buyer := range buyers {
			if order.UserID == buyer.UserID {
				bID = buyer.ID
				bName = buyer.BuyerName
			}
		}
		item := orderRes{
			IntegralOrder: order,
			BuyerName:     bName,
			BuyerID:       bID,
		}
		resList = append(resList, item)
	}

	xhttp.RespSuccessList(ctx, resList, count)
}

func CountShip(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"pay_status": model.PayStatusTypePaid,
	}

	filter["status"] = "toShip"

	count, err := integralOrderService.NewIntegralOrderService().Count(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, count)
}

type orderRes struct {
	model.IntegralOrder
	BuyerID   primitive.ObjectID `json:"buyer_id"`
	BuyerName string             `json:"buyer_name"`
}

package agentpay

import (
	"base/core/xhttp"
	"base/service/orderAgentPayService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"sort"
)

func ListByOrder(ctx *gin.Context) {
	var req = struct {
		OrderID string `json:"order_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	orderID, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"order_id": orderID,
	}

	list, err := orderAgentPayService.NewOrderAgentPayService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	sort.Sort(sortList(list))

	xhttp.RespSuccess(ctx, list)
}

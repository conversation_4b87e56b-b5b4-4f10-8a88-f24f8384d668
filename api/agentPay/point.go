package agentpay

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderAgentPayService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// PointBillStats 服务仓账单
func PointBillStats(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		TimeStamp      int64  `json:"time_stamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	pointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	begin, end, err := util.MonthScopeTimestamp(req.TimeStamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	_ = pointID

	filter := bson.M{
		"agent_pay_type": bson.M{
			"$in": []int{1, 2, 5},
		},
		"agent_pay_result.pay_status": "success",
		"service_point_id":            pointID,
		"created_at": bson.M{
			"$gte": begin,
			"$lte": end,
		},
	}

	list, err := orderAgentPayService.NewOrderAgentPayService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var pointServiceAmount int

	var loadAmount int
	var deliverAmount int
	for _, pay := range list {
		if pay.AgentPayType == model.AgentPayTypeDeliver {
			deliverAmount += pay.DeliverAmount
		} else {
			loadAmount += pay.WarehouseLoadAmount
			pointServiceAmount += pay.ServiceAmount - pay.ServiceFeeAmount
		}
	}

	totalAmount := loadAmount + deliverAmount + pointServiceAmount

	r := PointBillStatsRes{
		TotalAmount:      totalAmount,
		LoadAmount:       loadAmount,
		DeliverAmount:    deliverAmount,
		ServiceFeeAmount: pointServiceAmount,
	}

	xhttp.RespSuccess(ctx, r)
}

type PointBillStatsRes struct {
	TotalAmount      int `json:"total_amount"`
	DeliverAmount    int `json:"deliver_amount"`
	LoadAmount       int `json:"load_amount"`
	ServiceFeeAmount int `json:"point_service_amount"`
}

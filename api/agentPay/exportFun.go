package agentpay

import (
	"base/model"
	"base/service/orderAgentPayService"
	"bytes"
	"fmt"
	"log"
	"strconv"
	"time"
	"unicode/utf8"

	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

type OrderList struct {
	Order      model.Order         `json:"order"`
	Debt       model.OrderDebt     `json:"debt"`
	RefundList []model.OrderRefund `json:"refund_list"`
}

type parentOrderSort []model.ParentOrder

func (array parentOrderSort) Len() int {
	return len(array)
}

func (array parentOrderSort) Less(i, j int) bool {
	return array[i].CreatedAt < array[j].CreatedAt //从小到大， 若为大于号，则从大到小
}

func (array parentOrderSort) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

func toExcel(orders []model.Order, supplierName string, mPay map[primitive.ObjectID][]model.OrderAgentPay, refunds []model.OrderRefund, debts []model.OrderDebt) (*bytes.Buffer, error) {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()
	sheetName := "Sheet1"
	// 创建一个工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		fmt.Println(err)
		return nil, err
	}

	setSheet(f, sheetName)

	i := 1

	title(f, sheetName, i)

	i += 1
	object(f, sheetName, supplierName, i)

	//orderTime(f, sheetName, i+2, res.OrderTimeBegin, res.OrderTimeEnd, exportBegin, exportEnd)
	//createTime(f, sheetName, i+3)

	i += 1
	category(f, sheetName, i)

	//mParent := make(map[primitive.ObjectID][]OrderList)
	//for _, orderList := range list {
	//	mParent[orderList.Order.ParentOrderID] = append(mParent[orderList.Order.ParentOrderID], orderList)
	//}

	//sort.Sort(parentOrderSort(parentOrders))

	//var totalProductAmount int
	//var pAllFinalAmount int
	//var totalDeliverFeeAll int
	//var totalDeliverFeeSubsidy int
	//var totalDeliverFee int
	//var totalServiceFee int
	//var totalLoadFee int

	//var setColorList []string

	var totalHasBalanceAmount int
	var totalHasBalanceUserPayAmount int
	var totalDeserveBalanceAmount int
	var totalDiffAmount int

	var agentPayTimeBegin int64
	var agentPayTimeEnd int64

	i += 1
	for orderIndex, order := range orders {
		err = f.SetCellValue(sheetName, "A"+strconv.Itoa(i), orderIndex+1)
		err = f.SetCellValue(sheetName, "AB"+strconv.Itoa(i), order.ID.Hex())
		err = f.SetColWidth(sheetName, "AB"+strconv.Itoa(i), "AB"+strconv.Itoa(i), 20)

		productNum := len(order.ProductList)
		if productNum > 1 {
			err = f.MergeCell(sheetName, "A"+strconv.Itoa(i), "A"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "B"+strconv.Itoa(i), "B"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "L"+strconv.Itoa(i), "L"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "M"+strconv.Itoa(i), "M"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "N"+strconv.Itoa(i), "N"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "O"+strconv.Itoa(i), "O"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "P"+strconv.Itoa(i), "P"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "Q"+strconv.Itoa(i), "Q"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "R"+strconv.Itoa(i), "R"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "S"+strconv.Itoa(i), "S"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "T"+strconv.Itoa(i), "T"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "U"+strconv.Itoa(i), "U"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "V"+strconv.Itoa(i), "V"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "W"+strconv.Itoa(i), "W"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "X"+strconv.Itoa(i), "X"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "Y"+strconv.Itoa(i), "Y"+strconv.Itoa(i+productNum-1))
			err = f.MergeCell(sheetName, "Z"+strconv.Itoa(i), "Z"+strconv.Itoa(i+productNum-1))
		}
		err = f.SetCellValue(sheetName, "B"+strconv.Itoa(i), time.UnixMilli(order.CreatedAt).Format("2006/01/02 15:04:05"))
		for j, p := range order.ProductList {
			productTitle := fmt.Sprintf("%s", p.ProductTitle)
			err = f.SetCellValue(sheetName, "C"+strconv.Itoa(i+j), productTitle)
			// 单价
			err = f.SetCellValue(sheetName, "D"+strconv.Itoa(i+j), dealMoney(p.Price))
			saleWay := "按件"
			if p.IsCheckWeight {
				saleWay = "称重"
			}
			err = f.SetCellValue(sheetName, "E"+strconv.Itoa(i+j), saleWay)

			err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i+j), p.Num)
			err = f.SetCellValue(sheetName, "G"+strconv.Itoa(i+j), dealMoney(p.ProductAmount))
			err = f.SetCellValue(sheetName, "H"+strconv.Itoa(i+j), dealMoney(p.ProductAmount-p.CouponSplitAmount))
			err = f.SetCellValue(sheetName, "I"+strconv.Itoa(i+j), dealMoney(p.CouponSplitAmount))

			// 发货

			//var sortRefund int
			//			var afterSaleRefund int

			//err = f.SetCellValue(sheetName, "I"+strconv.Itoa(i+j), p.SortNum)

			runeLen := utf8.RuneCountInString(productTitle)
			if runeLen < 30 {
				runeLen = 30
			}
			err = f.SetRowHeight(sheetName, i+j, float64(runeLen))

			var sortRefund int
			var afterSaleRefund int
			for _, refund := range refunds {
				if refund.OrderID == order.ID && refund.ProductID == p.ProductID {
					if refund.RefundType == model.RefundTypeQuality {
						//	品控退款-商品
						sortRefund += refund.AuditAmount
					}
					if refund.RefundType == model.RefundTypeAfterSale && refund.AuditStatus == model.AuditStatusTypePass {
						//	品控退款-商品
						afterSaleRefund += refund.AuditAmount
					}
				}
			}
			err = f.SetCellValue(sheetName, "K"+strconv.Itoa(i+j), dealMoney(sortRefund))
			err = f.SetCellValue(sheetName, "L"+strconv.Itoa(i+j), dealMoney(afterSaleRefund))

			var debtNotPaidAmount int
			var debtPaidAmount int
			for _, debt := range debts {
				if debt.OrderID == order.ID {
					for _, productDebt := range debt.ProductList {
						if productDebt.ProductID == p.ProductID {
							if debt.PayStatus != 4 {
								debtNotPaidAmount += productDebt.Amount
							}
							if debt.PayStatus == 4 {
								debtPaidAmount += productDebt.Amount
							}
						}
					}
					//fmt.Println("debt order id:", debt.OrderID.Hex())
				}
			}

			err = f.SetCellValue(sheetName, "J"+strconv.Itoa(i+j), p.CommissionPercent)
			err = f.SetCellValue(sheetName, "M"+strconv.Itoa(i+j), dealMoney(debtNotPaidAmount))
			err = f.SetCellValue(sheetName, "N"+strconv.Itoa(i+j), dealMoney(debtPaidAmount))

		}

		if order.HasAgentPay {
			err = f.SetCellValue(sheetName, "O"+strconv.Itoa(i), "已分账")
		} else {
			refundAllF := true
			for _, productOrder := range order.ProductList {
				if productOrder.IsShipRefundAll == false {
					if refundAllF {
						refundAllF = false
					}
				}
			}
			if refundAllF {
				err = f.SetCellValue(sheetName, "O"+strconv.Itoa(i), "已全退")
			} else {
				err = f.SetCellValue(sheetName, "O"+strconv.Itoa(i), "待分账")
			}
		}

		if v, ok := mPay[order.ID]; ok {
			var hasBalanceAmount int
			var adjustAmount int
			for _, pay := range v {
				if pay.AgentPayType == model.AgentPayTypeNormal {

					//	普通订单
					var serviceAmount int
					//serviceAmount = pay.ServicePointCommissionAmount + pay.PlatformAmount + pay.WarehouseCommissionAmount

					// 手续费
					productAmount := serviceAmount + pay.SupplierFeeAmount + pay.SupplierAmount
					//- pay.WarehouseLoadFeeAmount
					//+ pay.WarehouseLoadFeeAmount
					err = f.SetCellValue(sheetName, "P"+strconv.Itoa(i), dealMoney(productAmount))

					// 理论分账金额
					// 商品金额-退款额

					//hasRefundProductAmount := 0
					var orderRefundList []model.OrderRefund
					for _, refund := range refunds {
						if refund.OrderID == order.ID {
							//if refund.RefundType == model.RefundTypeAfterShip {
							//	//	品控退款-商品
							//	//hasRefundProductAmount += refund.AuditAmount
							//}
							//if refund.RefundType == model.RefundTypeAfterSale && refund.AuditStatus == model.AuditStatusTypePass {
							//	//	售后退款-商品
							//	//hasRefundProductAmount += refund.AuditAmount
							//}
							orderRefundList = append(orderRefundList, refund)
						}
					}
					//
					//deserveProductAmount := 0
					//for _, productOrder := range order.ProductList {
					//	deserveProductAmount += productOrder.ProductAmount - hasRefundProductAmount
					//}

					// TODO
					// 待处理
					//deserveTotalProductAmount, deserveProductTotalCommission, deserveTotalFee := orderAgentPayService.NewOrderAgentPayService().BackProductCommission2(order, orderRefundList)
					deserveTotalProductAmount, deserveProductTotalCommission, deserveTotalFee := 0, 0, 0
					//orderAgentPayService.NewOrderAgentPayService().BackProductCommission2(order, orderRefundList)
					_ = deserveTotalProductAmount
					_ = deserveProductTotalCommission
					_ = deserveTotalFee

					deserveFinalAmount := deserveTotalProductAmount - deserveProductTotalCommission - deserveTotalFee
					//err = f.SetCellValue(sheetName, "W"+strconv.Itoa(i), dealMoney(deserveFinalAmount))
					totalDeserveBalanceAmount += deserveFinalAmount

					// 应调账金额
					if order.CreatedAt > 1690855823000 {
						diffAmount := deserveFinalAmount - pay.SupplierAmount
						totalDiffAmount += diffAmount
						err = f.SetCellValue(sheetName, "X"+strconv.Itoa(i), dealMoney(diffAmount))
					} else {
						err = f.SetCellValue(sheetName, "X"+strconv.Itoa(i), 0)
					}

					// 实付服务费
					err = f.SetCellValue(sheetName, "Q"+strconv.Itoa(i), dealMoney(serviceAmount))
					// 理论服务费
					//err = f.SetCellValue(sheetName, "S"+strconv.Itoa(i), dealMoney(deserveProductTotalCommission))

					//deserveSupplierFeeAmount := 0
					// 调账
					//err = f.SetCellValue(sheetName, "U"+strconv.Itoa(i), dealMoney(deserveTotalFee))
					err = f.SetCellValue(sheetName, "R"+strconv.Itoa(i), dealMoney(pay.SupplierFeeAmount))

					//splitRes, _ := payModule.NewOrderS().GetOrderSplitRuleListDetailS(pays.GetOrderSplitRuleListDetailReq{
					//	BizOrderNo: pay.BizOrderNo,
					//})

					//var splitAmount, splitAmountFee int
					//for _, detail := range splitRes.SplitRuleListDetail {
					//	splitAmount += detail.Amount
					//	splitAmountFee += detail.Fee
					//}
					//actualSupplierAmount := pay.OrderSplitAmount - splitAmount - pay.SupplierFeeAmount
					//err = f.SetCellValue(sheetName, "AA"+strconv.Itoa(i), dealMoney(actualSupplierAmount))
					//err = f.SetCellValue(sheetName, "AC"+strconv.Itoa(i), dealMoney(pay.SupplierAmount-actualSupplierAmount))
					err = f.SetCellValue(sheetName, "S"+strconv.Itoa(i), dealMoney(pay.SupplierAmount))

					orderAgentPayService.NewOrderAgentPayService()

					hasBalanceAmount += pay.SupplierAmount
					totalHasBalanceUserPayAmount += pay.SupplierAmount

				}

				if pay.AgentPayType == model.AgentPayTypeDebt {
					//	补差订单
					var serviceAmount int

					//serviceAmount = pay.ServicePointCommissionAmount + pay.PlatformAmount + pay.WarehouseCommissionAmount

					err = f.SetCellValue(sheetName, "T"+strconv.Itoa(i), dealMoney(pay.OrderPaidAmount-pay.WarehouseLoadFeeAmount-pay.WarehouseTransportAmount))
					err = f.SetCellValue(sheetName, "U"+strconv.Itoa(i), dealMoney(serviceAmount))
					err = f.SetCellValue(sheetName, "V"+strconv.Itoa(i), dealMoney(pay.SupplierFeeAmount))
					err = f.SetCellValue(sheetName, "W"+strconv.Itoa(i), dealMoney(pay.SupplierAmount))
					hasBalanceAmount += pay.SupplierAmount
				}

				if pay.AgentPayType == model.AgentPayTypeAdjust {
					// 调账
					adjustAmount += pay.SupplierAmount
					hasBalanceAmount += pay.SupplierAmount
				}

				if agentPayTimeBegin == 0 {
					agentPayTimeBegin = pay.CreatedAt
				}
				if agentPayTimeEnd == 0 {
					agentPayTimeEnd = pay.CreatedAt
				}

				if agentPayTimeBegin > pay.CreatedAt {
					agentPayTimeBegin = pay.CreatedAt
				}
				if agentPayTimeEnd < pay.CreatedAt {
					agentPayTimeEnd = pay.CreatedAt
				}
				if pay.OrderSplitAmount <= 0 {
					fmt.Printf("分账金额小于等于0，id: %s,订单id：%s\n", pay.ID.Hex(), pay.OrderID.Hex())
				}
			}

			// 调账
			//if adjustAmount > 0 {
			err = f.SetCellValue(sheetName, "Y"+strconv.Itoa(i), dealMoney(adjustAmount))
			//}

			totalHasBalanceAmount += hasBalanceAmount
			err = f.SetCellValue(sheetName, "Z"+strconv.Itoa(i), dealMoney(hasBalanceAmount))
		} else {
			//	 无分账记录

		}

		i += productNum
	}

	i += 1
	err = f.SetCellValue(sheetName, "Z"+strconv.Itoa(i), dealMoney(totalHasBalanceAmount))
	err = f.SetCellValue(sheetName, "X"+strconv.Itoa(i), dealMoney(totalDiffAmount))
	//err = f.SetCellValue(sheetName, "S"+strconv.Itoa(i), dealMoney(totalHasBalanceUserPayAmount))
	//err = f.SetCellValue(sheetName, "T"+strconv.Itoa(i), dealMoney(totalDeserveBalanceAmount))
	//err = f.SetCellValue(sheetName, "T"+strconv.Itoa(i+1), dealMoney(totalHasBalanceUserPayAmount-totalDeserveBalanceAmount))

	setProductStyle(f, sheetName, 2, i)
	//setColorStyle(f, sheetName, setColorList)

	// 小计
	//resForCol(f, sheetName, i, res, totalProductAmount, totalLoadFee, pAllFinalAmount)

	//setOther(f, sheetName, i+1, totalDeliverFee, totalDeliverFeeAll, totalDeliverFeeSubsidy, totalServiceFee, pAllFinalAmount)

	//err = f.SetRowHeight(sheetName, i, 26)
	//err = f.SetCellValue(sheetName, "B"+strconv.Itoa(i), "合计")
	////err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), dealMoney(totalProductAmount))
	////err = f.SetCellValue(sheetName, "I"+strconv.Itoa(i), dealMoney(totalLoadFee))
	//err = f.SetCellValue(sheetName, "J"+strconv.Itoa(i), dealMoney(totalDeliverFee))
	//err = f.SetCellValue(sheetName, "L"+strconv.Itoa(i), dealMoney(res.TotalShipRefundAmount))
	////err = f.SetCellValue(sheetName, "L"+strconv.Itoa(i), dealMoney(res.TotalAfterSalePassAmount))
	//err = f.SetCellValue(sheetName, "N"+strconv.Itoa(i), dealMoney(pAllFinalAmount))
	//err = f.SetCellValue(sheetName, "M"+strconv.Itoa(i), dealMoney(res.TotalDebtPaidAmount+res.TotalDebtNotPaidAmount))

	// 总计
	//total(f, sheetName, i+1, res, totalProductAmount, totalLoadFee, totalDeliverFee)

	// 汇总表
	i += 4
	//summary(f, sheetName, i, res, pAllFinalAmount, totalDeliverFee)

	note(f, sheetName, i)

	f.SetActiveSheet(index)

	toBuffer, err := f.WriteToBuffer()
	if err != nil {
		log.Println(err)
		return nil, err
	}

	ts := time.Now().Format("2006-01-02 15-04-05")

	f.SaveAs(fmt.Sprintf("./api/agentPay/供应商账单-%s-%s.xlsx", supplierName, ts))

	return toBuffer, nil
}

func setColorStyle(f *excelize.File, sheetName string, cellList []string) {
	var err error
	_ = err
	for _, s := range cellList {
		style, _ := f.NewStyle(&excelize.Style{
			Font: &excelize.Font{
				Bold:   true,
				Family: "宋体",
				Size:   10,
				Color:  "#ff0000",
			},
			Border: []excelize.Border{
				{Type: "left", Color: "000000", Style: 1},
				{Type: "top", Color: "000000", Style: 1},
				{Type: "bottom", Color: "000000", Style: 1},
				{Type: "right", Color: "000000", Style: 1},
			},
			Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
		})
		_ = f.SetCellStyle(sheetName, s, s, style)
	}
}

// 其他
func setOther(f *excelize.File, sheetName string, index, totalDeliverFee, totalDeliverFeeAll, totalDeliverFeeSubsidy, totalServiceFee, pAllFinalAmount int) {
	originIndex := index
	row := strconv.Itoa(index)
	var err error

	err = f.SetCellValue(sheetName, "B"+row, "其他")

	colNum := 3
	colValueNum := 17
	// 配送费
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), convertToCol(colNum+1)+strconv.Itoa(index+1))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), convertToCol(colNum+1)+strconv.Itoa(index+2))
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "配送费")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), "配送费平台补贴")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), "配送费实付")
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(totalDeliverFeeAll))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+1), -1*dealMoney(totalDeliverFeeSubsidy))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+2), dealMoney(totalDeliverFee))

	// 服务费
	index += 3
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), convertToCol(colNum+1)+strconv.Itoa(index+1))
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), convertToCol(colNum+1)+strconv.Itoa(index+2))
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "服务费")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+1), "服务费平台补贴")
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index+2), "服务费实付")
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(totalServiceFee))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+1), -1*dealMoney(totalServiceFee))
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index+2), 0)

	// 优惠券
	index += 3
	err = f.MergeCell(sheetName, convertToCol(colNum)+strconv.Itoa(index), convertToCol(colNum+1)+strconv.Itoa(index))
	err = f.SetCellValue(sheetName, convertToCol(colNum)+strconv.Itoa(index), "优惠券使用")
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), 0)

	//index += 1
	err = f.MergeCell(sheetName, "B"+strconv.Itoa(originIndex), "B"+strconv.Itoa(index))
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
	//err = f.MergeCell(sheetName, "B"+strconv.Itoa(index+1), "B"+strconv.Itoa(index))
	//if err != nil {
	//	zap.S().Errorf("%v", err.Error())
	//}

	index += 1
	err = f.SetCellValue(sheetName, "B"+strconv.Itoa(index), "合计")
	finalAmount := pAllFinalAmount + totalDeliverFee
	err = f.SetCellValue(sheetName, convertToCol(colValueNum)+strconv.Itoa(index), dealMoney(finalAmount))
	//
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+strconv.Itoa(originIndex), convertToCol(colValueNum)+strconv.Itoa(index), style)

	style2, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   12,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})

	err = f.SetCellStyle(sheetName, "A"+strconv.Itoa(index), convertToCol(colValueNum)+strconv.Itoa(index), style2)
	//zap.S().Info("originIndex::", originIndex)
	//err = f.SetRowHeight(sheetName, originIndex, 20)
	//err = f.SetRowHeight(sheetName, originIndex+1, 20)
	//err = f.SetRowHeight(sheetName, originIndex+2, 20)
	//err = f.SetRowHeight(sheetName, originIndex+3, 20)
	//err = f.SetRowHeight(sheetName, originIndex+4, 20)
	//err = f.SetRowHeight(sheetName, originIndex+5, 20)
	//err = f.SetRowHeight(sheetName, originIndex+6, 20)
	//err = f.SetRowHeight(sheetName, colValueNum+7, 20)
	//err = f.SetRowHeight(sheetName, colValueNum+8, 26)
}

// 商品名称
func setProductStyle(f *excelize.File, sheetName string, begin, end int) {
	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "left", Vertical: "center", WrapText: true},
	})

	_ = err

	cell1 := strconv.Itoa(begin)
	cell2 := strconv.Itoa(end)
	err = f.SetCellStyle(sheetName, "A"+cell1, "Z"+cell2, style)
}

//
//// 小计
//func resForCol(f *excelize.File, sheetName string, index int, res CalcRes, totalProductAmount, totalLoadFee, pAllFinalAmount int) {
//	row := strconv.Itoa(index)
//	//row2 := strconv.Itoa(index + 1)
//
//	var err error
//	_ = err
//
//	err = f.SetRowHeight(sheetName, index, 26)
//	err = f.SetCellValue(sheetName, "B"+row, "小计")
//	err = f.SetCellValue(sheetName, "H"+row, dealMoney(totalProductAmount))
//	err = f.SetCellValue(sheetName, "J"+row, dealMoney(totalLoadFee))
//	err = f.SetCellValue(sheetName, "N"+row, dealMoney(res.TotalShipRefundAmount))
//	err = f.SetCellValue(sheetName, "O"+row, dealMoney(res.TotalAfterSalePassAmount))
//	err = f.SetCellValue(sheetName, "P"+row, dealMoney(res.TotalDebtPaidAmount+res.TotalDebtNotPaidAmount))
//	err = f.SetCellValue(sheetName, "Q"+row, dealMoney(pAllFinalAmount))
//
//	style, err := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{
//			Bold:   true,
//			Family: "宋体",
//			Size:   12,
//		},
//		Fill: excelize.Fill{
//			Type:    "pattern",
//			Color:   []string{"#e1e1e1"},
//			Pattern: 1,
//		},
//		Border: []excelize.Border{
//			{Type: "left", Color: "000000", Style: 1},
//			{Type: "top", Color: "000000", Style: 1},
//			{Type: "bottom", Color: "000000", Style: 1},
//			{Type: "right", Color: "000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
//	})
//
//	err = f.SetCellStyle(sheetName, "A"+row, "Q"+row, style)
//
//	err = f.SetRowHeight(sheetName, index, 20)
//
//}
//
//// 汇总
//func summary(f *excelize.File, sheetName string, i int, res CalcRes, pAllFinalAmount, totalDeliverFee int) {
//	var err error
//	_ = err
//	err = f.SetCellValue(sheetName, "F"+strconv.Itoa(i), "汇总：")
//
//	j := i + 1
//	err = f.SetCellValue(sheetName, "G"+strconv.Itoa(j), "支付金额")
//	err = f.SetCellValue(sheetName, "G"+strconv.Itoa(j+1), dealMoney(res.TotalAmount+totalDeliverFee))
//	err = f.SetCellValue(sheetName, "H"+strconv.Itoa(j), "退款金额")
//	err = f.SetCellValue(sheetName, "H"+strconv.Itoa(j+1), dealMoney(res.TotalShipRefundAmount))
//	//err = f.SetCellValue(sheetName, "I"+strconv.Itoa(j), "商品合计")
//	//err = f.SetCellValue(sheetName, "I"+strconv.Itoa(j+1), dealMoney(pAllFinalAmount))
//	//err = f.SetCellValue(sheetName, "J"+strconv.Itoa(j), "配送费")
//	//err = f.SetCellValue(sheetName, "J"+strconv.Itoa(j+1), dealMoney(totalDeliverFee))
//	err = f.SetCellValue(sheetName, "I"+strconv.Itoa(j), "补差金额")
//	err = f.SetCellValue(sheetName, "I"+strconv.Itoa(j+1), dealMoney(res.TotalDebtPaidAmount+res.TotalDebtNotPaidAmount))
//	err = f.SetCellValue(sheetName, "J"+strconv.Itoa(j), "实际金额")
//
//	final := res.TotalFinalAmount + totalDeliverFee
//	err = f.SetCellValue(sheetName, "J"+strconv.Itoa(j+1), dealMoney(final))
//
//	err = f.SetRowHeight(sheetName, i, 30)
//	err = f.SetRowHeight(sheetName, j, 30)
//	err = f.SetRowHeight(sheetName, j+1, 30)
//
//	style1, err := f.NewStyle(&excelize.Style{
//		Font: &excelize.Font{
//			Bold:   true,
//			Family: "宋体",
//			Size:   10,
//		},
//		Border: []excelize.Border{
//			{Type: "left", Color: "000000", Style: 1},
//			{Type: "top", Color: "000000", Style: 1},
//			{Type: "bottom", Color: "000000", Style: 1},
//			{Type: "right", Color: "000000", Style: 1},
//		},
//		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
//	})
//	err = f.SetCellStyle(sheetName, "F"+strconv.Itoa(i), "J"+strconv.Itoa(j+1), style1)
//
//}

func float64Ptr(f float64) *float64 { return &f }
func boolPtr(f bool) *bool          { return &f }

func setSheet(f *excelize.File, sheetName string) {
	opts := excelize.PageLayoutMarginsOptions{
		Bottom: float64Ptr(0.22),
		Footer: float64Ptr(0.2),
		Header: float64Ptr(0.2),
		Left:   float64Ptr(0.14),
		Right:  float64Ptr(0.14),
		Top:    float64Ptr(0.22),
	}
	err := f.SetPageMargins(sheetName, &opts)
	if err != nil {
		zap.S().Info(err)
	}
	err = f.SetAppProps(&excelize.AppProperties{
		Application:       "Microsoft Excel",
		ScaleCrop:         true,
		DocSecurity:       3,
		Company:           "Company Name",
		LinksUpToDate:     true,
		HyperlinksChanged: true,
		AppVersion:        "16.0000",
	})
	_ = err

	err = f.SetSheetProps(sheetName, &excelize.SheetPropsOptions{
		FitToPage: boolPtr(true), // 开启自适应页面打印，默认值为 false
	})
	_ = err

	if err != nil {
		log.Println(err)
	}
}

func createTime(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	ts := time.Now().Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "E"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "生成时间："+ts)

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

func note(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	err := f.SetCellValue(sheetName, "B"+row, "备注说明：")

	err = f.MergeCell(sheetName, "C"+strconv.Itoa(index+1), "M"+strconv.Itoa(index+1))
	err = f.SetCellValue(sheetName, "C"+strconv.Itoa(index+1), "1. 调账所得为负时，平台不会回退;为正，平台将金额调账至供应商账户余额")

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   9,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		//Alignment: &excelize.Alignment{Horizontal: "left", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "M"+strconv.Itoa(index+1), style)

	_ = err

}

func orderTime(f *excelize.File, sheetName string, index int, min, max, exportBegin, exportEnd int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "Q"+row)
	if err != nil {

	}

	exportBeginFormat := time.UnixMilli(exportBegin).Format("2006/01/02 15:04:05")
	exportEndFormat := time.UnixMilli(exportEnd).Format("2006/01/02 15:04:05")

	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("订单区间：[ %s , %s ], 导出区间：[ %s , %s ]", minT, MaxT, exportBeginFormat, exportEndFormat))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

// 导出区间
func exportTime(f *excelize.File, sheetName string, index int, min, max int64) {
	row := strconv.Itoa(index)
	minT := time.UnixMilli(min).Format("2006/01/02 15:04:05")
	MaxT := time.UnixMilli(max).Format("2006/01/02 15:04:05")
	err := f.MergeCell(sheetName, "B"+row, "H"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, fmt.Sprintf("导出区间：[ %s , %s ]", minT, MaxT))

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		//Border: []excelize.Border{
		//	{Type: "left", Color: "000000", Style: 1},
		//	{Type: "top", Color: "000000", Style: 1},
		//	{Type: "bottom", Color: "000000", Style: 1},
		//	{Type: "right", Color: "000000", Style: 1},
		//},
		//Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	err = f.SetCellStyle(sheetName, "B"+row, "B"+row, style)

}

//
//func total(f *excelize.File, sheetName string, index int, res CalcRes, totalProductAmount, totalLoadFee, totalDeliverFee int) {
//	err := f.SetRowHeight(sheetName, index, 26)
//	if err != nil {
//		zap.S().Errorf("%v", err.Error())
//	}
//	row := strconv.Itoa(index)
//	name := "总计"
//	//if res.TotalDebtNotPaidAmount != 0 {
//	//	name = "总计（含未支付补差）"
//	//}
//
//	err = f.SetCellValue(sheetName, "B"+row, name)
//
//	amount := res.TotalFinalAmount + totalDeliverFee
//	err = f.SetCellValue(sheetName, "M"+row, dealMoney(amount))
//
//}

func object(f *excelize.File, sheetName, name string, index int) {
	row := strconv.Itoa(index)
	err := f.MergeCell(sheetName, "B"+row, "E"+row)
	if err != nil {

	}
	err = f.SetCellValue(sheetName, "B"+row, "供应商："+name)
	err = f.SetRowHeight(sheetName, 2, 24)
	timeStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   18,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
	})
	_ = err

	err = f.SetCellStyle(sheetName, "A"+row, "C"+row, timeStyle)

}

func category(f *excelize.File, sheetName string, index int) {
	row := strconv.Itoa(index)
	//row2 := strconv.Itoa(index + 1)
	var err error

	// 索引
	//err = f.SetCellValue(sheetName, convertToCol(index)+row, "")
	//err = f.SetColWidth(sheetName, convertToCol(index), convertToCol(index), 3)

	colNum := 2

	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 14)
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "下单时间")

	// 商品
	colNum += 1
	err = f.SetColWidth(sheetName, convertToCol(colNum), convertToCol(colNum), 28)
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "商品")

	// 订单信息
	colNum += 1
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "单价\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+1)+row, "计价\n方式")
	err = f.SetCellValue(sheetName, convertToCol(colNum+2)+row, "订单数量\n（件）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+3)+row, "订单金额\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+4)+row, "实付金额\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+5)+row, "代金券\n（元）")

	colNum += 6
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "服务费\n（%）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+1)+row, "品控退款\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+2)+row, "售后退款\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+3)+row, "补差未付\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+4)+row, "补差已付\n（元）")

	colNum += 5
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "分账状态\n")

	colNum += 1
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "实付分账金额\n（元）")
	//err = f.SetCellValue(sheetName, convertToCol(colNum+2)+row, "理论分账金额\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+1)+row, "实付服务费\n（元）")
	//err = f.SetCellValue(sheetName, convertToCol(colNum+4)+row, "理论服务费\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+2)+row, "实付手续费\n（元）")
	//err = f.SetCellValue(sheetName, convertToCol(colNum+6)+row, "理论手续费\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+3)+row, "实付分账所得\n（元）")
	//err = f.SetCellValue(sheetName, convertToCol(colNum+8)+row, "理论分账所得\n（元）")

	colNum += 4
	err = f.SetCellValue(sheetName, convertToCol(colNum)+row, "补差分账\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+1)+row, "补差服务费\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+2)+row, "补差手续费\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+3)+row, "补差分账所得\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+4)+row, "应调账\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+5)+row, "实际调账\n（元）")
	err = f.SetCellValue(sheetName, convertToCol(colNum+6)+row, "总分账\n（元）")

	//for i := 0; i < 11; i++ {
	//	err = f.SetColWidth(sheetName, convertToCol(4+i), convertToCol(4+i), 9)
	//}

	style, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   10,
		},
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#e1e1e1"},
			Pattern: 1,
		},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	})
	_ = style

	//err = f.SetCellStyle(sheetName, "A"+row, convertToCol(colNum+11)+row, style)

	//style2, err := f.NewStyle(&excelize.Style{
	//	Font: &excelize.Font{
	//		Bold:   true,
	//		Family: "宋体",
	//		Size:   10,
	//	},
	//	Fill: excelize.Fill{
	//		Type:    "pattern",
	//		Color:   []string{"#e1e1e1"},
	//		Pattern: 1,
	//	},
	//	Border: []excelize.Border{
	//		{Type: "left", Color: "000000", Style: 1},
	//		{Type: "top", Color: "000000", Style: 1},
	//		{Type: "bottom", Color: "000000", Style: 1},
	//		{Type: "right", Color: "000000", Style: 1},
	//	},
	//	Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center", WrapText: true},
	//})
	//
	//err = f.SetCellStyle(sheetName, "F"+row2, "P"+row2, style2)

	err = f.SetRowHeight(sheetName, index, 40)
	//err = f.SetRowHeight(sheetName, index+1, 26)

	_ = err
}

func dealMoney(amount int) float64 {
	f, exact := decimal.NewFromInt(int64(amount)).Div(decimal.NewFromInt(100)).Round(2).Float64()

	_ = exact

	return f
}

func dealWeight(w int) float64 {
	f, exact := decimal.NewFromInt(int64(w)).Div(decimal.NewFromInt(1000)).Round(1).Float64()

	_ = exact

	return f
}

func convertToCol(columnNumber int) string {
	var res []byte
	for columnNumber > 0 {
		a := columnNumber % 26
		if a == 0 {
			a = 26
		}
		res = append(res, 'A'+byte(a-1))
		columnNumber = (columnNumber - a) / 26
	}
	// 上面输出的res是反着的，前后交换
	for i, n := 0, len(res); i < n/2; i++ {
		res[i], res[n-1-i] = res[n-1-i], res[i]
	}
	return string(res)
}

func title(f *excelize.File, sheetName string, index int) {
	_ = index
	err := f.MergeCell(sheetName, "A1", "Z1")
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	err = f.SetRowHeight(sheetName, 1, 30)

	err = f.SetCellValue(sheetName, "A1", "账单")
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}

	titleStyle, err := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{
			Bold:   true,
			Family: "宋体",
			Size:   24,
		},
		Alignment: &excelize.Alignment{Horizontal: "center", Vertical: "center"},
	})
	if err != nil {

	}
	err = f.SetCellStyle(sheetName, "A1", "A1", titleStyle)
	if err != nil {
		zap.S().Errorf("%v", err.Error())
	}
}

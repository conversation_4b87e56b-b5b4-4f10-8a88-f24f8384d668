package agentpay

import (
	"base/core/xhttp"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"base/service/orderAgentPayService"
	"base/service/orderDebtService"
	"base/service/orderRefundService"
	"base/service/orderService"
	"base/util"
	"context"
	"encoding/json"
	"fmt"
	pays "github.com/cnbattle/allinpay/service"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

func Export(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		//"order_id": orderID,
		"created_at": bson.M{
			//"$gte": 1693497600000, // 0901
			//"$lte": 1696953600000, // 1011
			//"$gte": 1696089600000, // 1001
			//"$lte": 1697299200000, // 1015
			"$lte": 1711900800000, // 240401
			//"$lte": 1697385600000, // 1016
			//"$lte": 1696089599000, // 0930
			//"$lte": 1697767823000, // 1020
			//"$lte": 1694707200000, // 0915
			//"$gte": 1690819200000, // 0801
			//"$lte": 1693497599000, // 0831
			"$gte": 1709222400000, // 240301
			//"$gte": 1689004745000, // 0710
			//"$gte": 1696089600000, // 1001
			//"$lte": 1690819199000, // 0731
			//"$lte": 1689004745000, // 0710
			//"$lte": 1697385545000, // 2023-10-15 23:59:05
		},
		//"order_status": model.OrderStatusTypeFinish,
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToReceive,
		},
		"supplier_id": supplierID,
	}

	orders, err := orderService.NewOrderService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var orderIDs []primitive.ObjectID
	for _, o := range orders {
		orderIDs = append(orderIDs, o.ID)
	}

	refunds := make([]model.OrderRefund, 0)
	// 查询退款
	if len(orderIDs) > 0 {
		refunds, err = orderRefundService.NewOrderRefundService().List(ctx, bson.M{
			"order_id": bson.M{"$in": orderIDs},
			//"refund_type": model.RefundTypeAfterSale,
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}
	_ = refunds

	debts := make([]model.OrderDebt, 0)
	// 查询补差
	if len(orderIDs) > 0 {
		debts, err = orderDebtService.NewOrderDebtService().List(ctx, bson.M{"order_id": bson.M{"$in": orderIDs}})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}
	_ = debts

	filterAgentPay := bson.M{
		//"order_status": model.OrderStatusTypeFinish,
		"order_id": bson.M{
			"$in": orderIDs,
		},
	}

	payList, err := orderAgentPayService.NewOrderAgentPayService().List(ctx, filterAgentPay)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	// xhttp.RespSuccess(ctx, list)

	//	 计算

	mPay := make(map[primitive.ObjectID][]model.OrderAgentPay)

	var UnPayTotalAmount int
	var PayRefundTotalAmount int

	authentication, err := authenticationService.NewAuthenticationService().GetBySupplier(ctx, supplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	_ = authentication

	rdb := global.RDBDefault
	key := "payQuery:"
	_ = rdb
	_ = key

	total := len(payList)
	var end int64
	for index, pay := range payList {

		if pay.CreatedAt > end {
			end = pay.CreatedAt
		}

		mPay[pay.OrderID] = append(mPay[pay.OrderID], pay)

		if pay.AgentPayType == model.AgentPayTypeDebt || pay.AgentPayType == model.AgentPayTypeAdjust {
			continue
		}
		//if pay.AgentPayType != model.AgentPayTypeDebt {
		//	continue
		//}

		fmt.Printf("查询进度%d/%d\n", index+1, total)
		//err = orderAgentPayService.NewOrderAgentPayService().CheckSupplierAmount(ctx, pay.BizOrderNo)
		//if err != nil {
		//
		//}
		//continue

		var res pays.GetPaymentInformationDetailRes

		if rdb.Exists(context.Background(), key+pay.OriBizOrderNo).Val() > 0 {
			//	 取值
			//fmt.Println("减少查询")
			bytes, _ := rdb.Get(context.Background(), key+pay.OriBizOrderNo).Bytes()
			json.Unmarshal(bytes, &res)
			rdb.Expire(context.Background(), key+pay.OriBizOrderNo, time.Minute*5)

		} else {
			//	 查询
			res, err = payModule.NewOrderS().GetPaymentInformationDetailS(pays.GetPaymentInformationDetailReq{
				BizOrderNo: pay.OriBizOrderNo,
			})
			if err != nil {
				return
			}
			marshal, err := json.Marshal(res)
			if err != nil {
				return
			}
			rdb.Set(context.Background(), key+pay.OriBizOrderNo, string(marshal), time.Minute*4)
		}

		for _, info := range res.ReceiverInfoList {
			if info.ReceiverId == authentication.PayBizUserId {
				if info.PayRefundAmount != 0 {
					fmt.Println("已代付退款总金额")
					PayRefundTotalAmount += info.PayRefundAmount
				}
			}
		}

		if res.UnPayTotalAmount != 0 {
			for _, info := range res.ReceiverInfoList {
				if info.ReceiverId == authentication.PayBizUserId {
					UnPayTotalAmount += info.UnPayAmount

					if info.UnPayAmount != 0 {
						var createdAtFmt string
						for _, o := range orders {
							if pay.OrderID == o.ID {
								createdAtFmt = time.UnixMilli(o.CreatedAt).Format(time.RFC3339)
							}
						}
						fmt.Printf("查询进度%d/%d\n", index+1, total)
						fmt.Printf("代付还存在未代付,下单时间：%s，订单ID：%s,bizOrderNo:%s,orderNo:%s\n", createdAtFmt, pay.OrderID.Hex(), pay.OriBizOrderNo, res.CollOrderNo)
						fmt.Printf("金额::::%d\n", info.UnPayAmount)

						//if pay.OrderID.Hex() == "6515ac475d687512f78da04d" {
						//	continue
						//}
						//orderAgentPayService.NewOrderAgentPayService().SingleOrderAgentPayAdjust(ctx, pay.OrderID)
						//time.Sleep(time.Second * 2)
					}
				}
			}
		}
		if res.PayRefundTotalAmount != 0 {
			fmt.Println("已代付退款总金额")
		}
		_ = res

	}

	//return

	//supplier, err := supplierService.NewSupplierService().Get(ctx, supplierID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//_ = supplier
	//
	//fmt.Println("--------------------------")
	//_ = end
	////fmt.Printf("pay end::%d\n", end)
	fmt.Printf("UnPayTotalAmount::%d\n", UnPayTotalAmount)
	fmt.Printf("PayRefundTotalAmount::%d\n", PayRefundTotalAmount)
	//fmt.Println("--------------------------")
	//
	//// 导出
	//toExcel(orders, supplier.ShopSimpleName, mPay, refunds, debts)

}

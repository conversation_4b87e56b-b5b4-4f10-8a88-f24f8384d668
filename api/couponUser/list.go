package couponUser

import (
	"base/core/xhttp"
	"base/model"
	"base/service/couponUserService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// List 代金券账户列表
func List(ctx *gin.Context) {
	var req = struct {
		BuyerID      string                 `json:"buyer_id"`
		CouponStatus model.CouponStatusType `json:"coupon_status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"buyer_id":      buyerID,
		"coupon_status": req.CouponStatus,
		"deleted_at":    0,
	}
	list, err := couponUserService.NewCouponUserService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)
}

// ListByStock 代金券批次的使用情况
func ListByStock(ctx *gin.Context) {
	var req = struct {
		CouponStockID string `json:"coupon_stock_id"`
		Page          int64  `json:"page"`
		Limit         int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	couponStockID, err := util.ConvertToObjectWithCtx(ctx, req.CouponStockID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"coupon_stock_id": couponStockID,
	}
	list, count, err := couponUserService.NewCouponUserService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}

package couponUser

import (
	"base/core/xhttp"
	"base/service/couponUserService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// SendCoupon 发放优惠券
func SendCoupon(ctx *gin.Context) {
	var req = struct {
		BuyerID       string `json:"buyer_id"  validate:"len=24"`
		CouponStockID string `json:"coupon_stock_id"  validate:"len=24"`
		Remark        string `json:"remark"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.CouponStockID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = couponUserService.NewCouponUserService().Create(ctx, id, buyerID, req.Remark)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

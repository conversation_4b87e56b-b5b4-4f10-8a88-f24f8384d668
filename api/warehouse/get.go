package warehouse

import (
	"base/core/xhttp"
	"base/service/authenticationService"
	"base/service/multiUserService"
	"base/service/warehouseService"
	"base/types"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

func Get(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	i, err := warehouseService.NewWarehouseServiceService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	authentication, err := authenticationService.NewAuthenticationService().GetByWarehouse(ctx, i.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r := types.WarehouseRes{
		Warehouse:      i,
		IsMobileVerify: authentication.IsMobileVerify,
		PayMobile:      authentication.Mobile,
	}

	xhttp.RespSuccess(ctx, r)
}

// GetByUser 查询申请信息
func GetByUser(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	uid, err := util.ConvertToObject(req.UserID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	i, err := warehouseService.NewWarehouseServiceService().GetByUser(ctx, uid)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespErr(ctx, err)
		return
	}

	if errors.Is(err, mongo.ErrNoDocuments) {
		byUserAndObject, err := multiUserService.NewMultiUserService().GetByUserAndObject(ctx, uid)
		if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
			xhttp.RespErr(ctx, err)
			return
		}
		if byUserAndObject.ID != primitive.NilObjectID {
			//i.ID = byUserAndObject.ObjectID
			i, err = warehouseService.NewWarehouseServiceService().Get(ctx, byUserAndObject.ObjectID)
			if err != nil {
				xhttp.RespErr(ctx, err)
				return
			}
		}
	}

	authentication, err := authenticationService.NewAuthenticationService().GetByWarehouse(ctx, i.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	r := types.WarehouseRes{
		Warehouse:      i,
		IsMobileVerify: authentication.IsMobileVerify,
		PayMobile:      authentication.Mobile,
	}

	xhttp.RespSuccess(ctx, r)

}

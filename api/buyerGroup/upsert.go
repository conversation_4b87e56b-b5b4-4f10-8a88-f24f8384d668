package buyerGroup

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerGroupService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Upsert 采购商交流群
func Upsert(ctx *gin.Context) {
	var req = struct {
		ID            string              `json:"id"`
		Title         string              `json:"title"`
		Img           model.FileInfo      `json:"img"`
		ConditionType model.ConditionType `json:"condition_type"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var id primitive.ObjectID
	if len(req.ID) == 24 {
		id, err = util.ConvertToObjectWithNote(req.ID, "")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	err = buyerGroupService.NewBuyerGroupService().Upsert(ctx, id, req.Title, req.Img, req.ConditionType)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

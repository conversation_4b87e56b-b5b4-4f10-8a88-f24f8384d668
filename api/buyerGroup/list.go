package buyerGroup

import (
	"base/core/xhttp"
	"base/service/buyerGroupService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func List(ctx *gin.Context) {
	//env, err := xhttp.GetEnv(ctx)
	//if env != model.ObjectTypeBuyer {
	//	xhttp.RespSuccess(ctx, nil)
	//	return
	//}
	//userID, err := xhttp.UserID(ctx)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//buyer, err := buyerService.NewBuyerService().GetByUserID(ctx, userID)
	//if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//at := time.Now().Add(-time.Hour * 24 * 6).UnixMilli()
	//filter := bson.M{
	//	"updated_at": bson.M{
	//		"$gte": at,
	//	},
	//}
	//
	//if errors.Is(err, mongo.ErrNoDocuments) {
	//	//	 不存在
	//	filter["condition_type"] = model.ConditionTypeNotBuyer
	//	err = nil
	//} else {
	//	if buyer.AuditStatus == model.AuditStatusTypePass {
	//		//	已通过
	//		filter["condition_type"] = model.ConditionTypeBuyerPassed
	//	}
	//}
	//
	//list, err := buyerGroupService.NewBuyerGroupService().List(ctx, filter)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//xhttp.RespSuccess(ctx, list)
}

func ListAll(ctx *gin.Context) {
	filter := bson.M{}
	list, err := buyerGroupService.NewBuyerGroupService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, list)
}

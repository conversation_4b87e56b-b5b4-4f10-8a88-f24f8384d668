package invite

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/service/inviteService"
	"base/service/userService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ListRecord 邀请记录
func ListRecord(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	if len(req.UserID) < 24 {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "参数user_id缺失"))
		return
	}

	buyerID, err := util.ConvertToObjectWithNote(req.UserID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"inviter_user_id": buyerID,
	}

	records, err := inviteService.NewInviteService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, record := range records {
		ids = append(ids, record.InvitedUserID)
	}
	ids = append(ids, buyerID)

	list := make([]inviteRecord, 0, len(records))

	if len(ids) > 0 {
		users, err := userService.NewUserService().ListByIDs(ctx, ids)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		for _, record := range records {
			var invited string
			for _, u := range users {
				if record.InvitedUserID == u.ID {
					invited = util.DealMobile(u.Mobile)
				}
			}
			item := inviteRecord{
				Invite:            record,
				InvitedUserMobile: invited,
			}
			list = append(list, item)
		}
	}

	xhttp.RespSuccess(ctx, list)
}

type inviteRecord struct {
	model.Invite
	InvitedUserMobile string `json:"invited_user_mobile"`
}

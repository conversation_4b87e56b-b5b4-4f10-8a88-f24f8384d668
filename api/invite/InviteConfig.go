package invite

import (
	"base/core/xhttp"
	"base/model"
	"base/service/inviteService"
	"github.com/gin-gonic/gin"
	"time"
)

// GetInviteConfig 邀请配置
func GetInviteConfig(ctx *gin.Context) {
	data, err := inviteService.NewInviteService().GetInviteConfig(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

// UpdateInviteConfig 更新邀请配置
func UpdateInviteConfig(ctx *gin.Context) {
	var req = struct {
		IsOpen    bool  `json:"is_open"`
		TimeBegin int64 `json:"time_begin"`
		TimeEnd   int64 `json:"time_end"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	now := time.Now().UnixMilli()
	data := model.InviteConfig{
		IsOpen:    req.<PERSON><PERSON>,
		TimeBegin: req.TimeBegin,
		TimeEnd:   req.TimeEnd,
		UpdatedAt: now,
	}
	err = inviteService.NewInviteService().UpdateInviteConfig(ctx, data)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package profit

import (
	"base/core/xhttp"
	"base/model"
	"base/service/profitService"
	"base/service/supplierService"
	"base/types"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// SettleProfit 结算利润
func SettleProfit(ctx *gin.Context) {
	var req = struct {
		SupplierID     primitive.ObjectID `json:"supplier_id"`
		Amount         int                `json:"amount"`
		Remark         string             `json:"remark"`
		MonthTimestamp int64              `json:"month_timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	begin, _, err := util.MonthScopeTimestamp(req.MonthTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = profitService.NewProfitService().SettleProfit(ctx, req.SupplierID, req.Amount, req.Remark, begin)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

// ListSettlement 结算利润列表
func ListSettlement(ctx *gin.Context) {
	var req = struct {
		Page           int64 `json:"page"`
		Limit          int64 `json:"limit"`
		MonthTimestamp int64 `json:"month_timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	begin, _, err := util.MonthScopeTimestamp(req.MonthTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, total, err := profitService.NewProfitService().ListSettlement(ctx, begin, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, settlement := range data {
		ids = append(ids, settlement.SupplierID)
	}

	suppliers, err := supplierService.NewSupplierService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplierMap := make(map[primitive.ObjectID]model.Supplier)
	for _, supplier := range suppliers {
		supplierMap[supplier.ID] = supplier
	}

	var res []types.ProfitSettlementRes
	for _, settlement := range data {
		res = append(res, types.ProfitSettlementRes{
			ID:           settlement.ID,
			SupplierID:   settlement.SupplierID,
			SupplierName: supplierMap[settlement.SupplierID].ShopSimpleName,
			Amount:       settlement.Amount,
			Remark:       settlement.Remark,
			CreatedAt:    settlement.CreatedAt,
		})
	}

	xhttp.RespSuccessList(ctx, res, total)
}

// ListSettlementBySupplier 结算利润列表
func ListSettlementBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID     primitive.ObjectID `json:"supplier_id"`
		MonthTimestamp int64              `json:"month_timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	begin, _, err := util.MonthScopeTimestamp(req.MonthTimestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := profitService.NewProfitService().ListSettlementBySupplier(ctx, req.SupplierID, begin)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

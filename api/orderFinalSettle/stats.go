package orderFinalSettle

import (
	"base/core/xhttp"
	"base/service/orderFinalSettleService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// GetMonthlyStats 获取月度最终结算统计
func GetMonthlyStats(c *gin.Context) {
	var req struct {
		SupplierID     string `json:"supplier_id"`
		MonthTimestamp int64  `json:"month_timestamp"` // 开始时间戳
	}

	err := xhttp.Parse(c, &req)
	if err != nil {
		return
	}

	// 构建查询条件
	filter := bson.M{}

	// 供应商ID过滤
	supplierID, err := util.ConvertToObjectWithCtx(c, req.SupplierID)
	if err != nil {
		xhttp.RespErr(c, err)
		return
	}
	filter["supplier_id"] = supplierID

	// 时间范围过滤
	beginTimestamp, endTimestamp, err := util.MonthScopeTimestamp(req.MonthTimestamp)
	if err != nil {
		xhttp.RespErr(c, err)
		return
	}
	filter["order_created_at"] = bson.M{
		"$gte": beginTimestamp,
		"$lte": endTimestamp,
	}

	// 获取最终结算记录列表
	list, err := orderFinalSettleService.NewService().List(c, filter)
	if err != nil {
		xhttp.RespErr(c, err)
		return
	}

	var stats stats

	for _, settle := range list {
		stats.TotalProductAmount += settle.TotalProductAmount
		stats.TotalProductBuyPriceAmount += settle.TotalProductBuyPriceAmount
		stats.TotalAdjustSettleAmount += settle.TotalAdjustSettleAmount
		stats.TotalQualityRefundAmount += settle.TotalQualityRefundAmount
		stats.TotalAfterSaleRefundAmount += settle.TotalAfterSaleRefundAmount
		stats.TotalProfitAmount += settle.TotalFinalProfit
	}

	xhttp.RespSuccess(c, stats)
}

type stats struct {
	TotalProductAmount         int `json:"total_product_amount"`           // 总商品金额
	TotalAfterSaleRefundAmount int `json:"total_after_sale_refund_amount"` // 总售后退款金额
	TotalQualityRefundAmount   int `json:"total_quality_refund_amount"`    // 总品控退款金额
	TotalDebtAmount            int `json:"total_debt_amount"`              // 总补差金额
	TotalProfitAmount          int `json:"total_profit_amount"`            // 总利润金额
	TotalProductBuyPriceAmount int `json:"total_product_buy_price_amount"` // 总商品采购价金额
	TotalAdjustSettleAmount    int `json:"total_adjust_settle_amount"`     // 总调价结算金额
}

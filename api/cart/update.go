package cart

import (
	"base/core/xhttp"
	"base/service/cartService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Upsert 更新购物车
func Upsert(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id"`
		BuyerID   string `json:"buyer_id" `
		SkuIDCode string `json:"sku_id_code"` // sku编号
		Count     int    `json:"count" `      // 商品数量
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = cartService.NewCartService().Upsert(ctx, buyerID, productID, req.SkuIDCode, req.Count)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

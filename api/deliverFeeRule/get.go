package deliverFeeRule

import (
	"base/core/xhttp"
	"base/service/deliverFeeRuleService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func GetByServicePoint(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	servicePointID, err := util.ConvertToObjectWithNote(req.ServicePointID, "")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	loadFee, err := deliverFeeRuleService.NewDeliverFeeRuleService().GetByServicePoint(ctx, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, loadFee)
}

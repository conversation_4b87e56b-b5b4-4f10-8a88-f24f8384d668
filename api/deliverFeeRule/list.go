package deliverFeeRule

import (
	"base/core/xhttp"
	"base/model"
	"base/service/deliverFeeRuleService"
	"base/service/servicePointService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func List(ctx *gin.Context) {
	filter := bson.M{
		"deleted_at": 0,
		"is_open":    true,
	}
	points, err := servicePointService.NewServicePointService().ListCus(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	rules, err := deliverFeeRuleService.NewDeliverFeeRuleService().List(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var list []res
	for _, point := range points {
		var data model.DeliverFeeRule
		for _, rule := range rules {
			if rule.ServicePointID == point.ID {
				data = rule
			}
		}

		item := res{
			ServicePointID: point.ID,
			ServiceName:    point.Name,
			DeliverFeeRule: data,
		}
		list = append(list, item)
	}

	xhttp.RespSuccess(ctx, list)
}

type res struct {
	model.DeliverFeeRule
	ServicePointID primitive.ObjectID `json:"service_point_id"`
	ServiceName    string             `json:"service_name"`
}

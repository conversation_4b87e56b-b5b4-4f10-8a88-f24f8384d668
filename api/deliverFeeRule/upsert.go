package deliverFeeRule

import (
	"base/core/xhttp"
	"base/service/deliverFeeRuleService"
	"base/types"
	"github.com/gin-gonic/gin"
)

// Upsert 配送费
func Upsert(ctx *gin.Context) {
	var req types.DeliverFeeRuleUpsert
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	err = deliverFeeRuleService.NewDeliverFeeRuleService().Upsert(ctx, req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package orderAdjustSettle

import (
	"base/core/xhttp"
	"base/service/orderAdjustSettleService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// Get 获取调整结算记录详情
func Get(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	adjustmentID, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	result, err := orderAdjustSettleService.NewService().Get(ctx, adjustmentID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, result)
}

// GetByOrderID 根据订单ID获取调整结算记录
func GetByOrderID(ctx *gin.Context) {
	var req = struct {
		OrderID string `json:"order_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	orderID, err := util.ConvertToObjectWithCtx(ctx, req.OrderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	result, err := orderAdjustSettleService.NewService().GetByOrderID(ctx, orderID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, result)
}

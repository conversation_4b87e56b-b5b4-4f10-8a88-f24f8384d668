package product

import (
	"base/core/xhttp"
	"base/service/productService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// 关联商品

func CreateLink(ctx *gin.Context) {
	var req = struct {
		ProductID   string `json:"product_id"`
		PriceChange int    `json:"price_change"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplier, err := xhttp.CheckSupplier(ctx)
	if err != nil {
		return
	}

	err = productService.NewProductService().CreateLink(ctx, productID, req.PriceChange, supplier)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// RemoveLink 移除关联
func RemoveLink(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplier, err := xhttp.CheckSupplier(ctx)
	if err != nil {
		return
	}
	_ = supplier

	err = productService.NewProductService().RemoveLink(ctx, productID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func ListLinkedProduct(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplier, err := xhttp.CheckSupplier(ctx)
	if err != nil {
		return
	}

	ids, err := productService.NewProductService().ListLink(ctx, supplier.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, ids)
}

func CheckLink(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplier, err := xhttp.CheckSupplier(ctx)
	if err != nil {
		return
	}

	f, err := productService.NewProductService().CheckLink(ctx, productID, supplier.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, f)
}

func UpdateLinkPriceChange(ctx *gin.Context) {
	var req = struct {
		ProductID   string `json:"product_id"`
		PriceChange int    `json:"price_change"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().UpdateLinkPriceChange(ctx, productID, req.PriceChange)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func GetLinkInfo(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	productID, err := util.ConvertToObjectWithCtx(ctx, req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	supplier, err := xhttp.CheckSupplier(ctx)
	if err != nil {
		return
	}

	data, err := productService.NewProductService().GetLinkInfo(ctx, productID, supplier.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, data)
}

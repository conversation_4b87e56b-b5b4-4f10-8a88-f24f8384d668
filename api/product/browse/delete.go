package browse

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/browseService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		IDs []string `json:"ids" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	for _, i := range req.IDs {
		if len(i) != 24 {
			xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrParamError, nil, "浏览记录ID错误"))
			return
		}
		id, err := util.ConvertToObjectWithNote(i, "浏览信息ID")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
	}

	err = browseService.NewBrowseService().Delete(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

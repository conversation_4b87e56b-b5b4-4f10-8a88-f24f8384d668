package product

import (
	"base/core/xhttp"
	"base/service/productService"
	"base/types"
	"base/util"
	"errors"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
)

func Get(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"  validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	product, err := productService.NewProductService().Get(ctx, id)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespNoExist(ctx)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var r types.ProductRes
	r.Product = product

	//supplier, err := supplierService.NewSupplierService().Get(ctx, product.SupplierID)
	//if err != nil {
	//	zap.S().Error("查询供应商信息错误", err)
	//}
	//
	//r.SupplierTagListInfo = supplier.TagList

	f, _, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}

	if f {
		//getNumByProduct, err := cartService.NewCartService().GetNumByProduct(ctx, bID, product.ID)
		//if err != nil {
		//	xhttp.RespErr(ctx, err)
		//	return
		//}
		//r.CartNum = getNumByProduct
		//hasCollect, err := productCollectService.NewProductCollectService().Get(ctx, bID, product.ID)
		//if err != nil {
		//	xhttp.RespErr(ctx, err)
		//	return
		//}
		//r.HasCollect = hasCollect
	}

	if !f {
		r.Price = 0
		r.StartPrice = 0
		for i := range r.Product.SkuList {
			r.Product.SkuList[i].Price = 0
		}
	}

	xhttp.RespSuccess(ctx, r)
}

func GetOnly(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"  validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	product, err := productService.NewProductService().Get(ctx, id)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespNoExist(ctx)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, product)
}

func GetUpdate(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"  validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	product, err := productService.NewProductService().GetUpdate(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, product)
}

package product

import (
	"base/core/xhttp"
	"base/service/productService"
	"base/service/supplierService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"strconv"
)

// ListByCategory 根据分类
func ListByCategory(ctx *gin.Context) {
	var req = struct {
		CategoryID   string `json:"category_id" validate:"len=24"`
		FruitClassId string `json:"fruit_class_id" validate:"-"`
		Level        int    `json:"level" validate:"oneof=2 3"`
		Page         int64  `json:"page" validate:"min=1"`
		Limit        int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.CategoryID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	levelStr := strconv.Itoa(req.Level - 1)

	//pointID, err := xhttp.GetPointID(ctx)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	filter := bson.M{
		"category_ids." + levelStr: id,
		"sale":                     true,
		//"service_point_id":         pointID,
		"deleted_at": 0,
		"user_type": bson.M{
			"$ne": "YHT",
		},
	}

	if len(req.FruitClassId) == 24 {
		fID, err := util.ConvertToObjectWithNote(req.FruitClassId, "FruitClassId")
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["non_standard_attr.fruit_class_id"] = fID
	}

	products, i, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	list := make([]types.ProductRes, 0, len(products))

	mCartNum := make(map[primitive.ObjectID]int)

	f, bID, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}
	_ = bID

	if f {
		var ids []primitive.ObjectID
		for _, v := range products {
			ids = append(ids, v.ID)
		}
		//mCartNum = backCartNum(ctx, bID, ids)
	}

	for _, v := range products {
		supplier, err := supplierService.NewSupplierService().Get(ctx, v.SupplierID)
		if err != nil {
			zap.S().Errorf("查询供应商%v信息错误%v", v.SupplierID.Hex(), err)
		}
		if !f {
			v.Price = 0
			v.OriginPrice = 0
			v.StartPrice = 0
			for k, _ := range v.SkuList {
				v.SkuList[k].Price = 0
			}
		}
		list = append(list, types.ProductRes{
			Product:             v,
			SupplierTagListInfo: supplier.TagList,
			CartNum:             mCartNum[v.ID],
		})
	}

	xhttp.RespSuccessList(ctx, list, i)

}

func ListByCategoryWeb(ctx *gin.Context) {
	var req = struct {
		//ServicePointID string `json:"service_point_id"`
		CategoryID string `json:"category_id" validate:"len=24"`
		SaleType   string `json:"sale_type" validate:"-"` //   all   up   down
		Level      int    `json:"level" validate:"oneof=2 3"`
		Page       int64  `json:"page" validate:"min=1"`
		Limit      int64  `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.CategoryID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	//pointID, err := util.ConvertToObject(req.ServicePointID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}

	levelStr := strconv.Itoa(req.Level - 1)

	filter := bson.M{
		"category_ids." + levelStr: id,
		//"service_point_id":         pointID,
		"deleted_at": 0,
	}

	if req.SaleType != "all" {
		if req.SaleType == "up" {
			filter["sale"] = true
		}
		if req.SaleType == "down" {
			filter["sale"] = false
		}
	}

	products, i, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	list := make([]types.ProductRes, 0, len(products))

	//mCartNum := make(map[primitive.ObjectID]int)

	//f, bID, err := xhttp.CheckPrice(ctx)
	//if err != nil {
	//	return
	//}
	//_ = bID

	//if f {
	//	var ids []primitive.ObjectID
	//	for _, v := range products {
	//		ids = append(ids, v.ID)
	//	}
	//	//mCartNum = backCartNum(ctx, bID, ids)
	//}

	for _, v := range products {
		//	supplier, err := supplierService.NewSupplierService().Get(ctx, v.SupplierID)
		//	if err != nil {
		//		zap.S().Errorf("查询供应商%v信息错误%v", v.SupplierID.Hex(), err)
		//	}
		//	if !f {
		//		v.Price = 0
		//		v.OriginPrice = 0
		//	}
		list = append(list, types.ProductRes{
			Product: v,
			//SupplierTagListInfo: supplier.TagList,
			//CartNum:             mCartNum[v.ID],
		})
	}

	xhttp.RespSuccessList(ctx, list, i)

}

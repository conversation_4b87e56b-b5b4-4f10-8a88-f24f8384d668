package product

import (
	"base/core/xhttp"
	"base/service/productService"
	"base/service/supplierService"
	"base/types"
	"base/util"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
)

// ListByProductIDs 根据商品ID数组
func ListByProductIDs(ctx *gin.Context) {
	var req = struct {
		ProductIDs []string `json:"product_ids" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	var ids []primitive.ObjectID
	mSort := make(map[string]int)
	for j, i := range req.ProductIDs {
		id, err := util.ConvertToObject(i)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		ids = append(ids, id)
		mSort[i] = j
	}

	filter := bson.M{
		"_id":        bson.M{"$in": ids},
		"sale":       true,
		"deleted_at": 0,
	}

	products, err := productService.NewProductService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		supplier, err := supplierService.NewSupplierService().Get(ctx, v.SupplierID)
		if err != nil {
			zap.S().Error("查询供应商信息错误", err)
		}
		list = append(list, types.ProductRes{
			Product:             v,
			Sort:                mSort[v.ID.Hex()],
			SupplierTagListInfo: supplier.TagList,
		})
	}

	f, _, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}
	if !f {
		for j, _ := range list {
			list[j].Price = 0
			list[j].OriginPrice = 0
			list[j].StartPrice = 0
			for k, _ := range list[j].Product.SkuList {
				list[j].Product.SkuList[k].Price = 0
			}
		}
	}

	sort.Sort(ByIDList(list))

	xhttp.RespSuccess(ctx, list)

}

type ByIDList []types.ProductRes

func (array ByIDList) Len() int {
	return len(array)
}

func (array ByIDList) Less(i, j int) bool {
	return array[i].Sort < array[j].Sort //从小到大， 若为大于号，则从大到小
}

func (array ByIDList) Swap(i, j int) {
	array[i], array[j] = array[j], array[i]
}

func ListAuditBySupplierID(ctx *gin.Context) {
	// var req = struct {
	// 	SupplierID string `json:"supplier_id" validate:"len=24"`
	// 	Status     int    `json:"status" validate:"-"`
	// 	Page       int64  `json:"page" validate:"min=1"`
	// 	Limit      int64  `json:"limit" validate:"min=1"`
	// }{}
	// err := xhttp.Parse(ctx, &req)
	// if err != nil {
	// 	return
	// }

	// id, err := util.ConvertToObject(req.SupplierID)
	// if err != nil {
	// 	xhttp.RespErr(ctx, err)
	// 	return
	// }

	// products, count, err := productApplyService.NewProductApplyService().ListBySupplier(ctx, id, model.AuditStatusType(req.Status), req.Page, req.Limit)
	// if err != nil {
	// 	xhttp.RespErr(ctx, err)
	// 	return
	// }

	xhttp.RespSuccessList(ctx, nil, 0)

}

func ListExternalSale(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		SupplierID     string `json:"supplier_id"`
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"service_point_id": servicePointID,
		"is_external_sale": true,
		"deleted_at":       0,
	}

	if len(req.SupplierID) == 24 {
		id, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
		filter["supplier_id"] = id
	}
	list, err := productService.NewProductService().List(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)
}

// ListLatest 7日上新
func ListLatest(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page"`
		Limit int64 `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	now := time.Now()
	begin := now.Add(-1 * time.Hour * 24 * 7).UnixMilli()
	filter := bson.M{
		"sale": true,
		"updated_at": bson.M{
			"$gte": begin,
		},
		"deleted_at": 0,
	}

	expressID, _ := primitive.ObjectIDFromHex("66727385d948593db3eee799")
	yhtID, _ := primitive.ObjectIDFromHex("66792215e7ea14140bec6cba")
	stationID, _ := primitive.ObjectIDFromHex("66ea8e010bf5034b411ea72f")

	excludeIDList := []primitive.ObjectID{expressID, yhtID, stationID}

	filter["category_ids.1"] = bson.M{
		"$nin": excludeIDList,
	}

	products, count, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	f, _, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}

	if !f {
		for j, _ := range list {
			list[j].Price = 0
			list[j].OriginPrice = 0
			list[j].StartPrice = 0
			for k, _ := range list[j].Product.SkuList {
				list[j].Product.SkuList[k].Price = 0
			}
		}
	}

	xhttp.RespSuccessList(ctx, list, count)

}

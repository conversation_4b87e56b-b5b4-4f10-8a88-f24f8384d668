package product

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productAuditService"
	"base/service/productService"
	"base/types"
	"base/util"
	"errors"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
)

// GetAudit 商品审核详情
func GetAudit(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	product, err := productAuditService.NewProductAuditService().Get(ctx, id)
	if errors.Is(err, mongo.ErrNoDocuments) {
		xhttp.RespNoExist(ctx)
		return
	}
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, product)
}

// ListAuditBySupplier 商品审核列表
func ListAuditBySupplier(ctx *gin.Context) {
	var req = struct {
		SupplierID string `json:"supplier_id" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	supplierID, err := util.ConvertToObjectWithCtx(ctx, req.SupplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, err := productAuditService.NewProductAuditService().ListBySupplier(ctx, supplierID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, list)

}

// ListAudit 商品审核列表
func ListAudit(ctx *gin.Context) {
	var req = struct {
		Page   int64                 `json:"page"`
		Limit  int64                 `json:"limit"`
		Status model.AuditStatusType `json:"status"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, count, err := productAuditService.NewProductAuditService().List(ctx, req.Status, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var res []types.ProductAuditRes
	for _, audit := range list {

		var p model.Product

		if !audit.IsNew {
			p, err = productService.NewProductService().Get(ctx, audit.Product.ID)
		}

		res = append(res, types.ProductAuditRes{
			ProductAudit:  audit,
			OriginProduct: p,
		})
	}

	xhttp.RespSuccessList(ctx, res, count)
}

// DeleteAudit 删除商品审核记录
func DeleteAudit(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productAuditService.NewProductAuditService().Delete(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package product

import (
	"base/core/xhttp"
	"base/service/indexPartProductService"
	"base/service/productService"
	"base/service/shortcutService"
	"base/service/topicService"
	"base/util"
	"context"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		ProductID string `json:"product_id" validate:"required"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ProductID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productService.NewProductService().Del(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

func DeleteProduct(productID string) error {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Errorf("异步删除商品 error:%v", err)
			return
		}
	}()

	//上架
	id, err := util.ConvertToObjectWithNote(productID, "DeleteProduct product_id")
	if err != nil {
		return err
	}
	ctx := context.Background()

	//快捷栏
	err = shortcutService.NewShortcutService().DownProduct(ctx, id, true)
	if err != nil {
		return err
	}

	// 主题
	err = topicService.NewTopicService().DownProduct(ctx, id, true)
	if err != nil {
		return err
	}

	// 专区
	err = indexPartProductService.NewIndexPartProductService().DownProduct(ctx, id, true)
	if err != nil {
		return err
	}
	return nil
}

package product

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ListByYHT(ctx *gin.Context) {
	var req = struct {
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	// 久鲜农业
	id, _ := util.ConvertToObjectWithCtx(ctx, "6669529601a0bf21599cbd3b")

	filter := bson.M{
		"supplier_id": id,
		"user_type":   model.UserTypeYHT,
		"sale":        true,
		"deleted_at":  0,
	}

	products, i, err := productService.NewProductService().ListByCus(ctx, filter, 1, 20)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	list := make([]types.ProductRes, 0, len(products))

	f, bID, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}
	_ = bID

	if f {
		var ids []primitive.ObjectID
		for _, v := range products {
			ids = append(ids, v.ID)
		}
		//mCartNum = backCartNum(ctx, bID, ids)
	}

	for _, v := range products {
		if !f {
			v.Price = 0
		}
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	xhttp.RespSuccessList(ctx, list, i)
}

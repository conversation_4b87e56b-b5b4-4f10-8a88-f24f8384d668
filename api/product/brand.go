package product

import (
	"base/core/xhttp"
	"base/service/productService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// ListByBrand 品牌
func ListByBrand(ctx *gin.Context) {
	var req = struct {
		BrandID string `json:"brand_id" validate:"-"`
		Page    int64  `json:"page"`
		Limit   int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{
		"sale":       true,
		"deleted_at": 0,
	}

	brandID, err := util.ConvertToObjectWithCtx(ctx, req.BrandID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter["link_brand_id"] = brandID

	products, count, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	f, _, err := xhttp.CheckPrice(ctx)
	if err != nil {
		return
	}

	if !f {
		for j, _ := range list {
			list[j].Price = 0
			list[j].OriginPrice = 0
		}
	}

	xhttp.RespSuccessList(ctx, list, count)

}

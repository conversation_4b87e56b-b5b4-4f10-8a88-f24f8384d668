package productCollect

import (
	"base/core/xhttp"
	"base/service/productCollectService"
	"base/service/productService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func List(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
		Page    int64  `json:"page" validate:"min=1"`
		Limit   int64  `json:"limit" validate:"min=10"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	pointID, err := xhttp.GetPointID(ctx)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list, count, err := productCollectService.NewProductCollectService().List(ctx, id, pointID, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	var ids []primitive.ObjectID
	for _, collect := range list {
		ids = append(ids, collect.ProductID)
	}

	products, err := productService.NewProductService().ListByIDs(ctx, ids)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, products, count)
}

package product

import (
	"base/core/xhttp"
	"base/service/productService"
	"base/types"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// ListByCoverTag 封面标签
func ListByCoverTag(ctx *gin.Context) {
	var req = struct {
		TagID string `json:"tag_id" validate:"len=24"`
		Page  int64  `uri:"page" validate:"min=1"`
		Limit int64  `uri:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.TagID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"cover_tag._id": id,
	}

	products, i, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	xhttp.RespSuccessList(ctx, list, i)

}

// ListByNormalTag 普通标签
func ListByNormalTag(ctx *gin.Context) {
	var req = struct {
		TagID string `json:"tag_id" validate:"len=24"`
		Page  int64  `uri:"page" validate:"min=1"`
		Limit int64  `uri:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	id, err := util.ConvertToObject(req.TagID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	filter := bson.M{
		"tag_list._id": id,
	}

	products, i, err := productService.NewProductService().ListByCus(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	list := make([]types.ProductRes, 0, len(products))
	for _, v := range products {
		list = append(list, types.ProductRes{
			Product: v,
		})
	}

	xhttp.RespSuccessList(ctx, list, i)
}

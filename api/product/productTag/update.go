package productTag

import (
	"base/core/xhttp"
	"base/model"
	"base/service/productTagService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func UpdateTitle(ctx *gin.Context) {
	var req = struct {
		ID    string         `json:"id" validate:"len=24"`
		Title string         `json:"title" validate:"required"`
		Color string         `json:"color" validate:"-"`
		Img   model.FileInfo `json:"img"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObject(req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = productTagService.NewProductTagService().Update(ctx, id, req.Title, req.Color, req.Img)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

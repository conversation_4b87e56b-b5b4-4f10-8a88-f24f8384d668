package Integral

import (
	"base/core/xhttp"
	"base/model"
	"base/service/buyerService"
	"base/service/integralAccountService"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ListAccount(ctx *gin.Context) {
	var req = struct {
		Page  int64 `json:"page"`
		Limit int64 `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	list, count, err := integralAccountService.NewIntegralAccountService().ListByPage(ctx, bson.M{}, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var userIDs []primitive.ObjectID
	for _, account := range list {
		userIDs = append(userIDs, account.UserID)
	}

	resList := make([]res, 0, len(list))

	if len(userIDs) > 0 {
		buyers, err := buyerService.NewBuyerService().ListByCus(ctx, bson.M{
			"user_id": bson.M{"$in": userIDs},
		})
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}

		for _, account := range list {
			var buyerID primitive.ObjectID
			var buyerName string
			for _, buyer := range buyers {
				if buyer.UserID == account.UserID {
					buyerID = buyer.ID
					buyerName = buyer.BuyerName
					break
				}
			}

			item := res{
				IntegralAccount: account,
				BuyerID:         buyerID,
				BuyerName:       buyerName,
			}
			resList = append(resList, item)
		}
	}

	xhttp.RespSuccessList(ctx, resList, count)
}

type res struct {
	model.IntegralAccount
	BuyerID   primitive.ObjectID `json:"buyer_id"`
	BuyerName string             `json:"buyer_name"`
}

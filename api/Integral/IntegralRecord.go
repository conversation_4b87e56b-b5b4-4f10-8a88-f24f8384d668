package Integral

import (
	"base/core/xhttp"
	"base/service/integralRecordService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

// ListRecord 积分记录
func ListRecord(ctx *gin.Context) {
	var req = struct {
		IntegralAccountID string `json:"integral_account_id"`
		Page              int64  `json:"page"`
		Limit             int64  `json:"limit"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	integralAccountID, err := util.ConvertToObjectWithCtx(ctx, req.IntegralAccountID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"integral_account_id": integralAccountID,
	}

	records, count, err := integralRecordService.NewIntegralRecordService().ListByPage(ctx, filter, req.<PERSON>, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, records, count)
}

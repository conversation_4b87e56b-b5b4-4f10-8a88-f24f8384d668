package Integral

import (
	"base/core/xhttp"
	"base/model"
	"base/service/integralProductService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
)

func ListProduct(ctx *gin.Context) {
	var req = struct {
		//Status int   `json:"status"`
		Page  int64 `json:"page" validate:"min=1"`
		Limit int64 `json:"limit" validate:"min=1"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	filter := bson.M{
		"status":     model.IntegralProductStatusUp,
		"deleted_at": 0,
	}

	list, count, err := integralProductService.NewIntegralProductService().ListByPage(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccessList(ctx, list, count)
}

func GetProduct(ctx *gin.Context) {
	var req = struct {
		ID string `json:"id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	data, err := integralProductService.NewIntegralProductService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, data)
}

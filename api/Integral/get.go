package Integral

import (
	"base/core/xhttp"
	"base/service/integralAccountService"
	"base/util"
	"github.com/gin-gonic/gin"
)

// GetByUser 检查新会员
func GetByUser(ctx *gin.Context) {
	var req = struct {
		BuyerID string `json:"buyer_id"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}
	buyerID, err := util.ConvertToObjectWithCtx(ctx, req.BuyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	account, err := integralAccountService.NewIntegralAccountService().GetByBuyer(ctx, buyerID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, account)
}

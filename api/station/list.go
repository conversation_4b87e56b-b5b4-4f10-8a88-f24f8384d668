package station

import (
	"base/core/xhttp"
	"base/model"
	"base/service/authenticationService"
	"base/service/servicePointService"
	"base/service/stationService"
	"base/util"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func ListByWeb(ctx *gin.Context) {
	var req = struct {
		Page       int64            `json:"page"`
		Limit      int64            `json:"limit"`
		OpenStatus model.OpenStatus `json:"open_status"`
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	filter := bson.M{}

	if req.OpenStatus != "all" {
		filter["open_status"] = req.OpenStatus
	}

	stations, i, err := stationService.NewStationService().List(ctx, filter, req.Page, req.Limit)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var pointIDs, ids []primitive.ObjectID
	for _, station := range stations {
		pointIDs = append(pointIDs, station.ServicePointID)
		ids = append(ids, station.ID)
	}

	var points []model.ServicePoint
	var authenticationList []model.Authentication
	if len(pointIDs) > 0 {
		points, err = servicePointService.NewServicePointService().ListByIDs(ctx, pointIDs)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	if len(ids) > 0 {
		authenticationList, err = authenticationService.NewAuthenticationService().ListByStation(ctx, ids)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	list := make([]stationRes, 0, len(stations))

	for _, station := range stations {
		item := stationRes{
			Station: station,
		}

		for _, point := range points {
			if station.ServicePointID == point.ID {
				item.ServicePointName = point.Name
				break
			}
		}

		for _, auth := range authenticationList {
			if auth.ObjectType == model.ObjectTypeStation && station.ID == auth.ObjectID {
				item.IdentityStatus = model.IdentityStatusNo
				item.BankBindStatus = model.BankBindStatusNo
				item.IsMobileVerify = false
				item.VerifyMobile = auth.Mobile

				if auth.IdentityNo != "" {
					item.IdentityStatus = model.IdentityStatusYes
				}
				if auth.IndividualBankcardResult == "ok" {
					item.BankBindStatus = model.BankBindStatusYes
				}

				if auth.IsMobileVerify {
					item.IsMobileVerify = true
				}
				break
			}
		}

		list = append(list, item)
	}

	xhttp.RespSuccessList(ctx, list, i)
}

func ListByPoint(ctx *gin.Context) {
	var req = struct {
		ServicePointID string           `json:"service_point_id"`
		OpenStatus     model.OpenStatus `json:"open_status"`
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	id, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"service_point_id": id,
	}

	if req.OpenStatus != "all" {
		filter["open_status"] = req.OpenStatus
	}

	stations, err := stationService.NewStationService().ListCus(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	point, err := servicePointService.NewServicePointService().Get(ctx, id)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	list := make([]stationRes, 0, len(stations))

	for _, station := range stations {
		item := stationRes{
			Station:          station,
			ServicePointName: point.Name,
		}

		list = append(list, item)
	}

	xhttp.RespSuccess(ctx, list)
}

type stationRes struct {
	model.Station
	ServicePointName string               `json:"service_point_name"`
	IdentityStatus   model.IdentityStatus `json:"identity_status"`
	BankBindStatus   model.BankBindStatus `json:"bank_bind_status"`
	IsMobileVerify   bool                 `json:"is_mobile_verify"`
	VerifyMobile     string               `json:"verify_mobile"`
}

func List(ctx *gin.Context) {
	var req = struct {
	}{}

	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	stations, err := stationService.NewStationService().ListCus(ctx, bson.M{
		"open_status": model.OpenStatusOpen,
	})
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var ids []primitive.ObjectID
	for _, station := range stations {
		ids = append(ids, station.ServicePointID)
	}

	var points []model.ServicePoint
	if len(ids) > 0 {
		points, err = servicePointService.NewServicePointService().ListByIDs(ctx, ids)
		if err != nil {
			xhttp.RespErr(ctx, err)
			return
		}
	}

	list := make([]pointRes, 0, len(points))

	for _, point := range points {
		for _, station := range stations {

			if station.ServicePointID == point.ID {
				var exist bool
				for j, res := range list {
					if res.ServicePointID == point.ID {
						tempList := list[j].StationList

						tempList = append(tempList, station)
						list[j].StationList = tempList
						exist = true

					}
				}
				if !exist {
					item := pointRes{
						ServicePointID:   point.ID,
						ServicePointName: point.Name,
						StationList:      []model.Station{station},
					}
					list = append(list, item)
				}
			}

		}
	}

	xhttp.RespSuccess(ctx, list)
}

type pointRes struct {
	ServicePointID   primitive.ObjectID `json:"service_point_id"`
	ServicePointName string             `json:"service_point_name"`
	StationList      []model.Station    `json:"station_list"`
}

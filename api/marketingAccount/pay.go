package marketingAccount

import (
	"base/core/xhttp"
	"github.com/gin-gonic/gin"
)

// Pay 充值营销账户
func Pay(ctx *gin.Context) {
	//var req = struct {
	//	BuyerID   string `json:"buyer_id"`
	//	LoginCode string `json:"login_code"`
	//}{}
	//err := xhttp.Parse(ctx, &req)
	//if err != nil {
	//	return
	//}
	//
	//buyerID, err := util.ConvertToObjectWithNote(req.BuyerID, "")
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	//amount := 1000 * 100
	//
	//data, err := marketingAccountService.NewMarketingAccountService().CreateAndPay(ctx, buyerID, req.LoginCode, amount)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	return
	//}
	//
	xhttp.RespSuccess(ctx, nil)
}

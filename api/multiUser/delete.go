package multiUser

import (
	"base/core/xhttp"
	"base/service/multiUserService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Delete(ctx *gin.Context) {
	var req = struct {
		UserID string `json:"user_id" validate:"len=24"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	userID, err := util.ConvertToObjectWithNote(req.UserID, "multiUser user_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = multiUserService.NewMultiUserService().Delete(ctx, userID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

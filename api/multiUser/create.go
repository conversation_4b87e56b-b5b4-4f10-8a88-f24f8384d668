package multiUser

import (
	"base/core/xhttp"
	"base/service/multiUserService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func Create(ctx *gin.Context) {
	var req = struct {
		UserID   string `json:"user_id" validate:"len=24"`
		ObjectID string `json:"object_id" validate:"len=24"`
		Note     string `json:"note" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	userID, err := util.ConvertToObjectWithNote(req.UserID, "multiUser user_id")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	objectID, err := util.ConvertToObjectWithNote(req.ObjectID, "multiUser ObjectID")
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	err = multiUserService.NewMultiUserService().Create(ctx, userID, objectID, req.Note)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, nil)
}

package types

import (
	"base/model"
)

type BuyerApplyReq struct {
	BuyerID   string `json:"buyer_id"`   // 采购商ID---更新时传
	BuyerName string `json:"buyer_name"` // 采购商名称
	//BuyerType     model.BuyerType `json:"buyer_type"`     // 类型
	ContactUser   string `json:"contact_user"`   // 联系人
	ContactMobile string `json:"contact_mobile"` // 联系人手机号
	ApplyReason   string `json:"apply_reason"`   // 申请说明
	Entity        int    `json:"entity"`         // 1 有 2 无
	//RegionID           string          `json:"region_id" validate:"len=24"`                // 区域ID
	Location           model.Location      `json:"location"`             // 定位地址
	Address            string              `json:"address"`              // 详细地址
	BusinessLicenseImg model.FileInfo      `json:"business_license_img"` // 营业执照-图片
	ShopHeadImg        model.FileInfo      `json:"shop_head_img"`        // 门头照-图片
	BuyerNote          string              `json:"buyer_note"`           // 备注
	AddressNote        string              `json:"address_note"`         // 申请说明
	DeliverType        []model.DeliverType `json:"deliver_type"`         // 配送方式
	DeliverFee         int                 `json:"deliver_fee"`          // 配送费
	SubsidyAmount      int                 `json:"subsidy_amount"`       // 配送费补贴规则-门槛
	SubsidyPercent     int                 `json:"subsidy_percent"`      // 配送费补贴规则-比例
	InviteCode         string              `json:"invite_code"`          // 邀请码
}

type BuyerRes struct {
	model.Buyer
	BuyerTypeName  string `json:"buyer_type_name"`
	IsMobileVerify bool   `json:"is_mobile_verify"`
	PayMobile      string `json:"pay_mobile"`
	IsIdentity     bool   `json:"is_identity"`
}

//AddressType           int                   `json:"address_type"`

type BuyerAuditReq struct {
	Entity                  int                    `json:"entity"` // 类型
	BuyerID                 string                 `json:"buyer_id"`
	LinkUserID              string                 `json:"link_user_id"`
	AuditFailReason         string                 `json:"audit_fail_reason"`
	AuditStatus             model.AuditStatusType  `json:"audit_status"`
	BuyerNote               string                 `json:"buyer_note"`
	ServicePointId          string                 `json:"service_point_id"`
	AddressNote             string                 `json:"address_note"`
	DeliverFee              int                    `json:"deliver_fee"`
	SubsidyAmount           int                    `json:"subsidy_amount"`
	SubsidyPercent          int                    `json:"subsidy_percent"`
	DeliverType             []model.DeliverType    `json:"deliver_type"`
	DeliverFreeBegin        int64                  `json:"deliver_free_begin"`         // 配送限免期-开始
	DeliverFreeEnd          int64                  `json:"deliver_free_end"`           // 配送限免期-结束
	LogisticsNote           string                 `json:"logistics_note"`             // 物流说明
	LogisticsUnitFee        int                    `json:"logistics_unit_fee"`         // 物流费单价
	InstantDeliver          []model.InstantDeliver `json:"instant_deliver"`            // 即时配送
	ServiceFee              int                    `json:"service_fee"`                // 服务费
	ServiceFeeRebatePercent int                    `json:"service_fee_rebate_percent"` // 服务费-返利比例
	UserType                string                 `json:"user_type"`
}

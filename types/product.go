package types

import (
	"base/model"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ProductCreateReq 商品创建
type ProductCreateReq struct {
	SupplierID            string                  `json:"supplier_id" `
	SoldCount             int                     `json:"sold_count" `
	ProductUnitID         string                  `json:"product_unit_id"`                         // 单位类型
	HasParam              bool                    `json:"has_param" validate:"-"`                  // 是否有规格参数
	IsCheckWeight         bool                    `json:"is_check_weight" validate:"-"`            // 是否检查重量（分拣环节
	ProductParamType      model.ProductParamType  `json:"product_param_type" validate:"oneof=1 2"` // 规格类型 1水果类 2其他类
	CategoryIDs           []string                `json:"category_ids"`                            // 商品分类信息
	Title                 string                  `json:"title"`                                   // 商品名称
	Desc                  string                  `json:"desc" validate:"-"`                       // 商品详情
	PurchaseNote          string                  `json:"purchase_note"`                           // 采购说明-采购门店
	VideoFile             model.FileInfo          `json:"video_file"`                              // 商品视频
	CoverImg              model.FileInfo          `json:"cover_img"`                               // 商品封面图片
	DisplayFile           []model.FileInfo        `json:"display_file"`                            // 轮播展示图(视频)
	DescImg               []model.FileInfo        `json:"desc_img"`                                // 商品详情图
	Stock                 int                     `json:"stock"`                                   // 库存
	BuyMinLimit           int                     `json:"buy_min_limit"`                           // 起购 0 不限制
	BuyMaxLimit           int                     `json:"buy_max_limit"`                           // 限购 0 不限制
	Price                 int                     `json:"price"`                                   // 价格
	MarketWholesalePrice  int                     `json:"market_wholesale_price"`                  // 市场批发价
	EstimatePurchasePrice int                     `json:"estimate_purchase_price"`                 // 预估采购价
	CustomTagList         []model.CustomTag       `json:"custom_tag_list"`                         // 自定义标签
	SearchTagList         []string                `json:"search_tag_list"`                         // 搜索标签
	Weight                model.Weight            `json:"weight"`                                  // 重量
	AttrInfo              []model.FieldInfo       `json:"attr_info"`                               // 商品参数
	ProductOriginType     model.ProductOriginType `json:"product_origin_type"`                     // 原始产地
	SkuList               []model.Sku             `json:"sku_list"`                                // sku列表
}

// ProductUpdateReq 商品更新
type ProductUpdateReq struct {
	ID                    string                 `json:"id" validate:"len=24"`                    // 商品ID
	From                  string                 `json:"from"`                                    // 来源 audit/list
	ProductUnitID         string                 `json:"product_unit_id"`                         // 单位类型
	HasParam              bool                   `json:"has_param" validate:"-"`                  // 是否有规格参数
	IsCheckWeight         bool                   `json:"is_check_weight" validate:"-"`            // 是否检查重量（分拣环节
	ProductParamType      model.ProductParamType `json:"product_param_type" validate:"oneof=1 2"` // 规格类型 1水果类 2其他类
	CategoryIDs           []string               `json:"category_ids"`                            // 商品分类信息
	Title                 string                 `json:"title"`                                   // 商品名称
	Desc                  string                 `json:"desc"`                                    // 商品详情
	PurchaseNote          string                 `json:"purchase_note"`                           // 采购说明-采购门店
	VideoFile             model.FileInfo         `json:"video_file"`                              // 商品视频
	CoverImg              model.FileInfo         `json:"cover_img"`                               // 商品封面图片
	DisplayFile           []model.FileInfo       `json:"display_file"`                            // 轮播展示图(视频)
	DescImg               []model.FileInfo       `json:"desc_img"`                                // 商品详情图
	Stock                 int                    `json:"stock"`                                   // 库存
	BuyMinLimit           int                    `json:"buy_min_limit"`                           // 起购 0 不限制
	BuyMaxLimit           int                    `json:"buy_max_limit"`                           // 限购 0 不限制
	Price                 int                    `json:"price"`                                   // 价格
	MarketWholesalePrice  int                    `json:"market_wholesale_price"`                  // 市场批发价
	EstimatePurchasePrice int                    `json:"estimate_purchase_price"`                 // 预估采购价
	//SupplyPrice int `json:"supply_price"` // 供应价
	//OriginPrice       int                     `json:"origin_price"`                                   // 市场价
	//DiscountList      []model.ProductDiscount `json:"discount_list"`                                  // 折扣
	//DiscountPriceList []model.DiscountPrice   `json:"discount_price_list" bson:"discount_price_list"` // 折扣价
	CustomTagList     []model.CustomTag       `json:"custom_tag_list"`     // 自定义标签
	SearchTagList     []string                `json:"search_tag_list"`     // 搜索标签
	PriceList         []model.PerPrice        `json:"price_list"`          // 价格列表
	Weight            model.Weight            `json:"weight"`              // 重量
	AttrInfo          []model.FieldInfo       `json:"attr_info"`           // 商品参数
	ProductOriginType model.ProductOriginType `json:"product_origin_type"` // 原始产地
	SkuList           []model.Sku             `json:"sku_list"`            // sku列表
}

// Cart 购物车
type Cart struct {
	ID         primitive.ObjectID `bson:"_id" json:"id"`
	BuyerID    primitive.ObjectID `bson:"buyer_id" json:"buyer_id"`       // 采购商ID
	ProductID  primitive.ObjectID `bson:"product_id" json:"product_id"`   // 所属商品ID
	SupplierID primitive.ObjectID `bson:"supplier_id" json:"supplier_id"` // 供应商ID
	Count      int                `bson:"count" json:"count"`             // 商品数量
}

// ProductRes 商品详情
type ProductRes struct {
	model.Product
	Sort                int                 `json:"sort"`
	SupplierTagListInfo []model.SupplierTag `json:"supplier_tag_list_info"` // 商家标签
	CartNum             int                 `json:"cart_num"`               // 购物车数量
	HasCollect          bool                `json:"has_collect"`            // 是否收藏
}

// ProductAudit 商品审核
type ProductAudit struct {
	model.Product
	Reason  string `json:"reason"`
	ApplyAt int64  `json:"apply_at"`
}

// ProductAuditRes 商品审核详情
type ProductAuditRes struct {
	model.ProductAudit
	OriginProduct model.Product `json:"origin_product"`
}

// SkuPrice 商品sku价格
type SkuPrice struct {
	SkuIDCode string `json:"sku_id_code"`
	Price     int    `json:"price"`
}

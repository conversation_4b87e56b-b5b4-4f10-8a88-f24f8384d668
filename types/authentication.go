package types

import (
	"base/model"
)

type AuthenticationUpdateReq struct {
	ID          string      `json:"id" validate:"required"`
	Company     Company     `json:"company"`      // 企业信息
	BankAccount BankAccount `json:"bank_account"` // 银行账户

}

// Company 企业信息
type Company struct {
	CompanyType            model.CompanyType     `json:"company_type" `              // 企业类型
	CompanyName            string                `json:"company_name" `              // 企业名称
	CreditCode             string                `json:"credit_code" `               // 统一社会信用代码
	BusinessLicenseValidTo string                `json:"business_license_valid_to" ` // 营业执照有效期 --不需要
	BusinessLicenseImg     model.FileInfo        `json:"business_license_img" `      // 营业执照图
	BusinessLicense        model.BusinessLicense `json:"business_license" `
	Legal                  Legal                 `json:"legal" `
}

// Legal 法人
type Legal struct {
	LegalName      string         `json:"legal_name" bson:"legal_name"`               // 法人姓名
	IdentityType   int            `json:"identity_type" bson:"identity_type"`         // 法人证件类型  1 身份证
	LegalIds       string         `json:"legal_ids" bson:"legal_ids"`                 // 法人证件号码
	LegalPhone     string         `json:"legal_phone" bson:"legal_phone"`             // 法人手机号
	IDCard         model.IDCard   `json:"id_card" bson:"id_card"`                     // 身份证信息
	IdCardFrontImg model.FileInfo `bson:"id_card_front_img" json:"id_card_front_img"` // 身份证-正面
	IdCardBackImg  model.FileInfo `bson:"id_card_back_img" json:"id_card_back_img"`   // 身份证-背面
	//IdCardWithHandImg FileInfo `bson:"id_card_with_hand_img" json:"id_card_with_hand_img"` // 身份证-手持
}

type AccountType int

const (
	AccountTypePublic AccountType = 1
	AccountTypeSelf   AccountType = 0
)

// BankAccount 银行账户
type BankAccount struct {
	AccountType AccountType `json:"account_type"` // 银行账户类型-1对公/0对私
	CardNumber  string      `json:"card_number"`  // 账号

	// 对公
	ParentBankName string `json:"parent_bank_name"` // 开户银行名称
	BankName       string `json:"bank_name"`        // 开户行支行名称
	UnionBank      string `json:"union_bank"`       // 支付行号，12位数字

	// 对私
	BankReservedMobile string `json:"bank_reserved_mobile"` // 银行预留手机号

	BankcardImg model.FileInfo `json:"bankcard_img" bson:"bankcard_img"` // 银行卡图片
	Bankcard    model.Bankcard `json:"bankcard" bson:"bankcard"`         // 银行识别
}

package types

import (
	"base/model"
)

type TopicCreateReq struct {
	Sort        int            `json:"sort"`    // 排序
	Visible     bool           `json:"visible"` // 是否展示
	Page        string         `json:"page"`    // 页面
	ProductList []string       `json:"product_list"`
	TopImg      model.FileInfo `json:"top_img"` // 顶图
	Img         model.FileInfo `json:"img"`
}

type TopicUpdateReq struct {
	ID          string         `uri:"id" validate:"required"`
	Sort        int            `json:"sort"`    // 排序
	Visible     bool           `json:"visible"` // 是否展示
	Page        string         `json:"page"`    // 页面
	ProductList []string       `json:"product_list"`
	TopImg      model.FileInfo `json:"top_img"` // 顶图
	Img         model.FileInfo `json:"img"`
}

package types

import (
	"base/model"
)

// DeliverFeeRuleUpsert 创建和更新下单配送费收取规则
type DeliverFeeRuleUpsert struct {
	ServicePointID      string                      `json:"service_point_id" `
	Rules               []model.DistanceWithNumRule `json:"rules" `                 // 规则
	BaseDeliverFee      int                         `json:"base_deliver_fee" `      // 基础配送费
	BaseDeliverDistance int                         `json:"base_deliver_distance" ` // 基础配送距离
	//BaseDeliverWeight    int                         `json:"base_deliver_weight" `      // 基础配送重量
	//OverWeightFeePerUnit int                         `json:"over_weight_fee_per_unit" ` // 超重价格每单位
	//OverWeightKGPerUnit  int                         `json:"over_weight_kg_per_unit" `  // 超重单位
	FeePerKm        int `json:"fee_per_km"`         // 里程价每km
	SubsidyFeePerKm int `json:"subsidy_fee_per_km"` // 平台补贴-里程价每km

}

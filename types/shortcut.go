package types

import (
	"base/model"
)

type ShortcutCreate struct {
	ServicePointID string         `json:"service_point_id"`
	Title          string         `json:"title" validate:"max=5"` // 排序
	Sort           int            `json:"sort" validate:"min=0"`  // 排序
	Visible        bool           `json:"visible"`                // 是否展示
	Page           string         `json:"page"`                   // 页面
	ProductList    []string       `json:"product_list"`
	TopImg         model.FileInfo `json:"top_img"`                  // 顶图
	Icon           model.FileInfo `json:"icon" validate:"required"` // 轮播图展示图片地址
}

type ShortcutUpdate struct {
	ID          string         `uri:"id" validate:"required"`
	Title       string         `json:"title" validate:"max=5"` // 排序
	Sort        int            `json:"sort"`                   // 排序
	Visible     bool           `json:"visible"`                // 是否展示
	Page        string         `json:"page"`                   // 页面
	ProductList []string       `json:"product_list"`
	TopImg      model.FileInfo `json:"top_img"`                  // 顶图
	Icon        model.FileInfo `json:"icon" validate:"required"` // 轮播图展示图片地址
}

package types

import "base/model"

// OrderAdjustSettleCreateReq 创建调整结算记录请求
type OrderAdjustSettleCreateReq struct {
	OrderID     string                   `json:"order_id"`     // 订单ID
	Remark      string                   `json:"remark"`       // 备注
	ProductList []ProductAdjustSettleReq `json:"product_list"` // 商品列表
}

// ProductAdjustSettleReq 商品调整结算明细请求
type ProductAdjustSettleReq struct {
	ProductID    string `json:"product_id"`    // 商品ID
	SkuIDCode    string `json:"sku_id_code"`   // SKU编码
	AdjustAmount int    `json:"adjust_amount"` // 调整金额
	AdjustRemark string `json:"adjust_remark"` // 调整备注
}

// OrderAdjustSettleListReq 查询调整结算记录列表请求
type OrderAdjustSettleListReq struct {
	Page       int64  `json:"page"`        // 页码
	Limit      int64  `json:"limit"`       // 每页数量
	BuyerID    string `json:"buyer_id"`    // 采购商ID
	SupplierID string `json:"supplier_id"` // 供应商ID
}

// OrderAdjustSettleListRes 查询调整结算记录列表响应
type OrderAdjustSettleListRes struct {
	Total int64                     `json:"total"` // 总数
	List  []model.OrderAdjustSettle `json:"list"`  // 列表
}

// OrderAdjustSettleUpdateReq 更新调整结算记录请求(仅草稿状态可编辑)
type OrderAdjustSettleUpdateReq struct {
	ID          string                   `json:"id"`           // 调整结算记录ID
	Remark      string                   `json:"remark"`       // 调整结算备注
	ProductList []ProductAdjustSettleReq `json:"product_list"` // 商品调整结算明细
}

// OrderAdjustSettleConfirmReq 确认调整结算记录请求
type OrderAdjustSettleConfirmReq struct {
	// 确认操作不需要额外参数，由路径参数提供调整结算记录ID
}

// OrderAdjustSettleCancelReq 取消调整结算记录请求
type OrderAdjustSettleCancelReq struct {
	CancelReason string `json:"cancel_reason" binding:"max=200"` // 取消原因
}

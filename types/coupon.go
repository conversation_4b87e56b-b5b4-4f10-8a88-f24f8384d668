package types

import (
	"base/model"
)

// CouponCreateReq 优惠券新建
type CouponCreateReq struct {
	CouponStockType    model.CouponStockType `json:"coupon_stock_type"`    //  类型
	Title              string                `json:"title"`                //  标题
	Description        string                `json:"description"`          //  描述
	AvailableBeginTime int64                 `json:"available_begin_time"` // 可用时间
	AvailableEndTime   int64                 `json:"available_end_time"`   //  可用时间
	MaxSendNum         int                   `json:"max_send_num"`         //  最大发放数量
	MaxPerUserNum      int                   `json:"max_per_user_num"`     //  最大领取数量
	CouponAmount       int                   `json:"coupon_amount"`        //  优惠券面额
	MinAmount          int                   `json:"min_amount"`           //  门槛金额
	ValidDays          int                   `json:"valid_days"`           //  有效期天数
}

// CouponStockUpdateReq 优惠券批次更新
type CouponStockUpdateReq struct {
	ID                 string `json:"id"`                   //  类型
	Title              string `json:"title"`                //  标题
	Description        string `json:"description"`          //  描述
	AvailableBeginTime int64  `json:"available_begin_time"` // 可用时间
	AvailableEndTime   int64  `json:"available_end_time"`   //  可用时间
}

// CouponAccountCreateReqByAdmin 优惠券账户-管理员-发放
type CouponAccountCreateReqByAdmin struct {
	UserID   string `json:"user_id" validate:"len=24"`  // 用户ID
	BuyerID  string `json:"buyer_id" bson:"buyer_id"`   // 采购商ID
	CouponID string `json:"coupon_id" bson:"coupon_id"` // 优惠券ID
	Num      int    `json:"num" bson:"num"`             // 数量
}

// CouponAccountCreateReq 优惠券账户
type CouponAccountCreateReq struct {
	CouponID string `json:"coupon_id" bson:"coupon_id"` // 优惠券ID
	Num      int    `json:"num" bson:"num"`             // 数量
}

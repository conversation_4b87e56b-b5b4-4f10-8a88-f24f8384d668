package types

import "base/model"

type CategoryCreate struct {
	ParentID  string         `json:"parent_id"`                // 所属父节点
	Name      string         `json:"name" validate:"required"` // 分类名称
	Img       model.FileInfo `json:"img" validate:"-"`         // 图标
	IsSpecial bool           `json:"is_special"`               // 特殊分类
}

// CategoryUpdate 分类不能修改父节点
type CategoryUpdate struct {
	ID      string         `json:"id" validate:"len=24"`
	Name    string         `json:"name" validate:"required"` // 分类名称
	Img     model.FileInfo `json:"img" validate:"-"`         // 图标
	Visible bool           `json:"visible"`                  // 是否展示
}

type CategoryUpdateSort struct {
	List []Sort `json:"list"`
}

type CategorySort struct {
	ID   string `json:"id" validate:"len=24"`
	Sort int    `json:"sort"` // 排序
}

// CategoryRes 分类树形列表返回
type CategoryRes struct {
	ID       string         `json:"id"`
	ParentID string         `json:"parent_id"`          // 所属父节点
	Name     string         `json:"name"`               // 分类名称
	Img      model.FileInfo `json:"img" validate:"-"`   // 图标
	Sort     int            `json:"sort"`               // 排序
	Children []*CategoryRes `json:"children,omitempty"` // 子节点
}

// CategoryProduct 特殊分类添加商品列表信息
type CategoryProduct struct {
	ID         string   `json:"id" validate:"len=24"`
	ProductIds []string `json:"product_ids"` // 添加的商品分类

}

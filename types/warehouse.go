package types

import "base/model"

// CreateWarehouseReq 集中仓
type CreateWarehouseReq struct {
	Name              string            `json:"name" validate:"required"` // 中心仓名称
	Note              string            `json:"note"`                     // 备注
	AuthenticationReq AuthenticationReq `json:"authentication_req"  validate:"dive"`
}

type WarehouseRes struct {
	model.Warehouse
	IsMobileVerify bool   `json:"is_mobile_verify"`
	PayMobile      string `json:"pay_mobile"`
}

type QualityReq struct {
	ID                      string            `json:"id" validate:"len=24"` // 备货单ID
	QualityServicePointList []PointQualityNum `json:"quality_service_point_list" validate:"min=1"`
}

type PointQualityNum struct {
	ServicePointID string `json:"service_point_id" validate:"len=24"` // 备货单ID
	QualityNum     int    `json:"quality_num" validate:"min=0"`
}

package types

import (
	"base/model"
)

type UserAddrCreate struct {
	BuyerID  string         `json:"buyer_id"` // 具体地址
	Address  string         `json:"address"`  // 具体地址
	Contact  model.Contact  `json:"contact"`  // 收货人信息
	Location model.Location `json:"location"` // 地址经纬度
}

type UserAddrUpdate struct {
	ID       string         `json:"id" validate:"-"`       // id
	Address  string         `json:"address" validate:"-"`  // 具体地址
	Contact  model.Contact  `json:"contact" validate:"-"`  // 收货人信息
	Location model.Location `json:"location" validate:"-"` // 地址经纬度
}

type UserAddrAuditReq struct {
	Entity                  int                    `json:"entity"` // 类型
	AddressID               string                 `json:"address_id"`
	AuditFailReason         string                 `json:"audit_fail_reason"`
	AuditStatus             model.AuditStatusType  `json:"audit_status"`
	ServicePointId          string                 `json:"service_point_id"`
	AddressNote             string                 `json:"address_note"`
	DeliverFee              int                    `json:"deliver_fee"`
	SubsidyAmount           int                    `json:"subsidy_amount"`
	SubsidyPercent          int                    `json:"subsidy_percent"`
	DeliverType             []model.DeliverType    `json:"deliver_type"`
	DeliverFreeBegin        int64                  `json:"deliver_free_begin"`         // 配送限免期-开始
	DeliverFreeEnd          int64                  `json:"deliver_free_end"`           // 配送限免期-结束
	LogisticsNote           string                 `json:"logistics_note"`             // 物流备注
	LogisticsUnitFee        int                    `json:"logistics_unit_fee"`         // 物流费单价
	InstantDeliver          []model.InstantDeliver `json:"instant_deliver"`            // 即时配送
	ServiceFee              int                    `json:"service_fee"`                // 服务费
	ServiceFeeRebatePercent int                    `json:"service_fee_rebate_percent"` // 服务费-返利比例
	UserType                string                 `json:"user_type"`
}

type UpdateAddrAdminReq struct {
	Entity    int    `json:"entity"` // 类型
	AddressID string `json:"address_id"`
	//ServicePointId        string                 `json:"service_point_id"`
	AddressNote           string                 `json:"address_note"`
	DeliverFee            int                    `json:"deliver_fee"`
	DeliverSubsidyAmount  int                    `json:"deliver_subsidy_amount"`
	DeliverSubsidyPercent int                    `json:"deliver_subsidy_percent"`
	DeliverType           []model.DeliverType    `json:"deliver_type"`
	DeliverFreeBegin      int64                  `json:"deliver_free_begin"` // 配送限免期-开始
	DeliverFreeEnd        int64                  `json:"deliver_free_end"`   // 配送限免期-结束
	LogisticsNote         string                 `json:"logistics_note"`     // 物流备注
	LogisticsUnitFee      int                    `json:"logistics_unit_fee"` // 物流费单价
	InstantDeliver        []model.InstantDeliver `json:"instant_deliver"`    // 即时配送
	//PriceLevel            int                    `json:"price_level"`        // 价格等级（加价） +0 +5 +10
	ServiceFee              int    `json:"service_fee"`                // 服务费
	ServiceFeeRebatePercent int    `json:"service_fee_rebate_percent"` // 服务费-返利比例
	UserType                string `json:"user_type"`
}

type UpdateAddrLocationReq struct {
	AddressID string         `json:"address_id"`
	Address   string         `bson:"address" json:"address"`   // 具体地址
	Location  model.Location `bson:"location" json:"location"` // 地址经纬度
}

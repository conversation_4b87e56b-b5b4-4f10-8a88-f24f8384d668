package types

import "base/model"

type YeeMerchantUpdateReq struct {
	ID                      string                        `json:"id"`
	MerchantSubjectInfo     model.MerchantSubjectInfo     `json:"merchant_subject_info"`     // 主体信息
	MerchantCorporationInfo model.MerchantCorporationInfo `json:"merchant_corporation_info"` // 商户法人信息
	MerchantContactInfo     model.MerchantContactInfo     `json:"merchant_contact_info"`     // 商户联系人
	BusinessAddressInfo     model.BusinessAddressInfo     `json:"business_address_info"`     // 经营地址信息
	SettlementAccountInfo   model.SettlementAccountInfo   `json:"settlement_account_info"`   // 结算账户信息
}

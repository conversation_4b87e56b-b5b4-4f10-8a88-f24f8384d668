package types

import "base/model"

// DeliverFeeDetailReq 配送费明细查询请求
type DeliverFeeDetailReq struct {
	MonthTimestamp int64 `json:"month_timestamp" validate:"required"` // 月份的毫秒级时间戳
}

type DeliverFeeDetailStatsResp struct {
	TotalDeliverFee   int `json:"total_deliver_fee"`   // 总配送费
	SubsidyDeliverFee int `json:"subsidy_deliver_fee"` // 补贴配送费
	FinalDeliverFee   int `json:"final_deliver_fee"`   // 最终配送费
}

// DeliverFeeDetailResp 配送费明细响应
type DeliverFeeDetailResp struct {
	model.DeliverFeeDetail
	BuyerName string `json:"buyer_name"`
}

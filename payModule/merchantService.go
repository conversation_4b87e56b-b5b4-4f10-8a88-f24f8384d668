package payModule

import (
	"base/global"
	"github.com/cnbattle/allinpay"
	pays "github.com/cnbattle/allinpay/service"
)

type MerchantService interface {
	// QueryMerchantBalance 平台账户集余额查询
	QueryMerchantBalance(req pays.QueryMerchantBalanceReq) (pays.QueryMerchantBalanceRes, error)
	QueryReserveFundBalance(req pays.QueryReserveFundBalanceReq) (pays.QueryReserveFundBalanceRes, error)
}

type merchant struct {
	cli *allinpay.AllInPay
}

func NewMerchantS() MerchantService {
	return &merchant{
		cli: global.AllinPayClient,
	}

}

func (s merchant) QueryMerchantBalance(req pays.QueryMerchantBalanceReq) (pays.QueryMerchantBalanceRes, error) {
	res, err := s.cli.MerchantService.QueryMerchantBalance(req)
	if err != nil {
		return pays.QueryMerchantBalanceRes{}, err
	}
	return res, nil
}

// QueryReserveFundBalance 平台头寸查询
func (s merchant) QueryReserveFundBalance(req pays.QueryReserveFundBalanceReq) (pays.QueryReserveFundBalanceRes, error) {
	res, err := s.cli.MerchantService.QueryReserveFundBalance(req)
	if err != nil {
		return pays.QueryReserveFundBalanceRes{}, err
	}
	return res, nil
}

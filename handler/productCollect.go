package handler

import (
	"base/api/product/productCollect"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 商品收藏
func productCollectRouter(r *gin.RouterGroup) {
	r = r.Group("/product/collect")

	r2 := r.Group("/").Use(middleware.CheckToken)
	r2.POST("/upsert", productCollect.Upsert)
	r2.POST("/delete", productCollect.Delete)
	r2.POST("/list", productCollect.List)
	r2.POST("/check", productCollect.Check)
}

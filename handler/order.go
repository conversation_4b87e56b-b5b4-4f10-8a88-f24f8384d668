package handler

import (
	"base/api/order"
	"base/api/orderDebt"
	"base/api/orderServicePoint"
	"base/api/orderStats"
	"base/api/orderStockUp"
	"base/api/orderSupplier"
	"base/api/orderWarehouse"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// 订单
func orderRouter(r *gin.RouterGroup) {
	r = r.Group("/order")
	r.Use(middleware.CheckToken)

	r.POST("/create", order.Create)
	r.POST("/note/update", order.UpdateOrderNote)
	r.POST("/service/point/note/update", order.UpdateServicePointOrderNote)

	r.POST("/amount/calc", order.AmountCalc)
	r.POST("/deliver/fee/calc", order.DeliverFeeCalc)

	r.POST("/list/buyer", order.ListByBuyer)
	r.POST("/list/by/station", order.ListByStation)
	r.POST("/list/buyer/id", order.ListByBuyerID)
	r.POST("/list/to/pay/buyer", order.ListToPayByBuyer)
	r.POST("/list/to/receive/buyer", order.ListToReceiveByBuyer)
	r.POST("/list/to/ship/buyer", order.ListToShipByBuyer)

	r.POST("/list/parent", order.ListByParentOrder)

	r.POST("/status/buyer", order.ListStatusByBuyerID)

	r.POST("/get", order.Get)
	r.POST("/get/by/num", order.GetByNum)
	// r.POST("/get/product/image", order.GetOrderProductImage)
	r.POST("/get/product/image/by/order", order.GetOrderProductImageByOrderID)

	//	支付
	r.POST("/to/pay", order.ToPay)
	// 余额支付确认
	r.POST("/balance/pay/confirm", order.BalancePayConfirm)
	r.POST("/balance/pay/resend/sms", order.ResendBalancePaySMS)
	// 父单
	//r.POST("/parent/get", order.GetParentOrder)
	r.POST("/parent/get/pure", order.GetParentOrderPure)

	r.POST("/close", order.Close) // 关闭订单
	r.POST("/delete", order.Delete)
	r.POST("/cancel", order.Cancel) // 取消订单

	r.POST("/refund", order.Refund)                  // 售后-退款-单商品
	r.POST("/refund/withdraw", order.WithdrawRefund) // 撤销售后
	r.POST("/refund/confirm", order.ConfirmRefund)
	r.POST("/refund/re/submit", order.ReSubmitRefund) // 再次申请售后
	r.POST("/refund/get", order.GetRefund)
	r.POST("/list/after/sale", order.ListAfterSale)
	r.POST("/list/after/sale/by/buyer", order.ListAfterSaleByBuyer)
	r.POST("/refund/ship/for/product", order.GetShipRefundByProduct)
	r.POST("/refund/after/sale/for/product", order.GetAfterSaleRefundByProduct)
	r.POST("/refund/after/sale/for/order", order.ListAfterSaleRefund)
	//r.POST("/refund/list/all", order.ListAllRefund)
	r.POST("/refund/ship/for/order", order.ListShipRefund)
	r.POST("/refund/quality/for/product", order.GetQualityRefund)

	// 补差
	r.POST("/debt/get", orderDebt.GetByOrder)
	r.POST("/debt/get/by/id", orderDebt.GetByID)
	r.POST("/debt/list", orderDebt.List)
	r.POST("/debt/list/by/list", orderDebt.ListByBuyer)
	r.POST("/debt/to/pay", orderDebt.ToDebtPay)
	r.POST("/debt/exist/check", orderDebt.ExistDebtOrder)
	r.POST("/debt/count/not/paid", orderDebt.CountNotPaidByBuyer)
	r.POST("/debt/paid/refund", orderDebt.DebtRefund)

	//	供应商
	//	 备货
	r.POST("/stock/up/list", orderStockUp.ListStockUp)
	r.POST("/stock/up/list/has", orderStockUp.ListHasStockUp)
	r.POST("/stock/up/get", orderStockUp.GetStockUp)
	r.POST("/stock/up/to/do/list", orderStockUp.ListToDoStockUpOrder)
	r.POST("/doing/list/supplier", orderStockUp.ListDoingOrderForSupplier)
	//r.POST("/stock/up/to/do/list/all", orderStockUp.ListToDoStockUpOrderAll)
	//r.POST("/stock/up/order/add", orderStockUp.AddOrderToStockUp) // 加入备货组
	//r.POST("/stock/up/update", orderStockUp.UpdateStockUp)
	r.POST("/stock/up/do", orderStockUp.UpdateOrderToStockUp) // 加入备货   -- 临时
	r.POST("/tab/bar/stats/quality", orderStockUp.TabBarStats)
	r.POST("/count/not/confirm", orderStockUp.CountNotConfirm)

	// 订单
	r.POST("/list/supplier", orderSupplier.ListBySupplier)
	r.POST("/after/sale/list/supplier", orderSupplier.ListAfterSaleBySupplier)
	// 集中仓
	// 备货
	//r.POST("/quality/list", orderWarehouse.ListQuality)
	//r.POST("/quality/list/has", orderWarehouse.ListHasQuality)
	//r.POST("/quality/update", orderWarehouse.UpdateQuality)
	r.POST("/quality/stats", orderWarehouse.StatsQuality)
	r.POST("/list/to/stock/up", orderWarehouse.ListOrderToStockUp)
	r.POST("/do/stock/up", orderWarehouse.OrderDoStockUp)
	r.POST("/quality/list/temp", orderWarehouse.ListQualityTemp)
	r.POST("/quality/update/temp", orderWarehouse.UpdateQualityTemp)
	//r.POST("/quality/list/has/temp", orderWarehouse.ListHasQualityTemp)
	r.POST("/quality/list/search", orderWarehouse.SearchQuality)
	r.POST("/buy/stats", orderWarehouse.GetBuyStats)
	r.POST("/buy/stats/monthly/profit", orderWarehouse.GetMonthlyProfit)
	r.POST("/buy/stats/refresh", orderWarehouse.RefreshBuyStats)
	r.POST("/purchase/stats", orderStats.PurchaseStats)

	// 分拣
	//r.POST("/sort/list", orderWarehouse.ListSort)
	//r.POST("/sort/list/has", orderWarehouse.ListHasSort)
	r.POST("/sort/list/order", orderWarehouse.ListSortOrder)
	//r.POST("/sort/update", orderWarehouse.UpdateSort)
	r.POST("/sort/query", orderWarehouse.QuerySort)
	r.POST("/address/get", orderWarehouse.GetAddress)
	r.POST("/sort/list/temp", orderWarehouse.ListSortTemp)
	r.POST("/sort/list/search", orderWarehouse.SearchSort)
	r.POST("/sort/update/temp", orderWarehouse.UpdateSortTemp)
	r.POST("/sort/update/photo", orderWarehouse.UpdateSortPhoto)
	r.POST("/sort/list/order/temp", orderWarehouse.ListSortOrderTemp)

	// 发货
	//r.POST("/ship/list", orderWarehouse.ListShip)
	r.POST("/ship/list/temp", orderWarehouse.ListShipTemp)
	r.POST("/ship/list/by/buyer", orderWarehouse.ListShipByBuyer)
	r.POST("/ship/adjust", orderWarehouse.Adjust)
	//r.POST("/ship", orderWarehouse.ToShip)
	r.POST("/ship/temp", orderWarehouse.ToShipTemp)

	// 告警
	r.POST("/warning/ship", orderWarehouse.WarningShip)
	r.POST("/warning/over/weight", orderWarehouse.ListOverWeight)

	//	服务仓
	// 品控
	r.POST("/service/point/confirm/stats", orderServicePoint.StatsConfirm)
	r.POST("/service/point/confirm/not", orderServicePoint.ListNotConfirmOrder)

	// 交付
	r.POST("/service/point/arrive/to/list", orderServicePoint.ListToArrive)
	r.POST("/service/point/arrive", orderServicePoint.Arrive)
	//r.POST("/service/point/arrive/to/self/get", orderServicePoint.ListToSelfGet)
	r.POST("/service/point/list/self/get", orderServicePoint.ListSelfGet)
	r.POST("/service/point/list/logistics", orderServicePoint.ListLogistics)
	r.POST("/service/point/receive", orderServicePoint.Receive)
	r.POST("/service/point/receive/single", orderServicePoint.ReceiveSingle)
	r.POST("/service/point/logistics/create", orderServicePoint.LogisticsCreate)
	r.POST("/service/point/list/order", orderServicePoint.ListOrderForPoint)
	r.POST("/service/point/to/allot", orderServicePoint.ListToAllot)
	r.POST("/service/point/list/buyer", orderServicePoint.ListByBuyerID)

	// 即时配送
	r.POST("/service/point/list/instant", orderServicePoint.ListInstant)
	r.POST("/service/point/list/buyer/instant", orderServicePoint.ListByBuyerIDInstant)

	// 自提订单列表
	r.POST("/service/point/list/buyer/self/get", orderServicePoint.ListByBuyerIDSelfGet)
	// 物流
	r.POST("/service/point/list/buyer/logistics", orderServicePoint.ListByBuyerIDLogistics)

	r.POST("/service/point/allot", orderServicePoint.Allot)
	r.POST("/service/point/allot/list", orderServicePoint.AllotList)
	r.POST("/service/point/warning/receive", orderServicePoint.WarningReceive)
	r.POST("/service/point/warning/center", orderServicePoint.WarningDeliverCenter)
}

package handler

import (
	"base/api/invite"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// InviteRouter 邀请
func InviteRouter(r *gin.RouterGroup) {
	r = r.Group("/invite")

	r.Use(middleware.CheckToken)

	r.POST("/config/get", invite.GetInviteConfig)

	r.POST("/record/list", invite.ListRecord)
	//r.POST("/config/get", buyer.GetInviteConfig)
	//r.POST("/config/get", buyer.GetInviteConfig)
}

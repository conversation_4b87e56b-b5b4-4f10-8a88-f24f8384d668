package handler

import (
	"base/api/captcha"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 验证码
func captchaRouter(r *gin.RouterGroup) {
	r = r.Group("/captcha")
	r.POST("/send", captcha.Send)

	r2 := r.Group("/").Use(middleware.CheckToken)

	r2.POST("/send/pay/bind/mobile", captcha.SendPayBindMobile)
	r2.POST("/send/pay/bind/mobile/by/auth", captcha.SendPayBindMobileByAuth)

	r2.POST("/send/pay/unbind/mobile/by/auth", captcha.SendPayUnbindMobile)
}

package handler

import (
	"base/api/buyerBalance"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 余额
func buyerBalanceDepositRouter(r *gin.RouterGroup) {
	r = r.Group("/buyer/balance")

	r.Use(middleware.CheckToken)

	// 充值
	r.POST("/account/deposit", buyerBalance.Deposit)
	r.POST("/account/deposit/record", buyerBalance.ListDepositByBuyer)

	r.POST("/account/deposit/record/list/web", buyerBalance.ListDepositWeb)

	//	 余额
	//r.POST("/account/get", buyerBalance.GetAccount)

	r.POST("/account/withdraw", buyerBalance.Withdraw)

	r.POST("/record/list", buyerBalance.ListRecord)
}

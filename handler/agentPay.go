package handler

import (
	agentpay "base/api/agentPay"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

func agentPayRouter(r *gin.RouterGroup) {
	r = r.Group("/order/agent/pay")
	r.Use(middleware.CheckToken)
	r.POST("/list/by/order", agentpay.ListByOrder)
	r.POST("/list/by/supplier/export", agentpay.Export)

	r.POST("/not/amount", agentpay.GetNotPayAmount)
	r.POST("/not/list", agentpay.ListNotPay)

	r.POST("/service/point/bill/stats", agentpay.PointBillStats)
}

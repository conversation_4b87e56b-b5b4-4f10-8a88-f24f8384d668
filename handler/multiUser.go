package handler

import (
	"base/api/multiUser"
	"github.com/gin-gonic/gin"
)

// 多用户
func multiUserRouter(r *gin.RouterGroup) {
	r = r.Group("/multi/user")
	//
	r2 := r.Group("/")
	r2.POST("/create", multiUser.Create)
	r2.POST("/update", multiUser.Update)
	r2.POST("/delete", multiUser.Delete)
	//r2.POST("/list/by/warehouse", route.ListByWarehouse)
	//
	//r.Use(middleware.CheckUID, middleware.CheckToken)

}

package handler

import (
	"base/api/userAddr"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

func userAddrRouter(r *gin.RouterGroup) {
	r = r.Group("/user/addr")

	r.Use(middleware.CheckToken)

	r.POST("/add", userAddr.Add)
	r.PUT("/update", userAddr.Update)
	//r.PUT("/update/all", userAddr.UpdateAll)
	r.POST("/delete", userAddr.Del)
	r.GET("/list", userAddr.List)
	r.POST("/list/by/user", userAddr.ListByUser)
	r.POST("/list/by/buyer", userAddr.ListByBuyer)
	r.POST("/search/by/address", userAddr.SearchByAddress)
	r.POST("/get", userAddr.Get)
	r.POST("/get/default", userAddr.GetDefault)
}

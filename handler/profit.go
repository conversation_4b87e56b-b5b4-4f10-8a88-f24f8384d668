package handler

import (
	"base/api/profit"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// 利润
func profitRouter(r *gin.RouterGroup) {
	r = r.Group("/profit")

	r.Use(middleware.CheckToken)

	r.POST("/get/res", profit.GetRes)
	// r.POST("/list/settlement", profit.ListSettlement)
	// r.POST("/list/settlement/by/supplier", profit.ListSettlementBySupplier)
	// r.POST("/settle", profit.SettleProfit)
}

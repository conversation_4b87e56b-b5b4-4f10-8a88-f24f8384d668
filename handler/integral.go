package handler

import (
	"base/api/Integral"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// integralRouter 积分
func integralRouter(r *gin.RouterGroup) {
	r = r.Group("/integral")

	r.Use(middleware.CheckToken)

	r.POST("/account/get/user", Integral.GetByUser)

	r.POST("/account/list", Integral.ListAccount)

	r.POST("/record/list", Integral.ListRecord)

	r.POST("/product/list", Integral.ListProduct)
	r.POST("/product/get", Integral.GetProduct)

	//r.POST("/config/get", buyer.GetInviteConfig)
	//r.POST("/config/get", buyer.GetInviteConfig)
}

package handler

import (
	"base/api/sys"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

func sysRouter(r *gin.RouterGroup) {
	r = r.Group("/sys")

	r2 := r.Group("/")
	r2.POST("/sign/back/url", sys.SignAccUrl)

	r2.POST("/test", sys.Test)
	r2.POST("/test2", sys.Test2)
	r2.POST("/test3", sys.Test3)
	r2.POST("/test4", sys.Test4)
	r2.POST("/test/sms", sys.TestSMS)

	r2.POST("/excel", sys.ImportExcel)
	r2.POST("/excel/export/yht", sys.ExportYHT)
	r2.POST("/pre/data", sys.PreGetData)
	r2.POST("/by/token", sys.ByToken)
	r2.POST("/uuid", sys.GetUUID)
	r2.POST("/version/check", sys.CheckVersion)

	r2.POST("/announce/get", sys.AnnounceGet)
	r2.POST("/announce/get/yht", sys.YHTAnnounceGet)
	r2.POST("/announce/get/web", sys.AnnounceGetByWeb)

	r2.POST("/product/common/img/get", sys.GetProductCommonImg)

	r.Use(middleware.CheckToken)

	// ali oss
	r.GET("/oss/upload/sign/:dir", sys.GetOssUploadSign)
	r.POST("/oss/down", sys.Down)

	r.POST("/ocr/do", sys.Ocr)

	r.POST("/card/bin", sys.CardBin)

	//	 详情图之后
	r.POST("/product/common/img/update", sys.UpdateProductCommonImg)

	//	 公告
	r.POST("/announce/upsert", sys.AnnounceUpsert)
	r.POST("/announce/upsert/yht", sys.YHTAnnounceUpsert)
	r.POST("/announce/list", sys.ListAnnounce)

}

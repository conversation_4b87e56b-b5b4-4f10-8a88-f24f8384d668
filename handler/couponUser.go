package handler

import (
	"base/api/couponUser"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// 代金券账户
func couponUserRouter(r *gin.RouterGroup) {
	r = r.Group("/coupon/user")

	r.Use(middleware.CheckToken)

	r.POST("/list", couponUser.List)

	r.POST("/get", couponUser.Get)

	r.POST("/get/coupon/num", couponUser.GetCouponNum)

	r.POST("/list/by/stock", couponUser.ListByStock)

	r.POST("/send", couponUser.SendCoupon)

	//r.POST("/get/do", coupon.Do)
	//r.GET("/get/new/user/buyer", coupon.GetByBuyer)
}

package handler

import (
	"base/api/couponStock"
	"base/api/orderStats"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// 代金券
func couponRouter(r *gin.RouterGroup) {
	r = r.Group("/coupon")

	r.POST("/stock/get", couponStock.Get)

	r2 := r.Use(middleware.CheckToken)

	//	优惠券
	r2.POST("/stock/create", couponStock.Create)
	r2.POST("/stock/update", couponStock.Update)
	r2.POST("/stock/delete", couponStock.Delete)
	r2.POST("/stock/list", couponStock.List)

	r2.POST("/stats/subsidy/by/supplier", orderStats.GetSupplierCouponSubsidy)

	//r.POST("/get/do", coupon.Do)
	//r.GET("/get/new/user/buyer", coupon.GetByBuyer)
}

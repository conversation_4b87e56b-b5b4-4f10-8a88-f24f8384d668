package handler

import (
	"base/core/cmd"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterHandlers(c *cmd.Server) {
	InitSocketManager()

	e := c.Engine
	e.NoMethod(middleware.HandleNotFound)
	e.NoRoute(middleware.HandleNotFound)

	e.Use(middleware.GinLogger)
	//e.Use(gin.Logger())
	e.Use(middleware.Cors)
	e.Use(middleware.StopScan)

	back := e.Group("/")
	// 支付异步后台通知
	backRouter(back)
	mnsRouter(back)
	contactRouter(back)

	ws := e.Group("/")
	ws.GET("/ws", func(ctx *gin.Context) {
		handleWebSocket(ctx.Writer, ctx.Request)
	})

	//track.POST("/api/track", sys.Track)
	//track.POST("/api/track/list/user", sys.ListTrackByUser)

	// 环境
	//e.Use(middleware.CheckEnv)
	//e.Use(middleware.Timeout)
	e.Use(middleware.GinRecovery(true))

	api := e.Group("/api")
	api.Use(middleware.CheckEnv)
	captchaRouter(api)
	//api.Use(middleware.CheckToken)
	//api.Use(middleware.CheckAdmin(s))

	//区域
	regionRouter(api)

	authenticationRouter(api)

	yeeMerchantRouter(api)

	sysRouter(api)

	trackRouter(api)

	protocolRouter(api)
	userRouter(api)

	divisionRouter(api)

	//	采购商
	buyerRouter(api)
	buyerManagerRouter(api)

	InviteRouter(api)

	buyerActiveRouter(api)

	//	分类
	categoryRouter(api)
	// 分类标签
	tagRouter(api)

	//	供应商
	supplierRouter(api)

	supplierCollectRouter(api) // 收藏

	//集中仓
	warehouseRouter(api)

	// 服务点
	servicePointRouter(api)

	stationRouter(api)

	// 商品添加
	productRouter(api)

	productCollectRouter(api) // 收藏

	//	管理员
	adminRouter(api)

	// 路线
	routeRouter(api)

	// 用户收货地址
	userAddrRouter(api)
	// 订单
	orderRouter(api)

	orderDeliverNoteRouter(api)

	// 配送指派
	deliverAssignRouter(api)

	deliverScopeRouter(api)

	commentRouter(api)

	// 支付单
	payOrderRouter(api)

	// 支付账户
	payAccountRouter(api)

	// 首页
	indexRouter(api)

	promoteRouter(api)

	promoteNewRouter(api)

	// 优惠券
	couponRouter(api)

	couponUserRouter(api)

	//	浏览记录
	browseRouter(api)

	buyerBalanceDepositRouter(api)

	//	提现
	withdrawRouter(api)

	statsRouter(api)

	multiUserRouter(api)

	invoiceTitleRouter(api)
	invoiceRouter(api)

	billRouter(api)

	brandRouter(api)

	supplierBillRouter(api)

	warehouseLoadFeeRouter(api)

	marketingAccountRouter(api)

	agentPayRouter(api)

	integralRouter(api)

	integralOrderRouter(api)

	authUserRouter(api)

	retailRouter(api)

	profitRouter(api)

	// 订单最终结算记录
	orderFinalSettleRouter(api)

	// 配送费明细
	deliverFeeDetailRouter(api)

	// 订单调整结算记录
	orderAdjustSettleRouter(api)

}

package handler

import (
	"base/api/category"
	"github.com/gin-gonic/gin"
)

// 分类
func categoryRouter(r *gin.RouterGroup) {
	r = r.Group("/category")

	//r.Use(middleware.CheckUID, middleware.CheckToken)
	r.GET("/next/:parent_id/:visible_type", category.ListNext)
	r.GET("/list/first/:visible_type", category.ListFirst)
	r.POST("/detail", category.GetDetail)
	r.POST("/second/list/by/id", category.ListSecondByIDs)
}

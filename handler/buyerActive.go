package handler

import (
	"base/api/buyer"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 采购商-活跃时间
func buyerActiveRouter(r *gin.RouterGroup) {
	r = r.Group("/buyer/active")

	r.Use(middleware.CheckToken)

	r.POST("/expire/apply", buyer.ActiveExpireApply)
	r.POST("/expire/audit", buyer.ActiveExpireAudit)
	r.POST("/expire/update", buyer.ActiveExpireUpdate)
	r.POST("/expire/apply/list", buyer.ActiveExpireList)
}

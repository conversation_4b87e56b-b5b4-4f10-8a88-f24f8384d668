package handler

import (
	"base/api/notify"
	"base/global"

	"github.com/gin-gonic/gin"
)

var docUrl = "/api/doc/notify"

// 支付后台通知
func backRouter(r *gin.RouterGroup) {
	r.POST(global.BackUrl, notify.PayNotify)

	r.POST(global.BackUrlSignAcctProtocol, notify.PayNotifySignAcct)                 // 提现协议
	r.POST(global.BackUrlSignAcctProtocolPersonal, notify.PayNotifySignAcctPersonal) // 提现协议--个人银行卡

	r.POST(global.BackUrlWithdraw, notify.PayNotifyWithdraw)

	// 会员提现
	r.POST(global.BackUrlWithdrawBuyer, notify.PayNotifyWithdrawBuyer)

	r.POST(global.BackUrlAgentCollect, notify.PayNotifyAgentCollect)         //	托管代收
	r.POST(global.BackUrlAgentCollectDebt, notify.PayNotifyAgentCollectDebt) //	托管代收-补差

	r.POST(global.BackUrlSignalAgentPayTemp, notify.PayNotifySignalAgentPayTemp) // 单笔代付

	r.POST(global.BackUrlRefund, notify.PayNotifyRefund)
	r.POST(global.BackUrlRefundManual, notify.PayNotifyRefundManual)
	r.POST(global.BackUrlRefundDeliver, notify.PayNotifyRefundDeliver)

	//r.POST(global.BackUrlCancel, notify.PayNotifyCancel) // 取消订单

	r.POST(docUrl, notify.DocNotify) // 文档处理

	r.POST(global.BackUrlIntegralPay, notify.PayNotifyIntegralAgentCollect)              // 积分-托管代收
	r.POST(global.BackUrlIntegralCancel, notify.PayNotifyIntegralCancel)                 // 积分-取消
	r.POST(global.BackUrlIntegralSignalAgentPay, notify.PayNotifyIntegralSignalAgentPay) // 单笔代付

	//r.POST(global.BackUrlCancel, notify.PayNotifyCancel) // 大额转账入账的通知地址

	// jsapi
	r.POST(global.NotifyUrlPayNormalOrder, notify.NormalOrderPay) // 下单支付
	r.POST(global.NotifyUrlPayDebtOrder, notify.DebtOrderPay)     // 补差支付

	r.POST(global.NotifyUrlRefundNormalOrderCancel, notify.NormalOrderCancel)    // 取消订单
	r.POST(global.NotifyUrlRefundNormalOrderRefund, notify.PayNotifyRefundJSAPI) // 订单退款

	//	 yeePay
	r.POST(global.NotifyUrlYeePayMerchantRegister, notify.YeeRegister) // 入网
	r.POST(global.NotifyUrlYeePayMerchantBusiness, notify.YeeBusiness) // 业务结果

	r.POST(global.NotifyUrlYeePayTradeOrder, notify.YeeTradeOrder)                 // 业务结果
	r.POST(global.NotifyUrlYeePayTradeOrderDebt, notify.YeeTradeOrderDebt)         // 业务结果
	r.POST(global.NotifyUrlYeePayAggTutelagePrePay, notify.YeeAggTutelagePrePay)   // 业务结果
	r.POST(global.NotifyUrlYeePayAccountBookPay, notify.YeeAccountBookPay)         // 记账簿支付
	r.POST(global.NotifyUrlYeePayAccountBookPayDebt, notify.YeeAccountBookPayDebt) // 记账簿支付

	r.POST(global.NotifyUrlYeePayAccountBookDeposit, notify.YeeAccountBookDeposit) // 记账簿-充值提现

	r.POST(global.NotifyUrlYeePayTradeRefundCancel, notify.YeeTradeRefundCancel)             // 子单退款
	r.POST(global.NotifyUrlYeePayTradeRefundAfterSale, notify.YeeTradeRefundAfterSale)       // 子单退款
	r.POST(global.NotifyUrlYeePayTradeRefundQuality, notify.YeeTradeRefundQuality)           // 子单退款
	r.POST(global.NotifyUrlYeePayTradeRefundAdjustSettle, notify.YeeTradeRefundAdjustSettle) // 调整结算退款
	r.POST(global.NotifyUrlYeePayDebtPaidRefund, notify.YeeTradeRefundDebtPaid)              // 补差-退款
	r.POST(global.NotifyUrlYeePayTradeRefundDeliverFee, notify.YeeTradeRefundDeliver)        // 子单退款
	r.POST(global.NotifyUrlYeePayTradeRefundManual, notify.NotifyUrlYeePayTradeRefundManual) // 子单退款
	r.POST(global.NotifyUrlYeePayRefundAccountBookCharge, notify.YeeAccountBookRefund)       // 记账簿充值-退款

	r.POST(global.NotifyUrlYeePayWithdraw, notify.YeeWithdraw) // 提现
	r.POST(global.NotifyUrlYeePayTransfer, notify.YeeTransfer) // 转账

	r.POST(global.NotifyUrlYeeProductModify, notify.YeeProductModify)

}

package handler

import (
	"base/api/promoteNew"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 推广
func promoteNewRouter(r *gin.RouterGroup) {
	r = r.Group("/promote/new")

	r.POST("/list", promoteNew.List)

	r2 := r.Use(middleware.CheckToken)

	r2.POST("/create", promoteNew.Create)
	r2.POST("/delete", promoteNew.Delete)
	r2.POST("/get", promoteNew.Get)
	r2.POST("/list/by/point", promoteNew.ListByPoint)
}

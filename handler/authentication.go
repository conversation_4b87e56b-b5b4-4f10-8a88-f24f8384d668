package handler

import (
	"base/api/authentication"
	"base/api/withdraw"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 认证
func authenticationRouter(r *gin.RouterGroup) {
	r = r.Group("/authentication")

	r.Use(middleware.CheckToken)

	r.POST("/get", authentication.Get)
	r.POST("/get/by/buyer", authentication.GetByBuyer)
	r.POST("/get/by/point", authentication.GetByPoint)
	r.POST("/get/by/station", authentication.GetByStation)
	r.POST("/get/member", authentication.GetMember)
	r.POST("/get/member/by/buyer", authentication.GetMemberByBuyer)
	r.POST("/update", authentication.Update)
	// 绑定支付手机号
	r.POST("/bind/pay/phone", authentication.BindPayMobile)
	r.POST("/bind/pay/phone/by/auth", authentication.BindPayMobileByAuth)
	r.POST("/bind/pay/phone/by/station", authentication.BindPayMobileByStation)
	r.POST("/send/pay/phone/by/station", authentication.SendPayBindMobileByStation)
	r.POST("/unbind/pay/mobile/by/auth", authentication.UnbindPayMobile)
	r.POST("/company/info/set", authentication.SetCompany)
	r.POST("/company/bind/personal/bank", authentication.BindCompanyPersonalBank)
	// 提现协议
	r.POST("/sign/acct/protocol", withdraw.SignAcctProtocol)
	r.POST("/sign/acct/protocol/buyer", withdraw.SignAcctProtocolBuyer)
	r.POST("/sign/acct/protocol/query", withdraw.SignContractQuery)
	r.POST("/sign/acct/protocol/front/back", withdraw.SignContractFrontBack)

	//	银行卡
	r.POST("/bank/unbind", authentication.UnbindBank)
	r.POST("/bank/unbind/individual", authentication.UnbindIndividualBank)
	r.POST("/bank/apply/bind", authentication.ApplyBindBank)
}

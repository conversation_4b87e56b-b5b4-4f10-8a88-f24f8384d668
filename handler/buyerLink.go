package handler

import (
	"base/api/buyerManager"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

// 会员维护
func buyerManagerRouter(r *gin.RouterGroup) {
	r = r.Group("/buyer")

	r.Use(middleware.CheckToken)

	r.POST("/manager/link/create", buyerManager.CreateLink)
	r.POST("/manager/link/delete", buyerManager.DeleteLink)
	r.POST("/manager/link/list", buyerManager.ListLinkByManager)
	// 维护人
	r.POST("/manager/list", buyerManager.List)
	r.POST("/manager/create", buyerManager.Create)
	r.POST("/manager/update", buyerManager.Update)
	r.POST("/manager/delete", buyerManager.Delete)
	r.POST("/manager/get/by/buyer", buyerManager.GetByBuyer)
	r.POST("/manager/get/by/user", buyerManager.GetByUser)

	r.POST("/manager/stats/gen", buyerManager.GenStats)

}

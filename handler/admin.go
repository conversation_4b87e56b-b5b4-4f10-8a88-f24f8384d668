package handler

import (
	"base/api/admin/adminAddress"
	"base/api/admin/adminBuyer"
	"base/api/admin/adminCategory"
	adminDeposiSet "base/api/admin/adminDepositSet"
	"base/api/admin/adminFile"
	"base/api/admin/adminIndexPart"
	"base/api/admin/adminIndexPartProduct"
	"base/api/admin/adminInvoice"
	"base/api/admin/adminNormalUser"
	"base/api/admin/adminOrder"
	"base/api/admin/adminPayOcr"
	"base/api/admin/adminProduct"
	"base/api/admin/adminProductCommission"
	"base/api/admin/adminPromote"
	"base/api/admin/adminRoute"
	"base/api/admin/adminServicePoint"
	"base/api/admin/adminShortcut"
	"base/api/admin/adminSupplier"
	"base/api/admin/adminSwipe"
	"base/api/admin/adminTopic"
	"base/api/admin/adminUser"
	"base/api/admin/adminWarehouse"
	adminIntegralProduct "base/api/admin/adminintegralProduct"
	buyer "base/api/invite"
	"base/api/orderStats"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// 管理员
func adminRouter(r *gin.RouterGroup) {
	r = r.Group("/admin")

	r.Use(middleware.CheckToken)

	// 管理员
	rAdmin := r.Group("/")

	//.Use(middleware.CheckSuperAdmin)

	rAdmin.POST("/user/create", adminUser.Create)
	rAdmin.POST("/user/update", adminUser.Update)
	rAdmin.POST("/user/delete", adminUser.Delete)
	rAdmin.POST("/get/user", adminUser.GetByUser)
	rAdmin.POST("/user/auth/list", adminUser.ListAuth)

	rAdmin.POST("/normal/user/create", adminNormalUser.Create)
	rAdmin.POST("/normal/user/regex/mobile", adminNormalUser.ListByRegexMobile)
	rAdmin.POST("/normal/user/list", adminNormalUser.List)

	r.POST("/user/list", adminUser.List)

	// pay ocr 采集
	r.POST("/pay/ocr", adminPayOcr.AuditOcr)

	r.POST("/buyer/audit", adminBuyer.Audit)
	r.POST("/buyer/assign/point", adminBuyer.AssignPoint)
	r.POST("/buyer/list", adminBuyer.List)
	r.POST("/buyer/order/count", adminBuyer.CountOrderBuyer)
	r.POST("/buyer/list/audit", adminBuyer.ListAuditing)
	r.POST("/buyer/count", adminBuyer.Count)
	r.POST("/buyer/count/audit", adminBuyer.CountAudit)
	r.POST("/buyer/invoice/status/update", adminBuyer.UpdateInvoiceStatus)
	r.POST("/buyer/account/status/update", adminBuyer.UpdateAccountStatus)

	// 地址
	rAddr := r.Group("/address")
	rAddr.POST("/list", adminAddress.List)
	rAddr.POST("/audit", adminAddress.Audit)
	rAddr.POST("/assign/point", adminAddress.AssignPoint)
	rAddr.POST("/count", adminAddress.Count)
	rAddr.POST("/update", adminAddress.Update)
	rAddr.POST("/location/update", adminAddress.UpdateLocation)
	//rAddr.POST("/logistics/unit/fee/update", adminAddress.UpdateLogisticsUnitFee)
	rAddr.POST("/delete", adminAddress.Del)

	// 供应商
	r.POST("/supplier/audit", adminSupplier.Audit)
	r.POST("/supplier/list", adminSupplier.List)
	r.POST("/supplier/list/by/tag", adminSupplier.ListByTag)

	// 服务点
	r.POST("/service/point", adminServicePoint.Create)
	r.POST("/service/point/update/open", adminServicePoint.UpdateOpen)
	r.POST("/service/point/update/supplier/fee", adminServicePoint.UpdateSupplierFee)
	r.POST("/service/point/list", adminServicePoint.List)

	// 集中仓
	// 集中仓相关资料
	r.POST("/warehouse", adminWarehouse.Create)

	r.POST("/warehouse/list", adminWarehouse.List)
	r.POST("/warehouse/update", adminWarehouse.Update)

	// 分类操作管理
	r.POST("/category", adminCategory.Add)
	r.POST("/category/update", adminCategory.Update)
	r.POST("/category/sort/update", adminCategory.UpdateSort)
	r.POST("/category/delete", adminCategory.Del)
	r.POST("/category/product/update", adminCategory.UpdateProduct)
	r.POST("/category/list/first", adminCategory.ListFirstAll)
	r.POST("/category/list/next/all", adminCategory.ListNextAll)
	r.POST("/category/second/special/list", adminCategory.ListSecondSpecial)
	r.POST("/category/second/list/by/id", adminCategory.ListSecondByIDs)

	// 积分商品
	r.POST("/integral/product/create", adminIntegralProduct.Create)
	r.POST("/integral/product/update/data", adminIntegralProduct.UpdateData)
	r.POST("/integral/product/delete", adminIntegralProduct.Delete)
	r.POST("/integral/product/update/stock", adminIntegralProduct.UpdateStock)
	r.POST("/integral/product/update/sort", adminIntegralProduct.UpdateSort)
	r.POST("/integral/product/update/status", adminIntegralProduct.UpdateStatus)
	r.POST("/integral/product/list", adminIntegralProduct.List)

	// 商品
	r.POST("/product/audit", adminProduct.Audit)
	r.POST("/product/commission/update", adminProduct.UpdateCommission)
	r.POST("/product/desc/update", adminProduct.UpdateDesc)
	r.POST("/product/external/sale/update", adminProduct.UpdateExternalSale)
	r.POST("/product/commission/update/batch", adminProduct.UpdateCommissionBatch)
	r.POST("/product/audit/list", adminProduct.ListAudit)
	r.POST("/product/external/sale/list", adminProduct.ListExternalSale)
	//r.POST("/product/apply/get", adminProduct.Get)
	r.POST("/product/search", adminProduct.SearchForAdmin)
	r.POST("/product/stats", adminProduct.Stats)
	r.POST("/product/stats/sale", orderStats.ProductSaleStats)
	// 商品佣金
	r.POST("/product/commission/get", adminProductCommission.GetByProduct)
	//r.POST("/product/commission/update", adminProductCommission.Update)

	// 路线
	r.POST("/route/create", adminRoute.Create)
	r.POST("/route/update", adminRoute.Update)
	r.POST("/route/list", adminRoute.ListByWarehouse)

	// 轮播图
	rUI := r.Group("/")

	r.POST("/swipe/list", adminSwipe.List)
	rUI.POST("/swipe", adminSwipe.Create)
	rUI.POST("/swipe/update", adminSwipe.Update)
	rUI.POST("/swipe/update/sort", adminSwipe.UpdateSort)
	rUI.POST("/swipe/delete", adminSwipe.Delete)

	// 快捷栏
	r.POST("/shortcut/list", adminShortcut.List)
	rUI.POST("/shortcut", adminShortcut.Create)
	rUI.POST("/shortcut/update", adminShortcut.Update)
	rUI.POST("/shortcut/update/sort", adminShortcut.UpdateSort)
	rUI.POST("/shortcut/update/product", adminShortcut.UpdateProduct)
	rUI.POST("/shortcut/delete", adminShortcut.Delete)

	// 推广
	r.POST("/promote/create", adminPromote.Create)
	r.POST("/promote/list", adminPromote.List)
	r.POST("/promote/update/data", adminPromote.UpdateData)
	r.POST("/promote/update/status", adminPromote.UpdateStatus)
	r.POST("/promote/delete", adminPromote.Delete)

	// 主题
	r.POST("/topic/list", adminTopic.List)
	rUI.POST("/topic", adminTopic.Create)
	rUI.POST("/topic/update", adminTopic.Update)
	rUI.POST("/topic/update/product", adminTopic.UpdateProduct)
	rUI.DELETE("/topic/:id", adminTopic.Delete)

	// 专区
	r.POST("/index/part/update", adminIndexPart.Update)
	r.POST("/index/part/list", adminIndexPart.List)

	r.POST("/index/part/product/update", adminIndexPartProduct.Update)
	r.POST("/index/part/product/update/sort", adminIndexPartProduct.UpdateSort)

	//	保证金设置
	r.POST("/deposit/set", adminDeposiSet.Create)
	r.POST("/deposit/set/update", adminDeposiSet.Update)
	r.GET("/deposit/set/list", adminDeposiSet.List)

	//	订单
	r.POST("/order/list", adminOrder.List)
	r.POST("/order/stats/by/buyer", adminOrder.StatsByBuyer)
	r.POST("/order/list/by/buyer", adminOrder.ListByBuyer)
	r.POST("/order/list/yht", adminOrder.ListYHT)
	r.POST("/order/list/yht/export", adminOrder.DownExcel)

	r.POST("/order/retail/list", adminOrder.ListRetail)
	r.POST("/order/retail/refund/list", adminOrder.ListRetailRefundOrder)
	r.POST("/order/retail/ship", adminOrder.ShipRetail)
	r.POST("/order/retail/confirm", adminOrder.ConfirmRetail)

	r.POST("/order/cancel", adminOrder.Cancel)

	r.POST("/order/refund/list", adminOrder.ListRefundOrder)
	r.POST("/order/refund/list/yht", adminOrder.ListRefundOrderYHT)
	r.POST("/order/refund/list/by/product", adminOrder.ListRefundByProduct)

	r.POST("/order/debt/free", adminOrder.FreeDebt)

	r.POST("/invite/config/update", buyer.UpdateInviteConfig)

	r.POST("/order/refund/audit/count", adminOrder.CountAudit)

	rAfterSale := r.Group("/")

	//.Use(middleware.CheckAfterSaleAdmin)

	rAfterSale.POST("/order/refund/audit", adminOrder.AuditRefundOrder)

	//	发票
	r.POST("/invoice/audit", adminInvoice.Audit)
	r.POST("/invoice/issue", adminInvoice.Issue)
	r.POST("/invoice/list", adminInvoice.List)

	// 文件
	r.POST("/file/list", adminFile.ListFile)
}

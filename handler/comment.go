package handler

import (
	"base/api/comment"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

func commentRouter(r *gin.RouterGroup) {
	r = r.Group("/comment")

	r1 := r.Group("/")
	r1.Use(middleware.CheckToken)

	r1.POST("/create", comment.Create)
	r1.POST("/create/cus", comment.CreateCus)
	r1.POST("/reply", comment.Reply)
	r1.POST("/delete", comment.Delete)
	r1.POST("/delete/self", comment.DeleteSelf)
	r1.POST("/get", comment.Get)
	r1.POST("/get/order/product", comment.GetByOrderProduct)
	r1.POST("/center/list", comment.ListCenter)
	r1.POST("/list/supplier", comment.ListBySupplier)
	r1.POST("/list/manage", comment.ListByWeb)
	r1.POST("/list/buyer", comment.ListByBuyer)

	// 审核
	r1.POST("/audit", comment.Audit)

	//	 无需登录
	r2 := r.Group("/")
	r2.POST("/list/product", comment.ListByProduct)
}

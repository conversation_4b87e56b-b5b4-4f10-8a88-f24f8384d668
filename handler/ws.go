package handler

import (
	"base/model"
	"base/service/jwtService"
	"base/service/trackService"
	"base/util"
	"context"
	"encoding/json"
	"github.com/gorilla/websocket"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"log"
	"net/http"
	"strings"
	"sync"
	"time"
)

var upGrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	// 解决跨域问题
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

type Session struct {
	Conn          *websocket.Conn
	Authorization string             `json:"authorization"`
	BuyerID       primitive.ObjectID `json:"buyer_id"`
	IP            string             `json:"ip"`
}

type SessionManager struct {
	sessions map[string]*Session
	lock     sync.RWMutex
}

var sessionManager *SessionManager

func InitSocketManager() {
	sessionManager = NewSManager()
}

func NewSManager() *SessionManager {
	s := make(map[string]*Session)
	manager := &SessionManager{
		sessions: s,
		//lock:     sync.RWMutex{},
	}
	return manager
}

func (sm *SessionManager) AddSession(id string, session *Session) {
	zap.S().Infof("加入")
	sm.lock.Lock()
	defer sm.lock.Unlock()

	if len(id) == 0 {
		return
	}

	if sm.sessions == nil {
		s := make(map[string]*Session)
		s[id] = session
		sm.sessions = s
	}

	sm.sessions[id] = session

	trackService.NewTrackService().RecordEnterApp(session.BuyerID, session.IP, len(sm.sessions))

}

func (sm *SessionManager) RemoveSession(id string) {
	sm.lock.Lock()
	defer sm.lock.Unlock()

	if len(id) == 0 {
		return
	}

	//var ip string
	//var uid primitive.ObjectID

	if sm.sessions != nil {
		if _, ok := sm.sessions[id]; ok {
			//ip = sm.sessions[id].IP
			//uid = sm.sessions[id].UserID
			delete(sm.sessions, id)
		}
	}

	trackService.NewTrackService().RecordLeaveApp(len(sm.sessions))
}

func (sm *SessionManager) GetSession(id string) *Session {
	sm.lock.RLock()
	defer sm.lock.RUnlock()

	return sm.sessions[id]
}

func handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := upGrader.Upgrade(w, r, nil)
	if err != nil {
		log.Println(err)
		return
	}

	authorization := r.Header.Get("Authorization")

	//ip := r.re.Request.Header.Get("X-Real-IP")
	//if ip == "" {
	//	ip = ctx.Request.Header.Get("X-Forwarded-For")
	//}
	//if ip == "" {
	ip := r.RemoteAddr
	if strings.Contains(ip, ":") {
		split := strings.Split(ip, ":")
		if len(split) < 1 {
			zap.S().Warnf("ws ip截取为空")
		} else {
			ip = split[0]
		}
	}
	//}

	var buyerID primitive.ObjectID
	var id string

	if len(authorization) > 0 {
		myClaims, err := jwtService.NewJwtService().ParseToken(authorization)
		if err != nil {
			// 未登录，不连接
			zap.S().Infof("ws 无授权信息，使用ip")
			//return
		}
		if myClaims != nil && len(myClaims.ID) == 24 {
			buyerID, err = util.ConvertToObjectWithCtx(context.Background(), myClaims.ID)
			if err != nil {
				zap.S().Errorf("ws 解析授权信息异常，不连接")
				return
			}
			id = buyerID.Hex()
		}
	}

	if id == "" {
		id = ip
	}

	session := &Session{
		Conn:          conn,
		Authorization: authorization,
		BuyerID:       buyerID,
		IP:            ip,
	}

	// 将连接和会话关联起来
	sessionManager.AddSession(id, session)

	defer func() {
		// 从会话管理器中移除会话
		sessionManager.RemoveSession(id)

		// 关闭连接
		session.Conn.Close()

	}()

	for {
		// 读取消息
		messageType, message, readErr := conn.ReadMessage()
		_ = messageType

		if websocket.IsCloseError(readErr, 1005) {
			zap.S().Infof("关闭连接")
			//	关闭连接
			sessionManager.RemoveSession(id)
			session.Conn.Close()
			break
		}

		if websocket.IsCloseError(readErr, 1001) {
			zap.S().Infof("关闭连接")
			//	关闭连接
			sessionManager.RemoveSession(id)
			session.Conn.Close()
			break
		}

		if websocket.IsUnexpectedCloseError(err, 1006) {
			zap.S().Infof("超时-关闭连接")
			//	关闭连接
			sessionManager.RemoveSession(id)
			session.Conn.Close()
			break
		}

		if readErr != nil {
			zap.S().Warnf("%s", readErr.Error())
			break
		}

		// 处理消息
		//fmt.Println("Received message: ", string(message))

		go parseMessage(session, message)

		// 发送消息
		//err = conn.WriteMessage(websocket.TextMessage, []byte("Hello from server"))
		//if err != nil {
		//	log.Println(err)
		//	break
		//}
	}

}

type ReceiveMessage struct {
	Event   model.EventType `json:"event"`
	Content interface{}     `json:"content"`
}

func parseMessage(session *Session, msg []byte) {
	if err := recover(); err != nil {
		zap.S().Errorf("parseMessage error:%v", err)
		return
	}

	conn := session.Conn

	var r ReceiveMessage

	err := json.Unmarshal(msg, &r)
	if err != nil {
		zap.S().Errorf("解析ws消息失败：%s", err.Error())
		return
	}

	if r.Event != model.EventTypeHeartBeat {
		zap.S().Infof("来源：%s,消息：%s", session.BuyerID.Hex(), string(msg))
	}

	// 发送消息
	err = conn.WriteMessage(websocket.TextMessage, []byte("ok"))
	if err != nil {
		zap.S().Warnf("发送异常：%s", err.Error())
	}

	dealMsg(r, session)
}

func dealMsg(r ReceiveMessage, session *Session) {
	milli := time.Now().UnixMilli()
	data := model.Track{
		ID:      primitive.NewObjectID(),
		BuyerID: session.BuyerID,
		Event:   r.Event,
		IP:      session.IP,
	}

	switch r.Event {
	case model.EventTypeProductEnter, model.EventTypeProductLeave:
		data.CreatedAt = milli
		if r.Content != nil {
			idStr := r.Content.(string)
			pid, err := util.ConvertToObjectWithCtx(context.Background(), idStr)
			if err != nil {

			}
			data.ProductID = pid
		}

		break

	case model.EventTypeHeartBeat:
		trackService.NewTrackService().RecordStandingTime(session.BuyerID)
		return

	default:
		zap.S().Errorf("接收消息类型异常:%s", r.Event)
		return
	}

	trackService.NewTrackService().Create(context.Background(), data)
}

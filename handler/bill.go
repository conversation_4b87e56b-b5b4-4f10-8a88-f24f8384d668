package handler

import (
	"base/api/bill"
	"base/api/supplierBill"
	"base/core/middleware"
	"github.com/gin-gonic/gin"
)

func billRouter(r *gin.RouterGroup) {
	r = r.Group("/bill")
	r.Use(middleware.CheckToken)
	r.POST("/order/list", bill.ListOrderByBuyer)
	r.POST("/list", bill.ListByBuyer)
	//r.POST("/export", bill.Export)
	r.POST("/delete", bill.Delete)
}

func supplierBillRouter(r *gin.RouterGroup) {
	r = r.Group("/bill/supplier")
	r.Use(middleware.CheckToken)
	//r.POST("/order/list", bill.ListOrderByBuyer)
	r.POST("/list", supplierBill.ListBySupplier)
	r.POST("/list/final", supplierBill.ListFinalBill)
	//r.POST("/export", bill.Export)
	//r.POST("/delete", bill.Delete)
}

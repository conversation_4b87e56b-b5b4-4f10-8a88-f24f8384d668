package handler

import (
	"base/api/orderAdjustSettle"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

func orderAdjustSettleRouter(rg *gin.RouterGroup) {
	r := rg.Group("/order/adjust/settle")

	r.Use(middleware.CheckToken)

	// 创建调整结算记录
	r.POST("/create", orderAdjustSettle.Create)

	// 获取调整结算记录详情
	r.POST("/get", orderAdjustSettle.Get)

	// 根据订单ID获取调整结算记录
	r.POST("/get/by/order", orderAdjustSettle.GetByOrderID)

	// 查询调整结算记录列表
	r.POST("/list", orderAdjustSettle.List)

	// 查询调整结算记录列表
	r.POST("/list/by/buyer", orderAdjustSettle.ListByBuyer)

	// 查询调整结算记录列表
	r.POST("/list/by/supplier", orderAdjustSettle.ListBySupplier)

	// 编辑调整结算记录(仅草稿状态)
	r.POST("/update", orderAdjustSettle.Update)

	// 二次确认调整结算记录
	r.POST("/confirm", orderAdjustSettle.Confirm)

	// 取消调整结算记录
	r.POST("/close", orderAdjustSettle.Close)
}

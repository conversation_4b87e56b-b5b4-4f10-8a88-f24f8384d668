package cmd

import (
	"base/core/config"
	"log"
	"os"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type Server struct {
	Engine *gin.Engine
	c      *config.Config
}

func NewServer() *Server {
	gin.SetMode(config.Conf.Mode)
	e := gin.New()

	//e.Use(middleware.GinLogger)
	//e.Use(gin.Recovery())

	//err := e.SetTrustedProxies([]string{"127.0.0.1"})
	//if err != nil {
	//	zap.L().Error("error ",zap.Error(err))
	//}

	return &Server{Engine: e, c: config.Conf}
}

func (s *Server) Start() {
	addr := ":" + s.c.App.Port
	if s.c.Mode == "debug" {
		//addr = "127.0.0.1" + addr
	}

	hostname, err := os.Hostname()
	if err != nil {
		log.Fatalln(err)
	}
	zap.S().Info("run on "+addr, "\thost:", hostname)

	err = s.Engine.Run(addr)
	if err != nil {
		log.Fatalln(err)
	}
}

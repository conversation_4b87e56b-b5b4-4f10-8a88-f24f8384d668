package xhttp

import (
	"base/core/validate"
	"base/core/xerr"
	"base/model"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"io"
	"runtime"
)

func Parse(ctx *gin.Context, data any) error {
	pc, file, line, ok := runtime.Caller(1)
	f := runtime.FuncForPC(pc)
	if !ok {
		zap.S().<PERSON><PERSON><PERSON>("获取栈错误")
	}
	err := parse(ctx, data, "")
	if err != nil {
		requestID := getRequestID(ctx)
		zap.S().Errorf("requestID:%s,at %s:%d (Method %s) Cause by: %v", requestID, file, line, f.Name(), err)
		return err
	}
	return nil
}

func ParseUser(ctx *gin.Context, data any) (primitive.ObjectID, error) {
	pc, file, line, ok := runtime.Caller(1)
	f := runtime.FuncForPC(pc)
	if !ok {
		zap.S().Errorf("获取栈错误")
	}

	err := parse(ctx, data, "")
	if err != nil {
		requestID := getRequestID(ctx)
		zap.S().Errorf("requestID:%s,at %s:%d (Method %s) Cause by: %v", requestID, file, line, f.Name(), err)
		return primitive.NilObjectID, err
	}

	userID, err := UserID(ctx)
	if err != nil {
		requestID := getRequestID(ctx)
		zap.S().Errorf("requestID:%s,at %s:%d (Method %s) Cause by: %v", requestID, file, line, f.Name(), err)
		return [12]byte{}, err
	}

	return userID, err
}

func ParseEnv(ctx *gin.Context, data any) (model.ObjectType, error) {
	err := parse(ctx, data, "")
	if err != nil {
		return 0, err
	}

	env := ctx.GetInt("X-Env")
	objectType, err := model.BackObjectType(env)
	if err != nil {
		return 0, err
	}
	return objectType, nil
}

func ParsePage(ctx *gin.Context, data any) (Page, error) {
	err := parse(ctx, data, "")
	if err != nil {
		return Page{}, err
	}
	var page Page
	err = ctx.ShouldBindUri(&page)
	if err != nil {
		RespErr(ctx, xerr.NewErr(xerr.ErrParamError, err, "分页参数错误"))
		return Page{}, err
	}
	return page, nil
}

func parse(ctx *gin.Context, data any, note string) error {
	err := ctx.ShouldBindUri(data)
	if err != nil {
		requestID := getRequestID(ctx)
		zap.S().Errorf("requestID:%s, Cause by: %v", requestID, err.Error())
		RespErr(ctx, xerr.NewErr(xerr.ErrParamError, err, note+"参数错误:"+err.Error()))
		return err
	}

	err = ctx.ShouldBindJSON(data)
	if err != nil && err != io.EOF {
		requestID := getRequestID(ctx)
		zap.S().Errorf("requestID:%s, Cause by: %v", requestID, err.Error())
		RespErr(ctx, xerr.NewErr(xerr.ErrParamError, err, note+"参数错误:"+err.Error()))
		return err
	}

	if data == nil {
		return nil
	}
	err = validate.Struct(data)
	if err != nil {
		requestID := getRequestID(ctx)
		zap.S().Errorf("requestID:%s, Cause by: %v", requestID, err.Error())
		RespErr(ctx, xerr.NewErr(xerr.ErrParamError, err, note+"参数错误:"+err.Error()))
		return err
	}
	return nil
}

type Page struct {
	Page  int `uri:"page"`
	Limit int `uri:"limit"`
}

func getRequestID(ctx *gin.Context) string {
	return ctx.GetString("rid")
}

package xhttp

import (
	"base/model"
	"base/service/supplierService"
	"base/util"
	"errors"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"
)

func CheckSupplier(ctx *gin.Context) (model.Supplier, error) {
	userIDStr := ctx.GetString("user_id")
	objectID, err := util.ConvertToObject(userIDStr)
	if err != nil {
		RespErr(ctx, err)
		return model.Supplier{}, err
	}

	data, err := supplierService.NewSupplierService().GetByUser(objectID)
	if err == mongo.ErrNoDocuments {
		RespNoExist(ctx)
		return model.Supplier{}, err
	}
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		RespErr(ctx, err)
		return model.Supplier{}, err
	}

	return data, nil
}

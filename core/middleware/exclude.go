package middleware

import "github.com/gin-gonic/gin"

var excludeRoute = map[string]int{
	"/api/product/list/category":                   0,
	"/api/product/list/ids":                        0,
	"/api/product/supplier/for/user":               0,
	"/api/product/search":                          0,
	"/api/product/supplier/audit":                  0,
	"/api/product/supplier/audit/get":              0,
	"/api/product/list/index/part":                 0,
	"/api/product/unit/list":                       0,
	"/api/product/fruit/class/list/category":       0,
	"/api/product/list/supplier":                   0,
	"/api/product/cart/list":                       0,
	"/api/product/external/sale/list":              0,
	"/api/category/second/list/by/id":              0,
	"/api/protocol/get":                            0,
	"/api/order/list/buyer":                        0, //	 order
	"/api/order/list/buyer/id":                     0, //	 order
	"/api/admin/order/list/by/buyer":               0, //	 order
	"/api/order/list/to/pay/buyer":                 0,
	"/api/order/list/to/receive/buyer":             0,
	"/api/order/list/parent":                       0,
	"/api/order/status/buyer":                      0,
	"/api/order/get":                               0,
	"/api/order/get/by/num":                        0,
	"/api/invoice/down/excel":                      0,
	"/api/order/list/after/sale":                   0,
	"/api/order/refund/get":                        0,
	"/api/order/refund/ship/for/product":           0,
	"/api/order/refund/quality/for/product":        0,
	"/api/order/debt/get":                          0,
	"/api/order/stock/up/list":                     0, //	 备货
	"/api/order/stock/up/get":                      0,
	"/api/admin/user/list":                         0,
	"/api/order/stock/up/to/do/list":               0,
	"/api/order/list/supplier":                     0,
	"/api/order/quality/list":                      0,
	"/api/order/quality/stats":                     0,
	"/api/order/sort/list":                         0,
	"/api/order/list/order":                        0,
	"/api/order/address/get":                       0,
	"/api/order/ship/list":                         0,
	"/api/order/service/point/arrive/to/list":      0,
	"/api/order/service/point/arrive/to/self/get":  0,
	"/api/order/service/point/arrive/to/deliver":   0,
	"/api/order/service/point/list/order":          0,
	"/api/service/point/list/location":             0,
	"/api/buyer/user/":                             0,
	"/api/buyer/list/latest/active":                0,
	"/api/index/part/list":                         0,
	"/api/index/swipe/list":                        0,
	"/api/index/shortcut/list":                     0,
	"/api/index/topic/list":                        0,
	"/api/route/service/point":                     0,
	"/api/order/debt/exist/check":                  0,
	"/api/product/list/supplier/for/user":          0,
	"/api/product/browse/create":                   0,
	"/api/route/service/point/arrive":              0,
	"/api/product/get":                             0,
	"/api/supplier/stats/for/user":                 0,
	"/api/sys/desc/img/list":                       0,
	"/api/index/shortcut/get":                      0,
	"/api/admin/buyer/list":                        0,
	"/api/admin/category/list/first":               0,
	"/api/buyer/search":                            0,
	"/api/admin/category/list/next/all":            0,
	"/api/stats/warehouse/ship/list":               0,
	"/api/admin/warehouse/list":                    0,
	"/api/admin/index/part/list":                   0,
	"/api/stats/warehouse/ship/total":              0,
	"/api/admin/order/list":                        0,
	"/api/buyer/user":                              0,
	"/api/order/amount/calc":                       0,
	"/api/stats/today":                             0,
	"/api/category/next/:parent_id/:visible_type":  0,
	"/api/category/list/first/:visible_type":       0,
	"/api/supplier/user/:user_id":                  0,
	"/api/supplier/user/get":                       0,
	"/api/supplier/get":                            0,
	"/api/route/list/by/warehouse":                 0,
	"/api/warehouse/get/user":                      0,
	"/api/service/point/user/:user_id":             0,
	"/api/product/list/supplier/audit":             0,
	"/api/product/list/supplier/audit/get":         0,
	"/api/category/detail":                         0,
	"/api/supplier/tag/list":                       0,
	"/api/supplier/search":                         0,
	"/api/product/list/search":                     0,
	"/api/user/:user_id":                           0,
	"/api/user//qr/code":                           0,
	"/api/product/cart/num":                        0,
	"/api/admin/topic/list":                        0,
	"/api/product/cart/upsert":                     0,
	"/api/admin/route/list":                        0,
	"api/admin/supplier/list":                      0,
	"/api/product/tag/list":                        0,
	"/api/admin/shortcut/list":                     0,
	"/api/order/service/point/to/allot":            0,
	"/api/stats/warehouse/single/product/local":    0,
	"/api/order/deliver/assign/qr/get":             0,
	"/api/order/service/point/confirm/not":         0,
	"/api/admin/product/external/sale/list":        0,
	"/api/admin/product/search":                    0,
	"/api/order/list/to/stock/up":                  0,
	"/api/stats/warehouse/single/product":          0,
	"/api/order/quality/list/has":                  0,
	"/api/order/sort/query":                        0,
	"/api/order/stock/up/to/do/list/all":           0,
	"/api/stats/warehouse/single/product/temp":     0,
	"/api/stats/warehouse/ship/total/temp":         0,
	"/api/order/quality/list/temp":                 0,
	"/api/order/quality/list/has/temp":             0,
	"/api/order/sort/list/search":                  0,
	"/api/order/quality/list/search":               0,
	"/api/order/sort/list/temp":                    0,
	"/api/order/sort/list/has/temp":                0,
	"/api/order/sort/list/order/temp":              0,
	"/api/admin/order/refund/list":                 0,
	"/api/order/service/point/list/buyer/self/get": 0,
	"/api/order/service/point/list/self/get":       0,
	"/api/order/ship/list/temp":                    0,
	"/api/admin/product/audit/list":                0,
	"/api/order/service/point/allot/list":          0,
	"/api/order/tab/bar/stats/warehouse":           0,
	"/api/user/addr/get":                           0,
	"/api/order/debt/list":                         0,
	"/api/product/browse/list":                     0,
	"/api/product/list/search/supplier":            0,
	//	 统计
	"/api/stats/supplier/all/sale/monthly": 0,
	"/api/stats/online":                    0,
	"/api/stats/order":                     0,
	"/api/invoiceTitle/calc":               0,
	"/api/invoiceTitle/order/finish":       0,
	"/api/invoiceTitle/down/excel":         0,
	"/api/sys/announce/get":                0,
	"/api/sys/announce/get/web":            0,
	//	发票
	"/api/invoice/title/list":        0,
	"/api/invoice/title/get":         0,
	"/api/comment/center/list":       0,
	"/api/comment/center/get":        0,
	"/api/comment/get/order/product": 0,
	"/api/comment/list/product":      0,
	"/api/comment/list/manage":       0, // web

	//	 代金券
	"/api/coupon/account/list":           0,
	"/api/coupon/account/list/for/order": 0,
	"/api/coupon/get":                    0,

	//	代付
	"/api/order/agent/pay/list/by/order": 0,

	//	 积分
	"/api/integral/account/list":       0,
	"/api/integral/product/list":       0,
	"/api/admin/integral/product/list": 0,
	"/api/integral/record/list":        0,
	"/api/integral/account/get/user":   0,

	// 维护人
	"/api/buyer/link/user/list": 0,
	"/api/buyer/link/list":      0,
	"/api/buyer/link/count":     0,

	// 采购
	"/api/purchase/cart/list":           0,
	"/api/purchase/order/calc":          0,
	"/api/purchase/order/list":          0,
	"/api/purchase/order/list/supplier": 0,

	"/api/promote/list":            0,
	"/api/brand/get":               0,
	"/api/admin/order/retail/list": 0,

	"/api/admin/order/list/yht/export": 0,
	"/api/admin/order/list/yht/":       0,
	"/api/admin/order/refund/list/yht": 0,
	"/api/admin/order/stats/by/buyer":  0,

	"/api/promote/new/get":           0,
	"/api/promote/new/list/by/point": 0,
	"/api/promote/new/list":          0,

	"/ws":                      0,
	"/api/yee/merchant/upload": 0,
}

var excludeRouteAlways = map[string]int{
	"/api/protocol/get":               0,
	"/api/user/qr/code":               0,
	"/api/admin/order/stats/by/buyer": 0,
}

func isExclude(path, fullPath string) bool {
	if _, ok := excludeRouteAlways[fullPath]; ok {
		return true
	}

	if gin.Mode() == gin.DebugMode {
		return false
	}
	if _, ok := excludeRoute[fullPath]; ok {
		return true
	}

	if _, ok := excludeRoute[path]; ok {
		return true
	}

	return false
}

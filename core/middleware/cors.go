package middleware

import (
	"base/core/xerr"
	"base/core/xhttp"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"net/http"
)

func Cors(c *gin.Context) {
	method := c.Request.Method

	c.Header("Access-Control-Allow-Origin", "*")
	c.<PERSON>("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, UPDATE")
	c.<PERSON><PERSON>("Access-Control-Allow-Headers", "*")
	c.<PERSON><PERSON>("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
	c.<PERSON>er("Access-Control-Allow-Credentials", "true")

	//放行所有OPTIONS方法
	if method == "OPTIONS" {
		c.AbortWithStatus(http.StatusNoContent)
	}
	// 处理请求
	c.Next()
}

func StopScan(ctx *gin.Context) {
	ip := ctx.Request.Header.Get("X-Real-IP")
	if ip == "" {
		ip = ctx.Request.Header.Get("X-Forwarded-For")
	}
	if ip == "" {
		ip = ctx.Request.RemoteAddr
	}

	if ip == "**************" {
		zap.S().Warnf("拦截ip:%s", ip)
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrLoginExpire, nil))
		ctx.AbortWithStatus(http.StatusOK)
		return
	}

	ctx.Next()
}

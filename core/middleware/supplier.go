package middleware

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/model"
	"base/util"
	"github.com/gin-gonic/gin"
)

func CheckEnvSupplier(ctx *gin.Context) {
	env := getEnv(ctx)
	if env != model.ObjectTypeSupplier {
		xhttp.RespErr(ctx, xerr.NewErr(xerr.ErrEnvValue, nil, "错误，非供应商环境"))
		ctx.Abort()
		return
	}
	// 是否审核和状态
	userIDStr := ctx.GetString("user_id")

	objectID, err := util.ConvertToObject(userIDStr)
	if err != nil {
		xhttp.RespErr(ctx, err)
		ctx.Abort()
		return
	}
	_ = objectID

	//err = supplierService.NewSupplierService().CheckStatusByUserID(objectID)
	//if err != nil {
	//	xhttp.RespErr(ctx, err)
	//	ctx.Abort()
	//	return
	//}

	ctx.Next()
}

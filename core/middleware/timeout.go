package middleware

import (
	"context"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

func Timeout(c *gin.Context) {
	ctx, cancel := context.WithTimeout(c.Request.Context(), 30*time.Second)
	c.Request = c.Request.WithContext(ctx)
	finish := make(chan struct{})

	go func() {
		c.Next()
		finish <- struct{}{}
	}()

	select {
	case <-ctx.Done():
		c.<PERSON>.Header().Set("Transfer-Encoding", "true")
		cancel()
		c.AbortWithStatus(http.StatusRequestTimeout)
		return
	case <-finish:
		cancel()
	}
}

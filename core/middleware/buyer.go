package middleware

import (
	"base/core/xhttp"
	"base/service/buyerService"
	"base/util"
	"github.com/gin-gonic/gin"
)

func CheckBuyer(ctx *gin.Context) {
	// 是否审核和状态
	userIDStr := ctx.GetString("user_id")

	objectID, err := util.ConvertToObject(userIDStr)
	if err != nil {
		xhttp.RespErr(ctx, err)
		ctx.Abort()
		return
	}

	err = buyerService.NewBuyerService().CheckStatusByUserID(objectID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		ctx.Abort()
		return
	}

	ctx.Next()
}

package middleware

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
	"net/http"
	"time"
)

var limiter *rate.Limiter

func init() {
	limiter = rate.NewLimiter(10, 100)
}

func RateLimit(ctx *gin.Context) {
	defer func() {
		if err := recover(); err != nil {
			zap.S().Error(err)
			ctx.AbortWithStatusJSON(http.StatusInternalServerError, "系统忙")
			return
		}
	}()

	// 1
	//c, _ := context.WithTimeout(context.Background(), time.Second*1)
	//err := limiter.Wait(c)
	//if err != nil {
	//	fmt.Println("Error: ", err)
	//	ctx.AbortWithStatus(http.StatusTooManyRequests)
	//	return
	//}
	//	2
	f := limiter.AllowN(time.Now(), 5)
	if !f {
		ctx.AbortWithStatus(http.StatusTooManyRequests)
		return
	}
	ctx.Next()
}

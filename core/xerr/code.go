package xerr

const (
	ErrParamError = 4001 + iota
	ErrNoDocument
	ErrParamMissingAppID
	ErrParamMsgCodeNotExist
	ErrParamMsgCode
	ErrParamSku
	ErrOcrBank
	ErrOcrIdCard
	ErrOcrBusinessLicense
	ErrCategoryExistProduct
	ErrCategoryExistNextLevel
	ErrHasInvited
	ErrOcrIdCardFront
)

const (
	ErrOrder = 4101 + iota
	ErrOrderOverScope
	ErrOrderNoStock
	ErrOrderHasPaid // 已支付
)

const (
	ErrLoginExpire = 2001
)

const (
	ErrSysBusy = 5001 + iota
	ErrEnvMissing
	ErrEnvValue
)

var ErrCodeMsg = map[int]string{
	ErrLoginExpire:          "授权信息超时，请重新登录",
	ErrParamError:           "参数错误",
	ErrNoDocument:           "记录不存在",
	ErrEnvMissing:           "Header缺失参数:X-Env",
	ErrEnvValue:             "Header参数X-Env值错误",
	ErrParamMsgCodeNotExist: "验证码不存在",
	ErrParamMsgCode:         "验证码错误",
	ErrParamSku:             "sku参数错误，价格和库存不能为负数",
	ErrHasInvited:           "已被邀请",

	//	order
	ErrOrder:          "订单错误，请重试或联系客服",
	ErrOrderOverScope: "超出配送范围",
	ErrOrderNoStock:   "订单创建失败，部分产品库存不足",

	ErrOcrBank:            "银行卡识别错误",
	ErrOcrIdCard:          "身份证识别错误",
	ErrOcrBusinessLicense: "营业执照识别错误",

	//	sys
	ErrSysBusy: "系统忙，稍后重试",
}

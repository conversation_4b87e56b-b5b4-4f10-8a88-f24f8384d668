package validate

import (
	"errors"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zhtrans "github.com/go-playground/validator/v10/translations/zh"
)

var Validate *validator.Validate
var trans ut.Translator

func InitValidate() {
	//声明翻译对象
	uni := ut.New(zh.New())
	//设置翻译语言
	trans, _ = uni.GetTranslator("zh")
	//创建一个验证数据
	Validate = validator.New()
	//	注册自定义方法
	register(Validate)
	//注册默认翻译
	_ = zhtrans.RegisterDefaultTranslations(Validate, trans)
}

func Struct(data any) error {
	err := Validate.Struct(data)
	if err != nil {
		if validationErrors, ok := err.(validator.ValidationErrors); ok {
			n := validationErrors.Translate(trans)
			var msg string
			for _, v := range n {
				msg += v + ", "
			}
			msg = msg[:len(msg)-2]
			//zap.S().Warn("校验失败：", err)
			return errors.New(msg)
		}
		return err
	}
	return nil
}
